import { supabase } from '@/lib/supabase';

export class AuthService {
  static async getCurrentUser() {
    try {
      const result = await supabase.auth.getUser();

      // Handle specific auth errors
      if (result.error) {
        const error = result.error;
        if (error.message?.includes('User from sub claim in JWT does not exist') ||
            error.message?.includes('session_not_found') ||
            error.message?.includes('invalid_token') ||
            error.code === 'session_not_found') {
          // Clear invalid session
          await supabase.auth.signOut();
        }
      }

      return result;
    } catch (error) {
      console.error('AuthService.getCurrentUser error:', error);

      // Handle unexpected errors
      if (error instanceof Error &&
          (error.message?.includes('User from sub claim in JWT does not exist') ||
           error.message?.includes('session_not_found') ||
           error.message?.includes('invalid_token'))) {
        // Clear invalid session
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.error('Error signing out:', signOutError);
        }
      }

      throw error;
    }
  }

  static async getCustomerProfile(userId: string) {
    try {
      // Get current session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return { data: null, error: { message: 'Authentication required' } };
      }

      // Use the customer profile API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/customer/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return { data: null, error: { message: result.error || 'Failed to fetch customer profile' } };
      }

      return { data: result.profile, error: null };
    } catch (error) {
      console.error('Error in getCustomerProfile:', error);
      return { data: null, error: { message: 'An unexpected error occurred' } };
    }
  }

  static async hasCustomerProfile(userId: string) {
    try {
      // Get current session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return false;
      }

      // Use the customer profile exists API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/customer/profile/exists`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return false;
      }

      return result.exists || false;
    } catch (error) {
      console.error('Error in hasCustomerProfile:', error);
      return false;
    }
  }

  static async hasBusinessProfile(userId: string) {
    try {
      // Get current session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return false;
      }

      // Use the business profile exists API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/profile/exists`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return false;
      }

      return result.exists || false;
    } catch (error) {
      console.error('Error in hasBusinessProfile:', error);
      return false;
    }
  }

  static async getCustomerProfileForValidation(userId: string) {
    try {
      // Get current session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        return { data: null, error: { message: 'Authentication required' } };
      }

      // Use the customer profile API endpoint
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/customer/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      const result = await response.json();

      if (!response.ok) {
        return { data: null, error: { message: result.error || 'Failed to fetch customer profile' } };
      }

      // Extract only the validation fields
      const profile = result.profile;
      const validationData = {
        name: profile?.name,
        pincode: profile?.pincode,
        state: profile?.state,
        city: profile?.city,
        locality: profile?.locality,
        address_line: profile?.address_line,
        latitude: profile?.latitude,
        longitude: profile?.longitude,
      };

      return { data: validationData, error: null };
    } catch (error) {
      console.error('Error in getCustomerProfileForValidation:', error);
      return { data: null, error: { message: 'An unexpected error occurred' } };
    }
  }

  static async signOut() {
    return await supabase.auth.signOut();
  }

  static async signInWithMobilePassword(mobile: string, password: string) {
    return await supabase.auth.signInWithPassword({
      phone: mobile,
      password: password,
    });
  }
}