module.exports = {

"[project]/lib/actions/data:0e8eec [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899":"fetchBusinessReviews"},"lib/actions/reviews.ts",""] */ __turbopack_context__.s({
    "fetchBusinessReviews": (()=>fetchBusinessReviews)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var fetchBusinessReviews = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "fetchBusinessReviews"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/actions/data:4cd5e6 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7097170cd67aa830da6c5c6a671c70d1f195006853":"submitReview"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "submitReview": (()=>submitReview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var submitReview = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("7097170cd67aa830da6c5c6a671c70d1f195006853", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "submitReview"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/actions/data:9434ac [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40bf9c153311b3515e09fea919b70600d754f8d64c":"deleteReview"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "deleteReview": (()=>deleteReview)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var deleteReview = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40bf9c153311b3515e09fea919b70600d754f8d64c", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "deleteReview"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/actions/data:912de9 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca":"getInteractionStatus"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "getInteractionStatus": (()=>getInteractionStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var getInteractionStatus = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "getInteractionStatus"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vaW50ZXJhY3Rpb25zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHNlcnZlclwiO1xyXG5cclxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIkAvdXRpbHMvc3VwYWJhc2Uvc2VydmVyXCI7XHJcbmltcG9ydCB7IHJldmFsaWRhdGVQYXRoIH0gZnJvbSBcIm5leHQvY2FjaGVcIjtcclxuLy8gZ2V0U2VjdXJlQnVzaW5lc3NQcm9maWxlQnlTbHVnIGlzIGltcG9ydGVkIGJ1dCBub3QgdXNlZCBpbiB0aGlzIGZpbGVcclxuLy8gaW1wb3J0IHsgZ2V0U2VjdXJlQnVzaW5lc3NQcm9maWxlQnlTbHVnIH0gZnJvbSAnLi9zZWN1cmVCdXNpbmVzc1Byb2ZpbGVzJztcclxuLy8gaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycyc7IC8vIFJlbW92ZWQgdW51c2VkIGltcG9ydFxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN1YnNjcmliZVRvQnVzaW5lc3MoXHJcbiAgYnVzaW5lc3NQcm9maWxlSWQ6IHN0cmluZ1xyXG4pOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4ge1xyXG4gIC8vIGNvbnN0IGNvb2tpZVN0b3JlID0gY29va2llcygpOyAvLyBObyBsb25nZXIgbmVlZGVkIGhlcmVcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogeyB1c2VyIH0sXHJcbiAgICBlcnJvcjogYXV0aEVycm9yLFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuXHJcbiAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWQuXCIgfTtcclxuICB9XHJcblxyXG4gIC8vIFByZXZlbnQgYSBidXNpbmVzcyBmcm9tIHN1YnNjcmliaW5nIHRvIHRoZWlyIG93biBidXNpbmVzcyBjYXJkXHJcbiAgaWYgKHVzZXIuaWQgPT09IGJ1c2luZXNzUHJvZmlsZUlkKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgZXJyb3I6IFwiWW91IGNhbm5vdCBzdWJzY3JpYmUgdG8geW91ciBvd24gYnVzaW5lc3MgY2FyZC5cIixcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvLyBDaGVjayBpZiB0aGUgY3VycmVudCB1c2VyIGlzIGEgYnVzaW5lc3MgKGhhcyBhIGJ1c2luZXNzIHByb2ZpbGUpXHJcbiAgY29uc3QgeyBkYXRhOiB1c2VyQnVzaW5lc3NQcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgLnNlbGVjdChcImlkXCIpXHJcbiAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxyXG4gICAgLm1heWJlU2luZ2xlKCk7XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyAxLiBJbnNlcnQgc3Vic2NyaXB0aW9uIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogaW5zZXJ0RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwic3Vic2NyaXB0aW9uc1wiKVxyXG4gICAgICAuaW5zZXJ0KHsgdXNlcl9pZDogdXNlci5pZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSk7XHJcblxyXG4gICAgaWYgKGluc2VydEVycm9yKSB7XHJcbiAgICAgIC8vIEhhbmRsZSBwb3RlbnRpYWwgdW5pcXVlIGNvbnN0cmFpbnQgdmlvbGF0aW9uIChhbHJlYWR5IHN1YnNjcmliZWQpIGdyYWNlZnVsbHlcclxuICAgICAgaWYgKGluc2VydEVycm9yLmNvZGUgPT09IFwiMjM1MDVcIikge1xyXG4gICAgICAgIC8vIHVuaXF1ZV92aW9sYXRpb25cclxuICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgIGBVc2VyICR7dXNlci5pZH0gYWxyZWFkeSBzdWJzY3JpYmVkIHRvIGJ1c2luZXNzICR7YnVzaW5lc3NQcm9maWxlSWR9LmBcclxuICAgICAgICApO1xyXG4gICAgICAgIC8vIE9wdGlvbmFsbHkgcmV0dXJuIHN1Y2Nlc3MgdHJ1ZSBpZiBhbHJlYWR5IHN1YnNjcmliZWQgaXMgYWNjZXB0YWJsZVxyXG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICAgICAgfVxyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW5zZXJ0aW5nIHN1YnNjcmlwdGlvbjpcIiwgaW5zZXJ0RXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoaW5zZXJ0RXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm90ZTogV2UgZG9uJ3QgbmVlZCB0byBtYW51YWxseSB1cGRhdGUgdGhlIHN1YnNjcmlwdGlvbiBjb3VudFxyXG4gICAgLy8gVGhlIGRhdGFiYXNlIHRyaWdnZXIgJ3VwZGF0ZV90b3RhbF9zdWJzY3JpcHRpb25zJyB3aWxsIGhhbmRsZSB0aGlzIGF1dG9tYXRpY2FsbHlcclxuXHJcbiAgICAvLyAzLiBSZXZhbGlkYXRlIHBhdGhzXHJcbiAgICAvLyBSZXZhbGlkYXRlIHRoZSBzcGVjaWZpYyBjYXJkIHBhZ2UgYW5kIHBvdGVudGlhbGx5IHRoZSB1c2VyJ3MgZGFzaGJvYXJkXHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgLSBidXNpbmVzc19wcm9maWxlcyBoYXMgcHVibGljIHJlYWQgYWNjZXNzXHJcbiAgICBjb25zdCB7IGRhdGE6IGNhcmREYXRhIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbShcImJ1c2luZXNzX3Byb2ZpbGVzXCIpXHJcbiAgICAgIC5zZWxlY3QoXCJidXNpbmVzc19zbHVnXCIpXHJcbiAgICAgIC5lcShcImlkXCIsIGJ1c2luZXNzUHJvZmlsZUlkKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKGNhcmREYXRhPy5idXNpbmVzc19zbHVnKSB7XHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKGAvJHtjYXJkRGF0YS5idXNpbmVzc19zbHVnfWApO1xyXG4gICAgfVxyXG4gICAgcmV2YWxpZGF0ZVBhdGgoXCIvZGFzaGJvYXJkL2N1c3RvbWVyXCIpOyAvLyBSZXZhbGlkYXRlIGN1c3RvbWVyIGRhc2hib2FyZFxyXG5cclxuICAgIC8vIENoZWNrIGlmIHRoZSBjdXJyZW50IHVzZXIgaXMgYSBidXNpbmVzcyBhbmQgcmV2YWxpZGF0ZSBidXNpbmVzcyBkYXNoYm9hcmRcclxuICAgIGlmICh1c2VyQnVzaW5lc3NQcm9maWxlKSB7XHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9idXNpbmVzc1wiKTsgLy8gUmV2YWxpZGF0ZSBidXNpbmVzcyBkYXNoYm9hcmRcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoXCIvZGFzaGJvYXJkL2J1c2luZXNzL3N1YnNjcmlwdGlvbnNcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3Mgc3Vic2NyaXB0aW9ucyBwYWdlXHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmV2YWxpZGF0ZSB0aGUgYWN0aXZpdGllcyBwYWdlIGZvciB0aGUgYnVzaW5lc3NcclxuICAgIHJldmFsaWRhdGVQYXRoKGAvZGFzaGJvYXJkL2J1c2luZXNzL2FjdGl2aXRpZXNgKTtcclxuXHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJVbmV4cGVjdGVkIGVycm9yIGluIHN1YnNjcmliZVRvQnVzaW5lc3M6XCIsIGVycm9yKTtcclxuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9XHJcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkLlwiO1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcclxuICB9XHJcbn1cclxuXHJcbi8vIC0tLSBJbXBsZW1lbnRhdGlvbiBmb3Igb3RoZXIgYWN0aW9ucyAtLS1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1bnN1YnNjcmliZUZyb21CdXNpbmVzcyhcclxuICBidXNpbmVzc1Byb2ZpbGVJZDogc3RyaW5nXHJcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XHJcbiAgLy8gY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTsgLy8gQXdhaXQgdGhlIGFzeW5jIGZ1bmN0aW9uXHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gICAgZXJyb3I6IGF1dGhFcnJvcixcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XHJcblxyXG4gIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVc2VyIG5vdCBhdXRoZW50aWNhdGVkLlwiIH07XHJcbiAgfVxyXG5cclxuICAvLyBQcmV2ZW50IGEgYnVzaW5lc3MgZnJvbSB1bnN1YnNjcmliaW5nIGZyb20gdGhlaXIgb3duIGJ1c2luZXNzIGNhcmRcclxuICBpZiAodXNlci5pZCA9PT0gYnVzaW5lc3NQcm9maWxlSWQpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICBlcnJvcjogXCJZb3UgY2Fubm90IHVuc3Vic2NyaWJlIGZyb20geW91ciBvd24gYnVzaW5lc3MgY2FyZC5cIixcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvLyBDaGVjayBpZiB0aGUgY3VycmVudCB1c2VyIGlzIGEgYnVzaW5lc3MgKGhhcyBhIGJ1c2luZXNzIHByb2ZpbGUpXHJcbiAgY29uc3QgeyBkYXRhOiB1c2VyQnVzaW5lc3NQcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgLnNlbGVjdChcImlkXCIpXHJcbiAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxyXG4gICAgLm1heWJlU2luZ2xlKCk7XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyAxLiBEZWxldGUgc3Vic2NyaXB0aW9uIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogZGVsZXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwic3Vic2NyaXB0aW9uc1wiKVxyXG4gICAgICAuZGVsZXRlKClcclxuICAgICAgLm1hdGNoKHsgdXNlcl9pZDogdXNlci5pZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSk7XHJcblxyXG4gICAgaWYgKGRlbGV0ZUVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBzdWJzY3JpcHRpb246XCIsIGRlbGV0ZUVycm9yKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGRlbGV0ZUVycm9yLm1lc3NhZ2UpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vdGU6IFdlIGRvbid0IG5lZWQgdG8gbWFudWFsbHkgdXBkYXRlIHRoZSBzdWJzY3JpcHRpb24gY291bnRcclxuICAgIC8vIFRoZSBkYXRhYmFzZSB0cmlnZ2VyICd1cGRhdGVfdG90YWxfc3Vic2NyaXB0aW9ucycgd2lsbCBoYW5kbGUgdGhpcyBhdXRvbWF0aWNhbGx5XHJcblxyXG4gICAgLy8gMy4gUmV2YWxpZGF0ZSBwYXRoc1xyXG4gICAgLy8gVXNlIHJlZ3VsYXIgY2xpZW50IC0gYnVzaW5lc3NfcHJvZmlsZXMgaGFzIHB1YmxpYyByZWFkIGFjY2Vzc1xyXG4gICAgY29uc3QgeyBkYXRhOiBjYXJkRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgICAuc2VsZWN0KFwiYnVzaW5lc3Nfc2x1Z1wiKVxyXG4gICAgICAuZXEoXCJpZFwiLCBidXNpbmVzc1Byb2ZpbGVJZClcclxuICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmIChjYXJkRGF0YT8uYnVzaW5lc3Nfc2x1Zykge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChgLyR7Y2FyZERhdGEuYnVzaW5lc3Nfc2x1Z31gKTtcclxuICAgIH1cclxuICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9jdXN0b21lclwiKTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiB0aGUgY3VycmVudCB1c2VyIGlzIGEgYnVzaW5lc3MgYW5kIHJldmFsaWRhdGUgYnVzaW5lc3MgZGFzaGJvYXJkXHJcbiAgICBpZiAodXNlckJ1c2luZXNzUHJvZmlsZSkge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvYnVzaW5lc3NcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3MgZGFzaGJvYXJkXHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9idXNpbmVzcy9zdWJzY3JpcHRpb25zXCIpOyAvLyBSZXZhbGlkYXRlIGJ1c2luZXNzIHN1YnNjcmlwdGlvbnMgcGFnZVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFJldmFsaWRhdGUgdGhlIGFjdGl2aXRpZXMgcGFnZSBmb3IgdGhlIGJ1c2luZXNzXHJcbiAgICByZXZhbGlkYXRlUGF0aChgL2Rhc2hib2FyZC9idXNpbmVzcy9hY3Rpdml0aWVzYCk7XHJcblxyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiVW5leHBlY3RlZCBlcnJvciBpbiB1bnN1YnNjcmliZUZyb21CdXNpbmVzczpcIiwgZXJyb3IpO1xyXG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID1cclxuICAgICAgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIkFuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQuXCI7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN1Ym1pdFJldmlldyhcclxuICBidXNpbmVzc1Byb2ZpbGVJZDogc3RyaW5nLFxyXG4gIHJhdGluZzogbnVtYmVyLFxyXG4gIHJldmlld1RleHQ/OiBzdHJpbmcgfCBudWxsIC8vIEFsbG93IG51bGwgZm9yIHJldmlldyB0ZXh0XHJcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XHJcbiAgLy8gY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTsgLy8gQXdhaXQgdGhlIGFzeW5jIGZ1bmN0aW9uXHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gICAgZXJyb3I6IGF1dGhFcnJvcixcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XHJcblxyXG4gIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVc2VyIG5vdCBhdXRoZW50aWNhdGVkLlwiIH07XHJcbiAgfVxyXG5cclxuICAvLyBQcmV2ZW50IGEgYnVzaW5lc3MgZnJvbSByZXZpZXdpbmcgdGhlaXIgb3duIGJ1c2luZXNzIGNhcmRcclxuICBpZiAodXNlci5pZCA9PT0gYnVzaW5lc3NQcm9maWxlSWQpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJZb3UgY2Fubm90IHJldmlldyB5b3VyIG93biBidXNpbmVzcyBjYXJkLlwiIH07XHJcbiAgfVxyXG5cclxuICBpZiAocmF0aW5nIDwgMSB8fCByYXRpbmcgPiA1KSB7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IFwiUmF0aW5nIG11c3QgYmUgYmV0d2VlbiAxIGFuZCA1LlwiIH07XHJcbiAgfVxyXG5cclxuICB0cnkge1xyXG4gICAgLy8gVXBzZXJ0IHRoZSByZXZpZXc6IGluc2VydCBpZiBub3QgZXhpc3RzLCB1cGRhdGUgaWYgZXhpc3RzIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogdXBzZXJ0RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwicmF0aW5nc19yZXZpZXdzXCIpXHJcbiAgICAgIC51cHNlcnQoXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdXNlcl9pZDogdXNlci5pZCxcclxuICAgICAgICAgIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkLFxyXG4gICAgICAgICAgcmF0aW5nOiByYXRpbmcsXHJcbiAgICAgICAgICByZXZpZXdfdGV4dDogcmV2aWV3VGV4dCwgLy8gUGFzcyByZXZpZXdUZXh0IGRpcmVjdGx5XHJcbiAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksIC8vIEV4cGxpY2l0bHkgc2V0IHVwZGF0ZWRfYXQgb24gdXBzZXJ0XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBvbkNvbmZsaWN0OiBcInVzZXJfaWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWRcIiwgLy8gU3BlY2lmeSBjb25mbGljdCB0YXJnZXRcclxuICAgICAgICB9XHJcbiAgICAgICk7XHJcblxyXG4gICAgaWYgKHVwc2VydEVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdWJtaXR0aW5nIHJldmlldzpcIiwgdXBzZXJ0RXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IodXBzZXJ0RXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQXZlcmFnZSByYXRpbmcgaXMgaGFuZGxlZCBieSB0aGUgZGF0YWJhc2UgdHJpZ2dlclxyXG5cclxuICAgIC8vIFJldmFsaWRhdGUgcGF0aHNcclxuICAgIC8vIFVzZSByZWd1bGFyIGNsaWVudCAtIGJ1c2luZXNzX3Byb2ZpbGVzIGhhcyBwdWJsaWMgcmVhZCBhY2Nlc3NcclxuICAgIGNvbnN0IHsgZGF0YTogY2FyZERhdGEgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwiYnVzaW5lc3NfcHJvZmlsZXNcIilcclxuICAgICAgLnNlbGVjdChcImJ1c2luZXNzX3NsdWdcIilcclxuICAgICAgLmVxKFwiaWRcIiwgYnVzaW5lc3NQcm9maWxlSWQpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoY2FyZERhdGE/LmJ1c2luZXNzX3NsdWcpIHtcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoYC8ke2NhcmREYXRhLmJ1c2luZXNzX3NsdWd9YCk7XHJcbiAgICB9XHJcbiAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvY3VzdG9tZXJcIik7IC8vIFJldmFsaWRhdGUgY3VzdG9tZXIgZGFzaGJvYXJkIHdoZXJlIHJldmlld3MgbWlnaHQgYmUgc2hvd25cclxuXHJcbiAgICAvLyBSZXZhbGlkYXRlIHRoZSBhY3Rpdml0aWVzIHBhZ2UgZm9yIHRoZSBidXNpbmVzc1xyXG4gICAgcmV2YWxpZGF0ZVBhdGgoYC9kYXNoYm9hcmQvYnVzaW5lc3MvYWN0aXZpdGllc2ApO1xyXG5cclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIlVuZXhwZWN0ZWQgZXJyb3IgaW4gc3VibWl0UmV2aWV3OlwiLCBlcnJvcik7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZC5cIjtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlUmV2aWV3KFxyXG4gIGJ1c2luZXNzUHJvZmlsZUlkOiBzdHJpbmdcclxuKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcclxuICAvLyBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogeyB1c2VyIH0sXHJcbiAgICBlcnJvcjogYXV0aEVycm9yLFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuXHJcbiAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWQuXCIgfTtcclxuICB9XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgd2l0aCBwcm9wZXIgUkxTXHJcbiAgICBjb25zdCB7IGVycm9yOiBkZWxldGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJyYXRpbmdzX3Jldmlld3NcIilcclxuICAgICAgLmRlbGV0ZSgpXHJcbiAgICAgIC5tYXRjaCh7IHVzZXJfaWQ6IHVzZXIuaWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pO1xyXG5cclxuICAgIGlmIChkZWxldGVFcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgcmV2aWV3OlwiLCBkZWxldGVFcnJvcik7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihkZWxldGVFcnJvci5tZXNzYWdlKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBBdmVyYWdlIHJhdGluZyBpcyBoYW5kbGVkIGJ5IHRoZSBkYXRhYmFzZSB0cmlnZ2VyXHJcblxyXG4gICAgLy8gUmV2YWxpZGF0ZSBwYXRoc1xyXG4gICAgLy8gVXNlIHJlZ3VsYXIgY2xpZW50IC0gYnVzaW5lc3NfcHJvZmlsZXMgaGFzIHB1YmxpYyByZWFkIGFjY2Vzc1xyXG4gICAgY29uc3QgeyBkYXRhOiBjYXJkRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgICAuc2VsZWN0KFwiYnVzaW5lc3Nfc2x1Z1wiKVxyXG4gICAgICAuZXEoXCJpZFwiLCBidXNpbmVzc1Byb2ZpbGVJZClcclxuICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmIChjYXJkRGF0YT8uYnVzaW5lc3Nfc2x1Zykge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChgLyR7Y2FyZERhdGEuYnVzaW5lc3Nfc2x1Z31gKTtcclxuICAgIH1cclxuICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9jdXN0b21lclwiKTtcclxuXHJcbiAgICAvLyBSZXZhbGlkYXRlIHRoZSBhY3Rpdml0aWVzIHBhZ2UgZm9yIHRoZSBidXNpbmVzc1xyXG4gICAgcmV2YWxpZGF0ZVBhdGgoYC9kYXNoYm9hcmQvYnVzaW5lc3MvYWN0aXZpdGllc2ApO1xyXG5cclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIlVuZXhwZWN0ZWQgZXJyb3IgaW4gZGVsZXRlUmV2aWV3OlwiLCBlcnJvcik7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZC5cIjtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbGlrZUJ1c2luZXNzKFxyXG4gIGJ1c2luZXNzUHJvZmlsZUlkOiBzdHJpbmdcclxuKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcclxuICAvLyBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogeyB1c2VyIH0sXHJcbiAgICBlcnJvcjogYXV0aEVycm9yLFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuXHJcbiAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWQuXCIgfTtcclxuICB9XHJcblxyXG4gIC8vIFByZXZlbnQgYSBidXNpbmVzcyBmcm9tIGxpa2luZyB0aGVpciBvd24gYnVzaW5lc3MgY2FyZFxyXG4gIGlmICh1c2VyLmlkID09PSBidXNpbmVzc1Byb2ZpbGVJZCkge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIllvdSBjYW5ub3QgbGlrZSB5b3VyIG93biBidXNpbmVzcyBjYXJkLlwiIH07XHJcbiAgfVxyXG5cclxuICB0cnkge1xyXG4gICAgLy8gMS4gSW5zZXJ0IGxpa2UgLSBVc2UgcmVndWxhciBjbGllbnQgd2l0aCBwcm9wZXIgUkxTXHJcbiAgICBjb25zdCB7IGVycm9yOiBpbnNlcnRFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJsaWtlc1wiKVxyXG4gICAgICAuaW5zZXJ0KHsgdXNlcl9pZDogdXNlci5pZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSk7XHJcblxyXG4gICAgaWYgKGluc2VydEVycm9yKSB7XHJcbiAgICAgIC8vIEhhbmRsZSBwb3RlbnRpYWwgdW5pcXVlIGNvbnN0cmFpbnQgdmlvbGF0aW9uIChhbHJlYWR5IGxpa2VkKSBncmFjZWZ1bGx5XHJcbiAgICAgIGlmIChpbnNlcnRFcnJvci5jb2RlID09PSBcIjIzNTA1XCIpIHtcclxuICAgICAgICAvLyB1bmlxdWVfdmlvbGF0aW9uXHJcbiAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICBgVXNlciAke3VzZXIuaWR9IGFscmVhZHkgbGlrZWQgYnVzaW5lc3MgJHtidXNpbmVzc1Byb2ZpbGVJZH0uYFxyXG4gICAgICAgICk7XHJcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9OyAvLyBDb25zaWRlciBpdCBzdWNjZXNzIGlmIGFscmVhZHkgbGlrZWRcclxuICAgICAgfVxyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW5zZXJ0aW5nIGxpa2U6XCIsIGluc2VydEVycm9yKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGluc2VydEVycm9yLm1lc3NhZ2UpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vdGU6IFdlIGRvbid0IG5lZWQgdG8gbWFudWFsbHkgdXBkYXRlIHRoZSBsaWtlIGNvdW50XHJcbiAgICAvLyBUaGUgZGF0YWJhc2UgdHJpZ2dlciAndXBkYXRlX3RvdGFsX2xpa2VzJyB3aWxsIGhhbmRsZSB0aGlzIGF1dG9tYXRpY2FsbHlcclxuXHJcbiAgICAvLyAzLiBSZXZhbGlkYXRlIHBhdGhzXHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgLSBidXNpbmVzc19wcm9maWxlcyBoYXMgcHVibGljIHJlYWQgYWNjZXNzXHJcbiAgICBjb25zdCB7IGRhdGE6IGNhcmREYXRhIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbShcImJ1c2luZXNzX3Byb2ZpbGVzXCIpXHJcbiAgICAgIC5zZWxlY3QoXCJidXNpbmVzc19zbHVnXCIpXHJcbiAgICAgIC5lcShcImlkXCIsIGJ1c2luZXNzUHJvZmlsZUlkKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKGNhcmREYXRhPy5idXNpbmVzc19zbHVnKSB7XHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKGAvJHtjYXJkRGF0YS5idXNpbmVzc19zbHVnfWApO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGlmIHRoZSBjdXJyZW50IHVzZXIgaXMgYSBidXNpbmVzcyBhbmQgcmV2YWxpZGF0ZSBidXNpbmVzcyBkYXNoYm9hcmRcclxuICAgIGNvbnN0IHsgZGF0YTogdXNlckJ1c2luZXNzUHJvZmlsZSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgICAuc2VsZWN0KFwiaWRcIilcclxuICAgICAgLmVxKFwiaWRcIiwgdXNlci5pZClcclxuICAgICAgLm1heWJlU2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKHVzZXJCdXNpbmVzc1Byb2ZpbGUpIHtcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoXCIvZGFzaGJvYXJkL2J1c2luZXNzXCIpOyAvLyBSZXZhbGlkYXRlIGJ1c2luZXNzIGRhc2hib2FyZFxyXG4gICAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvYnVzaW5lc3MvbGlrZXNcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3MgbGlrZXMgcGFnZVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFJldmFsaWRhdGUgdGhlIGFjdGl2aXRpZXMgcGFnZSBmb3IgdGhlIGJ1c2luZXNzXHJcbiAgICByZXZhbGlkYXRlUGF0aChgL2Rhc2hib2FyZC9idXNpbmVzcy9hY3Rpdml0aWVzYCk7XHJcblxyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiVW5leHBlY3RlZCBlcnJvciBpbiBsaWtlQnVzaW5lc3M6XCIsIGVycm9yKTtcclxuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9XHJcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkLlwiO1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1bmxpa2VCdXNpbmVzcyhcclxuICBidXNpbmVzc1Byb2ZpbGVJZDogc3RyaW5nXHJcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XHJcbiAgLy8gY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTsgLy8gQXdhaXQgdGhlIGFzeW5jIGZ1bmN0aW9uXHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gICAgZXJyb3I6IGF1dGhFcnJvcixcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XHJcblxyXG4gIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVc2VyIG5vdCBhdXRoZW50aWNhdGVkLlwiIH07XHJcbiAgfVxyXG5cclxuICAvLyBQcmV2ZW50IGEgYnVzaW5lc3MgZnJvbSB1bmxpa2luZyB0aGVpciBvd24gYnVzaW5lc3MgY2FyZFxyXG4gIGlmICh1c2VyLmlkID09PSBidXNpbmVzc1Byb2ZpbGVJZCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBcIllvdSBjYW5ub3QgdW5saWtlIHlvdXIgb3duIGJ1c2luZXNzIGNhcmQuXCIsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgdHJ5IHtcclxuICAgIC8vIDEuIERlbGV0ZSBsaWtlIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogZGVsZXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwibGlrZXNcIilcclxuICAgICAgLmRlbGV0ZSgpXHJcbiAgICAgIC5tYXRjaCh7IHVzZXJfaWQ6IHVzZXIuaWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pO1xyXG5cclxuICAgIGlmIChkZWxldGVFcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgbGlrZTpcIiwgZGVsZXRlRXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZGVsZXRlRXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm90ZTogV2UgZG9uJ3QgbmVlZCB0byBtYW51YWxseSB1cGRhdGUgdGhlIGxpa2UgY291bnRcclxuICAgIC8vIFRoZSBkYXRhYmFzZSB0cmlnZ2VyICd1cGRhdGVfdG90YWxfbGlrZXMnIHdpbGwgaGFuZGxlIHRoaXMgYXV0b21hdGljYWxseVxyXG5cclxuICAgIC8vIDMuIFJldmFsaWRhdGUgcGF0aHNcclxuICAgIC8vIFVzZSByZWd1bGFyIGNsaWVudCAtIGJ1c2luZXNzX3Byb2ZpbGVzIGhhcyBwdWJsaWMgcmVhZCBhY2Nlc3NcclxuICAgIGNvbnN0IHsgZGF0YTogY2FyZERhdGEgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwiYnVzaW5lc3NfcHJvZmlsZXNcIilcclxuICAgICAgLnNlbGVjdChcImJ1c2luZXNzX3NsdWdcIilcclxuICAgICAgLmVxKFwiaWRcIiwgYnVzaW5lc3NQcm9maWxlSWQpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoY2FyZERhdGE/LmJ1c2luZXNzX3NsdWcpIHtcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoYC8ke2NhcmREYXRhLmJ1c2luZXNzX3NsdWd9YCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgdGhlIGN1cnJlbnQgdXNlciBpcyBhIGJ1c2luZXNzIGFuZCByZXZhbGlkYXRlIGJ1c2luZXNzIGRhc2hib2FyZFxyXG4gICAgY29uc3QgeyBkYXRhOiB1c2VyQnVzaW5lc3NQcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbShcImJ1c2luZXNzX3Byb2ZpbGVzXCIpXHJcbiAgICAgIC5zZWxlY3QoXCJpZFwiKVxyXG4gICAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxyXG4gICAgICAubWF5YmVTaW5nbGUoKTtcclxuXHJcbiAgICBpZiAodXNlckJ1c2luZXNzUHJvZmlsZSkge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvYnVzaW5lc3NcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3MgZGFzaGJvYXJkXHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9idXNpbmVzcy9saWtlc1wiKTsgLy8gUmV2YWxpZGF0ZSBidXNpbmVzcyBsaWtlcyBwYWdlXHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmV2YWxpZGF0ZSB0aGUgYWN0aXZpdGllcyBwYWdlIGZvciB0aGUgYnVzaW5lc3NcclxuICAgIHJldmFsaWRhdGVQYXRoKGAvZGFzaGJvYXJkL2J1c2luZXNzL2FjdGl2aXRpZXNgKTtcclxuXHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJVbmV4cGVjdGVkIGVycm9yIGluIHVubGlrZUJ1c2luZXNzOlwiLCBlcnJvcik7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZC5cIjtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0SW50ZXJhY3Rpb25TdGF0dXMoYnVzaW5lc3NQcm9maWxlSWQ6IHN0cmluZyk6IFByb21pc2U8e1xyXG4gIGlzU3Vic2NyaWJlZDogYm9vbGVhbjtcclxuICBoYXNMaWtlZDogYm9vbGVhbjtcclxuICB1c2VyUmF0aW5nOiBudW1iZXIgfCBudWxsO1xyXG4gIHVzZXJSZXZpZXc6IHN0cmluZyB8IG51bGw7XHJcbiAgZXJyb3I/OiBzdHJpbmc7XHJcbn0+IHtcclxuICAvLyBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuICBsZXQgdXNlcklkOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcclxuXHJcbiAgLy8gVHJ5IHRvIGdldCBhdXRoZW50aWNhdGVkIHVzZXIsIGJ1dCBwcm9jZWVkIGV2ZW4gaWYgbm90IGxvZ2dlZCBpblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuICBpZiAodXNlcikge1xyXG4gICAgdXNlcklkID0gdXNlci5pZDtcclxuICB9XHJcblxyXG4gIC8vIERlZmF1bHQgc3RhdHVzIGZvciBhbm9ueW1vdXMgdXNlcnNcclxuICBjb25zdCBkZWZhdWx0U3RhdHVzID0ge1xyXG4gICAgaXNTdWJzY3JpYmVkOiBmYWxzZSxcclxuICAgIGhhc0xpa2VkOiBmYWxzZSxcclxuICAgIHVzZXJSYXRpbmc6IG51bGwsXHJcbiAgICB1c2VyUmV2aWV3OiBudWxsLFxyXG4gIH07XHJcblxyXG4gIGlmICghdXNlcklkKSB7XHJcbiAgICByZXR1cm4gZGVmYXVsdFN0YXR1czsgLy8gUmV0dXJuIGRlZmF1bHQgaWYgbm8gdXNlciBpcyBsb2dnZWQgaW5cclxuICB9XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgLSBhbGwgdGhlc2UgdGFibGVzIGhhdmUgcHVibGljIHJlYWQgYWNjZXNzXHJcbiAgICAvLyBGZXRjaCBhbGwgc3RhdHVzZXMgaW4gcGFyYWxsZWxcclxuICAgIGNvbnN0IFtzdWJzY3JpcHRpb25SZXMsIGxpa2VSZXMsIHJldmlld1Jlc10gPSBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgIHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oXCJzdWJzY3JpcHRpb25zXCIpXHJcbiAgICAgICAgLnNlbGVjdChcImlkXCIsIHsgY291bnQ6IFwiZXhhY3RcIiwgaGVhZDogdHJ1ZSB9KSAvLyBKdXN0IGNoZWNrIGV4aXN0ZW5jZVxyXG4gICAgICAgIC5tYXRjaCh7IHVzZXJfaWQ6IHVzZXJJZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSksXHJcbiAgICAgIHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oXCJsaWtlc1wiKVxyXG4gICAgICAgIC5zZWxlY3QoXCJpZFwiLCB7IGNvdW50OiBcImV4YWN0XCIsIGhlYWQ6IHRydWUgfSkgLy8gSnVzdCBjaGVjayBleGlzdGVuY2VcclxuICAgICAgICAubWF0Y2goeyB1c2VyX2lkOiB1c2VySWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pLFxyXG4gICAgICBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKFwicmF0aW5nc19yZXZpZXdzXCIpXHJcbiAgICAgICAgLnNlbGVjdChcInJhdGluZywgcmV2aWV3X3RleHRcIilcclxuICAgICAgICAubWF0Y2goeyB1c2VyX2lkOiB1c2VySWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pXHJcbiAgICAgICAgLm1heWJlU2luZ2xlKCksIC8vIFVzZSBtYXliZVNpbmdsZSBhcyB1c2VyIG1pZ2h0IG5vdCBoYXZlIHJldmlld2VkXHJcbiAgICBdKTtcclxuXHJcbiAgICAvLyBDaGVjayBmb3IgZXJyb3JzIGluIHBhcmFsbGVsIGZldGNoZXNcclxuICAgIGlmIChzdWJzY3JpcHRpb25SZXMuZXJyb3IpXHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihcclxuICAgICAgICBgU3Vic2NyaXB0aW9uIGZldGNoIGVycm9yOiAke3N1YnNjcmlwdGlvblJlcy5lcnJvci5tZXNzYWdlfWBcclxuICAgICAgKTtcclxuICAgIGlmIChsaWtlUmVzLmVycm9yKVxyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYExpa2UgZmV0Y2ggZXJyb3I6ICR7bGlrZVJlcy5lcnJvci5tZXNzYWdlfWApO1xyXG4gICAgaWYgKHJldmlld1Jlcy5lcnJvcilcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBSZXZpZXcgZmV0Y2ggZXJyb3I6ICR7cmV2aWV3UmVzLmVycm9yLm1lc3NhZ2V9YCk7XHJcblxyXG4gICAgY29uc3QgcmV2aWV3RGF0YSA9IHJldmlld1Jlcy5kYXRhO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGlzU3Vic2NyaWJlZDogKHN1YnNjcmlwdGlvblJlcy5jb3VudCA/PyAwKSA+IDAsXHJcbiAgICAgIGhhc0xpa2VkOiAobGlrZVJlcy5jb3VudCA/PyAwKSA+IDAsXHJcbiAgICAgIHVzZXJSYXRpbmc6IHJldmlld0RhdGE/LnJhdGluZyA/PyBudWxsLFxyXG4gICAgICB1c2VyUmV2aWV3OiByZXZpZXdEYXRhPy5yZXZpZXdfdGV4dCA/PyBudWxsLFxyXG4gICAgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGludGVyYWN0aW9uIHN0YXR1czpcIiwgZXJyb3IpO1xyXG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID1cclxuICAgICAgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIkFuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQuXCI7XHJcbiAgICAvLyBSZXR1cm4gZGVmYXVsdCBzdGF0dXMgYnV0IGluY2x1ZGUgdGhlIGVycm9yIG1lc3NhZ2VcclxuICAgIHJldHVybiB7IC4uLmRlZmF1bHRTdGF0dXMsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJ5U0FvY3NCIn0=
}}),
"[project]/lib/actions/data:ff81e5 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4050870674cfbc37404b048f17c031e582c6817414":"likeBusiness"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "likeBusiness": (()=>likeBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var likeBusiness = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("4050870674cfbc37404b048f17c031e582c6817414", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "likeBusiness"); //# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIi4vaW50ZXJhY3Rpb25zLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHNlcnZlclwiO1xyXG5cclxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIkAvdXRpbHMvc3VwYWJhc2Uvc2VydmVyXCI7XHJcbmltcG9ydCB7IHJldmFsaWRhdGVQYXRoIH0gZnJvbSBcIm5leHQvY2FjaGVcIjtcclxuLy8gZ2V0U2VjdXJlQnVzaW5lc3NQcm9maWxlQnlTbHVnIGlzIGltcG9ydGVkIGJ1dCBub3QgdXNlZCBpbiB0aGlzIGZpbGVcclxuLy8gaW1wb3J0IHsgZ2V0U2VjdXJlQnVzaW5lc3NQcm9maWxlQnlTbHVnIH0gZnJvbSAnLi9zZWN1cmVCdXNpbmVzc1Byb2ZpbGVzJztcclxuLy8gaW1wb3J0IHsgY29va2llcyB9IGZyb20gJ25leHQvaGVhZGVycyc7IC8vIFJlbW92ZWQgdW51c2VkIGltcG9ydFxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN1YnNjcmliZVRvQnVzaW5lc3MoXHJcbiAgYnVzaW5lc3NQcm9maWxlSWQ6IHN0cmluZ1xyXG4pOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4ge1xyXG4gIC8vIGNvbnN0IGNvb2tpZVN0b3JlID0gY29va2llcygpOyAvLyBObyBsb25nZXIgbmVlZGVkIGhlcmVcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogeyB1c2VyIH0sXHJcbiAgICBlcnJvcjogYXV0aEVycm9yLFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuXHJcbiAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWQuXCIgfTtcclxuICB9XHJcblxyXG4gIC8vIFByZXZlbnQgYSBidXNpbmVzcyBmcm9tIHN1YnNjcmliaW5nIHRvIHRoZWlyIG93biBidXNpbmVzcyBjYXJkXHJcbiAgaWYgKHVzZXIuaWQgPT09IGJ1c2luZXNzUHJvZmlsZUlkKSB7XHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdWNjZXNzOiBmYWxzZSxcclxuICAgICAgZXJyb3I6IFwiWW91IGNhbm5vdCBzdWJzY3JpYmUgdG8geW91ciBvd24gYnVzaW5lc3MgY2FyZC5cIixcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvLyBDaGVjayBpZiB0aGUgY3VycmVudCB1c2VyIGlzIGEgYnVzaW5lc3MgKGhhcyBhIGJ1c2luZXNzIHByb2ZpbGUpXHJcbiAgY29uc3QgeyBkYXRhOiB1c2VyQnVzaW5lc3NQcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgLnNlbGVjdChcImlkXCIpXHJcbiAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxyXG4gICAgLm1heWJlU2luZ2xlKCk7XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyAxLiBJbnNlcnQgc3Vic2NyaXB0aW9uIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogaW5zZXJ0RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwic3Vic2NyaXB0aW9uc1wiKVxyXG4gICAgICAuaW5zZXJ0KHsgdXNlcl9pZDogdXNlci5pZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSk7XHJcblxyXG4gICAgaWYgKGluc2VydEVycm9yKSB7XHJcbiAgICAgIC8vIEhhbmRsZSBwb3RlbnRpYWwgdW5pcXVlIGNvbnN0cmFpbnQgdmlvbGF0aW9uIChhbHJlYWR5IHN1YnNjcmliZWQpIGdyYWNlZnVsbHlcclxuICAgICAgaWYgKGluc2VydEVycm9yLmNvZGUgPT09IFwiMjM1MDVcIikge1xyXG4gICAgICAgIC8vIHVuaXF1ZV92aW9sYXRpb25cclxuICAgICAgICBjb25zb2xlLmxvZyhcclxuICAgICAgICAgIGBVc2VyICR7dXNlci5pZH0gYWxyZWFkeSBzdWJzY3JpYmVkIHRvIGJ1c2luZXNzICR7YnVzaW5lc3NQcm9maWxlSWR9LmBcclxuICAgICAgICApO1xyXG4gICAgICAgIC8vIE9wdGlvbmFsbHkgcmV0dXJuIHN1Y2Nlc3MgdHJ1ZSBpZiBhbHJlYWR5IHN1YnNjcmliZWQgaXMgYWNjZXB0YWJsZVxyXG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICAgICAgfVxyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW5zZXJ0aW5nIHN1YnNjcmlwdGlvbjpcIiwgaW5zZXJ0RXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoaW5zZXJ0RXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm90ZTogV2UgZG9uJ3QgbmVlZCB0byBtYW51YWxseSB1cGRhdGUgdGhlIHN1YnNjcmlwdGlvbiBjb3VudFxyXG4gICAgLy8gVGhlIGRhdGFiYXNlIHRyaWdnZXIgJ3VwZGF0ZV90b3RhbF9zdWJzY3JpcHRpb25zJyB3aWxsIGhhbmRsZSB0aGlzIGF1dG9tYXRpY2FsbHlcclxuXHJcbiAgICAvLyAzLiBSZXZhbGlkYXRlIHBhdGhzXHJcbiAgICAvLyBSZXZhbGlkYXRlIHRoZSBzcGVjaWZpYyBjYXJkIHBhZ2UgYW5kIHBvdGVudGlhbGx5IHRoZSB1c2VyJ3MgZGFzaGJvYXJkXHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgLSBidXNpbmVzc19wcm9maWxlcyBoYXMgcHVibGljIHJlYWQgYWNjZXNzXHJcbiAgICBjb25zdCB7IGRhdGE6IGNhcmREYXRhIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbShcImJ1c2luZXNzX3Byb2ZpbGVzXCIpXHJcbiAgICAgIC5zZWxlY3QoXCJidXNpbmVzc19zbHVnXCIpXHJcbiAgICAgIC5lcShcImlkXCIsIGJ1c2luZXNzUHJvZmlsZUlkKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKGNhcmREYXRhPy5idXNpbmVzc19zbHVnKSB7XHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKGAvJHtjYXJkRGF0YS5idXNpbmVzc19zbHVnfWApO1xyXG4gICAgfVxyXG4gICAgcmV2YWxpZGF0ZVBhdGgoXCIvZGFzaGJvYXJkL2N1c3RvbWVyXCIpOyAvLyBSZXZhbGlkYXRlIGN1c3RvbWVyIGRhc2hib2FyZFxyXG5cclxuICAgIC8vIENoZWNrIGlmIHRoZSBjdXJyZW50IHVzZXIgaXMgYSBidXNpbmVzcyBhbmQgcmV2YWxpZGF0ZSBidXNpbmVzcyBkYXNoYm9hcmRcclxuICAgIGlmICh1c2VyQnVzaW5lc3NQcm9maWxlKSB7XHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9idXNpbmVzc1wiKTsgLy8gUmV2YWxpZGF0ZSBidXNpbmVzcyBkYXNoYm9hcmRcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoXCIvZGFzaGJvYXJkL2J1c2luZXNzL3N1YnNjcmlwdGlvbnNcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3Mgc3Vic2NyaXB0aW9ucyBwYWdlXHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmV2YWxpZGF0ZSB0aGUgYWN0aXZpdGllcyBwYWdlIGZvciB0aGUgYnVzaW5lc3NcclxuICAgIHJldmFsaWRhdGVQYXRoKGAvZGFzaGJvYXJkL2J1c2luZXNzL2FjdGl2aXRpZXNgKTtcclxuXHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJVbmV4cGVjdGVkIGVycm9yIGluIHN1YnNjcmliZVRvQnVzaW5lc3M6XCIsIGVycm9yKTtcclxuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9XHJcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkLlwiO1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcclxuICB9XHJcbn1cclxuXHJcbi8vIC0tLSBJbXBsZW1lbnRhdGlvbiBmb3Igb3RoZXIgYWN0aW9ucyAtLS1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1bnN1YnNjcmliZUZyb21CdXNpbmVzcyhcclxuICBidXNpbmVzc1Byb2ZpbGVJZDogc3RyaW5nXHJcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XHJcbiAgLy8gY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTsgLy8gQXdhaXQgdGhlIGFzeW5jIGZ1bmN0aW9uXHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gICAgZXJyb3I6IGF1dGhFcnJvcixcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XHJcblxyXG4gIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVc2VyIG5vdCBhdXRoZW50aWNhdGVkLlwiIH07XHJcbiAgfVxyXG5cclxuICAvLyBQcmV2ZW50IGEgYnVzaW5lc3MgZnJvbSB1bnN1YnNjcmliaW5nIGZyb20gdGhlaXIgb3duIGJ1c2luZXNzIGNhcmRcclxuICBpZiAodXNlci5pZCA9PT0gYnVzaW5lc3NQcm9maWxlSWQpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICBlcnJvcjogXCJZb3UgY2Fubm90IHVuc3Vic2NyaWJlIGZyb20geW91ciBvd24gYnVzaW5lc3MgY2FyZC5cIixcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvLyBDaGVjayBpZiB0aGUgY3VycmVudCB1c2VyIGlzIGEgYnVzaW5lc3MgKGhhcyBhIGJ1c2luZXNzIHByb2ZpbGUpXHJcbiAgY29uc3QgeyBkYXRhOiB1c2VyQnVzaW5lc3NQcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgLnNlbGVjdChcImlkXCIpXHJcbiAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxyXG4gICAgLm1heWJlU2luZ2xlKCk7XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyAxLiBEZWxldGUgc3Vic2NyaXB0aW9uIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogZGVsZXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwic3Vic2NyaXB0aW9uc1wiKVxyXG4gICAgICAuZGVsZXRlKClcclxuICAgICAgLm1hdGNoKHsgdXNlcl9pZDogdXNlci5pZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSk7XHJcblxyXG4gICAgaWYgKGRlbGV0ZUVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBzdWJzY3JpcHRpb246XCIsIGRlbGV0ZUVycm9yKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGRlbGV0ZUVycm9yLm1lc3NhZ2UpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vdGU6IFdlIGRvbid0IG5lZWQgdG8gbWFudWFsbHkgdXBkYXRlIHRoZSBzdWJzY3JpcHRpb24gY291bnRcclxuICAgIC8vIFRoZSBkYXRhYmFzZSB0cmlnZ2VyICd1cGRhdGVfdG90YWxfc3Vic2NyaXB0aW9ucycgd2lsbCBoYW5kbGUgdGhpcyBhdXRvbWF0aWNhbGx5XHJcblxyXG4gICAgLy8gMy4gUmV2YWxpZGF0ZSBwYXRoc1xyXG4gICAgLy8gVXNlIHJlZ3VsYXIgY2xpZW50IC0gYnVzaW5lc3NfcHJvZmlsZXMgaGFzIHB1YmxpYyByZWFkIGFjY2Vzc1xyXG4gICAgY29uc3QgeyBkYXRhOiBjYXJkRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgICAuc2VsZWN0KFwiYnVzaW5lc3Nfc2x1Z1wiKVxyXG4gICAgICAuZXEoXCJpZFwiLCBidXNpbmVzc1Byb2ZpbGVJZClcclxuICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmIChjYXJkRGF0YT8uYnVzaW5lc3Nfc2x1Zykge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChgLyR7Y2FyZERhdGEuYnVzaW5lc3Nfc2x1Z31gKTtcclxuICAgIH1cclxuICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9jdXN0b21lclwiKTtcclxuXHJcbiAgICAvLyBDaGVjayBpZiB0aGUgY3VycmVudCB1c2VyIGlzIGEgYnVzaW5lc3MgYW5kIHJldmFsaWRhdGUgYnVzaW5lc3MgZGFzaGJvYXJkXHJcbiAgICBpZiAodXNlckJ1c2luZXNzUHJvZmlsZSkge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvYnVzaW5lc3NcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3MgZGFzaGJvYXJkXHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9idXNpbmVzcy9zdWJzY3JpcHRpb25zXCIpOyAvLyBSZXZhbGlkYXRlIGJ1c2luZXNzIHN1YnNjcmlwdGlvbnMgcGFnZVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFJldmFsaWRhdGUgdGhlIGFjdGl2aXRpZXMgcGFnZSBmb3IgdGhlIGJ1c2luZXNzXHJcbiAgICByZXZhbGlkYXRlUGF0aChgL2Rhc2hib2FyZC9idXNpbmVzcy9hY3Rpdml0aWVzYCk7XHJcblxyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiVW5leHBlY3RlZCBlcnJvciBpbiB1bnN1YnNjcmliZUZyb21CdXNpbmVzczpcIiwgZXJyb3IpO1xyXG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID1cclxuICAgICAgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIkFuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQuXCI7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xyXG4gIH1cclxufVxyXG5cclxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHN1Ym1pdFJldmlldyhcclxuICBidXNpbmVzc1Byb2ZpbGVJZDogc3RyaW5nLFxyXG4gIHJhdGluZzogbnVtYmVyLFxyXG4gIHJldmlld1RleHQ/OiBzdHJpbmcgfCBudWxsIC8vIEFsbG93IG51bGwgZm9yIHJldmlldyB0ZXh0XHJcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XHJcbiAgLy8gY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTsgLy8gQXdhaXQgdGhlIGFzeW5jIGZ1bmN0aW9uXHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gICAgZXJyb3I6IGF1dGhFcnJvcixcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XHJcblxyXG4gIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVc2VyIG5vdCBhdXRoZW50aWNhdGVkLlwiIH07XHJcbiAgfVxyXG5cclxuICAvLyBQcmV2ZW50IGEgYnVzaW5lc3MgZnJvbSByZXZpZXdpbmcgdGhlaXIgb3duIGJ1c2luZXNzIGNhcmRcclxuICBpZiAodXNlci5pZCA9PT0gYnVzaW5lc3NQcm9maWxlSWQpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJZb3UgY2Fubm90IHJldmlldyB5b3VyIG93biBidXNpbmVzcyBjYXJkLlwiIH07XHJcbiAgfVxyXG5cclxuICBpZiAocmF0aW5nIDwgMSB8fCByYXRpbmcgPiA1KSB7XHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IFwiUmF0aW5nIG11c3QgYmUgYmV0d2VlbiAxIGFuZCA1LlwiIH07XHJcbiAgfVxyXG5cclxuICB0cnkge1xyXG4gICAgLy8gVXBzZXJ0IHRoZSByZXZpZXc6IGluc2VydCBpZiBub3QgZXhpc3RzLCB1cGRhdGUgaWYgZXhpc3RzIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogdXBzZXJ0RXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwicmF0aW5nc19yZXZpZXdzXCIpXHJcbiAgICAgIC51cHNlcnQoXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgdXNlcl9pZDogdXNlci5pZCxcclxuICAgICAgICAgIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkLFxyXG4gICAgICAgICAgcmF0aW5nOiByYXRpbmcsXHJcbiAgICAgICAgICByZXZpZXdfdGV4dDogcmV2aWV3VGV4dCwgLy8gUGFzcyByZXZpZXdUZXh0IGRpcmVjdGx5XHJcbiAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksIC8vIEV4cGxpY2l0bHkgc2V0IHVwZGF0ZWRfYXQgb24gdXBzZXJ0XHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICBvbkNvbmZsaWN0OiBcInVzZXJfaWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWRcIiwgLy8gU3BlY2lmeSBjb25mbGljdCB0YXJnZXRcclxuICAgICAgICB9XHJcbiAgICAgICk7XHJcblxyXG4gICAgaWYgKHVwc2VydEVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzdWJtaXR0aW5nIHJldmlldzpcIiwgdXBzZXJ0RXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IodXBzZXJ0RXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQXZlcmFnZSByYXRpbmcgaXMgaGFuZGxlZCBieSB0aGUgZGF0YWJhc2UgdHJpZ2dlclxyXG5cclxuICAgIC8vIFJldmFsaWRhdGUgcGF0aHNcclxuICAgIC8vIFVzZSByZWd1bGFyIGNsaWVudCAtIGJ1c2luZXNzX3Byb2ZpbGVzIGhhcyBwdWJsaWMgcmVhZCBhY2Nlc3NcclxuICAgIGNvbnN0IHsgZGF0YTogY2FyZERhdGEgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwiYnVzaW5lc3NfcHJvZmlsZXNcIilcclxuICAgICAgLnNlbGVjdChcImJ1c2luZXNzX3NsdWdcIilcclxuICAgICAgLmVxKFwiaWRcIiwgYnVzaW5lc3NQcm9maWxlSWQpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoY2FyZERhdGE/LmJ1c2luZXNzX3NsdWcpIHtcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoYC8ke2NhcmREYXRhLmJ1c2luZXNzX3NsdWd9YCk7XHJcbiAgICB9XHJcbiAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvY3VzdG9tZXJcIik7IC8vIFJldmFsaWRhdGUgY3VzdG9tZXIgZGFzaGJvYXJkIHdoZXJlIHJldmlld3MgbWlnaHQgYmUgc2hvd25cclxuXHJcbiAgICAvLyBSZXZhbGlkYXRlIHRoZSBhY3Rpdml0aWVzIHBhZ2UgZm9yIHRoZSBidXNpbmVzc1xyXG4gICAgcmV2YWxpZGF0ZVBhdGgoYC9kYXNoYm9hcmQvYnVzaW5lc3MvYWN0aXZpdGllc2ApO1xyXG5cclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIlVuZXhwZWN0ZWQgZXJyb3IgaW4gc3VibWl0UmV2aWV3OlwiLCBlcnJvcik7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZC5cIjtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZGVsZXRlUmV2aWV3KFxyXG4gIGJ1c2luZXNzUHJvZmlsZUlkOiBzdHJpbmdcclxuKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcclxuICAvLyBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogeyB1c2VyIH0sXHJcbiAgICBlcnJvcjogYXV0aEVycm9yLFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuXHJcbiAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWQuXCIgfTtcclxuICB9XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgd2l0aCBwcm9wZXIgUkxTXHJcbiAgICBjb25zdCB7IGVycm9yOiBkZWxldGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJyYXRpbmdzX3Jldmlld3NcIilcclxuICAgICAgLmRlbGV0ZSgpXHJcbiAgICAgIC5tYXRjaCh7IHVzZXJfaWQ6IHVzZXIuaWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pO1xyXG5cclxuICAgIGlmIChkZWxldGVFcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgcmV2aWV3OlwiLCBkZWxldGVFcnJvcik7XHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihkZWxldGVFcnJvci5tZXNzYWdlKTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBBdmVyYWdlIHJhdGluZyBpcyBoYW5kbGVkIGJ5IHRoZSBkYXRhYmFzZSB0cmlnZ2VyXHJcblxyXG4gICAgLy8gUmV2YWxpZGF0ZSBwYXRoc1xyXG4gICAgLy8gVXNlIHJlZ3VsYXIgY2xpZW50IC0gYnVzaW5lc3NfcHJvZmlsZXMgaGFzIHB1YmxpYyByZWFkIGFjY2Vzc1xyXG4gICAgY29uc3QgeyBkYXRhOiBjYXJkRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgICAuc2VsZWN0KFwiYnVzaW5lc3Nfc2x1Z1wiKVxyXG4gICAgICAuZXEoXCJpZFwiLCBidXNpbmVzc1Byb2ZpbGVJZClcclxuICAgICAgLnNpbmdsZSgpO1xyXG5cclxuICAgIGlmIChjYXJkRGF0YT8uYnVzaW5lc3Nfc2x1Zykge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChgLyR7Y2FyZERhdGEuYnVzaW5lc3Nfc2x1Z31gKTtcclxuICAgIH1cclxuICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9jdXN0b21lclwiKTtcclxuXHJcbiAgICAvLyBSZXZhbGlkYXRlIHRoZSBhY3Rpdml0aWVzIHBhZ2UgZm9yIHRoZSBidXNpbmVzc1xyXG4gICAgcmV2YWxpZGF0ZVBhdGgoYC9kYXNoYm9hcmQvYnVzaW5lc3MvYWN0aXZpdGllc2ApO1xyXG5cclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIlVuZXhwZWN0ZWQgZXJyb3IgaW4gZGVsZXRlUmV2aWV3OlwiLCBlcnJvcik7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZC5cIjtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gbGlrZUJ1c2luZXNzKFxyXG4gIGJ1c2luZXNzUHJvZmlsZUlkOiBzdHJpbmdcclxuKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcclxuICAvLyBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuXHJcbiAgY29uc3Qge1xyXG4gICAgZGF0YTogeyB1c2VyIH0sXHJcbiAgICBlcnJvcjogYXV0aEVycm9yLFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuXHJcbiAgaWYgKGF1dGhFcnJvciB8fCAhdXNlcikge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIlVzZXIgbm90IGF1dGhlbnRpY2F0ZWQuXCIgfTtcclxuICB9XHJcblxyXG4gIC8vIFByZXZlbnQgYSBidXNpbmVzcyBmcm9tIGxpa2luZyB0aGVpciBvd24gYnVzaW5lc3MgY2FyZFxyXG4gIGlmICh1c2VyLmlkID09PSBidXNpbmVzc1Byb2ZpbGVJZCkge1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBcIllvdSBjYW5ub3QgbGlrZSB5b3VyIG93biBidXNpbmVzcyBjYXJkLlwiIH07XHJcbiAgfVxyXG5cclxuICB0cnkge1xyXG4gICAgLy8gMS4gSW5zZXJ0IGxpa2UgLSBVc2UgcmVndWxhciBjbGllbnQgd2l0aCBwcm9wZXIgUkxTXHJcbiAgICBjb25zdCB7IGVycm9yOiBpbnNlcnRFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJsaWtlc1wiKVxyXG4gICAgICAuaW5zZXJ0KHsgdXNlcl9pZDogdXNlci5pZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSk7XHJcblxyXG4gICAgaWYgKGluc2VydEVycm9yKSB7XHJcbiAgICAgIC8vIEhhbmRsZSBwb3RlbnRpYWwgdW5pcXVlIGNvbnN0cmFpbnQgdmlvbGF0aW9uIChhbHJlYWR5IGxpa2VkKSBncmFjZWZ1bGx5XHJcbiAgICAgIGlmIChpbnNlcnRFcnJvci5jb2RlID09PSBcIjIzNTA1XCIpIHtcclxuICAgICAgICAvLyB1bmlxdWVfdmlvbGF0aW9uXHJcbiAgICAgICAgY29uc29sZS5sb2coXHJcbiAgICAgICAgICBgVXNlciAke3VzZXIuaWR9IGFscmVhZHkgbGlrZWQgYnVzaW5lc3MgJHtidXNpbmVzc1Byb2ZpbGVJZH0uYFxyXG4gICAgICAgICk7XHJcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9OyAvLyBDb25zaWRlciBpdCBzdWNjZXNzIGlmIGFscmVhZHkgbGlrZWRcclxuICAgICAgfVxyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaW5zZXJ0aW5nIGxpa2U6XCIsIGluc2VydEVycm9yKTtcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGluc2VydEVycm9yLm1lc3NhZ2UpO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIE5vdGU6IFdlIGRvbid0IG5lZWQgdG8gbWFudWFsbHkgdXBkYXRlIHRoZSBsaWtlIGNvdW50XHJcbiAgICAvLyBUaGUgZGF0YWJhc2UgdHJpZ2dlciAndXBkYXRlX3RvdGFsX2xpa2VzJyB3aWxsIGhhbmRsZSB0aGlzIGF1dG9tYXRpY2FsbHlcclxuXHJcbiAgICAvLyAzLiBSZXZhbGlkYXRlIHBhdGhzXHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgLSBidXNpbmVzc19wcm9maWxlcyBoYXMgcHVibGljIHJlYWQgYWNjZXNzXHJcbiAgICBjb25zdCB7IGRhdGE6IGNhcmREYXRhIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbShcImJ1c2luZXNzX3Byb2ZpbGVzXCIpXHJcbiAgICAgIC5zZWxlY3QoXCJidXNpbmVzc19zbHVnXCIpXHJcbiAgICAgIC5lcShcImlkXCIsIGJ1c2luZXNzUHJvZmlsZUlkKVxyXG4gICAgICAuc2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKGNhcmREYXRhPy5idXNpbmVzc19zbHVnKSB7XHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKGAvJHtjYXJkRGF0YS5idXNpbmVzc19zbHVnfWApO1xyXG4gICAgfVxyXG5cclxuICAgIC8vIENoZWNrIGlmIHRoZSBjdXJyZW50IHVzZXIgaXMgYSBidXNpbmVzcyBhbmQgcmV2YWxpZGF0ZSBidXNpbmVzcyBkYXNoYm9hcmRcclxuICAgIGNvbnN0IHsgZGF0YTogdXNlckJ1c2luZXNzUHJvZmlsZSB9ID0gYXdhaXQgc3VwYWJhc2VcclxuICAgICAgLmZyb20oXCJidXNpbmVzc19wcm9maWxlc1wiKVxyXG4gICAgICAuc2VsZWN0KFwiaWRcIilcclxuICAgICAgLmVxKFwiaWRcIiwgdXNlci5pZClcclxuICAgICAgLm1heWJlU2luZ2xlKCk7XHJcblxyXG4gICAgaWYgKHVzZXJCdXNpbmVzc1Byb2ZpbGUpIHtcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoXCIvZGFzaGJvYXJkL2J1c2luZXNzXCIpOyAvLyBSZXZhbGlkYXRlIGJ1c2luZXNzIGRhc2hib2FyZFxyXG4gICAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvYnVzaW5lc3MvbGlrZXNcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3MgbGlrZXMgcGFnZVxyXG4gICAgfVxyXG5cclxuICAgIC8vIFJldmFsaWRhdGUgdGhlIGFjdGl2aXRpZXMgcGFnZSBmb3IgdGhlIGJ1c2luZXNzXHJcbiAgICByZXZhbGlkYXRlUGF0aChgL2Rhc2hib2FyZC9idXNpbmVzcy9hY3Rpdml0aWVzYCk7XHJcblxyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKFwiVW5leHBlY3RlZCBlcnJvciBpbiBsaWtlQnVzaW5lc3M6XCIsIGVycm9yKTtcclxuICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9XHJcbiAgICAgIGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJBbiB1bmV4cGVjdGVkIGVycm9yIG9jY3VycmVkLlwiO1xyXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcclxuICB9XHJcbn1cclxuXHJcbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1bmxpa2VCdXNpbmVzcyhcclxuICBidXNpbmVzc1Byb2ZpbGVJZDogc3RyaW5nXHJcbik6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XHJcbiAgLy8gY29uc3QgY29va2llU3RvcmUgPSBjb29raWVzKCk7XHJcbiAgY29uc3Qgc3VwYWJhc2UgPSBhd2FpdCBjcmVhdGVDbGllbnQoKTsgLy8gQXdhaXQgdGhlIGFzeW5jIGZ1bmN0aW9uXHJcblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gICAgZXJyb3I6IGF1dGhFcnJvcixcclxuICB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKCk7XHJcblxyXG4gIGlmIChhdXRoRXJyb3IgfHwgIXVzZXIpIHtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogXCJVc2VyIG5vdCBhdXRoZW50aWNhdGVkLlwiIH07XHJcbiAgfVxyXG5cclxuICAvLyBQcmV2ZW50IGEgYnVzaW5lc3MgZnJvbSB1bmxpa2luZyB0aGVpciBvd24gYnVzaW5lc3MgY2FyZFxyXG4gIGlmICh1c2VyLmlkID09PSBidXNpbmVzc1Byb2ZpbGVJZCkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3VjY2VzczogZmFsc2UsXHJcbiAgICAgIGVycm9yOiBcIllvdSBjYW5ub3QgdW5saWtlIHlvdXIgb3duIGJ1c2luZXNzIGNhcmQuXCIsXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgdHJ5IHtcclxuICAgIC8vIDEuIERlbGV0ZSBsaWtlIC0gVXNlIHJlZ3VsYXIgY2xpZW50IHdpdGggcHJvcGVyIFJMU1xyXG4gICAgY29uc3QgeyBlcnJvcjogZGVsZXRlRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwibGlrZXNcIilcclxuICAgICAgLmRlbGV0ZSgpXHJcbiAgICAgIC5tYXRjaCh7IHVzZXJfaWQ6IHVzZXIuaWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pO1xyXG5cclxuICAgIGlmIChkZWxldGVFcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZGVsZXRpbmcgbGlrZTpcIiwgZGVsZXRlRXJyb3IpO1xyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZGVsZXRlRXJyb3IubWVzc2FnZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTm90ZTogV2UgZG9uJ3QgbmVlZCB0byBtYW51YWxseSB1cGRhdGUgdGhlIGxpa2UgY291bnRcclxuICAgIC8vIFRoZSBkYXRhYmFzZSB0cmlnZ2VyICd1cGRhdGVfdG90YWxfbGlrZXMnIHdpbGwgaGFuZGxlIHRoaXMgYXV0b21hdGljYWxseVxyXG5cclxuICAgIC8vIDMuIFJldmFsaWRhdGUgcGF0aHNcclxuICAgIC8vIFVzZSByZWd1bGFyIGNsaWVudCAtIGJ1c2luZXNzX3Byb2ZpbGVzIGhhcyBwdWJsaWMgcmVhZCBhY2Nlc3NcclxuICAgIGNvbnN0IHsgZGF0YTogY2FyZERhdGEgfSA9IGF3YWl0IHN1cGFiYXNlXHJcbiAgICAgIC5mcm9tKFwiYnVzaW5lc3NfcHJvZmlsZXNcIilcclxuICAgICAgLnNlbGVjdChcImJ1c2luZXNzX3NsdWdcIilcclxuICAgICAgLmVxKFwiaWRcIiwgYnVzaW5lc3NQcm9maWxlSWQpXHJcbiAgICAgIC5zaW5nbGUoKTtcclxuXHJcbiAgICBpZiAoY2FyZERhdGE/LmJ1c2luZXNzX3NsdWcpIHtcclxuICAgICAgcmV2YWxpZGF0ZVBhdGgoYC8ke2NhcmREYXRhLmJ1c2luZXNzX3NsdWd9YCk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gQ2hlY2sgaWYgdGhlIGN1cnJlbnQgdXNlciBpcyBhIGJ1c2luZXNzIGFuZCByZXZhbGlkYXRlIGJ1c2luZXNzIGRhc2hib2FyZFxyXG4gICAgY29uc3QgeyBkYXRhOiB1c2VyQnVzaW5lc3NQcm9maWxlIH0gPSBhd2FpdCBzdXBhYmFzZVxyXG4gICAgICAuZnJvbShcImJ1c2luZXNzX3Byb2ZpbGVzXCIpXHJcbiAgICAgIC5zZWxlY3QoXCJpZFwiKVxyXG4gICAgICAuZXEoXCJpZFwiLCB1c2VyLmlkKVxyXG4gICAgICAubWF5YmVTaW5nbGUoKTtcclxuXHJcbiAgICBpZiAodXNlckJ1c2luZXNzUHJvZmlsZSkge1xyXG4gICAgICByZXZhbGlkYXRlUGF0aChcIi9kYXNoYm9hcmQvYnVzaW5lc3NcIik7IC8vIFJldmFsaWRhdGUgYnVzaW5lc3MgZGFzaGJvYXJkXHJcbiAgICAgIHJldmFsaWRhdGVQYXRoKFwiL2Rhc2hib2FyZC9idXNpbmVzcy9saWtlc1wiKTsgLy8gUmV2YWxpZGF0ZSBidXNpbmVzcyBsaWtlcyBwYWdlXHJcbiAgICB9XHJcblxyXG4gICAgLy8gUmV2YWxpZGF0ZSB0aGUgYWN0aXZpdGllcyBwYWdlIGZvciB0aGUgYnVzaW5lc3NcclxuICAgIHJldmFsaWRhdGVQYXRoKGAvZGFzaGJvYXJkL2J1c2luZXNzL2FjdGl2aXRpZXNgKTtcclxuXHJcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoXCJVbmV4cGVjdGVkIGVycm9yIGluIHVubGlrZUJ1c2luZXNzOlwiLCBlcnJvcik7XHJcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPVxyXG4gICAgICBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6IFwiQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZC5cIjtcclxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0SW50ZXJhY3Rpb25TdGF0dXMoYnVzaW5lc3NQcm9maWxlSWQ6IHN0cmluZyk6IFByb21pc2U8e1xyXG4gIGlzU3Vic2NyaWJlZDogYm9vbGVhbjtcclxuICBoYXNMaWtlZDogYm9vbGVhbjtcclxuICB1c2VyUmF0aW5nOiBudW1iZXIgfCBudWxsO1xyXG4gIHVzZXJSZXZpZXc6IHN0cmluZyB8IG51bGw7XHJcbiAgZXJyb3I/OiBzdHJpbmc7XHJcbn0+IHtcclxuICAvLyBjb25zdCBjb29raWVTdG9yZSA9IGNvb2tpZXMoKTtcclxuICBjb25zdCBzdXBhYmFzZSA9IGF3YWl0IGNyZWF0ZUNsaWVudCgpOyAvLyBBd2FpdCB0aGUgYXN5bmMgZnVuY3Rpb25cclxuICBsZXQgdXNlcklkOiBzdHJpbmcgfCBudWxsID0gbnVsbDtcclxuXHJcbiAgLy8gVHJ5IHRvIGdldCBhdXRoZW50aWNhdGVkIHVzZXIsIGJ1dCBwcm9jZWVkIGV2ZW4gaWYgbm90IGxvZ2dlZCBpblxyXG4gIGNvbnN0IHtcclxuICAgIGRhdGE6IHsgdXNlciB9LFxyXG4gIH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKTtcclxuICBpZiAodXNlcikge1xyXG4gICAgdXNlcklkID0gdXNlci5pZDtcclxuICB9XHJcblxyXG4gIC8vIERlZmF1bHQgc3RhdHVzIGZvciBhbm9ueW1vdXMgdXNlcnNcclxuICBjb25zdCBkZWZhdWx0U3RhdHVzID0ge1xyXG4gICAgaXNTdWJzY3JpYmVkOiBmYWxzZSxcclxuICAgIGhhc0xpa2VkOiBmYWxzZSxcclxuICAgIHVzZXJSYXRpbmc6IG51bGwsXHJcbiAgICB1c2VyUmV2aWV3OiBudWxsLFxyXG4gIH07XHJcblxyXG4gIGlmICghdXNlcklkKSB7XHJcbiAgICByZXR1cm4gZGVmYXVsdFN0YXR1czsgLy8gUmV0dXJuIGRlZmF1bHQgaWYgbm8gdXNlciBpcyBsb2dnZWQgaW5cclxuICB9XHJcblxyXG4gIHRyeSB7XHJcbiAgICAvLyBVc2UgcmVndWxhciBjbGllbnQgLSBhbGwgdGhlc2UgdGFibGVzIGhhdmUgcHVibGljIHJlYWQgYWNjZXNzXHJcbiAgICAvLyBGZXRjaCBhbGwgc3RhdHVzZXMgaW4gcGFyYWxsZWxcclxuICAgIGNvbnN0IFtzdWJzY3JpcHRpb25SZXMsIGxpa2VSZXMsIHJldmlld1Jlc10gPSBhd2FpdCBQcm9taXNlLmFsbChbXHJcbiAgICAgIHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oXCJzdWJzY3JpcHRpb25zXCIpXHJcbiAgICAgICAgLnNlbGVjdChcImlkXCIsIHsgY291bnQ6IFwiZXhhY3RcIiwgaGVhZDogdHJ1ZSB9KSAvLyBKdXN0IGNoZWNrIGV4aXN0ZW5jZVxyXG4gICAgICAgIC5tYXRjaCh7IHVzZXJfaWQ6IHVzZXJJZCwgYnVzaW5lc3NfcHJvZmlsZV9pZDogYnVzaW5lc3NQcm9maWxlSWQgfSksXHJcbiAgICAgIHN1cGFiYXNlXHJcbiAgICAgICAgLmZyb20oXCJsaWtlc1wiKVxyXG4gICAgICAgIC5zZWxlY3QoXCJpZFwiLCB7IGNvdW50OiBcImV4YWN0XCIsIGhlYWQ6IHRydWUgfSkgLy8gSnVzdCBjaGVjayBleGlzdGVuY2VcclxuICAgICAgICAubWF0Y2goeyB1c2VyX2lkOiB1c2VySWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pLFxyXG4gICAgICBzdXBhYmFzZVxyXG4gICAgICAgIC5mcm9tKFwicmF0aW5nc19yZXZpZXdzXCIpXHJcbiAgICAgICAgLnNlbGVjdChcInJhdGluZywgcmV2aWV3X3RleHRcIilcclxuICAgICAgICAubWF0Y2goeyB1c2VyX2lkOiB1c2VySWQsIGJ1c2luZXNzX3Byb2ZpbGVfaWQ6IGJ1c2luZXNzUHJvZmlsZUlkIH0pXHJcbiAgICAgICAgLm1heWJlU2luZ2xlKCksIC8vIFVzZSBtYXliZVNpbmdsZSBhcyB1c2VyIG1pZ2h0IG5vdCBoYXZlIHJldmlld2VkXHJcbiAgICBdKTtcclxuXHJcbiAgICAvLyBDaGVjayBmb3IgZXJyb3JzIGluIHBhcmFsbGVsIGZldGNoZXNcclxuICAgIGlmIChzdWJzY3JpcHRpb25SZXMuZXJyb3IpXHJcbiAgICAgIHRocm93IG5ldyBFcnJvcihcclxuICAgICAgICBgU3Vic2NyaXB0aW9uIGZldGNoIGVycm9yOiAke3N1YnNjcmlwdGlvblJlcy5lcnJvci5tZXNzYWdlfWBcclxuICAgICAgKTtcclxuICAgIGlmIChsaWtlUmVzLmVycm9yKVxyXG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYExpa2UgZmV0Y2ggZXJyb3I6ICR7bGlrZVJlcy5lcnJvci5tZXNzYWdlfWApO1xyXG4gICAgaWYgKHJldmlld1Jlcy5lcnJvcilcclxuICAgICAgdGhyb3cgbmV3IEVycm9yKGBSZXZpZXcgZmV0Y2ggZXJyb3I6ICR7cmV2aWV3UmVzLmVycm9yLm1lc3NhZ2V9YCk7XHJcblxyXG4gICAgY29uc3QgcmV2aWV3RGF0YSA9IHJldmlld1Jlcy5kYXRhO1xyXG5cclxuICAgIHJldHVybiB7XHJcbiAgICAgIGlzU3Vic2NyaWJlZDogKHN1YnNjcmlwdGlvblJlcy5jb3VudCA/PyAwKSA+IDAsXHJcbiAgICAgIGhhc0xpa2VkOiAobGlrZVJlcy5jb3VudCA/PyAwKSA+IDAsXHJcbiAgICAgIHVzZXJSYXRpbmc6IHJldmlld0RhdGE/LnJhdGluZyA/PyBudWxsLFxyXG4gICAgICB1c2VyUmV2aWV3OiByZXZpZXdEYXRhPy5yZXZpZXdfdGV4dCA/PyBudWxsLFxyXG4gICAgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIGludGVyYWN0aW9uIHN0YXR1czpcIiwgZXJyb3IpO1xyXG4gICAgY29uc3QgZXJyb3JNZXNzYWdlID1cclxuICAgICAgZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBcIkFuIHVuZXhwZWN0ZWQgZXJyb3Igb2NjdXJyZWQuXCI7XHJcbiAgICAvLyBSZXR1cm4gZGVmYXVsdCBzdGF0dXMgYnV0IGluY2x1ZGUgdGhlIGVycm9yIG1lc3NhZ2VcclxuICAgIHJldHVybiB7IC4uLmRlZmF1bHRTdGF0dXMsIGVycm9yOiBlcnJvck1lc3NhZ2UgfTtcclxuICB9XHJcbn1cclxuIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJpU0E0U3NCIn0=
}}),
"[project]/lib/actions/data:641f97 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40ac030879d2dd9afcc77d6d490fed42fed2873adc":"subscribeToBusiness"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "subscribeToBusiness": (()=>subscribeToBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var subscribeToBusiness = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("40ac030879d2dd9afcc77d6d490fed42fed2873adc", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "subscribeToBusiness"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/actions/data:f789d9 [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"406e7e1a2d209fd5068ed48a9b20838464c164486f":"unlikeBusiness"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "unlikeBusiness": (()=>unlikeBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var unlikeBusiness = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("406e7e1a2d209fd5068ed48a9b20838464c164486f", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "unlikeBusiness"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/actions/data:6990ad [app-ssr] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff":"unsubscribeFromBusiness"},"lib/actions/interactions.ts",""] */ __turbopack_context__.s({
    "unsubscribeFromBusiness": (()=>unsubscribeFromBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-ssr] (ecmascript)");
"use turbopack no side effects";
;
var unsubscribeFromBusiness = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createServerReference"])("407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["findSourceMapURL"], "unsubscribeFromBusiness"); //# sourceMappingURL=data:application/json;base64,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
}}),
"[project]/lib/schemas/authSchemas.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "EmailOTPSchema": (()=>EmailOTPSchema),
    "IndianMobileSchema": (()=>IndianMobileSchema),
    "MobilePasswordLoginSchema": (()=>MobilePasswordLoginSchema),
    "PasswordComplexitySchema": (()=>PasswordComplexitySchema),
    "VerifyOTPSchema": (()=>VerifyOTPSchema)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-ssr] (ecmascript)");
;
const IndianMobileSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(10, {
    message: "Mobile number must be 10 digits"
}).max(10, {
    message: "Mobile number must be 10 digits"
}).regex(/^[6-9]\d{9}$/, {
    message: "Please enter a valid Indian mobile number"
});
const EmailOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    })
});
const VerifyOTPSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    email: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Email is required"
    }).email({
        message: "Please enter a valid email address"
    }),
    otp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(6, {
        message: "OTP must be 6 digits"
    }).max(6, {
        message: "OTP must be 6 digits"
    }).regex(/^\d{6}$/, {
        message: "OTP must be 6 digits"
    })
});
const PasswordComplexitySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().min(6, "Password must be at least 6 characters long").regex(/[A-Z]/, "Password must contain at least one uppercase letter").regex(/[a-z]/, "Password must contain at least one lowercase letter").regex(/\d/, "Password must contain at least one number").regex(/[^a-zA-Z0-9]/, "Password must contain at least one special character");
const MobilePasswordLoginSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].object({
    mobile: IndianMobileSchema,
    password: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["z"].string().trim().min(1, {
        message: "Password is required"
    })
});
}}),
"[project]/lib/utils/customBranding.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "generateCustomBrandingStyles": (()=>generateCustomBrandingStyles),
    "getBrandingText": (()=>getBrandingText),
    "getDefaultCustomBrandingSettings": (()=>getDefaultCustomBrandingSettings),
    "getPrimaryThemeColor": (()=>getPrimaryThemeColor),
    "getThemeSpecificHeaderImage": (()=>getThemeSpecificHeaderImage),
    "hasActiveCustomBranding": (()=>hasActiveCustomBranding),
    "hasAnyHeaderImage": (()=>hasAnyHeaderImage),
    "hasCustomBrandingAccess": (()=>hasCustomBrandingAccess),
    "shouldShowDukancardBranding": (()=>shouldShowDukancardBranding),
    "validateCustomBrandingSettings": (()=>validateCustomBrandingSettings)
});
function hasCustomBrandingAccess(userPlan) {
    return userPlan === "pro" || userPlan === "enterprise";
}
function shouldShowDukancardBranding(userPlan, customBranding) {
    // For users NOT on Pro/Enterprise plans, ALWAYS show DukanCard branding
    // regardless of any custom branding settings
    if (!hasCustomBrandingAccess(userPlan)) {
        return true;
    }
    // For Pro/Enterprise users, only hide branding if they explicitly set it to hidden
    return !customBranding?.hide_dukancard_branding;
}
function getThemeSpecificHeaderImage(userPlan, customBranding, currentTheme) {
    // Only return custom images for Pro/Enterprise users
    if (!hasCustomBrandingAccess(userPlan) || !customBranding) {
        return null;
    }
    const isDark = currentTheme === "dark";
    // Priority 1: Theme-specific images
    if (isDark && customBranding.custom_header_image_dark_url?.trim()) {
        return customBranding.custom_header_image_dark_url;
    }
    if (!isDark && customBranding.custom_header_image_light_url?.trim()) {
        return customBranding.custom_header_image_light_url;
    }
    // Priority 2: Fallback to opposite theme if current theme image is missing
    if (isDark && customBranding.custom_header_image_light_url?.trim()) {
        return customBranding.custom_header_image_light_url;
    }
    if (!isDark && customBranding.custom_header_image_dark_url?.trim()) {
        return customBranding.custom_header_image_dark_url;
    }
    // Priority 3: Legacy single image field for backward compatibility
    if (customBranding.custom_header_image_url?.trim()) {
        return customBranding.custom_header_image_url;
    }
    return null;
}
function getBrandingText(userPlan, customBranding) {
    // Only return custom text for Pro/Enterprise users
    if (hasCustomBrandingAccess(userPlan) && customBranding?.custom_header_text) {
        return customBranding.custom_header_text;
    }
    return null; // Will show default Dukancard branding
}
function getPrimaryThemeColor(userPlan, customBranding, fallbackThemeColor) {
    const defaultColor = "var(--brand-gold)";
    // Use theme_color if available and user is Pro/Enterprise
    if (hasCustomBrandingAccess(userPlan) && fallbackThemeColor) {
        return fallbackThemeColor;
    }
    return defaultColor;
}
function generateCustomBrandingStyles(userPlan, customBranding, fallbackThemeColor) {
    const primaryColor = getPrimaryThemeColor(userPlan, customBranding, fallbackThemeColor);
    const styles = {
        "--theme-color": primaryColor,
        "--theme-color-80": `${primaryColor}CC`,
        "--theme-color-50": `${primaryColor}80`,
        "--theme-color-30": `${primaryColor}4D`,
        "--theme-color-20": `${primaryColor}33`,
        "--theme-color-10": `${primaryColor}1A`,
        "--theme-color-5": `${primaryColor}0D`,
        "--theme-accent-end": "#E5C76E"
    };
    return styles;
}
function validateCustomBrandingSettings(settings) {
    const errors = [];
    // Validate header text length
    if (settings.custom_header_text && settings.custom_header_text.length > 50) {
        errors.push("Custom header text must be 50 characters or less");
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
function getDefaultCustomBrandingSettings() {
    return {
        custom_header_text: "",
        hide_dukancard_branding: false
    };
}
function hasActiveCustomBranding(customBranding) {
    if (!customBranding) return false;
    return !!(customBranding.custom_header_text || customBranding.custom_header_image_url || customBranding.custom_header_image_light_url || customBranding.custom_header_image_dark_url || customBranding.hide_dukancard_branding);
}
function hasAnyHeaderImage(customBranding) {
    if (!customBranding) return false;
    return !!(customBranding.custom_header_image_url?.trim() || customBranding.custom_header_image_light_url?.trim() || customBranding.custom_header_image_dark_url?.trim());
}
}}),
"[project]/lib/qrCodeGenerator.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions to generate and download QR codes in different formats:
 * 1. Enhanced QR code with business information in A4 format for better printing options
 * 2. Raw QR image for digital use
 */ __turbopack_context__.s({
    "downloadRawQRImage": (()=>downloadRawQRImage),
    "generateAndDownloadQRCode": (()=>generateAndDownloadQRCode)
});
async function downloadRawQRImage(svgElement, slug) {
    const svgData = new XMLSerializer().serializeToString(svgElement);
    return new Promise((resolve, reject)=>{
        const img = new Image();
        img.onload = ()=>{
            // Create a canvas with padding around the QR code
            // Increase size for higher quality output
            const padding = 50; // Increased padding for better appearance
            const canvas = document.createElement("canvas");
            // Set a larger fixed size for better quality (1000x1000 pixels)
            const canvasSize = 1000;
            canvas.width = canvasSize;
            canvas.height = canvasSize;
            const ctx = canvas.getContext("2d");
            if (!ctx) {
                reject(new Error("Could not get canvas context"));
                return;
            }
            // Fill with white background
            ctx.fillStyle = "#FFFFFF";
            ctx.fillRect(0, 0, canvasSize, canvasSize);
            // Enable high-quality image rendering
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = "high";
            // Calculate QR code size (canvas size minus padding on all sides)
            const qrSize = canvasSize - padding * 2;
            // Draw QR code centered with higher quality
            ctx.drawImage(img, padding, padding, qrSize, qrSize);
            // Add a subtle border around the QR code for better definition
            ctx.strokeStyle = "#EEEEEE";
            ctx.lineWidth = 2;
            ctx.strokeRect(padding - 2, padding - 2, qrSize + 4, qrSize + 4);
            // Convert to PNG with maximum quality and trigger download
            const pngFile = canvas.toDataURL("image/png", 1.0);
            const link = document.createElement("a");
            link.download = `${slug}-qr-code.png`;
            link.href = pngFile;
            link.click();
            resolve();
        };
        img.onerror = ()=>{
            reject(new Error("Could not load QR code SVG"));
        };
        img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
    });
}
async function generateAndDownloadQRCode(svgElement, businessInfo) {
    const { businessName, ownerName, address, slug, themeColor = "#F59E0B" } = businessInfo;
    // A4 dimensions in pixels at 300 DPI
    // A4 is 210mm × 297mm, which is approximately 2480 × 3508 pixels at 300 DPI
    const width = 2480;
    const height = 3508;
    // Create canvas
    const canvas = document.createElement("canvas");
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext("2d");
    if (!ctx) {
        throw new Error("Could not get canvas context");
    }
    // Fill background with white
    ctx.fillStyle = "#FFFFFF";
    ctx.fillRect(0, 0, width, height);
    // Create a subtle gradient background
    const gradient = ctx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, "#FFFFFF");
    gradient.addColorStop(1, "#F8F8F8");
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    // Add a subtle pattern overlay for texture
    createSubtlePattern(ctx, width, height);
    // Draw a more sophisticated frame with enhanced visual design
    drawSophisticatedFrame(ctx, width, height, themeColor);
    // Add decorative elements
    drawDecorativeElements(ctx, width, height, themeColor);
    // Add header with business name - handle long business names
    ctx.fillStyle = "#333333";
    ctx.textAlign = "center";
    // Start with large font size and reduce if needed
    let businessNameFontSize = 160;
    ctx.font = `bold ${businessNameFontSize}px 'Arial'`;
    // Check if business name is too long and reduce font size if needed
    let businessNameWidth = ctx.measureText(businessName).width;
    while(businessNameWidth > width * 0.75 && businessNameFontSize > 80){
        businessNameFontSize -= 10;
        ctx.font = `bold ${businessNameFontSize}px 'Arial'`;
        businessNameWidth = ctx.measureText(businessName).width;
    }
    // If still too long, split into multiple lines
    if (businessNameWidth > width * 0.75) {
        const businessNameLines = splitTextIntoLines(businessName, 30);
        let yPos = height * 0.12;
        businessNameLines.forEach((line)=>{
            ctx.fillText(line, width / 2, yPos, width * 0.8);
            yPos += businessNameFontSize * 0.8; // Add spacing between lines
        });
    } else {
        ctx.fillText(businessName, width / 2, height * 0.15, width * 0.8);
    }
    // Add a subtle underline below the business name
    const textWidth = ctx.measureText(businessName).width;
    const underlineWidth = Math.min(textWidth, width * 0.6);
    ctx.beginPath();
    ctx.moveTo(width / 2 - underlineWidth / 2, height * 0.17);
    ctx.lineTo(width / 2 + underlineWidth / 2, height * 0.17);
    ctx.strokeStyle = themeColor;
    ctx.lineWidth = 6;
    ctx.stroke();
    // Add "Scan to view our digital card" text with better styling
    ctx.font = "80px 'Arial'";
    ctx.fillStyle = "#555555";
    ctx.fillText("Scan to view our digital card", width / 2, height * 0.22, width * 0.8);
    // QR code size and positioning - adjust based on business name length
    // Use smaller QR code if business name is very long (multiple lines)
    const businessNameLines = splitTextIntoLines(businessName, 30);
    const qrSizeMultiplier = businessNameLines.length > 1 ? 0.3 : 0.35;
    const qrSize = Math.min(width, height) * qrSizeMultiplier;
    const qrX = (width - qrSize) / 2;
    const qrY = businessNameLines.length > 1 ? height * 0.35 : height * 0.3;
    // Draw an elegant container for the QR code
    drawQRCodeContainer(ctx, qrX, qrY, qrSize, themeColor);
    // Draw QR code
    const svgData = new XMLSerializer().serializeToString(svgElement);
    return new Promise((resolve, reject)=>{
        const img = new Image();
        img.onload = ()=>{
            // Draw QR code centered
            ctx.drawImage(img, qrX, qrY, qrSize, qrSize);
            // Add URL text below QR code with better styling
            // Handle potentially long slugs by reducing font size if needed
            const urlText = `dukancard.in/${slug}`;
            let urlFontSize = 70;
            ctx.font = `bold ${urlFontSize}px 'Arial'`;
            // Check if URL is too long and reduce font size if needed
            let urlWidth = ctx.measureText(urlText).width;
            while(urlWidth > width * 0.7 && urlFontSize > 40){
                urlFontSize -= 5;
                ctx.font = `bold ${urlFontSize}px 'Arial'`;
                urlWidth = ctx.measureText(urlText).width;
            }
            // Position URL with sufficient distance from QR code
            ctx.fillStyle = "#333333";
            ctx.fillText(urlText, width / 2, qrY + qrSize + 180, width * 0.8);
            // Add a divider line - position based on URL position
            const dividerY = qrY + qrSize + 240; // Position after URL
            drawDivider(ctx, width, dividerY, width * 0.7, themeColor);
            // Add owner name with better styling - handle long names
            let ownerNameFontSize = 100;
            ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;
            ctx.fillStyle = "#333333";
            // Check if owner name is too long and reduce font size if needed
            let ownerNameWidth = ctx.measureText(ownerName).width;
            while(ownerNameWidth > width * 0.75 && ownerNameFontSize > 60){
                ownerNameFontSize -= 5;
                ctx.font = `bold ${ownerNameFontSize}px 'Arial'`;
                ownerNameWidth = ctx.measureText(ownerName).width;
            }
            // If still too long, split into multiple lines
            if (ownerNameWidth > width * 0.75) {
                const ownerNameLines = splitTextIntoLines(ownerName, 25);
                let yPos = height * 0.75;
                ownerNameLines.forEach((line)=>{
                    ctx.fillText(line, width / 2, yPos, width * 0.8);
                    yPos += ownerNameFontSize * 0.7; // Add spacing between lines
                });
            } else {
                ctx.fillText(ownerName, width / 2, height * 0.75, width * 0.8);
            }
            // We're removing the location icon as it's causing visual issues
            // No need to draw the location icon anymore
            // Add address with better styling
            let addressFontSize = 70;
            ctx.font = `${addressFontSize}px 'Arial'`;
            ctx.fillStyle = "#555555";
            // Calculate starting position based on owner name position and length
            let yPosition;
            if (ownerNameWidth > width * 0.75) {
                // If owner name was split into multiple lines, position address accordingly
                const ownerNameLines = splitTextIntoLines(ownerName, 25);
                yPosition = height * 0.75 + ownerNameLines.length * ownerNameFontSize * 0.7 + 50;
            } else {
                yPosition = height * 0.8;
            }
            // Split address into multiple lines
            const addressLines = splitTextIntoLines(address, 50);
            // If address is very long, reduce font size
            if (addressLines.length > 3) {
                addressFontSize = 60;
                ctx.font = `${addressFontSize}px 'Arial'`;
            }
            // Draw each line of the address
            addressLines.forEach((line)=>{
                ctx.fillText(line, width / 2, yPosition, width * 0.8);
                yPosition += addressFontSize + 20; // Line height with spacing
            });
            // Add a footer with powered by text - position dynamically based on content
            ctx.font = "50px 'Arial'";
            ctx.fillStyle = "#888888";
            // Calculate footer position based on address length
            const footerY = Math.min(height - 100, yPosition + 150);
            ctx.fillText("Powered by Dukancard", width / 2, footerY, width * 0.8);
            // Convert to JPG and trigger download
            const jpgFile = canvas.toDataURL("image/jpeg", 0.95);
            const link = document.createElement("a");
            link.download = `${slug}-qrcode.jpg`;
            link.href = jpgFile;
            link.click();
            resolve();
        };
        img.onerror = ()=>{
            reject(new Error("Could not load QR code SVG"));
        };
        img.src = `data:image/svg+xml;base64,${btoa(svgData)}`;
    });
}
/**
 * Creates a subtle pattern overlay for texture
 */ function createSubtlePattern(ctx, width, height) {
    ctx.save();
    ctx.globalAlpha = 0.03;
    // Create a pattern of small dots
    const patternSize = 20;
    for(let x = 0; x < width; x += patternSize){
        for(let y = 0; y < height; y += patternSize){
            ctx.beginPath();
            ctx.arc(x, y, 1, 0, Math.PI * 2);
            ctx.fillStyle = "#000000";
            ctx.fill();
        }
    }
    ctx.restore();
}
/**
 * Draws a sophisticated frame with enhanced visual design
 */ function drawSophisticatedFrame(ctx, width, height, themeColor) {
    // Create a more elegant border with gradient
    const borderGradient = ctx.createLinearGradient(0, 0, width, height);
    borderGradient.addColorStop(0, themeColor);
    borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));
    borderGradient.addColorStop(1, themeColor);
    // Draw outer border with gradient
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 3;
    ctx.strokeRect(40, 40, width - 80, height - 80);
    // Draw inner border with gradient and shadow
    ctx.shadowColor = "rgba(0, 0, 0, 0.1)";
    ctx.shadowBlur = 15;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 8;
    ctx.strokeRect(80, 80, width - 160, height - 160);
    // Reset shadow
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    // Add corner decorations with enhanced design
    const cornerSize = 120;
    // Top-left corner
    drawCornerDecoration(ctx, 80, 80, cornerSize, themeColor, "top-left");
    // Top-right corner
    drawCornerDecoration(ctx, width - 80, 80, cornerSize, themeColor, "top-right");
    // Bottom-left corner
    drawCornerDecoration(ctx, 80, height - 80, cornerSize, themeColor, "bottom-left");
    // Bottom-right corner
    drawCornerDecoration(ctx, width - 80, height - 80, cornerSize, themeColor, "bottom-right");
    // Add subtle decorative patterns along the borders
    drawBorderPatterns(ctx, width, height, themeColor);
}
/**
 * Draws enhanced corner decorations
 */ function drawCornerDecoration(ctx, x, y, size, color, position) {
    ctx.save();
    // Create gradient for more elegant corners
    const cornerGradient = ctx.createLinearGradient(position.includes("left") ? x : x - size, position.includes("top") ? y : y - size, position.includes("left") ? x + size : x, position.includes("top") ? y + size : y);
    cornerGradient.addColorStop(0, color);
    cornerGradient.addColorStop(1, adjustColor(color, 20));
    ctx.strokeStyle = cornerGradient;
    ctx.lineWidth = 8;
    ctx.lineCap = "round";
    ctx.beginPath();
    if (position === "top-left") {
        ctx.moveTo(x, y + size);
        ctx.lineTo(x, y);
        ctx.lineTo(x + size, y);
        // Add decorative dot
        ctx.moveTo(x + 30, y + 30);
        ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);
    } else if (position === "top-right") {
        ctx.moveTo(x - size, y);
        ctx.lineTo(x, y);
        ctx.lineTo(x, y + size);
        // Add decorative dot
        ctx.moveTo(x - 30, y + 30);
        ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);
    } else if (position === "bottom-left") {
        ctx.moveTo(x, y - size);
        ctx.lineTo(x, y);
        ctx.lineTo(x + size, y);
        // Add decorative dot
        ctx.moveTo(x + 30, y - 30);
        ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);
    } else if (position === "bottom-right") {
        ctx.moveTo(x - size, y);
        ctx.lineTo(x, y);
        ctx.lineTo(x, y - size);
        // Add decorative dot
        ctx.moveTo(x - 30, y - 30);
        ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);
    }
    ctx.stroke();
    // Fill the decorative dots
    ctx.fillStyle = color;
    if (position === "top-left") {
        ctx.beginPath();
        ctx.arc(x + 30, y + 30, 8, 0, Math.PI * 2);
        ctx.fill();
    } else if (position === "top-right") {
        ctx.beginPath();
        ctx.arc(x - 30, y + 30, 8, 0, Math.PI * 2);
        ctx.fill();
    } else if (position === "bottom-left") {
        ctx.beginPath();
        ctx.arc(x + 30, y - 30, 8, 0, Math.PI * 2);
        ctx.fill();
    } else if (position === "bottom-right") {
        ctx.beginPath();
        ctx.arc(x - 30, y - 30, 8, 0, Math.PI * 2);
        ctx.fill();
    }
    ctx.restore();
}
/**
 * Draws enhanced decorative elements
 */ function drawDecorativeElements(ctx, width, height, themeColor) {
    // Add subtle decorative elements with enhanced design
    ctx.save();
    // Create gradient for decorative elements
    const gradientTopLeft = ctx.createRadialGradient(0, 0, 0, 0, 0, 400);
    gradientTopLeft.addColorStop(0, themeColor);
    gradientTopLeft.addColorStop(1, "rgba(255, 255, 255, 0)");
    const gradientBottomRight = ctx.createRadialGradient(width, height, 0, width, height, 400);
    gradientBottomRight.addColorStop(0, themeColor);
    gradientBottomRight.addColorStop(1, "rgba(255, 255, 255, 0)");
    // Adjust opacity for subtlety
    ctx.globalAlpha = 0.08;
    // Draw decorative gradient in top-left
    ctx.beginPath();
    ctx.arc(0, 0, 400, 0, Math.PI * 2);
    ctx.fillStyle = gradientTopLeft;
    ctx.fill();
    // Draw decorative gradient in bottom-right
    ctx.beginPath();
    ctx.arc(width, height, 400, 0, Math.PI * 2);
    ctx.fillStyle = gradientBottomRight;
    ctx.fill();
    // Add decorative patterns
    ctx.globalAlpha = 0.05;
    // Draw decorative pattern in center
    const patternSize = 60;
    const patternRows = Math.ceil(height / patternSize);
    const patternCols = Math.ceil(width / patternSize);
    for(let row = 0; row < patternRows; row++){
        for(let col = 0; col < patternCols; col++){
            // Only draw pattern in a diamond shape in the center
            const distanceFromCenter = Math.abs(row - patternRows / 2) + Math.abs(col - patternCols / 2);
            if (distanceFromCenter < patternRows / 3) {
                const x = col * patternSize;
                const y = row * patternSize;
                // Draw subtle pattern element
                if ((row + col) % 2 === 0) {
                    ctx.beginPath();
                    ctx.arc(x + patternSize / 2, y + patternSize / 2, 2, 0, Math.PI * 2);
                    ctx.fillStyle = themeColor;
                    ctx.fill();
                }
            }
        }
    }
    ctx.restore();
}
/**
 * Draws an elegant container for the QR code with enhanced visual design
 */ function drawQRCodeContainer(ctx, x, y, size, themeColor) {
    const padding = 100;
    const containerWidth = size + padding * 2;
    const containerHeight = size + padding * 2;
    const containerX = x - padding;
    const containerY = y - padding;
    // Draw white background with rounded corners
    ctx.fillStyle = "#FFFFFF";
    roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);
    ctx.fill();
    // Draw subtle shadow
    ctx.shadowColor = "rgba(0, 0, 0, 0.15)";
    ctx.shadowBlur = 40;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 15;
    ctx.fillStyle = "#FFFFFF";
    roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);
    ctx.fill();
    // Reset shadow
    ctx.shadowColor = "transparent";
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    // Draw elegant border with gradient
    const borderGradient = ctx.createLinearGradient(containerX, containerY, containerX + containerWidth, containerY + containerHeight);
    borderGradient.addColorStop(0, themeColor);
    borderGradient.addColorStop(0.5, adjustColor(themeColor, 20));
    borderGradient.addColorStop(1, themeColor);
    ctx.strokeStyle = borderGradient;
    ctx.lineWidth = 3;
    roundRect(ctx, containerX, containerY, containerWidth, containerHeight, 20);
    ctx.stroke();
    // Add decorative corner elements to the QR container
    const cornerSize = 30;
    // Top-left corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX, containerY + cornerSize);
    ctx.lineTo(containerX, containerY);
    ctx.lineTo(containerX + cornerSize, containerY);
    ctx.strokeStyle = themeColor;
    ctx.lineWidth = 5;
    ctx.stroke();
    // Top-right corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX + containerWidth - cornerSize, containerY);
    ctx.lineTo(containerX + containerWidth, containerY);
    ctx.lineTo(containerX + containerWidth, containerY + cornerSize);
    ctx.stroke();
    // Bottom-left corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX, containerY + containerHeight - cornerSize);
    ctx.lineTo(containerX, containerY + containerHeight);
    ctx.lineTo(containerX + cornerSize, containerY + containerHeight);
    ctx.stroke();
    // Bottom-right corner decoration
    ctx.beginPath();
    ctx.moveTo(containerX + containerWidth - cornerSize, containerY + containerHeight);
    ctx.lineTo(containerX + containerWidth, containerY + containerHeight);
    ctx.lineTo(containerX + containerWidth, containerY + containerHeight - cornerSize);
    ctx.stroke();
}
/**
 * Draws an enhanced divider line with decorative elements
 */ function drawDivider(ctx, x, y, width, color) {
    ctx.save();
    // Create gradient for divider
    const dividerGradient = ctx.createLinearGradient(x / 2 - width / 2, y, x / 2 + width / 2, y);
    dividerGradient.addColorStop(0, "rgba(255, 255, 255, 0)");
    dividerGradient.addColorStop(0.1, color);
    dividerGradient.addColorStop(0.5, adjustColor(color, 20));
    dividerGradient.addColorStop(0.9, color);
    dividerGradient.addColorStop(1, "rgba(255, 255, 255, 0)");
    // Draw main line with gradient
    ctx.beginPath();
    ctx.moveTo(x / 2 - width / 2, y);
    ctx.lineTo(x / 2 + width / 2, y);
    ctx.strokeStyle = dividerGradient;
    ctx.lineWidth = 3;
    ctx.stroke();
    // Draw decorative element in the middle
    ctx.beginPath();
    ctx.arc(x / 2, y, 15, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();
    // Add outer ring to the decorative element
    ctx.beginPath();
    ctx.arc(x / 2, y, 20, 0, Math.PI * 2);
    ctx.strokeStyle = color;
    ctx.lineWidth = 2;
    ctx.stroke();
    // Add small decorative elements along the line
    const numDots = 6;
    const dotSpacing = width / 4 / numDots;
    // Left side dots
    for(let i = 1; i <= numDots; i++){
        const dotX = x / 2 - width / 8 - i * dotSpacing;
        ctx.beginPath();
        ctx.arc(dotX, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();
    }
    // Right side dots
    for(let i = 1; i <= numDots; i++){
        const dotX = x / 2 + width / 8 + i * dotSpacing;
        ctx.beginPath();
        ctx.arc(dotX, y, 3, 0, Math.PI * 2);
        ctx.fillStyle = color;
        ctx.fill();
    }
    ctx.restore();
}
/**
 * Helper function to draw a rectangle with rounded corners
 */ function roundRect(ctx, x, y, width, height, radius) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
}
/**
 * Helper function to adjust color brightness
 */ function adjustColor(color, amount) {
    // Convert hex to RGB
    let r, g, b;
    if (color.startsWith("#")) {
        r = parseInt(color.slice(1, 3), 16);
        g = parseInt(color.slice(3, 5), 16);
        b = parseInt(color.slice(5, 7), 16);
    } else {
        // Default fallback color
        r = 245;
        g = 158;
        b = 11; // Default to brand gold
    }
    // Adjust brightness
    r = Math.max(0, Math.min(255, r + amount));
    g = Math.max(0, Math.min(255, g + amount));
    b = Math.max(0, Math.min(255, b + amount));
    // Convert back to hex
    return `#${r.toString(16).padStart(2, "0")}${g.toString(16).padStart(2, "0")}${b.toString(16).padStart(2, "0")}`;
}
/**
 * Draws decorative patterns along the borders
 */ function drawBorderPatterns(ctx, width, height, themeColor) {
    ctx.save();
    ctx.globalAlpha = 0.2;
    // Top border pattern
    for(let x = 120; x < width - 120; x += 40){
        ctx.beginPath();
        ctx.arc(x, 80, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    // Bottom border pattern
    for(let x = 120; x < width - 120; x += 40){
        ctx.beginPath();
        ctx.arc(x, height - 80, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    // Left border pattern
    for(let y = 120; y < height - 120; y += 40){
        ctx.beginPath();
        ctx.arc(80, y, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    // Right border pattern
    for(let y = 120; y < height - 120; y += 40){
        ctx.beginPath();
        ctx.arc(width - 80, y, 2, 0, Math.PI * 2);
        ctx.fillStyle = themeColor;
        ctx.fill();
    }
    ctx.restore();
}
/**
 * Helper function to split text into multiple lines
 */ function splitTextIntoLines(text, maxCharsPerLine) {
    const words = text.split(" ");
    const lines = [];
    let currentLine = "";
    words.forEach((word)=>{
        if ((currentLine + word).length <= maxCharsPerLine) {
            currentLine += (currentLine ? " " : "") + word;
        } else {
            lines.push(currentLine);
            currentLine = word;
        }
    });
    if (currentLine) {
        lines.push(currentLine);
    }
    return lines;
}
}}),
"[project]/lib/cardDownloader.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Utility functions to download business cards as PNG images
 * Uses modern-screenshot for HTML to image conversion with proper rounded corners
 */ __turbopack_context__.s({
    "downloadBusinessCard": (()=>downloadBusinessCard),
    "downloadBusinessCardAsPNG": (()=>downloadBusinessCardAsPNG),
    "findBusinessCardElement": (()=>findBusinessCardElement),
    "prepareCardForScreenshot": (()=>prepareCardForScreenshot)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$modern$2d$screenshot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/modern-screenshot/dist/index.mjs [app-ssr] (ecmascript)");
;
async function downloadBusinessCardAsPNG(cardElement, options) {
    const { businessSlug, quality = 1, scale = 3, preserveRoundedCorners = true } = options;
    try {
        // Get the actual dimensions of the card element
        const rect = cardElement.getBoundingClientRect();
        console.log('Card element dimensions:', {
            width: rect.width,
            height: rect.height,
            offsetWidth: cardElement.offsetWidth,
            offsetHeight: cardElement.offsetHeight,
            className: cardElement.className,
            tagName: cardElement.tagName
        });
        // Ensure we have valid dimensions
        if (rect.width === 0 || rect.height === 0) {
            throw new Error('Card element has invalid dimensions');
        }
        // Generate high-quality PNG using modern-screenshot with proper settings for rounded corners
        const dataUrl = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$modern$2d$screenshot$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["domToPng"])(cardElement, {
            quality,
            scale,
            backgroundColor: 'transparent',
            style: {
                // Ensure consistent rendering and preserve rounded corners
                transform: 'scale(1)',
                transformOrigin: 'top left',
                borderRadius: preserveRoundedCorners ? 'inherit' : '0',
                // Ensure the element is properly sized
                width: `${rect.width}px`,
                height: `${rect.height}px`,
                maxWidth: `${rect.width}px`,
                maxHeight: `${rect.height}px`,
                overflow: 'hidden'
            },
            // Use the actual rendered dimensions
            width: rect.width,
            height: rect.height
        });
        // Create download link
        const link = document.createElement('a');
        link.download = `${businessSlug}-digital-card.png`;
        link.href = dataUrl;
        link.click();
    } catch (error) {
        console.error('Error downloading business card as PNG:', error);
        throw new Error('Failed to download business card as PNG');
    }
}
async function downloadBusinessCard(cardElement, options) {
    return downloadBusinessCardAsPNG(cardElement, options);
}
function findBusinessCardElement(containerRef) {
    // Try to find the card element using various selectors, prioritizing the most specific
    const selectors = [
        '[data-card-element]',
        '.business-card-preview',
        '.business-card',
        '#business-card',
        '.card-preview'
    ];
    const container = containerRef?.current || document;
    const candidates = [];
    // Collect all potential card elements
    for (const selector of selectors){
        const elements = container.querySelectorAll(selector);
        elements.forEach((element)=>{
            const rect = element.getBoundingClientRect();
            // Business card should have reasonable dimensions (not too large)
            // Max width should be around 384px (max-w-sm) and height should be proportional
            if (rect.width > 0 && rect.height > 0 && rect.width <= 500) {
                candidates.push(element);
            }
        });
    }
    if (candidates.length === 0) {
        return null;
    }
    if (candidates.length === 1) {
        return candidates[0];
    }
    // If multiple candidates, prioritize based on context
    // 1. Prefer cards that are not in demo mode (check for unmasked data)
    // 2. Prefer cards that are visible and not hidden
    // 3. Prefer cards that are in the main content area (not in headers/footers)
    const scoredCandidates = candidates.map((element)=>{
        let score = 0;
        // Check if the card has unmasked phone/email (indicates authenticated context)
        const phoneElement = element.querySelector('a[href^="tel:"]');
        const emailElement = element.querySelector('a[href^="mailto:"]');
        const phoneText = phoneElement?.textContent || '';
        const emailText = emailElement?.textContent || '';
        // If phone/email don't contain asterisks, it's likely unmasked (authenticated)
        if (phoneText && !phoneText.includes('*')) score += 10;
        if (emailText && !emailText.includes('*')) score += 10;
        // Prefer visible elements
        const rect = element.getBoundingClientRect();
        if (rect.top >= 0 && rect.left >= 0) score += 5;
        // Prefer elements in main content areas (not in navigation or footer)
        const isInNav = element.closest('nav, header, footer');
        if (!isInNav) score += 5;
        // Prefer larger cards (main content vs thumbnails)
        if (rect.width > 300) score += 3;
        return {
            element,
            score
        };
    });
    // Sort by score (highest first) and return the best candidate
    scoredCandidates.sort((a, b)=>b.score - a.score);
    return scoredCandidates[0].element;
}
function prepareCardForScreenshot(cardElement) {
    const originalStyles = {
        transform: cardElement.style.transform,
        transformOrigin: cardElement.style.transformOrigin,
        position: cardElement.style.position,
        zIndex: cardElement.style.zIndex
    };
    // Apply screenshot-friendly styles
    cardElement.style.transform = 'scale(1)';
    cardElement.style.transformOrigin = 'top left';
    cardElement.style.position = 'relative';
    cardElement.style.zIndex = '1';
    // Return cleanup function
    return ()=>{
        cardElement.style.transform = originalStyles.transform;
        cardElement.style.transformOrigin = originalStyles.transformOrigin;
        cardElement.style.position = originalStyles.position;
        cardElement.style.zIndex = originalStyles.zIndex;
    };
}
}}),

};

//# sourceMappingURL=lib_4aa5bdd1._.js.map