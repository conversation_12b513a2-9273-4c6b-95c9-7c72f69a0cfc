import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@/utils/supabase/server';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Apply security middleware for reviews API
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  try {
    // 1. Rate limiting
    const ipAddress = getClientIP(req);
    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'posts_api',
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck;
    }

    // 2. JWT verification
    const token = extractBearerToken(req);
    if (!token) {
      return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
      return new NextResponse(JSON.stringify({ error: jwtResult.error || 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // 3. HMAC verification (for external calls)
    if (requireHMAC) {
      const hmacResult = await verifyHMACMiddleware(req, true);
      if (!hmacResult.success) {
        return new NextResponse(JSON.stringify({ error: hmacResult.error || 'HMAC verification failed' }), {
          status: hmacResult.status || 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      return {
        success: true,
        jwtPayload: jwtResult.payload,
        deviceId: hmacResult.deviceId,
      };
    }

    return {
      success: true,
      jwtPayload: jwtResult.payload,
    };

  } catch (error) {
    console.error('Unexpected error in security middleware:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal security error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for listing reviews
const listReviewsSchema = z.object({
  business_id: z.string().uuid(),
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  user_id: z.string().uuid().optional(),
});

/**
 * GET /api/reviews - List reviews for a business
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for listing reviews)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listReviewsSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      business_id,
      page = 1,
      limit = 20,
      user_id
    } = validation.data;
    const offset = (page - 1) * limit;

    const supabase = await createClient();

    // Build query for reviews
    let query = supabase
      .from('ratings_reviews')
      .select(`
        id, rating, review_text, created_at, updated_at,
        user_id, business_profile_id,
        customer_profiles!user_id(id, name, avatar_url)
      `, { count: 'exact' })
      .eq('business_profile_id', business_id);

    // Filter by user if specified
    if (user_id) {
      query = query.eq('user_id', user_id);
    }

    // Add sorting and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: reviews, error, count } = await query;

    if (error) {
      console.error('Error fetching reviews:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch reviews' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      reviews: reviews || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in reviews API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for creating/updating reviews
const reviewSchema = z.object({
  business_profile_id: z.string().uuid(),
  rating: z.number().min(1).max(5),
  review_text: z.string().max(1000).optional().nullable(),
});

/**
 * POST /api/reviews - Create or update a review
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = reviewSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { business_profile_id, rating, review_text } = validation.data;

    // Prevent self-review
    if (jwtPayload.sub === business_profile_id) {
      return new NextResponse(JSON.stringify({ 
        error: 'You cannot review your own business' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Upsert the review (insert or update if exists)
    const { data: review, error } = await supabase
      .from('ratings_reviews')
      .upsert({
        user_id: jwtPayload.sub,
        business_profile_id,
        rating,
        review_text,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id, business_profile_id',
      })
      .select(`
        id, rating, review_text, created_at, updated_at,
        user_id, business_profile_id,
        customer_profiles!user_id(id, name, avatar_url)
      `)
      .single();

    if (error) {
      console.error('Error creating/updating review:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to submit review' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      review,
      message: 'Review submitted successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create review API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for deleting reviews
const deleteReviewSchema = z.object({
  business_profile_id: z.string().uuid(),
});

/**
 * DELETE /api/reviews - Delete a review
 */
export async function DELETE(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = deleteReviewSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { business_profile_id } = validation.data;

    const supabase = await createClient();

    // Delete the review
    const { error } = await supabase
      .from('ratings_reviews')
      .delete()
      .eq('user_id', jwtPayload.sub)
      .eq('business_profile_id', business_profile_id);

    if (error) {
      console.error('Error deleting review:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to delete review' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: 'Review deleted successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in delete review API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
