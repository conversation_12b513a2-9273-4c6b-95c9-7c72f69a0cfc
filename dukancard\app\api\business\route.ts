import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { createClient } from '@/utils/supabase/server';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

// Validation schemas
const createBusinessSchema = z.object({
  business_name: z.string().min(1, "Business name is required"),
  contact_email: z.string().email("Invalid email address"),
  member_name: z.string().min(1, "Member name is required"),
  title: z.string().min(1, "Title is required"),
  business_category: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  whatsapp_number: z.string().length(10, "WhatsApp number must be 10 digits").optional().nullable(),
  address_line: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  pincode: z.string().optional().nullable(),
  locality: z.string().optional().nullable(),
  about_bio: z.string().max(250, "About/bio cannot exceed 250 characters").optional().nullable(),
  business_slug: z.string().min(3, "Slug must be at least 3 characters"),
  status: z.enum(["online", "offline"]).default("offline"),
  logo_url: z.string().url().optional().nullable(),
  instagram_url: z.string().url().optional().nullable(),
  facebook_url: z.string().url().optional().nullable(),
  established_year: z.number().int().min(1900).max(new Date().getFullYear()).optional().nullable(),
  delivery_info: z.string().optional().nullable(),
  business_hours: z.record(z.string(), z.any()).optional().nullable(),
});



const listBusinessSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  search: z.string().optional(),
  category: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  pincode: z.string().optional(),
  locality: z.string().optional(),
  pincodes: z.string().optional(), // For multiple pincodes (comma-separated)
  status: z.enum(["online", "offline"]).optional(),
  sort_by: z.enum(["name_asc", "name_desc", "created_asc", "created_desc", "likes_asc", "likes_desc", "subscriptions_asc", "subscriptions_desc", "rating_asc", "rating_desc"]).optional(),
  ids_only: z.string().transform(val => val === 'true').optional(), // For discovery endpoint
});

/**
 * Security middleware wrapper for business API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtPayload = jwtResult.payload;

  // 3. Verify HMAC signature (if required)
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, true);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { jwtPayload };
}

/**
 * GET /api/business - List business profiles with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for public business listing)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listBusinessSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({
        error: 'Invalid query parameters',
        details: validation.error.issues,
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      category,
      city,
      state,
      pincode,
      locality,
      pincodes,
      status,
      sort_by = "created_desc",
      ids_only = false
    } = validation.data;
    const offset = (page - 1) * limit;

    // Build query
    const supabase = await createClient();

    // Select fields based on ids_only parameter
    const selectFields = ids_only
      ? 'id'
      : `id, business_name, business_slug, logo_url, member_name, title,
         business_category, city, state, pincode, locality, status, total_likes, total_subscriptions,
         average_rating, created_at, updated_at, contact_email, established_year, about_bio,
         phone, instagram_url, facebook_url, whatsapp_number, address_line, delivery_info, business_hours`;

    let query = supabase
      .from('business_profiles')
      .select(selectFields, { count: 'exact' });

    // Apply filters
    if (search) {
      query = query.or(`business_name.ilike.%${search}%,member_name.ilike.%${search}%`);
    }
    if (category) {
      query = query.eq('business_category', category);
    }
    if (city) {
      query = query.eq('city', city);
    }
    if (state) {
      query = query.eq('state', state);
    }
    if (pincode) {
      query = query.eq('pincode', pincode);
    }
    if (locality) {
      query = query.eq('locality', locality);
    }
    if (pincodes) {
      // Handle multiple pincodes (comma-separated)
      const pincodeArray = pincodes.split(',').map(p => p.trim()).filter(p => p);
      if (pincodeArray.length > 0) {
        query = query.in('pincode', pincodeArray);
      }
    }
    if (status) {
      query = query.eq('status', status);
    } else {
      // Default to online businesses only
      query = query.eq('status', 'online');
    }

    // Apply sorting
    switch (sort_by) {
      case "name_asc":
        query = query.order('business_name', { ascending: true });
        break;
      case "name_desc":
        query = query.order('business_name', { ascending: false });
        break;
      case "created_asc":
        query = query.order('created_at', { ascending: true });
        break;
      case "created_desc":
        query = query.order('created_at', { ascending: false });
        break;
      case "likes_asc":
        query = query.order('total_likes', { ascending: true });
        break;
      case "likes_desc":
        query = query.order('total_likes', { ascending: false });
        break;
      case "subscriptions_asc":
        query = query.order('total_subscriptions', { ascending: true });
        break;
      case "subscriptions_desc":
        query = query.order('total_subscriptions', { ascending: false });
        break;
      case "rating_asc":
        query = query.order('average_rating', { ascending: true });
        break;
      case "rating_desc":
        query = query.order('average_rating', { ascending: false });
        break;
      default:
        query = query.order('created_at', { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: businesses, error, count } = await query;

    if (error) {
      console.error('Error fetching business profiles:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch business profiles' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    interface BusinessIdOnly {
  id: string;
}

    if (ids_only) {
      if (error) {
        return NextResponse.json({ business_ids: [], count: 0 });
      }
      const business_ids = (businesses as BusinessIdOnly[]).map((b) => b.id);
      return NextResponse.json({ business_ids, count: count || 0 });
    }

    return NextResponse.json({
      businesses: businesses || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    });

  } catch (error) {
    console.error('Unexpected error in GET /api/business:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * POST /api/business - Create a new business profile
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = createBusinessSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({
        error: 'Validation failed',
        details: validation.error.issues,
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const businessData = validation.data;
    const supabase = await createClient();

    // Check if user already has a business profile
    const { data: existingProfile } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', jwtPayload.user_id)
      .maybeSingle();

    if (existingProfile) {
      return new NextResponse(JSON.stringify({ error: 'Business profile already exists' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if business slug is unique
    const { data: slugExists } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('business_slug', businessData.business_slug)
      .maybeSingle();

    if (slugExists) {
      return new NextResponse(JSON.stringify({ error: 'Business slug already exists' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Create business profile
    const { data: newBusiness, error } = await supabase
      .from('business_profiles')
      .insert({
        id: jwtPayload.user_id,
        ...businessData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating business profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to create business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({
      business: newBusiness,
      message: 'Business profile created successfully',
    }, { status: 201 });

  } catch (error) {
    console.error('Unexpected error in POST /api/business:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
