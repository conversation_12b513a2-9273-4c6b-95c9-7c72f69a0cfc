"use server";

import { SitemapProfileData } from "./types";

/**
 * Securely fetch business profiles for sitemap using the API endpoint
 */
export async function getSecureBusinessProfilesForSitemap(): Promise<{
  data?: SitemapProfileData[];
  error?: string;
}> {
  try {
    // Use the business sitemap API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/sitemap`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profiles for sitemap",
      };
    }

    const profiles = result.profiles || [];

    // If there are no profiles, return empty array
    if (!profiles || profiles.length === 0) {
      return { data: [] };
    }

    // Create a map to deduplicate by business_slug
    const uniqueProfiles = new Map<
      string,
      { business_slug: string; updated_at: string }
    >();

    // Add all profiles to the map (this automatically deduplicates by business_slug)
    profiles.forEach((profile: { business_slug: string; updated_at: string }) => {
      if (profile.business_slug) {
        uniqueProfiles.set(profile.business_slug, {
          business_slug: profile.business_slug,
          updated_at: profile.updated_at,
        });
      }
    });

    // Convert map values to array
    const combinedProfiles = Array.from(uniqueProfiles.values());

    // Return the deduplicated profiles
    return { data: combinedProfiles };
  } catch (_e) {
    return { error: "An unexpected error occurred." };
  }
}
