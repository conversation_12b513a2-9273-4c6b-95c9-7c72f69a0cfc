{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/profileRetrieval.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport {\r\n  BusinessProfilePublicData,\r\n  BusinessProfileWithProducts,\r\n} from \"./types\";\r\n\r\n/**\r\n * Securely fetch a business profile by slug using the service role key\r\n * This bypasses RLS and ensures sensitive data is not exposed to the client\r\n */\r\nexport async function getSecureBusinessProfileBySlug(slug: string): Promise<{\r\n  data?: BusinessProfilePublicData;\r\n  error?: string;\r\n}> {\r\n  if (!slug) {\r\n    return { error: \"Business slug is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the business profile API endpoint for public access by slug\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to fetch business profile\",\r\n      };\r\n    }\r\n\r\n    const profileData = result.business;\r\n    if (!profileData) {\r\n      return { error: \"Profile not found.\" };\r\n    }\r\n\r\n    return { data: profileData };\r\n  } catch (e) {\r\n    console.error(\"Exception in getSecureBusinessProfileBySlug:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetch a business profile with products by slug using the service role key\r\n */\r\nexport async function getSecureBusinessProfileWithProductsBySlug(\r\n  slug: string\r\n): Promise<{\r\n  data?: BusinessProfileWithProducts;\r\n  error?: string;\r\n}> {\r\n  if (!slug) {\r\n    return { error: \"Business slug is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the business profile API endpoint for public access by slug\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}?include_products=true`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to fetch business profile\",\r\n      };\r\n    }\r\n\r\n    const profileData = result.business;\r\n    if (!profileData) {\r\n      return { error: \"Profile not found.\" };\r\n    }\r\n\r\n    const safeData: BusinessProfileWithProducts = {\r\n      ...profileData,\r\n      products_services: profileData.products_services || [],\r\n    };\r\n\r\n    return { data: safeData };\r\n  } catch (e) {\r\n    console.error(\r\n      \"Exception in getSecureBusinessProfileWithProductsBySlug:\",\r\n      e\r\n    );\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAWO,eAAe,+BAA+B,IAAY;IAI/D,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;QAA6B;IAC/C;IAEA,IAAI;QACF,kEAAkE;QAClE,MAAM,WAAW,MAAM,MAAM,6DAAoC,mBAAmB,EAAE,MAAM,EAAE;YAC5F,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,OAAO;YAAE,MAAM;QAAY;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,2CACpB,IAAY;IAKZ,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;QAA6B;IAC/C;IAEA,IAAI;QACF,kEAAkE;QAClE,MAAM,WAAW,MAAM,MAAM,6DAAoC,mBAAmB,EAAE,KAAK,sBAAsB,CAAC,EAAE;YAClH,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,MAAM,WAAwC;YAC5C,GAAG,WAAW;YACd,mBAAmB,YAAY,iBAAiB,IAAI,EAAE;QACxD;QAEA,OAAO;YAAE,MAAM;QAAS;IAC1B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CACX,4DACA;QAEF,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAvFsB;IAyCA;;AAzCA,+OAAA;AAyCA,+OAAA", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  let headersList: Headers | null = null;\r\n  let cookieStore: any = null;\r\n\r\n  try {\r\n    // Dynamically import next/headers to avoid issues in edge runtime\r\n    const { headers, cookies } = await import('next/headers');\r\n    headersList = await headers();\r\n    cookieStore = await cookies();\r\n  } catch (error) {\r\n    // If next/headers is not available (e.g., in edge runtime), continue without it\r\n    console.warn('next/headers not available in this context, using fallback');\r\n  }\r\n\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    (headersList && headersList.get('x-playwright-testing') === 'true');\r\n\r\n  if (isTestEnvironment && headersList) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  // If cookies are not available, create a basic server client\r\n  if (!cookieStore) {\r\n    return createServerClient(\r\n      supabaseUrl,\r\n      supabaseAnonKey,\r\n      {\r\n        cookies: {\r\n          getAll() {\r\n            return [];\r\n          },\r\n          setAll() {\r\n            // No-op when cookies are not available\r\n          },\r\n        },\r\n      }\r\n    ) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAIO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,IAAI,cAA8B;IAClC,IAAI,cAAmB;IAEvB,IAAI;QACF,kEAAkE;QAClE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,cAAc,MAAM;QACpB,cAAc,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,gFAAgF;QAChF,QAAQ,IAAI,CAAC;IACf;IAEA,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAClC,eAAe,YAAY,GAAG,CAAC,4BAA4B;IAE9D,IAAI,qBAAqB,aAAa;QACpC,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,6DAA6D;IAC7D,IAAI,CAAC,aAAa;QAChB,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;YACE,SAAS;gBACP;oBACE,OAAO,EAAE;gBACX;gBACA;gBACE,uCAAuC;gBACzC;YACF;QACF;IAEJ;IAEA,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/gallery/utils.ts"], "sourcesContent": ["// Removed PlanType import as we're now free for all users\r\n\r\n/**\r\n * Get the gallery image limit - 10 images for all users\r\n * @returns The gallery image limit (10 images)\r\n */\r\nexport function getGalleryLimit(): number {\r\n  return 10;\r\n}\r\n\r\n/**\r\n * Check if a user can add more gallery images based on the 10 image limit\r\n * @param currentCount The current number of gallery images\r\n * @returns Whether the user can add more gallery images\r\n */\r\nexport function canAddMoreGalleryImages(currentCount: number): boolean {\r\n  const limit = getGalleryLimit();\r\n  return currentCount < limit;\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAE1D;;;CAGC;;;;AACM,SAAS;IACd,OAAO;AACT;AAOO,SAAS,wBAAwB,YAAoB;IAC1D,MAAM,QAAQ;IACd,OAAO,eAAe;AACxB", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/gallery.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { SupabaseClient } from \"@supabase/supabase-js\";\r\nimport { Database } from \"@/types/supabase\";\r\nimport { getGalleryLimit } from \"@/app/(dashboard)/dashboard/business/gallery/utils\";\r\nimport { Tables } from \"@/types/supabase\";\r\n\r\ntype BusinessProfiles = Tables<'business_profiles'>;\r\n\r\nexport interface GalleryImage {\r\n  id: string;\r\n  url: string; // Public URL for display\r\n  path: string; // Storage path\r\n  created_at: string;\r\n}\r\n\r\n/**\r\n * Get gallery images for a specific business\r\n * @param businessId The business ID\r\n * @returns Gallery images\r\n */\r\nexport async function getBusinessGalleryImages(businessId: string): Promise<{\r\n  images: GalleryImage[];\r\n  error?: string;\r\n}> {\r\n  const supabase = (await createClient()) as SupabaseClient<Database>;\r\n\r\n  try {\r\n    const { data: profileData, error } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"gallery\")\r\n      .eq(\"id\", businessId)\r\n      .single<Pick<BusinessProfiles, \"gallery\">>();\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching business profile:\", error);\r\n      return {\r\n        images: [],\r\n        error: `Failed to fetch gallery images: ${error.message}`,\r\n      };\r\n    }\r\n\r\n    const gallery = profileData?.gallery || [];\r\n    const images = Array.isArray(gallery) ? gallery : [];\r\n\r\n    // Sort by created_at in descending order\r\n    images.sort((a, b) => {\r\n      return (\r\n        new Date((b as { created_at: string }).created_at).getTime() - new Date((a as { created_at: string }).created_at).getTime()\r\n      );\r\n    });\r\n\r\n    return { images: images as unknown as GalleryImage[] };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching gallery images:\", error);\r\n    return {\r\n      images: [],\r\n      error: `An unexpected error occurred: ${\r\n        error instanceof Error ? error.message : String(error)\r\n      }`,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get gallery images for a business by slug (limited to 4 for gallery tab)\r\n * @param businessSlug The business slug\r\n * @returns Gallery images (max 4) and total count\r\n */\r\nexport async function getBusinessGalleryImagesForTab(\r\n  businessSlug: string\r\n): Promise<{\r\n  images: GalleryImage[];\r\n  totalCount: number;\r\n  error?: string;\r\n}> {\r\n  const supabase = (await createClient()) as SupabaseClient<Database>;\r\n\r\n  try {\r\n    // First, get the business ID from the slug\r\n    const { data: business, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"business_slug\", businessSlug)\r\n      .eq(\"status\", \"online\")\r\n      .single<Pick<BusinessProfiles, \"id\">>();\r\n\r\n    if (businessError || !business) {\r\n      console.error(\"Error fetching business profile:\", businessError);\r\n      return { images: [], totalCount: 0, error: \"Business not found\" };\r\n    }\r\n\r\n    // Platform is now free for all users - no subscription limits\r\n    const galleryLimit = getGalleryLimit();\r\n\r\n    // Get the business profile with gallery data\r\n    const { data: profileData, error: galleryError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"gallery\")\r\n      .eq(\"id\", business.id)\r\n      .single<Pick<BusinessProfiles, \"gallery\">>();\r\n\r\n    if (galleryError) {\r\n      console.error(\"Error fetching business gallery:\", galleryError);\r\n      return {\r\n        images: [],\r\n        totalCount: 0,\r\n        error: `Failed to fetch gallery images: ${galleryError.message}`,\r\n      };\r\n    }\r\n\r\n    // Parse gallery data\r\n    const gallery = profileData?.gallery || [];\r\n    const images = Array.isArray(gallery) ? gallery : [];\r\n\r\n    // Sort by created_at in descending order (newest first)\r\n    images.sort((a, b) => {\r\n      return (\r\n        new Date((b as { created_at: string }).created_at).getTime() - new Date((a as { created_at: string }).created_at).getTime()\r\n      );\r\n    });\r\n\r\n    // Apply plan limit first, then limit to max 4 for gallery tab\r\n    // This ensures we respect both plan restrictions and gallery tab display limits\r\n    const planLimitedImages = images.slice(0, galleryLimit);\r\n    const finalLimit = Math.min(planLimitedImages.length, 4);\r\n    const limitedImages = planLimitedImages.slice(0, finalLimit);\r\n\r\n    // Total count should also respect the plan limit, not show all images\r\n    const totalCount = planLimitedImages.length;\r\n\r\n    return { images: limitedImages as unknown as GalleryImage[], totalCount };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching gallery images for tab:\", error);\r\n    return {\r\n      images: [],\r\n      totalCount: 0,\r\n      error: `An unexpected error occurred: ${\r\n        error instanceof Error ? error.message : String(error)\r\n      }`,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get gallery images for a business by slug\r\n * @param businessSlug The business slug\r\n * @returns Gallery images\r\n */\r\nexport async function getBusinessGalleryImagesBySlug(\r\n  businessSlug: string\r\n): Promise<{\r\n  images: GalleryImage[];\r\n  error?: string;\r\n}> {\r\n  const supabase = (await createClient()) as SupabaseClient<Database>;\r\n\r\n  try {\r\n    // First, get the business ID from the slug\r\n    const { data: business, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"business_slug\", businessSlug)\r\n      .eq(\"status\", \"online\")\r\n      .single<Pick<BusinessProfiles, \"id\">>();\r\n\r\n    if (businessError || !business) {\r\n      console.error(\"Error fetching business profile:\", businessError);\r\n      return { images: [], error: \"Business not found\" };\r\n    }\r\n\r\n    // Platform is now free for all users - no subscription limits\r\n    const galleryLimit = getGalleryLimit();\r\n\r\n    // Get the business profile with gallery data\r\n    const { data: profileData, error: galleryError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"gallery\")\r\n      .eq(\"id\", business.id)\r\n      .single<Pick<BusinessProfiles, \"gallery\">>();\r\n\r\n    if (galleryError) {\r\n      console.error(\"Error fetching business gallery:\", galleryError);\r\n      return {\r\n        images: [],\r\n        error: `Failed to fetch gallery images: ${galleryError.message}`,\r\n      };\r\n    }\r\n\r\n    // Parse gallery data\r\n    const gallery = profileData?.gallery || [];\r\n    const images = Array.isArray(gallery) ? gallery : [];\r\n\r\n    // Sort by created_at in descending order (newest first)\r\n    images.sort((a, b) => {\r\n      return (\r\n        new Date((b as { created_at: string }).created_at).getTime() - new Date((a as { created_at: string }).created_at).getTime()\r\n      );\r\n    });\r\n\r\n    // Limit the number of images based on the plan\r\n    // This ensures we only return the number of images allowed by the plan\r\n    const limitedImages = images.slice(0, galleryLimit);\r\n\r\n    return { images: limitedImages as unknown as GalleryImage[] };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching gallery images by slug:\", error);\r\n    return {\r\n      images: [],\r\n      error: `An unexpected error occurred: ${\r\n        error instanceof Error ? error.message : String(error)\r\n      }`,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Get paginated gallery images for a business by slug\r\n * @param businessSlug The business slug\r\n * @param page Page number (1-based)\r\n * @param limit Number of images per page\r\n * @returns Paginated gallery images with metadata\r\n */\r\nexport async function getBusinessGalleryImagesPaginated(\r\n  businessSlug: string,\r\n  page: number = 1,\r\n  limit: number = 20\r\n): Promise<{\r\n  images: GalleryImage[];\r\n  totalCount: number;\r\n  totalPages: number;\r\n  currentPage: number;\r\n  hasNextPage: boolean;\r\n  hasPrevPage: boolean;\r\n  error?: string;\r\n}> {\r\n  const supabase = (await createClient()) as SupabaseClient<Database>;\r\n\r\n  try {\r\n    // First, get the business ID from the slug\r\n    const { data: business, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"business_slug\", businessSlug)\r\n      .eq(\"status\", \"online\")\r\n      .single<Pick<BusinessProfiles, \"id\">>();\r\n\r\n    if (businessError || !business) {\r\n      console.error(\"Error fetching business profile:\", businessError);\r\n      return {\r\n        images: [],\r\n        totalCount: 0,\r\n        totalPages: 0,\r\n        currentPage: page,\r\n        hasNextPage: false,\r\n        hasPrevPage: false,\r\n        error: \"Business not found\",\r\n      };\r\n    }\r\n\r\n    // Platform is now free for all users - no subscription limits\r\n    const galleryLimit = getGalleryLimit();\r\n\r\n    // Get the business profile with gallery data\r\n    const { data: profileData, error: galleryError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"gallery\")\r\n      .eq(\"id\", business.id)\r\n      .single<Pick<BusinessProfiles, \"gallery\">>();\r\n\r\n    if (galleryError) {\r\n      console.error(\"Error fetching business gallery:\", galleryError);\r\n      return {\r\n        images: [],\r\n        totalCount: 0,\r\n        totalPages: 0,\r\n        currentPage: page,\r\n        hasNextPage: false,\r\n        hasPrevPage: false,\r\n        error: `Failed to fetch gallery images: ${galleryError.message}`,\r\n      };\r\n    }\r\n\r\n    // Parse gallery data\r\n    const gallery = profileData?.gallery || [];\r\n    const allImages = Array.isArray(gallery) ? gallery : [];\r\n\r\n    // Sort by created_at in descending order (newest first)\r\n    allImages.sort((a, b) => {\r\n      return (\r\n        new Date((b as { created_at: string }).created_at).getTime() - new Date((a as { created_at: string }).created_at).getTime()\r\n      );\r\n    });\r\n\r\n    // Apply plan limit first\r\n    const planLimitedImages = allImages.slice(0, galleryLimit);\r\n    const totalCount = planLimitedImages.length;\r\n\r\n    // Calculate pagination\r\n    const totalPages = Math.ceil(totalCount / limit);\r\n    const startIndex = (page - 1) * limit;\r\n    const endIndex = startIndex + limit;\r\n\r\n    // Get paginated images\r\n    const paginatedImages = planLimitedImages.slice(startIndex, endIndex);\r\n\r\n    return {\r\n      images: paginatedImages as unknown as GalleryImage[],\r\n      totalCount,\r\n      totalPages,\r\n      currentPage: page,\r\n      hasNextPage: page < totalPages,\r\n      hasPrevPage: page > 1,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error fetching paginated gallery images:\", error);\r\n    return {\r\n      images: [],\r\n      totalCount: 0,\r\n      totalPages: 0,\r\n      currentPage: page,\r\n      hasNextPage: false,\r\n      hasPrevPage: false,\r\n      error: `An unexpected error occurred: ${\r\n        error instanceof Error ? error.message : String(error)\r\n      }`,\r\n    };\r\n  }\r\n}"], "names": [], "mappings": ";;;;;;;;AAEA;AAGA;;;;;;AAiBO,eAAe,yBAAyB,UAAkB;IAI/D,MAAM,WAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAEnC,IAAI;QACF,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,YACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,QAAQ,EAAE;gBACV,OAAO,CAAC,gCAAgC,EAAE,MAAM,OAAO,EAAE;YAC3D;QACF;QAEA,MAAM,UAAU,aAAa,WAAW,EAAE;QAC1C,MAAM,SAAS,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAEpD,yCAAyC;QACzC,OAAO,IAAI,CAAC,CAAC,GAAG;YACd,OACE,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO;QAE7H;QAEA,OAAO;YAAE,QAAQ;QAAoC;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;YACL,QAAQ,EAAE;YACV,OAAO,CAAC,8BAA8B,EACpC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAChD;QACJ;IACF;AACF;AAOO,eAAe,+BACpB,YAAoB;IAMpB,MAAM,WAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAEnC,IAAI;QACF,2CAA2C;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,UAAU,UACb,MAAM;QAET,IAAI,iBAAiB,CAAC,UAAU;YAC9B,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,QAAQ,EAAE;gBAAE,YAAY;gBAAG,OAAO;YAAqB;QAClE;QAEA,8DAA8D;QAC9D,MAAM,eAAe,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;QAEnC,6CAA6C;QAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,QAAQ,EAAE;gBACV,YAAY;gBACZ,OAAO,CAAC,gCAAgC,EAAE,aAAa,OAAO,EAAE;YAClE;QACF;QAEA,qBAAqB;QACrB,MAAM,UAAU,aAAa,WAAW,EAAE;QAC1C,MAAM,SAAS,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAEpD,wDAAwD;QACxD,OAAO,IAAI,CAAC,CAAC,GAAG;YACd,OACE,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO;QAE7H;QAEA,8DAA8D;QAC9D,gFAAgF;QAChF,MAAM,oBAAoB,OAAO,KAAK,CAAC,GAAG;QAC1C,MAAM,aAAa,KAAK,GAAG,CAAC,kBAAkB,MAAM,EAAE;QACtD,MAAM,gBAAgB,kBAAkB,KAAK,CAAC,GAAG;QAEjD,sEAAsE;QACtE,MAAM,aAAa,kBAAkB,MAAM;QAE3C,OAAO;YAAE,QAAQ;YAA4C;QAAW;IAC1E,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YACL,QAAQ,EAAE;YACV,YAAY;YACZ,OAAO,CAAC,8BAA8B,EACpC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAChD;QACJ;IACF;AACF;AAOO,eAAe,+BACpB,YAAoB;IAKpB,MAAM,WAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAEnC,IAAI;QACF,2CAA2C;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,UAAU,UACb,MAAM;QAET,IAAI,iBAAiB,CAAC,UAAU;YAC9B,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,QAAQ,EAAE;gBAAE,OAAO;YAAqB;QACnD;QAEA,8DAA8D;QAC9D,MAAM,eAAe,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;QAEnC,6CAA6C;QAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,QAAQ,EAAE;gBACV,OAAO,CAAC,gCAAgC,EAAE,aAAa,OAAO,EAAE;YAClE;QACF;QAEA,qBAAqB;QACrB,MAAM,UAAU,aAAa,WAAW,EAAE;QAC1C,MAAM,SAAS,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAEpD,wDAAwD;QACxD,OAAO,IAAI,CAAC,CAAC,GAAG;YACd,OACE,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO;QAE7H;QAEA,+CAA+C;QAC/C,uEAAuE;QACvE,MAAM,gBAAgB,OAAO,KAAK,CAAC,GAAG;QAEtC,OAAO;YAAE,QAAQ;QAA2C;IAC9D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qDAAqD;QACnE,OAAO;YACL,QAAQ,EAAE;YACV,OAAO,CAAC,8BAA8B,EACpC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAChD;QACJ;IACF;AACF;AASO,eAAe,kCACpB,YAAoB,EACpB,OAAe,CAAC,EAChB,QAAgB,EAAE;IAUlB,MAAM,WAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAEnC,IAAI;QACF,2CAA2C;QAC3C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,iBAAiB,cACpB,EAAE,CAAC,UAAU,UACb,MAAM;QAET,IAAI,iBAAiB,CAAC,UAAU;YAC9B,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,QAAQ,EAAE;gBACV,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,OAAO;YACT;QACF;QAEA,8DAA8D;QAC9D,MAAM,eAAe,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;QAEnC,6CAA6C;QAC7C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,qBACL,MAAM,CAAC,WACP,EAAE,CAAC,MAAM,SAAS,EAAE,EACpB,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,QAAQ,EAAE;gBACV,YAAY;gBACZ,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,OAAO,CAAC,gCAAgC,EAAE,aAAa,OAAO,EAAE;YAClE;QACF;QAEA,qBAAqB;QACrB,MAAM,UAAU,aAAa,WAAW,EAAE;QAC1C,MAAM,YAAY,MAAM,OAAO,CAAC,WAAW,UAAU,EAAE;QAEvD,wDAAwD;QACxD,UAAU,IAAI,CAAC,CAAC,GAAG;YACjB,OACE,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO,KAAK,IAAI,KAAK,AAAC,EAA6B,UAAU,EAAE,OAAO;QAE7H;QAEA,yBAAyB;QACzB,MAAM,oBAAoB,UAAU,KAAK,CAAC,GAAG;QAC7C,MAAM,aAAa,kBAAkB,MAAM;QAE3C,uBAAuB;QACvB,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;QAC1C,MAAM,aAAa,CAAC,OAAO,CAAC,IAAI;QAChC,MAAM,WAAW,aAAa;QAE9B,uBAAuB;QACvB,MAAM,kBAAkB,kBAAkB,KAAK,CAAC,YAAY;QAE5D,OAAO;YACL,QAAQ;YACR;YACA;YACA,aAAa;YACb,aAAa,OAAO;YACpB,aAAa,OAAO;QACtB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uDAAuD;QACrE,OAAO;YACL,QAAQ,EAAE;YACV,YAAY;YACZ,YAAY;YACZ,aAAa;YACb,aAAa;YACb,aAAa;YACb,OAAO,CAAC,8BAA8B,EACpC,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO,QAChD;QACJ;IACF;AACF;;;IAnTsB;IAgDA;IAgFA;IA0EA;;AA1MA,+OAAA;AAgDA,+OAAA;AAgFA,+OAAA;AA0EA,+OAAA", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/discovery.ts"], "sourcesContent": ["\"use server\";\n\nimport { BusinessProfilePublicData, BusinessSortBy } from \"./types\";\n\n/**\n * Securely fetch business profiles for discover page using the service role key\n */\nexport async function getSecureBusinessProfilesForDiscover(\n  pincodes: string | string[],\n  locality?: string | null,\n  page: number = 1,\n  limit: number = 10,\n  sortBy: BusinessSortBy = \"created_desc\"\n): Promise<{\n  data?: BusinessProfilePublicData[];\n  count?: number;\n  error?: string;\n}> {\n  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {\n    return { error: \"At least one pincode is required.\" };\n  }\n\n  // Convert single pincode to array for consistent handling\n  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];\n\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    queryParams.set('pincodes', pincodeArray.join(','));\n    queryParams.set('page', page.toString());\n    queryParams.set('limit', limit.toString());\n    queryParams.set('status', 'online');\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n        apiSortBy = 'likes_asc';\n        break;\n      case 'likes_desc':\n        apiSortBy = 'likes_desc';\n        break;\n      case 'subscriptions_asc':\n        apiSortBy = 'subscriptions_asc';\n        break;\n      case 'subscriptions_desc':\n        apiSortBy = 'subscriptions_desc';\n        break;\n      case 'rating_asc':\n        apiSortBy = 'rating_asc';\n        break;\n      case 'rating_desc':\n        apiSortBy = 'rating_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n\n    // Use the business profile API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profiles\",\n      };\n    }\n\n    const businesses = result.businesses || [];\n    const count = result.pagination?.total || 0;\n\n    // Transform data to match expected format\n    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {\n      return {\n        ...profile,\n        // Add missing fields with default values if not present\n        total_visits: profile.total_visits || 0,\n        today_visits: profile.today_visits || 0,\n        yesterday_visits: profile.yesterday_visits || 0,\n        visits_7_days: profile.visits_7_days || 0,\n        visits_30_days: profile.visits_30_days || 0,\n        city_slug: profile.city_slug || null,\n        state_slug: profile.state_slug || null,\n        locality_slug: profile.locality_slug || null,\n        gallery: profile.gallery || null,\n        latitude: profile.latitude || null,\n        longitude: profile.longitude || null,\n      } as BusinessProfilePublicData;\n    });\n\n    return { data: safeData, count };\n  } catch (e) {\n    console.error(\"Exception in getSecureBusinessProfilesForDiscover:\", e);\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n\n/**\n * Securely fetch business profile IDs for discover page products using the service role key\n */\nexport async function getSecureBusinessProfileIdsForDiscover(\n  pincodes: string | string[],\n  locality?: string | null,\n  sortBy: BusinessSortBy = \"created_desc\"\n): Promise<{\n  data?: string[];\n  error?: string;\n}> {\n  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {\n    return { error: \"At least one pincode is required.\" };\n  }\n\n  // Convert single pincode to array for consistent handling\n  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];\n\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    queryParams.set('pincodes', pincodeArray.join(','));\n    queryParams.set('status', 'online');\n    queryParams.set('ids_only', 'true');\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n        apiSortBy = 'likes_asc';\n        break;\n      case 'likes_desc':\n        apiSortBy = 'likes_desc';\n        break;\n      case 'subscriptions_asc':\n        apiSortBy = 'subscriptions_asc';\n        break;\n      case 'subscriptions_desc':\n        apiSortBy = 'subscriptions_desc';\n        break;\n      case 'rating_asc':\n        apiSortBy = 'rating_asc';\n        break;\n      case 'rating_desc':\n        apiSortBy = 'rating_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n\n    // Use the business profile API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profile IDs\",\n      };\n    }\n\n    const businessIds = result.business_ids || [];\n\n    return { data: businessIds };\n  } catch (e) {\n    console.error(\"Exception in getSecureBusinessProfileIdsForDiscover:\", e);\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAOO,eAAe,qCACpB,QAA2B,EAC3B,QAAwB,EACxB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAyB,cAAc;IAMvC,IAAI,CAAC,YAAa,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAI;QACnE,OAAO;YAAE,OAAO;QAAoC;IACtD;IAEA,0DAA0D;IAC1D,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW;QAAC;KAAS;IAEpE,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,YAAY,aAAa,IAAI,CAAC;QAC9C,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;QACrC,YAAY,GAAG,CAAC,SAAS,MAAM,QAAQ;QACvC,YAAY,GAAG,CAAC,UAAU;QAE1B,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC,YAAY;QAC9B;QAEA,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,aAAa,OAAO,UAAU,IAAI,EAAE;QAC1C,MAAM,QAAQ,OAAO,UAAU,EAAE,SAAS;QAE1C,0CAA0C;QAC1C,MAAM,WAAwC,WAAW,GAAG,CAAC,CAAC;YAC5D,OAAO;gBACL,GAAG,OAAO;gBACV,wDAAwD;gBACxD,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,eAAe,QAAQ,aAAa,IAAI;gBACxC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,WAAW,QAAQ,SAAS,IAAI;gBAChC,YAAY,QAAQ,UAAU,IAAI;gBAClC,eAAe,QAAQ,aAAa,IAAI;gBACxC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI;YAClC;QACF;QAEA,OAAO;YAAE,MAAM;YAAU;QAAM;IACjC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,sDAAsD;QACpE,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,uCACpB,QAA2B,EAC3B,QAAwB,EACxB,SAAyB,cAAc;IAKvC,IAAI,CAAC,YAAa,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAI;QACnE,OAAO;YAAE,OAAO;QAAoC;IACtD;IAEA,0DAA0D;IAC1D,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW;QAAC;KAAS;IAEpE,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,YAAY,aAAa,IAAI,CAAC;QAC9C,YAAY,GAAG,CAAC,UAAU;QAC1B,YAAY,GAAG,CAAC,YAAY;QAE5B,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC,YAAY;QAC9B;QAEA,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,cAAc,OAAO,YAAY,IAAI,EAAE;QAE7C,OAAO;YAAE,MAAM;QAAY;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,wDAAwD;QACtE,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAzMsB;IAmHA;;AAnHA,+OAAA;AAmHA,+OAAA", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/search.ts"], "sourcesContent": ["\"use server\";\n\nimport { BusinessProfilePublicData, BusinessSortBy } from \"./types\";\n\n/**\n * Securely fetch all business profiles or search by name and/or location\n */\nexport async function getSecureBusinessProfiles(\n  searchTerm?: string | null,\n  pincode?: string | null,\n  locality?: string | null,\n  page: number = 1,\n  limit: number = 20,\n  sortBy: BusinessSortBy = \"created_desc\",\n  category?: string | null\n): Promise<{\n  data?: BusinessProfilePublicData[];\n  count?: number;\n  error?: string;\n}> {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    queryParams.set('page', page.toString());\n    queryParams.set('limit', limit.toString());\n    queryParams.set('status', 'online');\n\n    if (searchTerm) {\n      queryParams.set('search', searchTerm.trim());\n    }\n    if (pincode) {\n      queryParams.set('pincode', pincode);\n    }\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n    if (category && category.trim()) {\n      queryParams.set('category', category.trim());\n    }\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n        apiSortBy = 'likes_asc';\n        break;\n      case 'likes_desc':\n        apiSortBy = 'likes_desc';\n        break;\n      case 'subscriptions_asc':\n        apiSortBy = 'subscriptions_asc';\n        break;\n      case 'subscriptions_desc':\n        apiSortBy = 'subscriptions_desc';\n        break;\n      case 'rating_asc':\n        apiSortBy = 'rating_asc';\n        break;\n      case 'rating_desc':\n        apiSortBy = 'rating_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    // Use the business profile API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profiles\",\n      };\n    }\n\n    const businesses = result.businesses || [];\n    const count = result.pagination?.total || 0;\n\n    // Transform data to match expected format\n    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {\n      return {\n        ...profile,\n        // Add missing fields with default values if not present\n        total_visits: profile.total_visits || 0,\n        today_visits: profile.today_visits || 0,\n        yesterday_visits: profile.yesterday_visits || 0,\n        visits_7_days: profile.visits_7_days || 0,\n        visits_30_days: profile.visits_30_days || 0,\n        city_slug: profile.city_slug || null,\n        state_slug: profile.state_slug || null,\n        locality_slug: profile.locality_slug || null,\n        gallery: profile.gallery || null,\n        latitude: profile.latitude || null,\n        longitude: profile.longitude || null,\n      } as BusinessProfilePublicData;\n    });\n\n    return { data: safeData, count };\n  } catch (e) {\n    console.error(\"Exception in getSecureBusinessProfiles:\", e);\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAOO,eAAe,0BACpB,UAA0B,EAC1B,OAAuB,EACvB,QAAwB,EACxB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAyB,cAAc,EACvC,QAAwB;IAMxB,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;QACrC,YAAY,GAAG,CAAC,SAAS,MAAM,QAAQ;QACvC,YAAY,GAAG,CAAC,UAAU;QAE1B,IAAI,YAAY;YACd,YAAY,GAAG,CAAC,UAAU,WAAW,IAAI;QAC3C;QACA,IAAI,SAAS;YACX,YAAY,GAAG,CAAC,WAAW;QAC7B;QACA,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC,YAAY;QAC9B;QACA,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,YAAY,GAAG,CAAC,YAAY,SAAS,IAAI;QAC3C;QAEA,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,aAAa,OAAO,UAAU,IAAI,EAAE;QAC1C,MAAM,QAAQ,OAAO,UAAU,EAAE,SAAS;QAE1C,0CAA0C;QAC1C,MAAM,WAAwC,WAAW,GAAG,CAAC,CAAC;YAC5D,OAAO;gBACL,GAAG,OAAO;gBACV,wDAAwD;gBACxD,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,eAAe,QAAQ,aAAa,IAAI;gBACxC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,WAAW,QAAQ,SAAS,IAAI;gBAChC,YAAY,QAAQ,UAAU,IAAI;gBAClC,eAAe,QAAQ,aAAa,IAAI;gBACxC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI;YAClC;QACF;QAEA,OAAO;YAAE,MAAM;YAAU;QAAM;IACjC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAjHsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1052, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/sitemap.ts"], "sourcesContent": ["\"use server\";\n\nimport { SitemapProfileData } from \"./types\";\n\n/**\n * Securely fetch business profiles for sitemap using the API endpoint\n */\nexport async function getSecureBusinessProfilesForSitemap(): Promise<{\n  data?: SitemapProfileData[];\n  error?: string;\n}> {\n  try {\n    // Use the business sitemap API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/sitemap`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profiles for sitemap\",\n      };\n    }\n\n    const profiles = result.profiles || [];\n\n    // If there are no profiles, return empty array\n    if (!profiles || profiles.length === 0) {\n      return { data: [] };\n    }\n\n    // Create a map to deduplicate by business_slug\n    const uniqueProfiles = new Map<\n      string,\n      { business_slug: string; updated_at: string }\n    >();\n\n    // Add all profiles to the map (this automatically deduplicates by business_slug)\n    profiles.forEach((profile: { business_slug: string; updated_at: string }) => {\n      if (profile.business_slug) {\n        uniqueProfiles.set(profile.business_slug, {\n          business_slug: profile.business_slug,\n          updated_at: profile.updated_at,\n        });\n      }\n    });\n\n    // Convert map values to array\n    const combinedProfiles = Array.from(uniqueProfiles.values());\n\n    // Return the deduplicated profiles\n    return { data: combinedProfiles };\n  } catch (_e) {\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAOO,eAAe;IAIpB,IAAI;QACF,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,qBAAqB,CAAC,EAAE;YACvF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,WAAW,OAAO,QAAQ,IAAI,EAAE;QAEtC,+CAA+C;QAC/C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;QAEA,+CAA+C;QAC/C,MAAM,iBAAiB,IAAI;QAK3B,iFAAiF;QACjF,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,QAAQ,aAAa,EAAE;gBACzB,eAAe,GAAG,CAAC,QAAQ,aAAa,EAAE;oBACxC,eAAe,QAAQ,aAAa;oBACpC,YAAY,QAAQ,UAAU;gBAChC;YACF;QACF;QAEA,8BAA8B;QAC9B,MAAM,mBAAmB,MAAM,IAAI,CAAC,eAAe,MAAM;QAEzD,mCAAmC;QACnC,OAAO;YAAE,MAAM;QAAiB;IAClC,EAAE,OAAO,IAAI;QACX,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IArDsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/access.ts"], "sourcesContent": ["\"use server\";\r\n\r\n/**\r\n * Check if the current user has access to a business profile\r\n * This is used to verify ownership before allowing certain operations\r\n */\r\nexport async function checkBusinessProfileAccess(\r\n  businessProfileId: string\r\n): Promise<{\r\n  hasAccess: boolean;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the business access API endpoint\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/access`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        // Note: In a real implementation, this would need proper authentication headers\r\n        // For now, this is a placeholder - the actual implementation would need\r\n        // to pass through the user's JWT token and HMAC signature\r\n      },\r\n      body: JSON.stringify({\r\n        business_profile_id: businessProfileId,\r\n      }),\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        hasAccess: false,\r\n        error: result.error || \"Failed to check business profile access\",\r\n      };\r\n    }\r\n\r\n    return { hasAccess: result.has_access };\r\n  } catch (e) {\r\n    console.error(\"Exception in checkBusinessProfileAccess:\", e);\r\n    return { hasAccess: false, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get the current user's business profile ID\r\n * This is useful for operations that need to know the user's business profile\r\n */\r\nexport async function getCurrentUserBusinessProfileId(): Promise<{\r\n  profileId?: string;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the business access API endpoint\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/access/me`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        // Note: In a real implementation, this would need proper authentication headers\r\n        // For now, this is a placeholder - the actual implementation would need\r\n        // to pass through the user's JWT token and HMAC signature\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to get current user business profile ID\",\r\n      };\r\n    }\r\n\r\n    return { profileId: result.profile_id };\r\n  } catch (e) {\r\n    console.error(\"Exception in getCurrentUserBusinessProfileId:\", e);\r\n    return { error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAMO,eAAe,2BACpB,iBAAyB;IAKzB,IAAI;QACF,uCAAuC;QACvC,MAAM,WAAW,MAAM,MAAM,6DAAoC,oBAAoB,CAAC,EAAE;YACtF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAIlB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,qBAAqB;YACvB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,WAAW;gBACX,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,OAAO;YAAE,WAAW,OAAO,UAAU;QAAC;IACxC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,WAAW;YAAO,OAAO;QAA+B;IACnE;AACF;AAMO,eAAe;IAIpB,IAAI;QACF,uCAAuC;QACvC,MAAM,WAAW,MAAM,MAAM,6DAAoC,uBAAuB,CAAC,EAAE;YACzF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAIlB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,OAAO;YAAE,WAAW,OAAO,UAAU;QAAC;IACxC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IAxEsB;IA0CA;;AA1CA,+OAAA;AA0CA,+OAAA", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/location.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { BusinessProfilePublicData, BusinessSortBy } from \"./types\";\r\n\r\n/**\r\n * Securely fetch business profiles by location using the service role key\r\n */\r\nexport async function getSecureBusinessProfilesByLocation(\r\n  location: {\r\n    pincode?: string;\r\n    city?: string;\r\n    state?: string;\r\n    locality?: string;\r\n  },\r\n  page: number = 1,\r\n  limit: number = 20,\r\n  sortBy: BusinessSortBy = \"created_desc\"\r\n): Promise<{\r\n  data?: BusinessProfilePublicData[];\r\n  count?: number;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Build query parameters\r\n    const queryParams = new URLSearchParams();\r\n    queryParams.set('page', page.toString());\r\n    queryParams.set('limit', limit.toString());\r\n    queryParams.set('status', 'online');\r\n\r\n    // Add location filters if provided\r\n    if (location.pincode) {\r\n      queryParams.set('pincode', location.pincode);\r\n    }\r\n    if (location.city) {\r\n      queryParams.set('city', location.city);\r\n    }\r\n    if (location.state) {\r\n      queryParams.set('state', location.state);\r\n    }\r\n    if (location.locality) {\r\n      queryParams.set('locality', location.locality);\r\n    }\r\n\r\n    // Convert sortBy to API format\r\n    let apiSortBy = 'created_desc';\r\n    switch (sortBy) {\r\n      case 'name_asc':\r\n        apiSortBy = 'name_asc';\r\n        break;\r\n      case 'name_desc':\r\n        apiSortBy = 'name_desc';\r\n        break;\r\n      case 'created_asc':\r\n        apiSortBy = 'created_asc';\r\n        break;\r\n      case 'created_desc':\r\n        apiSortBy = 'created_desc';\r\n        break;\r\n      case 'likes_asc':\r\n        apiSortBy = 'likes_asc';\r\n        break;\r\n      case 'likes_desc':\r\n        apiSortBy = 'likes_desc';\r\n        break;\r\n      case 'subscriptions_asc':\r\n        apiSortBy = 'subscriptions_asc';\r\n        break;\r\n      case 'subscriptions_desc':\r\n        apiSortBy = 'subscriptions_desc';\r\n        break;\r\n      case 'rating_asc':\r\n        apiSortBy = 'rating_asc';\r\n        break;\r\n      case 'rating_desc':\r\n        apiSortBy = 'rating_desc';\r\n        break;\r\n    }\r\n    queryParams.set('sort_by', apiSortBy);\r\n\r\n    // Use the business profile API endpoint\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to fetch business profiles by location\",\r\n      };\r\n    }\r\n\r\n    const businesses = result.businesses || [];\r\n    const count = result.pagination?.total || 0;\r\n\r\n    // Transform data to match expected format\r\n    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {\r\n      return {\r\n        ...profile,\r\n        // Add missing fields with default values if not present\r\n        total_visits: profile.total_visits || 0,\r\n        today_visits: profile.today_visits || 0,\r\n        yesterday_visits: profile.yesterday_visits || 0,\r\n        visits_7_days: profile.visits_7_days || 0,\r\n        visits_30_days: profile.visits_30_days || 0,\r\n        city_slug: profile.city_slug || null,\r\n        state_slug: profile.state_slug || null,\r\n        locality_slug: profile.locality_slug || null,\r\n        gallery: profile.gallery || null,\r\n        latitude: profile.latitude || null,\r\n        longitude: profile.longitude || null,\r\n      } as BusinessProfilePublicData;\r\n    });\r\n\r\n    return { data: safeData, count };\r\n  } catch (error) {\r\n    console.error(\r\n      \"Unexpected error in getSecureBusinessProfilesByLocation:\",\r\n      error\r\n    );\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAOO,eAAe,oCACpB,QAKC,EACD,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAyB,cAAc;IAMvC,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;QACrC,YAAY,GAAG,CAAC,SAAS,MAAM,QAAQ;QACvC,YAAY,GAAG,CAAC,UAAU;QAE1B,mCAAmC;QACnC,IAAI,SAAS,OAAO,EAAE;YACpB,YAAY,GAAG,CAAC,WAAW,SAAS,OAAO;QAC7C;QACA,IAAI,SAAS,IAAI,EAAE;YACjB,YAAY,GAAG,CAAC,QAAQ,SAAS,IAAI;QACvC;QACA,IAAI,SAAS,KAAK,EAAE;YAClB,YAAY,GAAG,CAAC,SAAS,SAAS,KAAK;QACzC;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,YAAY,GAAG,CAAC,YAAY,SAAS,QAAQ;QAC/C;QAEA,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,aAAa,OAAO,UAAU,IAAI,EAAE;QAC1C,MAAM,QAAQ,OAAO,UAAU,EAAE,SAAS;QAE1C,0CAA0C;QAC1C,MAAM,WAAwC,WAAW,GAAG,CAAC,CAAC;YAC5D,OAAO;gBACL,GAAG,OAAO;gBACV,wDAAwD;gBACxD,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,eAAe,QAAQ,aAAa,IAAI;gBACxC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,WAAW,QAAQ,SAAS,IAAI;gBAChC,YAAY,QAAQ,UAAU,IAAI;gBAClC,eAAe,QAAQ,aAAa,IAAI;gBACxC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI;YAClC;QACF;QAEA,OAAO;YAAE,MAAM;YAAU;QAAM;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,4DACA;QAEF,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAvHsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1315, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/interactions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { revalidatePath } from \"next/cache\";\r\n// getSecureBusinessProfileBySlug is imported but not used in this file\r\n// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';\r\n// import { cookies } from 'next/headers'; // Removed unused import\r\n\r\nexport async function subscribeToBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies(); // No longer needed here\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from subscribing to their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot subscribe to your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Insert subscription - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already subscribed) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already subscribed to business ${businessProfileId}.`\r\n        );\r\n        // Optionally return success true if already subscribed is acceptable\r\n        return { success: true };\r\n      }\r\n      console.error(\"Error inserting subscription:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Revalidate the specific card page and potentially the user's dashboard\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in subscribeToBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\n// --- Implementation for other actions ---\r\n\r\nexport async function unsubscribeFromBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unsubscribing from their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unsubscribe from your own business card.\",\r\n    };\r\n  }\r\n\r\n  // Check if the current user is a business (has a business profile)\r\n  const { data: userBusinessProfile } = await supabase\r\n    .from(\"business_profiles\")\r\n    .select(\"id\")\r\n    .eq(\"id\", user.id)\r\n    .maybeSingle();\r\n\r\n  try {\r\n    // 1. Delete subscription - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"subscriptions\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting subscription:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the subscription count\r\n    // The database trigger 'update_total_subscriptions' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/subscriptions\"); // Revalidate business subscriptions page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unsubscribeFromBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function submitReview(\r\n  businessProfileId: string,\r\n  rating: number,\r\n  reviewText?: string | null // Allow null for review text\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from reviewing their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot review your own business card.\" };\r\n  }\r\n\r\n  if (rating < 1 || rating > 5) {\r\n    return { success: false, error: \"Rating must be between 1 and 5.\" };\r\n  }\r\n\r\n  try {\r\n    // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS\r\n    const { error: upsertError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .upsert(\r\n        {\r\n          user_id: user.id,\r\n          business_profile_id: businessProfileId,\r\n          rating: rating,\r\n          review_text: reviewText, // Pass reviewText directly\r\n          updated_at: new Date().toISOString(), // Explicitly set updated_at on upsert\r\n        },\r\n        {\r\n          onConflict: \"user_id, business_profile_id\", // Specify conflict target\r\n        }\r\n      );\r\n\r\n    if (upsertError) {\r\n      console.error(\"Error submitting review:\", upsertError);\r\n      throw new Error(upsertError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\"); // Revalidate customer dashboard where reviews might be shown\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in submitReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function deleteReview(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"ratings_reviews\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting review:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Average rating is handled by the database trigger\r\n\r\n    // Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n    revalidatePath(\"/dashboard/customer\");\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in deleteReview:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function likeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from liking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return { success: false, error: \"You cannot like your own business card.\" };\r\n  }\r\n\r\n  try {\r\n    // 1. Insert like - Use regular client with proper RLS\r\n    const { error: insertError } = await supabase\r\n      .from(\"likes\")\r\n      .insert({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (insertError) {\r\n      // Handle potential unique constraint violation (already liked) gracefully\r\n      if (insertError.code === \"23505\") {\r\n        // unique_violation\r\n        console.log(\r\n          `User ${user.id} already liked business ${businessProfileId}.`\r\n        );\r\n        return { success: true }; // Consider it success if already liked\r\n      }\r\n      console.error(\"Error inserting like:\", insertError);\r\n      throw new Error(insertError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in likeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function unlikeBusiness(\r\n  businessProfileId: string\r\n): Promise<{ success: boolean; error?: string }> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n\r\n  const {\r\n    data: { user },\r\n    error: authError,\r\n  } = await supabase.auth.getUser();\r\n\r\n  if (authError || !user) {\r\n    return { success: false, error: \"User not authenticated.\" };\r\n  }\r\n\r\n  // Prevent a business from unliking their own business card\r\n  if (user.id === businessProfileId) {\r\n    return {\r\n      success: false,\r\n      error: \"You cannot unlike your own business card.\",\r\n    };\r\n  }\r\n\r\n  try {\r\n    // 1. Delete like - Use regular client with proper RLS\r\n    const { error: deleteError } = await supabase\r\n      .from(\"likes\")\r\n      .delete()\r\n      .match({ user_id: user.id, business_profile_id: businessProfileId });\r\n\r\n    if (deleteError) {\r\n      console.error(\"Error deleting like:\", deleteError);\r\n      throw new Error(deleteError.message);\r\n    }\r\n\r\n    // Note: We don't need to manually update the like count\r\n    // The database trigger 'update_total_likes' will handle this automatically\r\n\r\n    // 3. Revalidate paths\r\n    // Use regular client - business_profiles has public read access\r\n    const { data: cardData } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"business_slug\")\r\n      .eq(\"id\", businessProfileId)\r\n      .single();\r\n\r\n    if (cardData?.business_slug) {\r\n      revalidatePath(`/${cardData.business_slug}`);\r\n    }\r\n\r\n    // Check if the current user is a business and revalidate business dashboard\r\n    const { data: userBusinessProfile } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", user.id)\r\n      .maybeSingle();\r\n\r\n    if (userBusinessProfile) {\r\n      revalidatePath(\"/dashboard/business\"); // Revalidate business dashboard\r\n      revalidatePath(\"/dashboard/business/likes\"); // Revalidate business likes page\r\n    }\r\n\r\n    // Revalidate the activities page for the business\r\n    revalidatePath(`/dashboard/business/activities`);\r\n\r\n    return { success: true };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in unlikeBusiness:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { success: false, error: errorMessage };\r\n  }\r\n}\r\n\r\nexport async function getInteractionStatus(businessProfileId: string): Promise<{\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  userRating: number | null;\r\n  userReview: string | null;\r\n  error?: string;\r\n}> {\r\n  // const cookieStore = cookies();\r\n  const supabase = await createClient(); // Await the async function\r\n  let userId: string | null = null;\r\n\r\n  // Try to get authenticated user, but proceed even if not logged in\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (user) {\r\n    userId = user.id;\r\n  }\r\n\r\n  // Default status for anonymous users\r\n  const defaultStatus = {\r\n    isSubscribed: false,\r\n    hasLiked: false,\r\n    userRating: null,\r\n    userReview: null,\r\n  };\r\n\r\n  if (!userId) {\r\n    return defaultStatus; // Return default if no user is logged in\r\n  }\r\n\r\n  try {\r\n    // Use regular client - all these tables have public read access\r\n    // Fetch all statuses in parallel\r\n    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([\r\n      supabase\r\n        .from(\"subscriptions\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"likes\")\r\n        .select(\"id\", { count: \"exact\", head: true }) // Just check existence\r\n        .match({ user_id: userId, business_profile_id: businessProfileId }),\r\n      supabase\r\n        .from(\"ratings_reviews\")\r\n        .select(\"rating, review_text\")\r\n        .match({ user_id: userId, business_profile_id: businessProfileId })\r\n        .maybeSingle(), // Use maybeSingle as user might not have reviewed\r\n    ]);\r\n\r\n    // Check for errors in parallel fetches\r\n    if (subscriptionRes.error)\r\n      throw new Error(\r\n        `Subscription fetch error: ${subscriptionRes.error.message}`\r\n      );\r\n    if (likeRes.error)\r\n      throw new Error(`Like fetch error: ${likeRes.error.message}`);\r\n    if (reviewRes.error)\r\n      throw new Error(`Review fetch error: ${reviewRes.error.message}`);\r\n\r\n    const reviewData = reviewRes.data;\r\n\r\n    return {\r\n      isSubscribed: (subscriptionRes.count ?? 0) > 0,\r\n      hasLiked: (likeRes.count ?? 0) > 0,\r\n      userRating: reviewData?.rating ?? null,\r\n      userReview: reviewData?.review_text ?? null,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching interaction status:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    // Return default status but include the error message\r\n    return { ...defaultStatus, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;;;;;;AAKO,eAAe,oBACpB,iBAAyB;IAEzB,0DAA0D;IAC1D,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,iEAAiE;IACjE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,8DAA8D;QAC9D,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,+EAA+E;YAC/E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,CAAC;gBAExE,qEAAqE;gBACrE,OAAO;oBAAE,SAAS;gBAAK;YACzB;YACA,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,yEAAyE;QACzE,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;QAEvE,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAIO,eAAe,wBACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,qEAAqE;IACrE,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,mEAAmE;IACnE,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;IAEd,IAAI;QACF,8DAA8D;QAC9D,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,iBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,gEAAgE;QAChE,mFAAmF;QAEnF,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,4EAA4E;QAC5E,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,sCAAsC,yCAAyC;QAChG;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB,EACzB,MAAc,EACd,UAA0B,AAAC,6BAA6B;;IAExD,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,4DAA4D;IAC5D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA4C;IAC9E;IAEA,IAAI,SAAS,KAAK,SAAS,GAAG;QAC5B,OAAO;YAAE,SAAS;YAAO,OAAO;QAAkC;IACpE;IAEA,IAAI;QACF,iGAAiG;QACjG,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,mBACL,MAAM,CACL;YACE,SAAS,KAAK,EAAE;YAChB,qBAAqB;YACrB,QAAQ;YACR,aAAa;YACb,YAAY,IAAI,OAAO,WAAW;QACpC,GACA;YACE,YAAY;QACd;QAGJ,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,6DAA6D;QAEpG,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,IAAI;QACF,qCAAqC;QACrC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,mBACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,oDAAoD;QAEpD,mBAAmB;QACnB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QACA,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE;QAEf,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,aACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,yDAAyD;IACzD,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0C;IAC5E;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAErE,IAAI,aAAa;YACf,0EAA0E;YAC1E,IAAI,YAAY,IAAI,KAAK,SAAS;gBAChC,mBAAmB;gBACnB,QAAQ,GAAG,CACT,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,CAAC,CAAC;gBAEhE,OAAO;oBAAE,SAAS;gBAAK,GAAG,uCAAuC;YACnE;YACA,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,eACpB,iBAAyB;IAEzB,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAElE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACd,OAAO,SAAS,EACjB,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAE/B,IAAI,aAAa,CAAC,MAAM;QACtB,OAAO;YAAE,SAAS;YAAO,OAAO;QAA0B;IAC5D;IAEA,2DAA2D;IAC3D,IAAI,KAAK,EAAE,KAAK,mBAAmB;QACjC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,SACL,MAAM,GACN,KAAK,CAAC;YAAE,SAAS,KAAK,EAAE;YAAE,qBAAqB;QAAkB;QAEpE,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM,YAAY,OAAO;QACrC;QAEA,wDAAwD;QACxD,2EAA2E;QAE3E,sBAAsB;QACtB,gEAAgE;QAChE,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,qBACL,MAAM,CAAC,iBACP,EAAE,CAAC,MAAM,mBACT,MAAM;QAET,IAAI,UAAU,eAAe;YAC3B,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;QAC7C;QAEA,4EAA4E;QAC5E,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,MAAM,SACzC,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,WAAW;QAEd,IAAI,qBAAqB;YACvB,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,wBAAwB,gCAAgC;YACvE,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,8BAA8B,iCAAiC;QAChF;QAEA,kDAAkD;QAClD,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD,EAAE,CAAC,8BAA8B,CAAC;QAE/C,OAAO;YAAE,SAAS;QAAK;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAa;IAC/C;AACF;AAEO,eAAe,qBAAqB,iBAAyB;IAOlE,iCAAiC;IACjC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD,KAAK,2BAA2B;IAClE,IAAI,SAAwB;IAE5B,mEAAmE;IACnE,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,MAAM;QACR,SAAS,KAAK,EAAE;IAClB;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,cAAc;QACd,UAAU;QACV,YAAY;QACZ,YAAY;IACd;IAEA,IAAI,CAAC,QAAQ;QACX,OAAO,eAAe,yCAAyC;IACjE;IAEA,IAAI;QACF,gEAAgE;QAChE,iCAAiC;QACjC,MAAM,CAAC,iBAAiB,SAAS,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9D,SACG,IAAI,CAAC,iBACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,SACG,IAAI,CAAC,SACL,MAAM,CAAC,MAAM;gBAAE,OAAO;gBAAS,MAAM;YAAK,GAAG,uBAAuB;aACpE,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB;YACnE,SACG,IAAI,CAAC,mBACL,MAAM,CAAC,uBACP,KAAK,CAAC;gBAAE,SAAS;gBAAQ,qBAAqB;YAAkB,GAChE,WAAW;SACf;QAED,uCAAuC;QACvC,IAAI,gBAAgB,KAAK,EACvB,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,gBAAgB,KAAK,CAAC,OAAO,EAAE;QAEhE,IAAI,QAAQ,KAAK,EACf,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE;QAC9D,IAAI,UAAU,KAAK,EACjB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,UAAU,KAAK,CAAC,OAAO,EAAE;QAElE,MAAM,aAAa,UAAU,IAAI;QAEjC,OAAO;YACL,cAAc,CAAC,gBAAgB,KAAK,IAAI,CAAC,IAAI;YAC7C,UAAU,CAAC,QAAQ,KAAK,IAAI,CAAC,IAAI;YACjC,YAAY,YAAY,UAAU;YAClC,YAAY,YAAY,eAAe;QACzC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,sDAAsD;QACtD,OAAO;YAAE,GAAG,aAAa;YAAE,OAAO;QAAa;IACjD;AACF;;;IAvgBsB;IAuFA;IA4EA;IA2EA;IAsDA;IA8EA;IA0EA;;AA5bA,+OAAA;AAuFA,+OAAA;AA4EA,+OAAA;AA2EA,+OAAA;AAsDA,+OAAA;AA8EA,+OAAA;AA0EA,+OAAA", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/supabase/constants.ts"], "sourcesContent": ["// lib/supabase/constants.ts\n\nexport const TABLES = {\n  BLOGS: \"blogs\",\n  BUSINESS_POSTS: \"business_posts\",\n  BUSINESS_PROFILES: \"business_profiles\",\n  CUSTOMER_POSTS: \"customer_posts\",\n  CUSTOMER_PROFILES: \"customer_profiles\",\n  CUSTOMER_PROFILES_PUBLIC: \"customer_profiles_public\",\n  LIKES: \"likes\",\n  PINCODES: \"pincodes\",\n  PRODUCTS_SERVICES: \"products_services\",\n  PRODUCT_VARIANTS: \"product_variants\",\n  SUBSCRIPTIONS: \"subscriptions\",\n  STORAGE_CLEANUP_CONFIG: \"storage_cleanup_config\",\n  STORAGE_CLEANUP_PROGRESS: \"storage_cleanup_progress\",\n  RATINGS_REVIEWS: \"ratings_reviews\",\n  POST_LIKES: \"post_likes\",\n  POST_COMMENTS: \"post_comments\",\n  COMMENT_LIKES: \"comment_likes\",\n} as const;\n\nexport const BUCKETS = {\n  BUSINESS: \"business\",\n  CUSTOMERS: \"customers\",\n} as const;\n\nexport const COLUMNS = {\n  ID: \"id\",\n  CREATED_AT: \"created_at\",\n  UPDATED_AT: \"updated_at\",\n  NAME: \"name\",\n  EMAIL: \"email\",\n  PHONE: \"phone\",\n  CITY: \"city\",\n  STATE: \"state\",\n  PINCODE: \"pincode\",\n  PLAN_ID: \"plan_id\",\n  LOCALITY: \"locality\",\n  CITY_SLUG: \"city_slug\",\n  STATE_SLUG: \"state_slug\",\n  LOCALITY_SLUG: \"locality_slug\",\n  LOGO_URL: \"logo_url\",\n  IMAGE_URL: \"image_url\",\n  IMAGES: \"images\",\n  SLUG: \"slug\",\n  STATUS: \"status\",\n  CONTENT: \"content\",\n  GALLERY: \"gallery\",\n  DESCRIPTION: \"description\",\n  TITLE: \"title\",\n  USER_ID: \"user_id\",\n  BUSINESS_ID: \"business_id\",\n  CUSTOMER_ID: \"customer_id\",\n  BUSINESS_NAME: \"business_name\",\n  BUSINESS_SLUG: \"business_slug\",\n  PRODUCT_ID: \"product_id\",\n  PRODUCT_TYPE: \"product_type\",\n  BUSINESS_PROFILE_ID: \"business_profile_id\",\n  RATING: \"rating\",\n  REVIEW_TEXT: \"review_text\",\n  AVATAR_URL: \"avatar_url\",\n  ADDRESS_LINE: \"address_line\",\n  POST_ID: \"post_id\",\n  POST_SOURCE: \"post_source\",\n  COMMENT_ID: \"comment_id\",\n  PARENT_COMMENT_ID: \"parent_comment_id\",\n  IS_PINNED: \"is_pinned\",\n  IS_EDITED: \"is_edited\",\n  EDITED_AT: \"edited_at\",\n} as const;\n\n// Note: RPC functions for likes and comments have been removed in favor of direct queries\n// This provides better performance and more control over the logic\n\nexport const POST_SOURCES = {\n  BUSINESS: \"business\",\n  CUSTOMER: \"customer\",\n} as const;\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;AAErB,MAAM,SAAS;IACpB,OAAO;IACP,gBAAgB;IAChB,mBAAmB;IACnB,gBAAgB;IAChB,mBAAmB;IACnB,0BAA0B;IAC1B,OAAO;IACP,UAAU;IACV,mBAAmB;IACnB,kBAAkB;IAClB,eAAe;IACf,wBAAwB;IACxB,0BAA0B;IAC1B,iBAAiB;IACjB,YAAY;IACZ,eAAe;IACf,eAAe;AACjB;AAEO,MAAM,UAAU;IACrB,UAAU;IACV,WAAW;AACb;AAEO,MAAM,UAAU;IACrB,IAAI;IACJ,YAAY;IACZ,YAAY;IACZ,MAAM;IACN,OAAO;IACP,OAAO;IACP,MAAM;IACN,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,UAAU;IACV,WAAW;IACX,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,SAAS;IACT,aAAa;IACb,aAAa;IACb,eAAe;IACf,eAAe;IACf,YAAY;IACZ,cAAc;IACd,qBAAqB;IACrB,QAAQ;IACR,aAAa;IACb,YAAY;IACZ,cAAc;IACd,SAAS;IACT,aAAa;IACb,YAAY;IACZ,mBAAmB;IACnB,WAAW;IACX,WAAW;IACX,WAAW;AACb;AAKO,MAAM,eAAe;IAC1B,UAAU;IACV,UAAU;AACZ", "debugId": null}}, {"offset": {"line": 1850, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\n\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\"; // Reuse type\n// Removed unused import - noStore\nimport { COLUMNS, TABLES } from \"@/lib/supabase/constants\";\n\n\n// Define sort options available on the public page\nexport type PublicProductSortBy =\n  | \"created_asc\"\n  | \"created_desc\"\n  | \"updated_asc\"\n  | \"updated_desc\"\n  | \"price_asc\"\n  | \"price_desc\"\n  | \"name_asc\"\n  | \"name_desc\";\n\n// Fetch subsequent pages of products for a specific business with search and filter\nexport async function fetchMoreProducts(\n  businessId: string,\n  page: number = 1,\n  sortBy: PublicProductSortBy = \"created_desc\",\n  pageSize: number = 20,\n  searchTerm?: string | null,\n  productType?: string | null\n): Promise<{\n  data?: ProductServiceData[];\n  error?: string;\n  totalCount?: number;\n}> {\n  if (!businessId) {\n    return { error: \"Business ID is required.\" };\n  }\n  if (page < 1) {\n    return { error: \"Page number must be 1 or greater.\" };\n  }\n  if (pageSize < 1) {\n    return { error: \"Page size must be 1 or greater.\" };\n  }\n\n  const supabase = await createClient();\n  const offset = (page - 1) * pageSize;\n\n  // Build count query first to get total count\n  let countQuery = supabase\n    .from(TABLES.PRODUCTS_SERVICES)\n    .select(COLUMNS.ID, { count: \"exact\" })\n    .eq(COLUMNS.BUSINESS_ID, businessId)\n    .eq(\"is_available\", true);\n\n  // Apply search filter if provided\n  if (searchTerm && searchTerm.trim().length > 0) {\n    countQuery = countQuery.ilike(COLUMNS.NAME, `%${searchTerm.trim()}%`);\n  }\n\n  // Apply product type filter if provided\n  if (productType && productType !== \"all\") {\n    countQuery = countQuery.eq(COLUMNS.PRODUCT_TYPE, productType);\n  }\n\n  // Get total count\n  const { count, error: countError } = await countQuery;\n\n  if (countError) {\n    return { error: \"Failed to count products\" };\n  }\n\n  // Build the main query for fetching products\n  let query = supabase\n    .from(TABLES.PRODUCTS_SERVICES)\n    .select(`\n            id,\n            business_id,\n            name,\n            description,\n            base_price,\n            discounted_price,\n            product_type,\n            is_available,\n            image_url,\n            created_at,\n            updated_at,\n            slug\n        `)\n    .eq(COLUMNS.BUSINESS_ID, businessId)\n    .eq(\"is_available\", true);\n\n  // Apply search filter if provided\n  if (searchTerm && searchTerm.trim().length > 0) {\n    query = query.ilike(\"name\", `%${searchTerm.trim()}%`);\n  }\n\n  // Apply product type filter if provided\n  if (productType && productType !== \"all\") {\n    query = query.eq(\"product_type\", productType);\n  }\n\n  // Apply Sorting\n  switch (sortBy) {\n    case \"created_asc\":\n      query = query.order(\"created_at\", { ascending: true });\n      break;\n    case \"updated_desc\":\n      query = query.order(\"updated_at\", { ascending: false });\n      break;\n    case \"price_asc\":\n      // Sort by discounted_price first, then base_price for price_asc\n      query = query\n        .order(\"discounted_price\", { ascending: true, nullsFirst: false })\n        .order(\"base_price\", { ascending: true, nullsFirst: false });\n      break;\n    case \"price_desc\":\n      // Sort by discounted_price first, then base_price for price_desc\n      query = query\n        .order(\"discounted_price\", { ascending: false, nullsFirst: false })\n        .order(\"base_price\", { ascending: false, nullsFirst: false });\n      break;\n    case \"name_asc\":\n      query = query.order(\"name\", { ascending: true });\n      break;\n    case \"name_desc\":\n      query = query.order(\"name\", { ascending: false });\n      break;\n    case \"created_desc\":\n    default:\n      query = query.order(\"created_at\", { ascending: false });\n      break;\n  }\n\n  query = query.range(offset, offset + pageSize - 1);\n\n  const { data, error } = await query;\n\n  if (error) {\n    return { error: \"Failed to fetch products.\" };\n  }\n\n  return {\n    data: (data as unknown as ProductServiceData[]) ?? [],\n    totalCount: count || 0,\n  };\n}\n\n// Removed visit tracking logic as metrics tables have been deleted"], "names": [], "mappings": ";;;;;AAEA;AAGA,kCAAkC;AAClC;;;;;;AAeO,eAAe,kBACpB,UAAkB,EAClB,OAAe,CAAC,EAChB,SAA8B,cAAc,EAC5C,WAAmB,EAAE,EACrB,UAA0B,EAC1B,WAA2B;IAM3B,IAAI,CAAC,YAAY;QACf,OAAO;YAAE,OAAO;QAA2B;IAC7C;IACA,IAAI,OAAO,GAAG;QACZ,OAAO;YAAE,OAAO;QAAoC;IACtD;IACA,IAAI,WAAW,GAAG;QAChB,OAAO;YAAE,OAAO;QAAkC;IACpD;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;IAE5B,6CAA6C;IAC7C,IAAI,aAAa,SACd,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,4HAAA,CAAA,UAAO,CAAC,EAAE,EAAE;QAAE,OAAO;IAAQ,GACpC,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,YACxB,EAAE,CAAC,gBAAgB;IAEtB,kCAAkC;IAClC,IAAI,cAAc,WAAW,IAAI,GAAG,MAAM,GAAG,GAAG;QAC9C,aAAa,WAAW,KAAK,CAAC,4HAAA,CAAA,UAAO,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;IACtE;IAEA,wCAAwC;IACxC,IAAI,eAAe,gBAAgB,OAAO;QACxC,aAAa,WAAW,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,YAAY,EAAE;IACnD;IAEA,kBAAkB;IAClB,MAAM,EAAE,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;IAE3C,IAAI,YAAY;QACd,OAAO;YAAE,OAAO;QAA2B;IAC7C;IAEA,6CAA6C;IAC7C,IAAI,QAAQ,SACT,IAAI,CAAC,4HAAA,CAAA,SAAM,CAAC,iBAAiB,EAC7B,MAAM,CAAC,CAAC;;;;;;;;;;;;;QAaL,CAAC,EACJ,EAAE,CAAC,4HAAA,CAAA,UAAO,CAAC,WAAW,EAAE,YACxB,EAAE,CAAC,gBAAgB;IAEtB,kCAAkC;IAClC,IAAI,cAAc,WAAW,IAAI,GAAG,MAAM,GAAG,GAAG;QAC9C,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,WAAW,IAAI,GAAG,CAAC,CAAC;IACtD;IAEA,wCAAwC;IACxC,IAAI,eAAe,gBAAgB,OAAO;QACxC,QAAQ,MAAM,EAAE,CAAC,gBAAgB;IACnC;IAEA,gBAAgB;IAChB,OAAQ;QACN,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;YACpD;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YACrD;QACF,KAAK;YACH,gEAAgE;YAChE,QAAQ,MACL,KAAK,CAAC,oBAAoB;gBAAE,WAAW;gBAAM,YAAY;YAAM,GAC/D,KAAK,CAAC,cAAc;gBAAE,WAAW;gBAAM,YAAY;YAAM;YAC5D;QACF,KAAK;YACH,iEAAiE;YACjE,QAAQ,MACL,KAAK,CAAC,oBAAoB;gBAAE,WAAW;gBAAO,YAAY;YAAM,GAChE,KAAK,CAAC,cAAc;gBAAE,WAAW;gBAAO,YAAY;YAAM;YAC7D;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAK;YAC9C;QACF,KAAK;YACH,QAAQ,MAAM,KAAK,CAAC,QAAQ;gBAAE,WAAW;YAAM;YAC/C;QACF,KAAK;QACL;YACE,QAAQ,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YACrD;IACJ;IAEA,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,WAAW;IAEhD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,OAAO;YAAE,OAAO;QAA4B;IAC9C;IAEA,OAAO;QACL,MAAM,AAAC,QAA4C,EAAE;QACrD,YAAY,SAAS;IACvB;AACF,EAEA,mEAAmE;;;IA7H7C;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/secureCustomerProfiles.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\n// Define types for the customer profile data\r\ntype CustomerProfilePublicData = {\r\n  id: string;\r\n  name: string | null;\r\n  email: string | null;\r\n  avatar_url: string | null;\r\n  created_at: string | null;\r\n  updated_at: string | null;\r\n};\r\n\r\n/**\r\n * Securely fetch a customer profile by ID using the service role key\r\n * This bypasses RLS and ensures sensitive data is not exposed to the client\r\n */\r\nexport async function getSecureCustomerProfileById(\r\n  userId: string\r\n): Promise<{\r\n  data?: CustomerProfilePublicData;\r\n  error?: string;\r\n}> {\r\n  if (!userId) {\r\n    return { error: \"User ID is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch the customer profile from public view\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"*\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(\"Secure Fetch Error:\", error);\r\n      return { error: `Failed to fetch customer profile: ${error.message}` };\r\n    }\r\n\r\n    if (!data) {\r\n      return { error: \"Profile not found.\" };\r\n    }\r\n\r\n    // Data from public view is already safe\r\n    const safeData: CustomerProfilePublicData = {\r\n      id: data.id!,\r\n      name: data.name,\r\n      email: null, // Not available in public view\r\n      avatar_url: data.avatar_url,\r\n      created_at: data.created_at,\r\n      updated_at: data.updated_at,\r\n    };\r\n\r\n    return { data: safeData };\r\n  } catch (e) {\r\n    console.error(\"Exception in getSecureCustomerProfileById:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetch multiple customer profiles by IDs using the service role key\r\n */\r\nexport async function getSecureCustomerProfilesByIds(\r\n  userIds: string[]\r\n): Promise<{\r\n  data?: CustomerProfilePublicData[];\r\n  error?: string;\r\n}> {\r\n  if (!userIds || userIds.length === 0) {\r\n    return { data: [] };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch the customer profiles from public view\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url, created_at, updated_at\")\r\n      .in(\"id\", userIds);\r\n\r\n    if (error) {\r\n      console.error(\"Secure Fetch Error:\", error);\r\n      return { error: `Failed to fetch customer profiles: ${error.message}` };\r\n    }\r\n\r\n    // Map to expected format (email not available in public view)\r\n    const safeData = data?.map((profile: any) => ({\r\n      ...profile,\r\n      email: null // Not available in public view\r\n    })) || [];\r\n\r\n    return { data: safeData as CustomerProfilePublicData[] };\r\n  } catch (e) {\r\n    console.error(\"Exception in getSecureCustomerProfilesByIds:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetch all customer profiles using the service role key\r\n * This is primarily for testing purposes\r\n */\r\nexport async function getAllSecureCustomerProfiles(): Promise<{\r\n  data?: CustomerProfilePublicData[];\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch all customer profiles from public view\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url, created_at, updated_at\");\r\n\r\n    if (error) {\r\n      console.error(\"Secure Fetch Error:\", error);\r\n      return { error: `Failed to fetch customer profiles: ${error.message}` };\r\n    }\r\n\r\n    // Map to expected format (email not available in public view)\r\n    const safeData = data?.map((profile: any) => ({\r\n      ...profile,\r\n      email: null // Not available in public view\r\n    })) || [];\r\n\r\n    return { data: safeData as CustomerProfilePublicData[] };\r\n  } catch (e) {\r\n    console.error(\"Exception in getAllSecureCustomerProfiles:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely check if a user has access to their own customer profile\r\n * This is used for authenticated users to access their own profile\r\n */\r\nexport async function checkUserCustomerProfileAccess(\r\n  userId: string\r\n): Promise<{\r\n  hasAccess: boolean;\r\n  error?: string;\r\n}> {\r\n  if (!userId) {\r\n    return { hasAccess: false, error: \"User ID is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use regular client for authenticated user\r\n    const supabase = await createClient();\r\n\r\n    // Check if the user is authenticated\r\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\r\n\r\n    if (authError || !user) {\r\n      return { hasAccess: false, error: \"User not authenticated.\" };\r\n    }\r\n\r\n    // Verify the requested userId matches the authenticated user\r\n    if (user.id !== userId) {\r\n      return { hasAccess: false, error: \"Unauthorized access attempt.\" };\r\n    }\r\n\r\n    // Check if the user has a customer profile\r\n    const { data, error } = await supabase\r\n      .from(\"customer_profiles\")\r\n      .select(\"id\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (error) {\r\n      console.error(\"Profile Access Check Error:\", error);\r\n      return { hasAccess: false, error: \"Database error checking access.\" };\r\n    }\r\n\r\n    return { hasAccess: !!data };\r\n  } catch (e) {\r\n    console.error(\"Exception in checkUserCustomerProfileAccess:\", e);\r\n    return { hasAccess: false, error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get user profile data (either business or customer) for reviews\r\n * This function will check both tables and return the appropriate profile data\r\n */\r\nexport async function getUserProfileForReview(\r\n  userId: string\r\n): Promise<{\r\n  data?: {\r\n    id: string;\r\n    name: string | null;\r\n    avatar_url: string | null;\r\n    is_business: boolean;\r\n  };\r\n  error?: string;\r\n}> {\r\n  if (!userId) {\r\n    return { error: \"User ID is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // First check customer_profiles_public\r\n    const { data: customerProfile, error: customerError } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (customerError) {\r\n      console.error(\"Error fetching customer profile:\", customerError);\r\n      return { error: `Failed to fetch customer profile: ${customerError.message}` };\r\n    }\r\n\r\n    if (customerProfile) {\r\n      return {\r\n        data: {\r\n          id: customerProfile.id!,\r\n          name: customerProfile.name,\r\n          avatar_url: customerProfile.avatar_url,\r\n          is_business: false\r\n        }\r\n      };\r\n    }\r\n\r\n    // If not found in customer_profiles, check business_profiles\r\n    const { data: businessProfile, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id, business_name, logo_url\")\r\n      .eq(\"id\", userId)\r\n      .maybeSingle();\r\n\r\n    if (businessError) {\r\n      console.error(\"Error fetching business profile:\", businessError);\r\n      return { error: `Failed to fetch business profile: ${businessError.message}` };\r\n    }\r\n\r\n    if (businessProfile) {\r\n      return {\r\n        data: {\r\n          id: businessProfile.id,\r\n          name: businessProfile.business_name,\r\n          avatar_url: businessProfile.logo_url,\r\n          is_business: true\r\n        }\r\n      };\r\n    }\r\n\r\n    return { error: \"User profile not found in either customer or business profiles.\" };\r\n  } catch (e) {\r\n    console.error(\"Exception in getUserProfileForReview:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get multiple user profiles (either business or customer) for reviews\r\n */\r\nexport async function getUserProfilesForReviews(\r\n  userIds: string[]\r\n): Promise<{\r\n  data?: {\r\n    [key: string]: {\r\n      id: string;\r\n      name: string | null;\r\n      avatar_url: string | null;\r\n      is_business: boolean;\r\n    }\r\n  };\r\n  error?: string;\r\n}> {\r\n  if (!userIds || userIds.length === 0) {\r\n    return { data: {} };\r\n  }\r\n\r\n  try {\r\n    // Use the public view which only exposes safe data\r\n    const supabase = await createClient();\r\n\r\n    // Fetch customer profiles from public view\r\n    const { data: customerProfiles, error: customerError } = await supabase\r\n      .from(\"customer_profiles_public\")\r\n      .select(\"id, name, avatar_url\")\r\n      .in(\"id\", userIds);\r\n\r\n    if (customerError) {\r\n      console.error(\"Error fetching customer profiles:\", customerError);\r\n      return { error: `Failed to fetch customer profiles: ${customerError.message}` };\r\n    }\r\n\r\n    // Fetch business profiles\r\n    const { data: businessProfiles, error: businessError } = await supabase\r\n      .from(\"business_profiles\")\r\n      .select(\"id, business_name, logo_url\")\r\n      .in(\"id\", userIds);\r\n\r\n    if (businessError) {\r\n      console.error(\"Error fetching business profiles:\", businessError);\r\n      return { error: `Failed to fetch business profiles: ${businessError.message}` };\r\n    }\r\n\r\n    // Combine the results into a map of user ID to profile data\r\n    const profilesMap: {\r\n      [key: string]: {\r\n        id: string;\r\n        name: string | null;\r\n        avatar_url: string | null;\r\n        is_business: boolean;\r\n      }\r\n    } = {};\r\n\r\n    // Add customer profiles to the map\r\n    customerProfiles?.forEach((profile: any) => {\r\n      profilesMap[profile.id] = {\r\n        id: profile.id,\r\n        name: profile.name,\r\n        avatar_url: profile.avatar_url,\r\n        is_business: false\r\n      };\r\n    });\r\n\r\n    // Add business profiles to the map\r\n    businessProfiles?.forEach((profile: any) => {\r\n      // Only add if not already in the map (customer profiles take precedence)\r\n      if (!profilesMap[profile.id]) {\r\n        profilesMap[profile.id] = {\r\n          id: profile.id,\r\n          name: profile.business_name,\r\n          avatar_url: profile.logo_url,\r\n          is_business: true\r\n        };\r\n      }\r\n    });\r\n\r\n    return { data: profilesMap };\r\n  } catch (e) {\r\n    console.error(\"Exception in getUserProfilesForReviews:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;;;AAgBO,eAAe,6BACpB,MAAc;IAKd,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,OAAO;QAAuB;IACzC;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,8CAA8C;QAC9C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,OAAO,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;YAAC;QACvE;QAEA,IAAI,CAAC,MAAM;YACT,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,wCAAwC;QACxC,MAAM,WAAsC;YAC1C,IAAI,KAAK,EAAE;YACX,MAAM,KAAK,IAAI;YACf,OAAO;YACP,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,UAAU;QAC7B;QAEA,OAAO;YAAE,MAAM;QAAS;IAC1B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,+BACpB,OAAiB;IAKjB,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;YAAE,MAAM,EAAE;QAAC;IACpB;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,+CAA+C;QAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC,gDACP,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YAAC;QACxE;QAEA,8DAA8D;QAC9D,MAAM,WAAW,MAAM,IAAI,CAAC,UAAiB,CAAC;gBAC5C,GAAG,OAAO;gBACV,OAAO,KAAK,+BAA+B;YAC7C,CAAC,MAAM,EAAE;QAET,OAAO;YAAE,MAAM;QAAwC;IACzD,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAMO,eAAe;IAIpB,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,+CAA+C;QAC/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,4BACL,MAAM,CAAC;QAEV,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,MAAM,OAAO,EAAE;YAAC;QACxE;QAEA,8DAA8D;QAC9D,MAAM,WAAW,MAAM,IAAI,CAAC,UAAiB,CAAC;gBAC5C,GAAG,OAAO;gBACV,OAAO,KAAK,+BAA+B;YAC7C,CAAC,MAAM,EAAE;QAET,OAAO;YAAE,MAAM;QAAwC;IACzD,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAMO,eAAe,+BACpB,MAAc;IAKd,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,WAAW;YAAO,OAAO;QAAuB;IAC3D;IAEA,IAAI;QACF,4CAA4C;QAC5C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,qCAAqC;QACrC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAA0B;QAC9D;QAEA,6DAA6D;QAC7D,IAAI,KAAK,EAAE,KAAK,QAAQ;YACtB,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAA+B;QACnE;QAEA,2CAA2C;QAC3C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;gBAAE,WAAW;gBAAO,OAAO;YAAkC;QACtE;QAEA,OAAO;YAAE,WAAW,CAAC,CAAC;QAAK;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,WAAW;YAAO,OAAO;QAAgC;IACpE;AACF;AAMO,eAAe,wBACpB,MAAc;IAUd,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,OAAO;QAAuB;IACzC;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,uCAAuC;QACvC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC3D,IAAI,CAAC,4BACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,OAAO,CAAC,kCAAkC,EAAE,cAAc,OAAO,EAAE;YAAC;QAC/E;QAEA,IAAI,iBAAiB;YACnB,OAAO;gBACL,MAAM;oBACJ,IAAI,gBAAgB,EAAE;oBACtB,MAAM,gBAAgB,IAAI;oBAC1B,YAAY,gBAAgB,UAAU;oBACtC,aAAa;gBACf;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC3D,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM,QACT,WAAW;QAEd,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,OAAO,CAAC,kCAAkC,EAAE,cAAc,OAAO,EAAE;YAAC;QAC/E;QAEA,IAAI,iBAAiB;YACnB,OAAO;gBACL,MAAM;oBACJ,IAAI,gBAAgB,EAAE;oBACtB,MAAM,gBAAgB,aAAa;oBACnC,YAAY,gBAAgB,QAAQ;oBACpC,aAAa;gBACf;YACF;QACF;QAEA,OAAO;YAAE,OAAO;QAAkE;IACpF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,yCAAyC;QACvD,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,0BACpB,OAAiB;IAYjB,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,OAAO;YAAE,MAAM,CAAC;QAAE;IACpB;IAEA,IAAI;QACF,mDAAmD;QACnD,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,2CAA2C;QAC3C,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,4BACL,MAAM,CAAC,wBACP,EAAE,CAAC,MAAM;QAEZ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,cAAc,OAAO,EAAE;YAAC;QAChF;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,gBAAgB,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SAC5D,IAAI,CAAC,qBACL,MAAM,CAAC,+BACP,EAAE,CAAC,MAAM;QAEZ,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;gBAAE,OAAO,CAAC,mCAAmC,EAAE,cAAc,OAAO,EAAE;YAAC;QAChF;QAEA,4DAA4D;QAC5D,MAAM,cAOF,CAAC;QAEL,mCAAmC;QACnC,kBAAkB,QAAQ,CAAC;YACzB,WAAW,CAAC,QAAQ,EAAE,CAAC,GAAG;gBACxB,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,IAAI;gBAClB,YAAY,QAAQ,UAAU;gBAC9B,aAAa;YACf;QACF;QAEA,mCAAmC;QACnC,kBAAkB,QAAQ,CAAC;YACzB,yEAAyE;YACzE,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,EAAE;gBAC5B,WAAW,CAAC,QAAQ,EAAE,CAAC,GAAG;oBACxB,IAAI,QAAQ,EAAE;oBACd,MAAM,QAAQ,aAAa;oBAC3B,YAAY,QAAQ,QAAQ;oBAC5B,aAAa;gBACf;YACF;QACF;QAEA,OAAO;YAAE,MAAM;QAAY;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IA5UsB;IAkDA;IA0CA;IAmCA;IAiDA;IA2EA;;AA3PA,+OAAA;AAkDA,+OAAA;AA0CA,+OAAA;AAmCA,+OAAA;AAiDA,+OAAA;AA2EA,+OAAA", "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/reviews.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\n// revalidatePath is imported but not used in this file\r\n// import { revalidatePath } from 'next/cache';\r\nimport { getUserProfilesForReviews } from \"./secureCustomerProfiles\";\r\nimport { Tables } from \"@/types/supabase\";\r\n\r\ntype ReviewWithUser = Tables<\"ratings_reviews\"> & {\r\n  user_profile?: {\r\n    id: string;\r\n    name: string | null;\r\n    avatar_url: string | null;\r\n    is_business: boolean;\r\n    business_slug: string | null;\r\n  };\r\n};\r\nexport type { ReviewWithUser };\r\n\r\nexport type ReviewSortBy =\r\n  | \"newest\"\r\n  | \"oldest\"\r\n  | \"highest_rating\"\r\n  | \"lowest_rating\";\r\n\r\n// Fetch reviews for a business with pagination and sorting\r\nexport async function fetchBusinessReviews(\r\n  businessProfileId: string,\r\n  page: number = 1,\r\n  limit: number = 5,\r\n  sortBy: ReviewSortBy = \"newest\"\r\n): Promise<{\r\n  data: ReviewWithUser[];\r\n  totalCount: number;\r\n  error?: string;\r\n}> {\r\n  const _supabase = await createClient();\r\n\r\n  try {\r\n    // Create admin client for secure operations\r\n    const supabase = await createClient();\r\n\r\n    // First, get the reviews with pagination and sorting\r\n    let query = supabase\r\n      .from(\"ratings_reviews\")\r\n      .select(\"*\", { count: \"exact\" })\r\n      .eq(\"business_profile_id\", businessProfileId)\r\n      // Don't show reviews where the user is reviewing their own business\r\n      .neq(\"user_id\", businessProfileId);\r\n\r\n    // Apply sorting\r\n    switch (sortBy) {\r\n      case \"oldest\":\r\n        query = query.order(\"created_at\", { ascending: true });\r\n        break;\r\n      case \"highest_rating\":\r\n        query = query.order(\"rating\", { ascending: false });\r\n        break;\r\n      case \"lowest_rating\":\r\n        query = query.order(\"rating\", { ascending: true });\r\n        break;\r\n      case \"newest\":\r\n      default:\r\n        query = query.order(\"created_at\", { ascending: false });\r\n        break;\r\n    }\r\n\r\n    // Apply pagination\r\n    const from = (page - 1) * limit;\r\n    const to = from + limit - 1;\r\n    query = query.range(from, to);\r\n\r\n    // Execute the query\r\n    const { data: reviewsData, error: reviewsError, count } = await query;\r\n\r\n    if (reviewsError) {\r\n      console.error(\"Error fetching reviews:\", reviewsError);\r\n      return { data: [], totalCount: 0, error: reviewsError.message };\r\n    }\r\n\r\n    // If no reviews, return empty array\r\n    if (!reviewsData || reviewsData.length === 0) {\r\n      return { data: [], totalCount: count || 0 };\r\n    }\r\n\r\n    // Get all user IDs from the reviews\r\n    const userIds = [\r\n      ...new Set(\r\n        reviewsData.map((review: { user_id: string }) => review.user_id)\r\n      ),\r\n    ] as string[];\r\n\r\n    // Use the secure method to fetch user profiles (both customer and business)\r\n    const { data: profilesMap, error: profilesError } =\r\n      await getUserProfilesForReviews(userIds);\r\n\r\n    if (profilesError) {\r\n      console.error(\"Error fetching user profiles:\", profilesError);\r\n      // Continue without profiles\r\n    }\r\n\r\n    // Get business user IDs from the profiles\r\n    const businessUserIds = userIds.filter(\r\n      (id: string) => profilesMap?.[id]?.is_business\r\n    );\r\n\r\n    // Create a map of business IDs to their slugs\r\n    let businessSlugMap: Record<string, string | null> = {};\r\n\r\n    // Fetch business slugs for all business reviewers at once\r\n    if (businessUserIds.length > 0) {\r\n      const { data: businessSlugs } = await supabase\r\n        .from(\"business_profiles\")\r\n        .select(\"id, business_slug\")\r\n        .in(\"id\", businessUserIds);\r\n\r\n      // Create a map of business IDs to their slugs\r\n      if (businessSlugs) {\r\n        businessSlugMap = businessSlugs.reduce(\r\n          (\r\n            acc: Record<string, string | null>,\r\n            business: { id: string; business_slug: string | null }\r\n          ) => {\r\n            acc[business.id] = business.business_slug;\r\n            return acc;\r\n          },\r\n          {} as Record<string, string | null>\r\n        );\r\n      }\r\n    }\r\n\r\n    // Process the reviews data with profile information\r\n    const processedData: ReviewWithUser[] = reviewsData.map((review: any) => {\r\n      const profile = profilesMap?.[review.user_id];\r\n      const userProfile = profile\r\n        ? {\r\n            ...profile,\r\n            business_slug: profile.is_business\r\n              ? businessSlugMap[review.user_id] || null\r\n              : null,\r\n          }\r\n        : undefined;\r\n\r\n      return {\r\n        ...review,\r\n        user_profile: userProfile,\r\n      };\r\n    });\r\n\r\n    return {\r\n      data: processedData,\r\n      totalCount: count || 0,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Unexpected error in fetchBusinessReviews:\", error);\r\n    const errorMessage =\r\n      error instanceof Error ? error.message : \"An unexpected error occurred.\";\r\n    return { data: [], totalCount: 0, error: errorMessage };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA,uDAAuD;AACvD,+CAA+C;AAC/C;;;;;;AAqBO,eAAe,qBACpB,iBAAyB,EACzB,OAAe,CAAC,EAChB,QAAgB,CAAC,EACjB,SAAuB,QAAQ;IAM/B,MAAM,YAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAEnC,IAAI;QACF,4CAA4C;QAC5C,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;QAElC,qDAAqD;QACrD,IAAI,QAAQ,SACT,IAAI,CAAC,mBACL,MAAM,CAAC,KAAK;YAAE,OAAO;QAAQ,GAC7B,EAAE,CAAC,uBAAuB,kBAC3B,oEAAoE;SACnE,GAAG,CAAC,WAAW;QAElB,gBAAgB;QAChB,OAAQ;YACN,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBACpD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,UAAU;oBAAE,WAAW;gBAAM;gBACjD;YACF,KAAK;gBACH,QAAQ,MAAM,KAAK,CAAC,UAAU;oBAAE,WAAW;gBAAK;gBAChD;YACF,KAAK;YACL;gBACE,QAAQ,MAAM,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBACrD;QACJ;QAEA,mBAAmB;QACnB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAC1B,MAAM,KAAK,OAAO,QAAQ;QAC1B,QAAQ,MAAM,KAAK,CAAC,MAAM;QAE1B,oBAAoB;QACpB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM;QAEhE,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBAAE,MAAM,EAAE;gBAAE,YAAY;gBAAG,OAAO,aAAa,OAAO;YAAC;QAChE;QAEA,oCAAoC;QACpC,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC5C,OAAO;gBAAE,MAAM,EAAE;gBAAE,YAAY,SAAS;YAAE;QAC5C;QAEA,oCAAoC;QACpC,MAAM,UAAU;eACX,IAAI,IACL,YAAY,GAAG,CAAC,CAAC,SAAgC,OAAO,OAAO;SAElE;QAED,4EAA4E;QAC5E,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAC/C,MAAM,CAAA,GAAA,wIAAA,CAAA,4BAAyB,AAAD,EAAE;QAElC,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,4BAA4B;QAC9B;QAEA,0CAA0C;QAC1C,MAAM,kBAAkB,QAAQ,MAAM,CACpC,CAAC,KAAe,aAAa,CAAC,GAAG,EAAE;QAGrC,8CAA8C;QAC9C,IAAI,kBAAiD,CAAC;QAEtD,0DAA0D;QAC1D,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,qBACL,MAAM,CAAC,qBACP,EAAE,CAAC,MAAM;YAEZ,8CAA8C;YAC9C,IAAI,eAAe;gBACjB,kBAAkB,cAAc,MAAM,CACpC,CACE,KACA;oBAEA,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,aAAa;oBACzC,OAAO;gBACT,GACA,CAAC;YAEL;QACF;QAEA,oDAAoD;QACpD,MAAM,gBAAkC,YAAY,GAAG,CAAC,CAAC;YACvD,MAAM,UAAU,aAAa,CAAC,OAAO,OAAO,CAAC;YAC7C,MAAM,cAAc,UAChB;gBACE,GAAG,OAAO;gBACV,eAAe,QAAQ,WAAW,GAC9B,eAAe,CAAC,OAAO,OAAO,CAAC,IAAI,OACnC;YACN,IACA;YAEJ,OAAO;gBACL,GAAG,MAAM;gBACT,cAAc;YAChB;QACF;QAEA,OAAO;YACL,MAAM;YACN,YAAY,SAAS;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3C,OAAO;YAAE,MAAM,EAAE;YAAE,YAAY;YAAG,OAAO;QAAa;IACxD;AACF;;;IArIsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%5BcardSlug%5D/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getSecureBusinessProfileWithProductsBySlug as '407591979af61a7d463be0dd2c1b3975f1e37c6d4d'} from 'ACTIONS_MODULE0'\nexport {getSecureBusinessProfileBySlug as '40e464278039bdd26313983f57189fe5ad16207de5'} from 'ACTIONS_MODULE0'\nexport {getBusinessGalleryImagesBySlug as '401de3de75c96ed9402f56536c4055a2a1e481faa3'} from 'ACTIONS_MODULE1'\nexport {getBusinessGalleryImages as '40ab6a62d529134e66e01b1076bd34abc258d01f1a'} from 'ACTIONS_MODULE1'\nexport {getBusinessGalleryImagesForTab as '40d99fa4a23448e324eb34055f899bfbe14f69c4a4'} from 'ACTIONS_MODULE1'\nexport {getBusinessGalleryImagesPaginated as '70b33159413534880b95c7d6a0075cc6c1d55e240a'} from 'ACTIONS_MODULE1'\nexport {getSecureBusinessProfileIdsForDiscover as '706494db815230f39fde096a9039730918baa8e0b5'} from 'ACTIONS_MODULE2'\nexport {getSecureBusinessProfilesForDiscover as '7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33'} from 'ACTIONS_MODULE2'\nexport {getSecureBusinessProfiles as '7f19088479bd4247b361efa63e1f08c59cbae5a424'} from 'ACTIONS_MODULE3'\nexport {getSecureBusinessProfilesForSitemap as '00be04a805bc46661f0f4971fc802a16df9b7de3d1'} from 'ACTIONS_MODULE4'\nexport {getCurrentUserBusinessProfileId as '00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede'} from 'ACTIONS_MODULE5'\nexport {checkBusinessProfileAccess as '407666f47811af7f53e12500216410b8cf9c72486c'} from 'ACTIONS_MODULE5'\nexport {getSecureBusinessProfilesByLocation as '78aef494570fba887a7b263a064fef1e8968bf098e'} from 'ACTIONS_MODULE6'\nexport {getInteractionStatus as '40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca'} from 'ACTIONS_MODULE7'\nexport {likeBusiness as '4050870674cfbc37404b048f17c031e582c6817414'} from 'ACTIONS_MODULE7'\nexport {subscribeToBusiness as '40ac030879d2dd9afcc77d6d490fed42fed2873adc'} from 'ACTIONS_MODULE7'\nexport {unlikeBusiness as '406e7e1a2d209fd5068ed48a9b20838464c164486f'} from 'ACTIONS_MODULE7'\nexport {unsubscribeFromBusiness as '407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff'} from 'ACTIONS_MODULE7'\nexport {fetchMoreProducts as '7e2ba45ccb00f66ac30c4be3781c321dc836b9feb2'} from 'ACTIONS_MODULE8'\nexport {fetchBusinessReviews as '780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899'} from 'ACTIONS_MODULE9'\nexport {submitReview as '7097170cd67aa830da6c5c6a671c70d1f195006853'} from 'ACTIONS_MODULE7'\nexport {deleteReview as '40bf9c153311b3515e09fea919b70600d754f8d64c'} from 'ACTIONS_MODULE7'\n"], "names": [], "mappings": ";AAAA;AAEA;AAIA;AAEA;AACA;AACA;AAEA;AACA;AAKA;AACA", "debugId": null}}, {"offset": {"line": 2580, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/PublicCardPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/[cardSlug]/PublicCardPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/[cardSlug]/PublicCardPageClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 2594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/PublicCardPageClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/[cardSlug]/PublicCardPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/[cardSlug]/PublicCardPageClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 2608, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2618, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/utils.ts"], "sourcesContent": ["import { BusinessSortBy } from \"./types\";\r\n\r\n/**\r\n * Apply sorting to a Supabase query based on the provided sort option\r\n * Using 'any' type here is acceptable since we're working with Supabase query builder\r\n * which has a complex type structure that's difficult to represent precisely\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport function applySorting(query: any, sortBy: BusinessSortBy): any {\r\n  switch (sortBy) {\r\n    case \"name_asc\":\r\n      return query.order(\"business_name\", { ascending: true });\r\n    case \"name_desc\":\r\n      return query.order(\"business_name\", { ascending: false });\r\n    case \"created_asc\":\r\n      return query.order(\"created_at\", { ascending: true });\r\n    case \"created_desc\":\r\n      return query.order(\"created_at\", { ascending: false });\r\n    case \"likes_asc\":\r\n      return query.order(\"total_likes\", { ascending: true });\r\n    case \"likes_desc\":\r\n      return query.order(\"total_likes\", { ascending: false });\r\n    case \"subscriptions_asc\":\r\n      return query.order(\"total_subscriptions\", { ascending: true });\r\n    case \"subscriptions_desc\":\r\n      return query.order(\"total_subscriptions\", { ascending: false });\r\n    case \"rating_asc\":\r\n      return query.order(\"average_rating\", { ascending: true });\r\n    case \"rating_desc\":\r\n      return query.order(\"average_rating\", { ascending: false });\r\n    default:\r\n      return query.order(\"created_at\", { ascending: false });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the current ISO timestamp\r\n */\r\nexport function getCurrentISOTimestamp(): string {\r\n  return new Date().toISOString();\r\n}\r\n\r\n/**\r\n * Create a subscription map from subscription data\r\n */\r\nexport function createSubscriptionMap(subscriptionsData: Array<{\r\n  business_profile_id: string;\r\n  subscription_status: string | null;\r\n  plan_id: string | null;\r\n}> | null) {\r\n  const subscriptionMap = new Map<string, {\r\n    subscription_status: string | null;\r\n    plan_id: string | null;\r\n  }>();\r\n\r\n  if (subscriptionsData) {\r\n    // Group by business_profile_id and take the most recent one\r\n    subscriptionsData.forEach(sub => {\r\n      if (!subscriptionMap.has(sub.business_profile_id)) {\r\n        subscriptionMap.set(sub.business_profile_id, {\r\n          subscription_status: sub.subscription_status,\r\n          plan_id: sub.plan_id\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  return subscriptionMap;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,aAAa,KAAU,EAAE,MAAsB;IAC7D,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,iBAAiB;gBAAE,WAAW;YAAK;QACxD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,iBAAiB;gBAAE,WAAW;YAAM;QACzD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;QACrD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;QACtD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAK;QACtD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM;QACvD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,uBAAuB;gBAAE,WAAW;YAAK;QAC9D,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,uBAAuB;gBAAE,WAAW;YAAM;QAC/D,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,kBAAkB;gBAAE,WAAW;YAAK;QACzD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,kBAAkB;gBAAE,WAAW;YAAM;QAC1D;YACE,OAAO,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;IACxD;AACF;AAKO,SAAS;IACd,OAAO,IAAI,OAAO,WAAW;AAC/B;AAKO,SAAS,sBAAsB,iBAI7B;IACP,MAAM,kBAAkB,IAAI;IAK5B,IAAI,mBAAmB;QACrB,4DAA4D;QAC5D,kBAAkB,OAAO,CAAC,CAAA;YACxB,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,mBAAmB,GAAG;gBACjD,gBAAgB,GAAG,CAAC,IAAI,mBAAmB,EAAE;oBAC3C,qBAAqB,IAAI,mBAAmB;oBAC5C,SAAS,IAAI,OAAO;gBACtB;YACF;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2703, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/index.ts"], "sourcesContent": ["// Re-export types and utility functions\r\nexport * from './types';\r\nexport * from './utils';\r\n\r\n// Re-export server actions\r\nexport {\r\n  getSecureBusinessProfileBySlug,\r\n  getSecureBusinessProfileWithProductsBySlug\r\n} from './profileRetrieval';\r\n\r\nexport {\r\n  getSecureBusinessProfilesForDiscover,\r\n  getSecureBusinessProfileIdsForDiscover\r\n} from './discovery';\r\n\r\nexport {\r\n  getSecureBusinessProfiles\r\n} from './search';\r\n\r\nexport {\r\n  getSecureBusinessProfilesForSitemap\r\n} from './sitemap';\r\n\r\nexport {\r\n  checkBusinessProfileAccess,\r\n  getCurrentUserBusinessProfileId\r\n} from './access';\r\n\r\nexport {\r\n  getSecureBusinessProfilesByLocation\r\n} from './location';\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;AACxC;AACA;AAEA,2BAA2B;AAC3B;AAKA;AAKA;AAIA;AAIA;AAKA", "debugId": null}}, {"offset": {"line": 2744, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/OfflineBusinessMessage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "debugId": null}}, {"offset": {"line": 2758, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/OfflineBusinessMessage.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 2772, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2782, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/page.tsx"], "sourcesContent": ["import { createClient } from \"@/utils/supabase/server\";\r\nimport { notFound } from \"next/navigation\";\r\nimport {\r\n  ProductServiceData,\r\n  ProductSortBy,\r\n} from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport PublicCardPageClient from \"./PublicCardPageClient\";\r\nimport { AdData } from \"@/types/ad\";\r\nimport type { Metadata } from \"next\";\r\n\r\nimport { getSecureBusinessProfileBySlug } from \"@/lib/actions/businessProfiles\";\r\nimport { getBusinessGalleryImagesForTab } from \"@/lib/actions/gallery\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport OfflineBusinessMessage from \"./components/OfflineBusinessMessage\";\r\n\r\nconst INITIAL_PRODUCTS_PAGE_SIZE = 20;\r\n\r\n// All users now have access to all features - no plan restrictions\r\n\r\n// Helper function to determine if platform ads should be shown\r\nconst shouldShowPlatformAds = (): boolean => {\r\n  // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads\r\n  return true; // Always show platform ads as fallback\r\n};\r\n\r\nexport default async function PublicCardPage({\r\n  params,\r\n}: {\r\n  params: Promise<{ cardSlug: string }>;\r\n}) {\r\n  const { cardSlug } = await params;\r\n  const supabase = await createClient();\r\n\r\n  // Use the secure method to fetch the business profile\r\n  const { data: businessProfile, error: profileError } =\r\n    await getSecureBusinessProfileBySlug(cardSlug);\r\n\r\n  if (profileError || !businessProfile) {\r\n    console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);\r\n    notFound();\r\n  }\r\n\r\n  // Check if the profile is online\r\n  if (businessProfile.status !== \"online\") {\r\n    console.log(\r\n      `Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`\r\n    );\r\n    // Show offline message instead of 404\r\n    return <OfflineBusinessMessage />;\r\n  }\r\n\r\n  // We no longer check subscription status, only if the business is online\r\n  // The status field is the only thing that matters now\r\n\r\n  // Check if required fields are missing but status is still online\r\n  // Define the required fields directly here to avoid type issues\r\n  const requiredFields = [\r\n    \"member_name\",\r\n    \"title\",\r\n    \"business_name\",\r\n    \"phone\",\r\n    \"address_line\",\r\n    \"pincode\",\r\n    \"city\",\r\n    \"state\",\r\n    \"locality\",\r\n    \"contact_email\",\r\n  ];\r\n\r\n  const missingRequiredFields = requiredFields.filter(\r\n    (field) =>\r\n      !businessProfile[field as keyof typeof businessProfile] ||\r\n      String(businessProfile[field as keyof typeof businessProfile]).trim() ===\r\n        \"\"\r\n  );\r\n\r\n  if (missingRequiredFields.length > 0 && businessProfile.status === \"online\") {\r\n    console.log(\r\n      `Business profile ${cardSlug} is missing required fields but status is online, updating to offline. Missing fields: ${missingRequiredFields.join(\r\n        \", \"\r\n      )}`\r\n    );\r\n\r\n    // Update the profile using API route\r\n    try {\r\n      await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/${businessProfile.id}`, {\r\n        method: 'PATCH',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ status: \"offline\" }),\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to update business profile status:\", error);\r\n    }\r\n\r\n    // Show offline message instead of 404\r\n    return <OfflineBusinessMessage />;\r\n  }\r\n\r\n  // We no longer check subscription status, only if the business is online\r\n  // The status field is the only thing that matters now\r\n\r\n  let topAdData: AdData = null;\r\n\r\n  // Fetch platform ads for all businesses (Pro/Enterprise can override with their own custom ads)\r\n  if (shouldShowPlatformAds()) {\r\n    try {\r\n      // First, check if the custom_ad_targets table exists (for backward compatibility)\r\n      const { count, error: tableCheckError } = await supabase\r\n        .from(\"custom_ad_targets\")\r\n        .select(\"*\", { count: \"exact\", head: true });\r\n\r\n      // If the table exists and migration has been applied\r\n      if (count !== null && !tableCheckError) {\r\n        // Use the get_ad_for_pincode function to find the appropriate ad\r\n        const pincode = businessProfile.pincode || \"999999\"; // Use a dummy pincode if none provided\r\n        const { data: adData, error: adError } = await supabase.rpc(\r\n          \"get_ad_for_pincode\",\r\n          { target_pincode: pincode }\r\n        );\r\n\r\n        if (adData && adData.length > 0) {\r\n          // Found an ad (either pincode-specific or global)\r\n          topAdData = {\r\n            type: \"custom\",\r\n            imageUrl: adData[0].ad_image_url,\r\n            linkUrl: adData[0].ad_link_url,\r\n          };\r\n        } else {\r\n          // No custom ads found or error occurred\r\n          if (adError)\r\n            console.error(`Error fetching ad for pincode ${pincode}:`, adError);\r\n          topAdData = null; // Show placeholder when no custom ads are available\r\n        }\r\n      } else {\r\n        // Fallback to old approach if migration hasn't been applied yet\r\n        if (businessProfile.pincode) {\r\n          const { data: customAd } = await supabase\r\n            .from(\"custom_ads\")\r\n            .select(\"ad_image_url, ad_link_url\")\r\n            .eq(\"is_active\", true)\r\n            .or(\r\n              `targeting_locations.eq.'\"global\"',targeting_locations.cs.'[\"${businessProfile.pincode}\"]'`\r\n            )\r\n            .order(\"created_at\", { ascending: false })\r\n            .limit(1)\r\n            .maybeSingle();\r\n\r\n          if (customAd) {\r\n            topAdData = {\r\n              type: \"custom\",\r\n              imageUrl: customAd.ad_image_url,\r\n              linkUrl: customAd.ad_link_url,\r\n            };\r\n          } else {\r\n            // No matching custom ad found\r\n            topAdData = null;\r\n          }\r\n        } else {\r\n          // If business has no pincode, try to find global ads\r\n          const { data: globalAd } = await supabase\r\n            .from(\"custom_ads\")\r\n            .select(\"ad_image_url, ad_link_url\")\r\n            .eq(\"is_active\", true)\r\n            .eq(\"targeting_locations\", '\"global\"')\r\n            .order(\"created_at\", { ascending: false })\r\n            .limit(1)\r\n            .maybeSingle();\r\n\r\n          if (globalAd) {\r\n            topAdData = {\r\n              type: \"custom\",\r\n              imageUrl: globalAd.ad_image_url,\r\n              linkUrl: globalAd.ad_link_url,\r\n            };\r\n          } else {\r\n            topAdData = null;\r\n          }\r\n        }\r\n      }\r\n    } catch (adFetchError) {\r\n      console.error(`Error fetching custom ad:`, adFetchError);\r\n      topAdData = null; // fallback on error\r\n    }\r\n  }\r\n\r\n  const defaultSortPreference: ProductSortBy = \"created_desc\";\r\n\r\n  // Use the admin client to bypass RLS policies\r\n\r\n  const productQuery = supabase\r\n    .from(\"products_services\")\r\n    .select(\"*\", { count: \"exact\" })\r\n    .eq(\"business_id\", businessProfile.id)\r\n    .eq(\"is_available\", true)\r\n    .order(\"created_at\", { ascending: false })\r\n    .limit(INITIAL_PRODUCTS_PAGE_SIZE);\r\n\r\n  const {\r\n    data: initialProducts,\r\n    error: productsError,\r\n    count: totalProductCount,\r\n  } = await productQuery;\r\n\r\n  if (productsError) {\r\n    console.error(\r\n      `Error fetching initial products for business ${businessProfile.id}:`,\r\n      productsError\r\n    );\r\n    // Consider if this should be a fatal error or just log\r\n  }\r\n\r\n  // Fetch gallery images for this business (limited to 4 for gallery tab)\r\n  const {\r\n    images: galleryImages,\r\n    totalCount: galleryTotalCount,\r\n    error: galleryError,\r\n  } = await getBusinessGalleryImagesForTab(cardSlug);\r\n\r\n  if (galleryError) {\r\n    console.error(\r\n      `Error fetching gallery images for business ${businessProfile.id}:`,\r\n      galleryError\r\n    );\r\n    // Don't fail the page load for gallery error, just log it\r\n  }\r\n\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n  const currentUserId = user?.id || null;\r\n\r\n  // Fetch total reviews count for the business (excluding self-reviews)\r\n  const { count: totalReviews, error: reviewsCountError } = await supabase\r\n    .from(\"ratings_reviews\")\r\n    .select(\"id\", { count: \"exact\", head: true })\r\n    .eq(\"business_profile_id\", businessProfile.id)\r\n    .neq(\"user_id\", businessProfile.id); // Don't count self-reviews\r\n\r\n  if (reviewsCountError) {\r\n    console.error(\r\n      `Error fetching reviews count for business ${businessProfile.id}:`,\r\n      reviewsCountError\r\n    );\r\n  }\r\n\r\n  // Create a new object with the total reviews count and ensure all required fields are properly typed\r\n  const businessProfileWithReviews: BusinessCardData & { total_reviews: number } = {\r\n    ...businessProfile,\r\n    total_reviews: totalReviews || 0,\r\n    // Ensure all required fields are properly typed and present\r\n    phone: businessProfile.phone || \"\",\r\n    city: businessProfile.city || \"\",\r\n    state: businessProfile.state || \"\",\r\n    pincode: businessProfile.pincode || \"\",\r\n    locality: businessProfile.locality || \"\",\r\n    address_line: businessProfile.address_line || \"\",\r\n    business_name: businessProfile.business_name || \"\",\r\n    contact_email: businessProfile.contact_email || \"\",\r\n    member_name: businessProfile.member_name || \"\",\r\n    status: businessProfile.status as \"online\" | \"offline\",\r\n    title: businessProfile.title || \"\",\r\n    business_category: businessProfile.business_category || \"\",\r\n    // Custom branding and ads features removed - all users now have access to all features\r\n    // custom_branding field removed - all users now have access to all features\r\n    // custom_ads field removed - all users now have access to all features\r\n\r\n\r\n\r\n\r\n\r\n    whatsapp_number: businessProfile.whatsapp_number || undefined,\r\n    instagram_url: businessProfile.instagram_url || undefined,\r\n    facebook_url: businessProfile.facebook_url || undefined,\r\n    about_bio: businessProfile.about_bio || undefined,\r\n    business_slug: businessProfile.business_slug || undefined,\r\n    // theme_color field removed - using default styling\r\n    delivery_info: businessProfile.delivery_info || undefined,\r\n    total_likes: businessProfile.total_likes || 0,\r\n    total_subscriptions: businessProfile.total_subscriptions || 0,\r\n    average_rating: businessProfile.average_rating || undefined,\r\n    business_hours: businessProfile.business_hours || null,\r\n    // trial_end_date field removed - all users now have access to all features\r\n    created_at: businessProfile.created_at || undefined,\r\n    updated_at: businessProfile.updated_at || undefined,\r\n    established_year: businessProfile.established_year || null,\r\n\r\n\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col\">\r\n      <PublicCardPageClient\r\n        businessProfile={businessProfileWithReviews as BusinessCardData}\r\n        initialProducts={(initialProducts as unknown as ProductServiceData[]) ?? []}\r\n        totalProductCount={totalProductCount ?? 0}\r\n        defaultSortPreference={defaultSortPreference}\r\n        isAuthenticated={isAuthenticated}\r\n        currentUserId={currentUserId}\r\n        userPlan=\"enterprise\"\r\n        topAdData={topAdData}\r\n        galleryImages={galleryImages ?? []}\r\n        galleryTotalCount={galleryTotalCount ?? 0}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport async function generateMetadata({\r\n  params,\r\n}: {\r\n  params: Promise<{ cardSlug: string }>;\r\n}): Promise<Metadata> {\r\n  const { cardSlug } = await params;\r\n  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || \"https://dukancard.in\";\r\n  const pageUrl = `${siteUrl}/${cardSlug}`;\r\n\r\n  // Use the secure method to fetch the business profile\r\n  const { data: businessProfile, error: profileError } =\r\n    await getSecureBusinessProfileBySlug(cardSlug);\r\n\r\n  if (profileError || !businessProfile) {\r\n    // Use notFound() to trigger the 404 page for non-existent business slugs\r\n    notFound();\r\n  }\r\n\r\n  const businessName = businessProfile.business_name || \"Business\";\r\n\r\n  // For offline businesses, only use the business name in the title\r\n  // For online businesses, include the address\r\n  let baseTitle = businessName;\r\n  let fullAddress = \"\";\r\n\r\n  if (businessProfile.status === \"online\") {\r\n    // Create a complete address string from all address components\r\n    const addressComponents = [\r\n      businessProfile.address_line,\r\n      businessProfile.city,\r\n      businessProfile.state,\r\n      businessProfile.pincode,\r\n    ].filter(Boolean);\r\n\r\n    fullAddress = addressComponents.join(\", \");\r\n\r\n    // Create the SEO title with business name and address\r\n    if (fullAddress) {\r\n      baseTitle = `${businessName} - ${fullAddress}`;\r\n    }\r\n  }\r\n\r\n  // Create different descriptions for online and offline businesses\r\n  let description = \"\";\r\n\r\n  if (businessProfile.status === \"online\") {\r\n    description =\r\n      `Visit ${businessName}'s digital business card on Dukancard. ${\r\n        businessProfile.about_bio ? businessProfile.about_bio + \" \" : \"\"\r\n      }Find products, services, contact info, and location${\r\n        fullAddress ? ` at ${fullAddress}` : \"\"\r\n      }.`.trim();\r\n  } else {\r\n    description = `${businessName}'s digital business card on Dukancard is currently offline. Check back later or discover other businesses nearby.`;\r\n  }\r\n  const ogImage = businessProfile.logo_url || `${siteUrl}/opengraph-image.png`;\r\n\r\n  const keywords = [\r\n    businessName,\r\n    \"\", // Empty business_category\r\n    businessProfile?.city,\r\n    businessProfile?.state,\r\n    \"digital business card\",\r\n    \"online storefront\",\r\n    \"shop near me\",\r\n    \"Dukancard\",\r\n  ].filter(Boolean);\r\n\r\n  const schema = {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"LocalBusiness\",\r\n    name: businessName,\r\n    description: businessProfile.about_bio || description,\r\n    url: pageUrl,\r\n    image: businessProfile.logo_url,\r\n    telephone: businessProfile?.phone,\r\n    address: {\r\n      \"@type\": \"PostalAddress\",\r\n      streetAddress: businessProfile.address_line,\r\n      addressLocality: businessProfile.city,\r\n      addressRegion: businessProfile.state,\r\n      postalCode: businessProfile.pincode,\r\n      addressCountry: \"IN\",\r\n    },\r\n  };\r\n\r\n  return {\r\n    title: baseTitle,\r\n    description,\r\n    keywords: keywords.filter((k): k is string => k !== null),\r\n    alternates: {\r\n      canonical: `/${cardSlug}`,\r\n    },\r\n    openGraph: {\r\n      title: baseTitle,\r\n      description,\r\n      url: pageUrl,\r\n      siteName: \"Dukancard\",\r\n      type: \"profile\",\r\n      locale: \"en_IN\",\r\n      images: [\r\n        {\r\n          url: ogImage,\r\n          alt: `${businessName} - Digital Business Card`,\r\n        },\r\n      ],\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title: baseTitle,\r\n      description,\r\n      images: [ogImage],\r\n    },\r\n    other: {\r\n      \"application-ld+json\": JSON.stringify(schema),\r\n    },\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAKA;AAIA;AAAA;AACA;AAEA;;;;;;;;AAEA,MAAM,6BAA6B;AAEnC,mEAAmE;AAEnE,+DAA+D;AAC/D,MAAM,wBAAwB;IAC5B,gGAAgG;IAChG,OAAO,MAAM,uCAAuC;AACtD;AAEe,eAAe,eAAe,EAC3C,MAAM,EAGP;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM;IAC3B,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,sDAAsD;IACtD,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAClD,MAAM,CAAA,GAAA,sJAAA,CAAA,iCAA8B,AAAD,EAAE;IAEvC,IAAI,gBAAgB,CAAC,iBAAiB;QACpC,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC9D,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,iCAAiC;IACjC,IAAI,gBAAgB,MAAM,KAAK,UAAU;QACvC,QAAQ,GAAG,CACT,CAAC,iBAAiB,EAAE,SAAS,wBAAwB,EAAE,gBAAgB,MAAM,CAAC,EAAE,CAAC;QAEnF,sCAAsC;QACtC,qBAAO,8OAAC,4JAAA,CAAA,UAAsB;;;;;IAChC;IAEA,yEAAyE;IACzE,sDAAsD;IAEtD,kEAAkE;IAClE,gEAAgE;IAChE,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,wBAAwB,eAAe,MAAM,CACjD,CAAC,QACC,CAAC,eAAe,CAAC,MAAsC,IACvD,OAAO,eAAe,CAAC,MAAsC,EAAE,IAAI,OACjE;IAGN,IAAI,sBAAsB,MAAM,GAAG,KAAK,gBAAgB,MAAM,KAAK,UAAU;QAC3E,QAAQ,GAAG,CACT,CAAC,iBAAiB,EAAE,SAAS,uFAAuF,EAAE,sBAAsB,IAAI,CAC9I,OACC;QAGL,qCAAqC;QACrC,IAAI;YACF,MAAM,MAAM,6DAAoC,cAAc,EAAE,gBAAgB,EAAE,EAAE,EAAE;gBACpF,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAU;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;QAC7D;QAEA,sCAAsC;QACtC,qBAAO,8OAAC,4JAAA,CAAA,UAAsB;;;;;IAChC;IAEA,yEAAyE;IACzE,sDAAsD;IAEtD,IAAI,YAAoB;IAExB,gGAAgG;IAChG,IAAI,yBAAyB;QAC3B,IAAI;YACF,kFAAkF;YAClF,MAAM,EAAE,KAAK,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAC7C,IAAI,CAAC,qBACL,MAAM,CAAC,KAAK;gBAAE,OAAO;gBAAS,MAAM;YAAK;YAE5C,qDAAqD;YACrD,IAAI,UAAU,QAAQ,CAAC,iBAAiB;gBACtC,iEAAiE;gBACjE,MAAM,UAAU,gBAAgB,OAAO,IAAI,UAAU,uCAAuC;gBAC5F,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,OAAO,EAAE,GAAG,MAAM,SAAS,GAAG,CACzD,sBACA;oBAAE,gBAAgB;gBAAQ;gBAG5B,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;oBAC/B,kDAAkD;oBAClD,YAAY;wBACV,MAAM;wBACN,UAAU,MAAM,CAAC,EAAE,CAAC,YAAY;wBAChC,SAAS,MAAM,CAAC,EAAE,CAAC,WAAW;oBAChC;gBACF,OAAO;oBACL,wCAAwC;oBACxC,IAAI,SACF,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAC,EAAE;oBAC7D,YAAY,MAAM,oDAAoD;gBACxE;YACF,OAAO;gBACL,gEAAgE;gBAChE,IAAI,gBAAgB,OAAO,EAAE;oBAC3B,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,cACL,MAAM,CAAC,6BACP,EAAE,CAAC,aAAa,MAChB,EAAE,CACD,CAAC,4DAA4D,EAAE,gBAAgB,OAAO,CAAC,GAAG,CAAC,EAE5F,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM,GACvC,KAAK,CAAC,GACN,WAAW;oBAEd,IAAI,UAAU;wBACZ,YAAY;4BACV,MAAM;4BACN,UAAU,SAAS,YAAY;4BAC/B,SAAS,SAAS,WAAW;wBAC/B;oBACF,OAAO;wBACL,8BAA8B;wBAC9B,YAAY;oBACd;gBACF,OAAO;oBACL,qDAAqD;oBACrD,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,MAAM,SAC9B,IAAI,CAAC,cACL,MAAM,CAAC,6BACP,EAAE,CAAC,aAAa,MAChB,EAAE,CAAC,uBAAuB,YAC1B,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM,GACvC,KAAK,CAAC,GACN,WAAW;oBAEd,IAAI,UAAU;wBACZ,YAAY;4BACV,MAAM;4BACN,UAAU,SAAS,YAAY;4BAC/B,SAAS,SAAS,WAAW;wBAC/B;oBACF,OAAO;wBACL,YAAY;oBACd;gBACF;YACF;QACF,EAAE,OAAO,cAAc;YACrB,QAAQ,KAAK,CAAC,CAAC,yBAAyB,CAAC,EAAE;YAC3C,YAAY,MAAM,oBAAoB;QACxC;IACF;IAEA,MAAM,wBAAuC;IAE7C,8CAA8C;IAE9C,MAAM,eAAe,SAClB,IAAI,CAAC,qBACL,MAAM,CAAC,KAAK;QAAE,OAAO;IAAQ,GAC7B,EAAE,CAAC,eAAe,gBAAgB,EAAE,EACpC,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC,cAAc;QAAE,WAAW;IAAM,GACvC,KAAK,CAAC;IAET,MAAM,EACJ,MAAM,eAAe,EACrB,OAAO,aAAa,EACpB,OAAO,iBAAiB,EACzB,GAAG,MAAM;IAEV,IAAI,eAAe;QACjB,QAAQ,KAAK,CACX,CAAC,6CAA6C,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,EACrE;IAEF,uDAAuD;IACzD;IAEA,wEAAwE;IACxE,MAAM,EACJ,QAAQ,aAAa,EACrB,YAAY,iBAAiB,EAC7B,OAAO,YAAY,EACpB,GAAG,MAAM,CAAA,GAAA,yHAAA,CAAA,iCAA8B,AAAD,EAAE;IAEzC,IAAI,cAAc;QAChB,QAAQ,KAAK,CACX,CAAC,2CAA2C,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,EACnE;IAEF,0DAA0D;IAC5D;IAEA,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,MAAM,kBAAkB,CAAC,CAAC;IAC1B,MAAM,gBAAgB,MAAM,MAAM;IAElC,sEAAsE;IACtE,MAAM,EAAE,OAAO,YAAY,EAAE,OAAO,iBAAiB,EAAE,GAAG,MAAM,SAC7D,IAAI,CAAC,mBACL,MAAM,CAAC,MAAM;QAAE,OAAO;QAAS,MAAM;IAAK,GAC1C,EAAE,CAAC,uBAAuB,gBAAgB,EAAE,EAC5C,GAAG,CAAC,WAAW,gBAAgB,EAAE,GAAG,2BAA2B;IAElE,IAAI,mBAAmB;QACrB,QAAQ,KAAK,CACX,CAAC,0CAA0C,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAClE;IAEJ;IAEA,qGAAqG;IACrG,MAAM,6BAA2E;QAC/E,GAAG,eAAe;QAClB,eAAe,gBAAgB;QAC/B,4DAA4D;QAC5D,OAAO,gBAAgB,KAAK,IAAI;QAChC,MAAM,gBAAgB,IAAI,IAAI;QAC9B,OAAO,gBAAgB,KAAK,IAAI;QAChC,SAAS,gBAAgB,OAAO,IAAI;QACpC,UAAU,gBAAgB,QAAQ,IAAI;QACtC,cAAc,gBAAgB,YAAY,IAAI;QAC9C,eAAe,gBAAgB,aAAa,IAAI;QAChD,eAAe,gBAAgB,aAAa,IAAI;QAChD,aAAa,gBAAgB,WAAW,IAAI;QAC5C,QAAQ,gBAAgB,MAAM;QAC9B,OAAO,gBAAgB,KAAK,IAAI;QAChC,mBAAmB,gBAAgB,iBAAiB,IAAI;QACxD,uFAAuF;QACvF,4EAA4E;QAC5E,uEAAuE;QAMvE,iBAAiB,gBAAgB,eAAe,IAAI;QACpD,eAAe,gBAAgB,aAAa,IAAI;QAChD,cAAc,gBAAgB,YAAY,IAAI;QAC9C,WAAW,gBAAgB,SAAS,IAAI;QACxC,eAAe,gBAAgB,aAAa,IAAI;QAChD,oDAAoD;QACpD,eAAe,gBAAgB,aAAa,IAAI;QAChD,aAAa,gBAAgB,WAAW,IAAI;QAC5C,qBAAqB,gBAAgB,mBAAmB,IAAI;QAC5D,gBAAgB,gBAAgB,cAAc,IAAI;QAClD,gBAAgB,gBAAgB,cAAc,IAAI;QAClD,2EAA2E;QAC3E,YAAY,gBAAgB,UAAU,IAAI;QAC1C,YAAY,gBAAgB,UAAU,IAAI;QAC1C,kBAAkB,gBAAgB,gBAAgB,IAAI;IAGxD;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,4IAAA,CAAA,UAAoB;YACnB,iBAAiB;YACjB,iBAAiB,AAAC,mBAAuD,EAAE;YAC3E,mBAAmB,qBAAqB;YACxC,uBAAuB;YACvB,iBAAiB;YACjB,eAAe;YACf,UAAS;YACT,WAAW;YACX,eAAe,iBAAiB,EAAE;YAClC,mBAAmB,qBAAqB;;;;;;;;;;;AAIhD;AAEO,eAAe,iBAAiB,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM;IAC3B,MAAM,UAAU,6DAAoC;IACpD,MAAM,UAAU,GAAG,QAAQ,CAAC,EAAE,UAAU;IAExC,sDAAsD;IACtD,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,YAAY,EAAE,GAClD,MAAM,CAAA,GAAA,sJAAA,CAAA,iCAA8B,AAAD,EAAE;IAEvC,IAAI,gBAAgB,CAAC,iBAAiB;QACpC,yEAAyE;QACzE,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,MAAM,eAAe,gBAAgB,aAAa,IAAI;IAEtD,kEAAkE;IAClE,6CAA6C;IAC7C,IAAI,YAAY;IAChB,IAAI,cAAc;IAElB,IAAI,gBAAgB,MAAM,KAAK,UAAU;QACvC,+DAA+D;QAC/D,MAAM,oBAAoB;YACxB,gBAAgB,YAAY;YAC5B,gBAAgB,IAAI;YACpB,gBAAgB,KAAK;YACrB,gBAAgB,OAAO;SACxB,CAAC,MAAM,CAAC;QAET,cAAc,kBAAkB,IAAI,CAAC;QAErC,sDAAsD;QACtD,IAAI,aAAa;YACf,YAAY,GAAG,aAAa,GAAG,EAAE,aAAa;QAChD;IACF;IAEA,kEAAkE;IAClE,IAAI,cAAc;IAElB,IAAI,gBAAgB,MAAM,KAAK,UAAU;QACvC,cACE,CAAC,MAAM,EAAE,aAAa,uCAAuC,EAC3D,gBAAgB,SAAS,GAAG,gBAAgB,SAAS,GAAG,MAAM,GAC/D,mDAAmD,EAClD,cAAc,CAAC,IAAI,EAAE,aAAa,GAAG,GACtC,CAAC,CAAC,CAAC,IAAI;IACZ,OAAO;QACL,cAAc,GAAG,aAAa,iHAAiH,CAAC;IAClJ;IACA,MAAM,UAAU,gBAAgB,QAAQ,IAAI,GAAG,QAAQ,oBAAoB,CAAC;IAE5E,MAAM,WAAW;QACf;QACA;QACA,iBAAiB;QACjB,iBAAiB;QACjB;QACA;QACA;QACA;KACD,CAAC,MAAM,CAAC;IAET,MAAM,SAAS;QACb,YAAY;QACZ,SAAS;QACT,MAAM;QACN,aAAa,gBAAgB,SAAS,IAAI;QAC1C,KAAK;QACL,OAAO,gBAAgB,QAAQ;QAC/B,WAAW,iBAAiB;QAC5B,SAAS;YACP,SAAS;YACT,eAAe,gBAAgB,YAAY;YAC3C,iBAAiB,gBAAgB,IAAI;YACrC,eAAe,gBAAgB,KAAK;YACpC,YAAY,gBAAgB,OAAO;YACnC,gBAAgB;QAClB;IACF;IAEA,OAAO;QACL,OAAO;QACP;QACA,UAAU,SAAS,MAAM,CAAC,CAAC,IAAmB,MAAM;QACpD,YAAY;YACV,WAAW,CAAC,CAAC,EAAE,UAAU;QAC3B;QACA,WAAW;YACT,OAAO;YACP;YACA,KAAK;YACL,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;gBACN;oBACE,KAAK;oBACL,KAAK,GAAG,aAAa,wBAAwB,CAAC;gBAChD;aACD;QACH;QACA,SAAS;YACP,MAAM;YACN,OAAO;YACP;YACA,QAAQ;gBAAC;aAAQ;QACnB;QACA,OAAO;YACL,uBAAuB,KAAK,SAAS,CAAC;QACxC;IACF;AACF", "debugId": null}}]}