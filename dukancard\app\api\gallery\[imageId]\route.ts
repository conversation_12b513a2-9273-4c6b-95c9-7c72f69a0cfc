import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { applySecurityMiddleware } from "@/lib/middleware/security";
import { BUCKETS, COLUMNS, TABLES } from "@/lib/supabase/constants";
import { Json } from "@/types/supabase";

// Types
interface GalleryImage {
  id: string;
  url: string;
  path: string;
  created_at: string;
}

/**
 * DELETE /api/gallery/[imageId] - Delete a gallery image
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ imageId: string }> }
) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { user } = securityResult;
    const { imageId } = await params;
    const supabase = await createClient();

    if (!imageId) {
      return new NextResponse(JSON.stringify({ 
        error: "Image ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get the current gallery data
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.GALLERY)
      .eq(COLUMNS.ID, user.id)
      .single();

    if (profileError || !profileData) {
      return new NextResponse(JSON.stringify({ 
        error: "Failed to fetch business profile" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Find the image to delete
    const gallery = (Array.isArray(profileData?.gallery) ? profileData.gallery : []) as GalleryImage[];
    const imageToDelete = Array.isArray(gallery)
      ? gallery.find(img => img.id === imageId)
      : null;

    if (!imageToDelete) {
      return new NextResponse(JSON.stringify({ 
        error: "Image not found" 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Try to delete from storage
    let storageDeleteSuccess = false;
    let storageError = null;
    
    try {
      const { error: deleteError } = await supabase.storage
        .from(BUCKETS.BUSINESS)
        .remove([imageToDelete.path]);

      if (deleteError) {
        console.error("Storage deletion error:", {
          error: deleteError,
          path: imageToDelete.path,
          bucket: BUCKETS.BUSINESS
        });
        storageError = deleteError;
      } else {
        storageDeleteSuccess = true;
      }
    } catch (error) {
      console.error("Exception during storage deletion:", error);
      storageError = error;
    }

    // Update the gallery array in business_profiles
    const updatedGallery = Array.isArray(gallery)
      ? gallery.filter(img => img.id !== imageId)
      : [];

    const { error: updateError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update({ gallery: updatedGallery as Json })
      .eq(COLUMNS.ID, user.id);

    if (updateError) {
      return new NextResponse(JSON.stringify({ 
        error: `Failed to update gallery: ${updateError.message}` 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Return success with storage deletion status
    const response: any = {
      message: "Gallery image deleted successfully"
    };

    if (!storageDeleteSuccess) {
      response.warning = "Image removed from gallery, but storage cleanup may have failed";
    }

    return new NextResponse(JSON.stringify(response), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in gallery DELETE:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
