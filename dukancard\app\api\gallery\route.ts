import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { verifyJWTToken, extractBearerToken } from "@/lib/middleware/jwt";
import { getGalleryImagePath } from "@/lib/utils/storage-paths";
import { getGalleryLimit, canAddMoreGalleryImages } from "@/app/(dashboard)/dashboard/business/gallery/utils";
import { BUCKETS, COLUMNS, TABLES } from "@/lib/supabase/constants";
import { J<PERSON> } from "@/types/supabase";
import { z } from "zod";

// Types
interface GalleryImage {
  id: string;
  url: string;
  path: string;
  created_at: string;
}

const uploadGalleryImageSchema = z.object({
  image: z.instanceof(File).refine(
    (file) => ["image/png", "image/jpeg", "image/gif", "image/webp"].includes(file.type),
    "Invalid file type. Only PNG, JPEG, GIF, and WebP are allowed."
  ).refine(
    (file) => file.size <= 10 * 1024 * 1024, // 10MB
    "File size must be less than 10MB."
  ),
});

async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return { user: { id: jwtResult.payload.sub } };
}

/**
 * GET /api/gallery - Get gallery images for authenticated user
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { user } = securityResult;
    const supabase = await createClient();

    // Get the current gallery data
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.GALLERY)
      .eq(COLUMNS.ID, user.id)
      .single();

    if (profileError) {
      console.error("Error fetching gallery:", profileError);
      return new NextResponse(JSON.stringify({ 
        error: "Failed to fetch gallery images" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const gallery = (Array.isArray(profileData?.gallery) ? profileData.gallery : []) as GalleryImage[];
    const galleryLimit = getGalleryLimit();
    
    // Apply plan limit
    const limitedGallery = gallery.slice(0, galleryLimit);

    return new NextResponse(JSON.stringify({
      images: limitedGallery,
      totalCount: limitedGallery.length,
      limit: galleryLimit
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in gallery GET:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * POST /api/gallery - Upload a new gallery image
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { user } = securityResult;
    const supabase = await createClient();

    // Parse form data
    const formData = await req.formData();
    const file = formData.get("image") as File | null;

    if (!file) {
      return new NextResponse(JSON.stringify({ 
        error: "No image file provided" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate file
    const validation = uploadGalleryImageSchema.safeParse({ image: file });
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: validation.error.errors[0].message 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get current gallery to check limits
    const { data: profileData, error: profileError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .select(COLUMNS.GALLERY)
      .eq(COLUMNS.ID, user.id)
      .single();

    if (profileError) {
      console.error("Error fetching profile for gallery check:", profileError);
      return new NextResponse(JSON.stringify({ 
        error: "Failed to check gallery limits" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const gallery = (Array.isArray(profileData?.gallery) ? profileData.gallery : []) as GalleryImage[];
    
    // Check if user can add more images
    if (!canAddMoreGalleryImages(gallery.length)) {
      return new NextResponse(JSON.stringify({ 
        error: "Gallery limit reached. Please upgrade your plan to add more images." 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Upload image to storage
    const timestamp = new Date().getTime();
    const imagePath = getGalleryImagePath(user.id, timestamp);
    const fileBuffer = Buffer.from(await file.arrayBuffer());

    const { error: uploadError } = await supabase.storage
      .from(BUCKETS.BUSINESS)
      .upload(imagePath, fileBuffer, {
        contentType: file.type,
        upsert: true
      });

    if (uploadError) {
      console.error("Gallery Image Upload Error:", uploadError);
      return new NextResponse(JSON.stringify({ 
        error: `Failed to upload gallery image: ${uploadError.message}` 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get the public URL
    const { data: urlData } = supabase.storage
      .from(BUCKETS.BUSINESS)
      .getPublicUrl(imagePath);

    if (!urlData?.publicUrl) {
      return new NextResponse(JSON.stringify({ 
        error: "Could not retrieve public URL after upload" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Create new gallery image object
    const newImage: GalleryImage = {
      id: `gallery_${timestamp}`,
      url: urlData.publicUrl,
      path: imagePath,
      created_at: new Date().toISOString()
    };

    // Update the gallery array in business_profiles
    const updatedGallery = [...gallery, newImage];

    const { error: updateError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update({ gallery: updatedGallery as Json })
      .eq(COLUMNS.ID, user.id);

    if (updateError) {
      console.error("Gallery Update Error:", updateError);

      // Try to clean up the uploaded image if the database update fails
      await supabase.storage
        .from(BUCKETS.BUSINESS)
        .remove([imagePath]);

      return new NextResponse(JSON.stringify({ 
        error: `Failed to update gallery: ${updateError.message}` 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      image: newImage,
      message: "Gallery image uploaded successfully"
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in gallery POST:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * PATCH /api/gallery - Update gallery image order
 */
export async function PATCH(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { user } = securityResult;
    const supabase = await createClient();

    const body = await req.json();
    const { orderedImages } = body;

    if (!Array.isArray(orderedImages)) {
      return new NextResponse(JSON.stringify({ 
        error: "Invalid ordered images data" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update the gallery array in business_profiles with the new order
    const { error: updateError } = await supabase
      .from(TABLES.BUSINESS_PROFILES)
      .update({ gallery: orderedImages as Json })
      .eq(COLUMNS.ID, user.id);

    if (updateError) {
      console.error("Gallery Order Update Error:", updateError);
      return new NextResponse(JSON.stringify({ 
        error: `Failed to update gallery order: ${updateError.message}` 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: "Gallery order updated successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in gallery PATCH:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}