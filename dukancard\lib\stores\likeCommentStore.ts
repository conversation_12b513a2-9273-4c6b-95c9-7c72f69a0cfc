import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import { enableMapSet } from 'immer';
// import type { StateCreator } from 'zustand';

enableMapSet();
import type { PostLikeStatus, CommentLikeStatus, PostCommentWithUser } from '@/types/like-comment';
import {
  likePost,
  unlikePost,
  getPostLikeStatus,
  // togglePostLike
} from '@/lib/actions/likes/postLikes';
import {
  likeComment,
  unlikeComment,
  getCommentLikeStatus,
  // toggleCommentLike
} from '@/lib/actions/likes/commentLikes';
import { authenticatedApiCall } from '@/lib/utils/authenticatedFetch';
import {
  getPaginatedPostLikes,
  // getPaginatedPostComments,
  getPaginatedCommentReplies,
  // type PaginatedResponse,
  type PostLike,
  type PostComment
} from '@/lib/services/pagination/paginationService';

// State interfaces
interface PostLikeState {
  [postKey: string]: PostLikeStatus; // postKey format: "postId:postSource"
}

interface CommentLikeState {
  [commentId: string]: CommentLikeStatus;
}

interface PostCommentsState {
  [postKey: string]: {
    comments: PostCommentWithUser[];
    loading: boolean;
    hasMore: boolean;
    lastFetched: number;
    nextCursor?: string;
    prevCursor?: string;
  };
}

interface PostLikesListState {
  [postKey: string]: {
    likes: PostLike[];
    loading: boolean;
    hasMore: boolean;
    nextCursor?: string;
    prevCursor?: string;
    lastFetched: number;
  };
}

interface CommentRepliesState {
  [commentId: string]: {
    replies: PostComment[];
    loading: boolean;
    hasMore: boolean;
    nextCursor?: string;
    prevCursor?: string;
    lastFetched: number;
  };
}

interface LikeCommentStore {
  // State
  postLikes: PostLikeState;
  commentLikes: CommentLikeState;
  postComments: PostCommentsState;
  postLikesList: PostLikesListState;
  commentReplies: CommentRepliesState;

  // Loading states
  likingPosts: Set<string>; // postKeys currently being liked/unliked
  likingComments: Set<string>; // commentIds currently being liked/unliked
  loadingComments: Set<string>; // postKeys currently loading comments
  loadingLikesList: Set<string>; // postKeys currently loading likes list
  loadingReplies: Set<string>; // commentIds currently loading replies
  
  // Actions for post likes
  likePost: (_postId: string, _postSource: 'business' | 'customer') => Promise<void>;
  unlikePost: (_postId: string, _postSource: 'business' | 'customer') => Promise<void>;
  togglePostLike: (_postId: string, _postSource: 'business' | 'customer') => Promise<void>;
  getPostLikeStatus: (_postId: string, _postSource: 'business' | 'customer', _refresh?: boolean) => Promise<void>;
  
  // Actions for comment likes
  likeComment: (_commentId: string) => Promise<void>;
  unlikeComment: (_commentId: string) => Promise<void>;
  toggleCommentLike: (_commentId: string) => Promise<void>;
  getCommentLikeStatus: (_commentId: string) => Promise<void>;
  
  // Actions for comments
  createComment: (_postId: string, _postSource: 'business' | 'customer', _content: string, _parentCommentId?: string) => Promise<boolean>;
  editComment: (_commentId: string, _content: string) => Promise<boolean>;
  deleteComment: (_commentId: string, _postId: string, _postSource: 'business' | 'customer') => Promise<boolean>;
  pinComment: (_commentId: string, _postId: string, _postSource: 'business' | 'customer') => Promise<boolean>;
  unpinComment: (_commentId: string, _postId: string, _postSource: 'business' | 'customer') => Promise<boolean>;
  loadComments: (_postId: string, _postSource: 'business' | 'customer', _refresh?: boolean) => Promise<void>;

  // Pagination actions
  loadPostLikesList: (_postId: string, _postSource: 'business' | 'customer', _refresh?: boolean) => Promise<void>;
  loadMorePostLikes: (_postId: string, _postSource: 'business' | 'customer') => Promise<void>;
  loadCommentReplies: (_commentId: string, _refresh?: boolean) => Promise<void>;
  loadMoreCommentReplies: (_commentId: string) => Promise<void>;

  // Utility actions
  clearCache: () => void;
  clearPostCache: (_postId: string, _postSource: 'business' | 'customer') => void;
  cleanupExpiredCache: () => void;
  limitCacheSize: () => void;
}

const createPostKey = (postId: string, postSource: 'business' | 'customer') => `${postId}:${postSource}`;

export const useLikeCommentStore = create<LikeCommentStore>()(
  devtools(
    persist(
      immer((set: (_fn: (_draft: LikeCommentStore) => void) => void, get: () => LikeCommentStore) => ({
        // Initial state
        postLikes: {},
        commentLikes: {},
        postComments: {},
        postLikesList: {},
        commentReplies: {},
        likingPosts: new Set(),
        likingComments: new Set(),
        loadingComments: new Set(),
        loadingLikesList: new Set(),
        loadingReplies: new Set(),

        // Post like actions
        likePost: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          set((state: LikeCommentStore) => {
            state.likingPosts.add(postKey);
          });

          try {
            // Optimistic update
            set((state: LikeCommentStore) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = true;
                state.postLikes[postKey].like_count += 1;
              } else {
                state.postLikes[postKey] = { is_liked: true, like_count: 1 };
              }
            });

            const result = await likePost(postId, postSource);
            
            if (result.success && result.like_count !== undefined) {
              set((state: LikeCommentStore) => {
                state.postLikes[postKey] = {
                  is_liked: true,
                  like_count: result.like_count!
                };
              });
              // Force refresh from DB to ensure consistency
              get().getPostLikeStatus(postId, postSource, true);
            } else if (!result.success) {
              // Revert optimistic update
              set((state: LikeCommentStore) => {
                if (state.postLikes[postKey]) {
                  state.postLikes[postKey].is_liked = false;
                  state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
                }
              });
            }
          } catch (_error) {
            // Revert optimistic update
            set((state: LikeCommentStore) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = false;
                state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
              }
            });
          } finally {
            set((state: LikeCommentStore) => {
              state.likingPosts.delete(postKey);
            });
          }
        },

        unlikePost: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          
          set((state: LikeCommentStore) => {
            state.likingPosts.add(postKey);
          });

          try {
            // Optimistic update
            set((state: LikeCommentStore) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = false;
                state.postLikes[postKey].like_count = Math.max(0, state.postLikes[postKey].like_count - 1);
              }
            });

            const result = await unlikePost(postId, postSource);
            
            if (result.success && result.like_count !== undefined) {
              set((state: LikeCommentStore) => {
                state.postLikes[postKey] = {
                  is_liked: false,
                  like_count: result.like_count!
                };
              });
              // Force refresh from DB to ensure consistency
              get().getPostLikeStatus(postId, postSource, true);
            } else if (!result.success) {
              // Revert optimistic update
              set((state: LikeCommentStore) => {
                if (state.postLikes[postKey]) {
                  state.postLikes[postKey].is_liked = true;
                  state.postLikes[postKey].like_count += 1;
                }
              });
            }
          } catch (_error) {
            // Revert optimistic update
            set((state: LikeCommentStore) => {
              if (state.postLikes[postKey]) {
                state.postLikes[postKey].is_liked = true;
                state.postLikes[postKey].like_count += 1;
              }
            });
          } finally {
            set((state: LikeCommentStore) => {
              state.likingPosts.delete(postKey);
            });
          }
        },

        togglePostLike: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          const currentStatus = get().postLikes[postKey];
          
          if (currentStatus?.is_liked) {
            await get().unlikePost(postId, postSource);
          } else {
            await get().likePost(postId, postSource);
          }
        },

        getPostLikeStatus: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);

          try {
            const status = await getPostLikeStatus(postId, postSource);
            set((state: LikeCommentStore) => {
              state.postLikes[postKey] = {
                is_liked: status.is_liked,
                like_count: status.like_count
              };
            });
          } catch (_error) {
            console.error('Error fetching post like status:', _error);
          }
        },

        // Comment like actions (similar pattern)
        likeComment: async (commentId: string) => {
          set((state: LikeCommentStore) => {
            state.likingComments.add(commentId);
          });

          try {
            // Optimistic update
            set((state: LikeCommentStore) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = true;
                state.commentLikes[commentId].like_count += 1;
              } else {
                state.commentLikes[commentId] = { is_liked: true, like_count: 1 };
              }
            });

            const result = await likeComment(commentId);
            
            if (result.success && result.like_count !== undefined) {
              set((state: LikeCommentStore) => {
                state.commentLikes[commentId] = {
                  is_liked: true,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state: LikeCommentStore) => {
                if (state.commentLikes[commentId]) {
                  state.commentLikes[commentId].is_liked = false;
                  state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
                }
              });
            }
          } catch (_error) {
            // Revert optimistic update
            set((state: LikeCommentStore) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = false;
                state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
              }
            });
          } finally {
            set((state: LikeCommentStore) => {
              state.likingComments.delete(commentId);
            });
          }
        },

        unlikeComment: async (commentId: string) => {
          set((state: LikeCommentStore) => {
            state.likingComments.add(commentId);
          });

          try {
            // Optimistic update
            set((state: LikeCommentStore) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = false;
                state.commentLikes[commentId].like_count = Math.max(0, state.commentLikes[commentId].like_count - 1);
              }
            });

            const result = await unlikeComment(commentId);
            
            if (result.success && result.like_count !== undefined) {
              set((state: LikeCommentStore) => {
                state.commentLikes[commentId] = {
                  is_liked: false,
                  like_count: result.like_count!
                };
              });
            } else if (!result.success) {
              // Revert optimistic update
              set((state: LikeCommentStore) => {
                if (state.commentLikes[commentId]) {
                  state.commentLikes[commentId].is_liked = true;
                  state.commentLikes[commentId].like_count += 1;
                }
              });
            }
          } catch (_error) {
            // Revert optimistic update
            set((state: LikeCommentStore) => {
              if (state.commentLikes[commentId]) {
                state.commentLikes[commentId].is_liked = true;
                state.commentLikes[commentId].like_count += 1;
              }
            });
          } finally {
            set((state: LikeCommentStore) => {
              state.likingComments.delete(commentId);
            });
          }
        },

        toggleCommentLike: async (commentId: string) => {
          const currentStatus = get().commentLikes[commentId];
          
          if (currentStatus?.is_liked) {
            await get().unlikeComment(commentId);
          } else {
            await get().likeComment(commentId);
          }
        },

        getCommentLikeStatus: async (commentId: string) => {
          try {
            const status = await getCommentLikeStatus(commentId);
            set((state: LikeCommentStore) => {
              state.commentLikes[commentId] = {
                is_liked: status.is_liked,
                like_count: status.like_count
              };
            });
          } catch (_error) {
            console.error('Error fetching comment like status:', _error);
          }
        },

        // Comment CRUD actions
        createComment: async (postId: string, postSource: 'business' | 'customer', content: string, parentCommentId?: string) => {
          try {
            const response = await authenticatedApiCall('/api/comments', {
              method: 'POST',
              body: JSON.stringify({
                post_id: postId,
                post_source: postSource,
                content,
                parent_comment_id: parentCommentId,
              }),
            });

            if (!response.success) {
              throw new Error(response.error || 'Failed to create comment');
            }

            // Refresh comments for this post
            await get().loadComments(postId, postSource, true);
            return true;
          } catch (_error) {
            console.error('Error creating comment:', _error);
            return false;
          }
        },

        editComment: async (commentId: string, content: string) => {
          try {
            const response = await authenticatedApiCall(`/api/comments/${commentId}`, {
              method: 'PATCH',
              body: JSON.stringify({
                content,
              }),
            });

            if (!response.success) {
              throw new Error(response.error || 'Failed to edit comment');
            }

            const updatedComment = response.data?.comment;

            if (updatedComment) {
              // Update local state
              set((state: LikeCommentStore) => {
                for (const postKey in state.postComments) {
                  const comment = state.postComments[postKey].comments.find((c: PostCommentWithUser) => c.id === commentId);
                  if (comment) {
                    comment.content = updatedComment.content;
                    comment.updated_at = updatedComment.updated_at;
                    break;
                  }
                }
              });
            }
            return true;
          } catch (_error) {
            console.error('Error editing comment:', _error);
            return false;
          }
        },

        deleteComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          try {
            const response = await authenticatedApiCall(`/api/comments/${commentId}`, {
              method: 'DELETE',
            });

            if (!response.success) {
              throw new Error(response.error || 'Failed to delete comment');
            }

            // Remove comment from local state
            const postKey = createPostKey(postId, postSource);
            set((state: LikeCommentStore) => {
              if (state.postComments[postKey]) {
                state.postComments[postKey].comments = state.postComments[postKey].comments.filter(
                  (comment: PostCommentWithUser) => comment.id !== commentId
                );
              }
            });
            return true;
          } catch (_error) {
            console.error('Error deleting comment:', _error);
            return false;
          }
        },

        pinComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          try {
            const response = await authenticatedApiCall(`/api/comments/${commentId}`, {
              method: 'PATCH',
              body: JSON.stringify({
                is_pinned: true,
              }),
            });

            if (!response.success) {
              throw new Error(response.error || 'Failed to pin comment');
            }

            // Update local state
            const postKey = createPostKey(postId, postSource);
            set((state: LikeCommentStore) => {
              if (state.postComments[postKey]) {
                const comment = state.postComments[postKey].comments.find((c: PostCommentWithUser) => c.id === commentId);
                if (comment) {
                  comment.is_pinned = true;
                }
              }
            });
            return true;
          } catch (_error) {
            console.error('Error pinning comment:', _error);
            return false;
          }
        },

        unpinComment: async (commentId: string, postId: string, postSource: 'business' | 'customer') => {
          try {
            const response = await authenticatedApiCall(`/api/comments/${commentId}`, {
              method: 'PATCH',
              body: JSON.stringify({
                is_pinned: false,
              }),
            });

            if (!response.success) {
              throw new Error(response.error || 'Failed to unpin comment');
            }

            // Update local state
            const postKey = createPostKey(postId, postSource);
            set((state: LikeCommentStore) => {
              if (state.postComments[postKey]) {
                const comment = state.postComments[postKey].comments.find((c: PostCommentWithUser) => c.id === commentId);
                if (comment) {
                  comment.is_pinned = false;
                }
              }
            });
            return true;
          } catch (_error) {
            console.error('Error unpinning comment:', _error);
            return false;
          }
        },

        loadComments: async (postId: string, postSource: 'business' | 'customer', refresh = false) => {
          const postKey = createPostKey(postId, postSource);

          // Cleanup expired cache periodically
          if (Math.random() < 0.1) { // 10% chance to run cleanup
            get().cleanupExpiredCache();
            get().limitCacheSize();
          }

          // Check if already loading
          if (get().loadingComments.has(postKey)) {
            return;
          }

          // Check cache (5 minutes)
          const cached = get().postComments[postKey];
          if (!refresh && cached && (Date.now() - cached.lastFetched) < 5 * 60 * 1000) {
            return;
          }

          set((state: LikeCommentStore) => {
            state.loadingComments.add(postKey);
          });

          try {
            const response = await authenticatedApiCall(`/api/comments?post_id=${postId}&post_source=${postSource}&limit=50&page=1`);

            if (response.success && response.data) {
              set((state: LikeCommentStore) => {
                state.postComments[postKey] = {
                  comments: response.data.comments || [],
                  loading: false,
                  hasMore: (response.data.comments || []).length === 50,
                  lastFetched: Date.now()
                };
              });
            }
          } catch (_error) {
            console.error('Error loading comments:', _error);
          } finally {
            set((state: LikeCommentStore) => {
              state.loadingComments.delete(postKey);
            });
          }
        },

        // Pagination actions
        loadPostLikesList: async (postId: string, postSource: 'business' | 'customer', refresh = false) => {
          const postKey = createPostKey(postId, postSource);

          // Check if already loading
          if (get().loadingLikesList.has(postKey)) {
            return;
          }

          // Check cache (5 minutes)
          const cached = get().postLikesList[postKey];
          if (!refresh && cached && (Date.now() - cached.lastFetched) < 5 * 60 * 1000) {
            return;
          }

          set((state: LikeCommentStore) => {
            state.loadingLikesList.add(postKey);
          });

          try {
            const result = await getPaginatedPostLikes(postId, postSource, { limit: 20 });

            set((state: LikeCommentStore) => {
              state.postLikesList[postKey] = {
                likes: result.data,
                loading: false,
                hasMore: result.hasMore,
                nextCursor: result.nextCursor,
                prevCursor: result.prevCursor,
                lastFetched: Date.now()
              };
            });
          } catch (_error) {
            console.error('Error loading post likes list:', _error);
          } finally {
            set((state: LikeCommentStore) => {
              state.loadingLikesList.delete(postKey);
            });
          }
        },

        loadMorePostLikes: async (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          const currentData = get().postLikesList[postKey];

          if (!currentData || !currentData.hasMore || !currentData.nextCursor) {
            return;
          }

          try {
            const result = await getPaginatedPostLikes(postId, postSource, {
              limit: 20,
              cursor: currentData.nextCursor
            });

            set((state: LikeCommentStore) => {
              if (state.postLikesList[postKey]) {
                state.postLikesList[postKey].likes.push(...result.data);
                state.postLikesList[postKey].hasMore = result.hasMore;
                state.postLikesList[postKey].nextCursor = result.nextCursor;
              }
            });
          } catch (_error) {
            console.error('Error loading more post likes:', _error);
          }
        },

        loadCommentReplies: async (commentId: string, refresh = false) => {
          // Check if already loading
          if (get().loadingReplies.has(commentId)) {
            return;
          }

          // Check cache (5 minutes)
          const cached = get().commentReplies[commentId];
          if (!refresh && cached && (Date.now() - cached.lastFetched) < 5 * 60 * 1000) {
            return;
          }

          set((state: LikeCommentStore) => {
            state.loadingReplies.add(commentId);
          });

          try {
            const result = await getPaginatedCommentReplies(commentId, { limit: 10 });

            set((state: LikeCommentStore) => {
              state.commentReplies[commentId] = {
                replies: result.data,
                loading: false,
                hasMore: result.hasMore,
                nextCursor: result.nextCursor,
                prevCursor: result.prevCursor,
                lastFetched: Date.now()
              };
            });
          } catch (_error) {
            console.error('Error loading comment replies:', _error);
          } finally {
            set((state: LikeCommentStore) => {
              state.loadingReplies.delete(commentId);
            });
          }
        },

        loadMoreCommentReplies: async (commentId: string) => {
          const currentData = get().commentReplies[commentId];

          if (!currentData || !currentData.hasMore || !currentData.nextCursor) {
            return;
          }

          try {
            const result = await getPaginatedCommentReplies(commentId, {
              limit: 10,
              cursor: currentData.nextCursor
            });

            set((state: LikeCommentStore) => {
              if (state.commentReplies[commentId]) {
                state.commentReplies[commentId].replies.push(...result.data);
                state.commentReplies[commentId].hasMore = result.hasMore;
                state.commentReplies[commentId].nextCursor = result.nextCursor;
              }
            });
          } catch (_error) {
            console.error('Error loading more comment replies:', _error);
          }
        },

        // Utility actions
        clearCache: () => {
          set((state: LikeCommentStore) => {
            state.postLikes = {};
            state.commentLikes = {};
            state.postComments = {};
            state.postLikesList = {};
            state.commentReplies = {};
          });
        },

        clearPostCache: (postId: string, postSource: 'business' | 'customer') => {
          const postKey = createPostKey(postId, postSource);
          set((state: LikeCommentStore) => {
            delete state.postLikes[postKey];
            delete state.postComments[postKey];
            delete state.postLikesList[postKey];
          });
        },

        // Cleanup expired cache entries (older than 1 hour)
        cleanupExpiredCache: () => {
          const now = Date.now();
          const EXPIRY_TIME = 60 * 60 * 1000; // 1 hour

          set((state: LikeCommentStore) => {
            // Clean up post comments
            Object.keys(state.postComments).forEach(postKey => {
              const data = state.postComments[postKey];
              if (data && (now - data.lastFetched) > EXPIRY_TIME) {
                delete state.postComments[postKey];
              }
            });

            // Clean up post likes lists
            Object.keys(state.postLikesList).forEach(postKey => {
              const data = state.postLikesList[postKey];
              if (data && (now - data.lastFetched) > EXPIRY_TIME) {
                delete state.postLikesList[postKey];
              }
            });

            // Clean up comment replies
            Object.keys(state.commentReplies).forEach(commentId => {
              const data = state.commentReplies[commentId];
              if (data && (now - data.lastFetched) > EXPIRY_TIME) {
                delete state.commentReplies[commentId];
              }
            });

            // Keep post likes and comment likes as they're more frequently accessed
            // and smaller in size
          });
        },

        // Limit cache size to prevent memory bloat
        limitCacheSize: () => {
          const MAX_ENTRIES = 100;

          set((state: LikeCommentStore) => {
            // Limit post comments cache
            const commentKeys = Object.keys(state.postComments);
            if (commentKeys.length > MAX_ENTRIES) {
              const sortedKeys = commentKeys
                .map(key => ({ key, lastFetched: state.postComments[key]?.lastFetched || 0 }))
                .sort((a, b) => b.lastFetched - a.lastFetched)
                .slice(MAX_ENTRIES);

              sortedKeys.forEach(({ key }) => {
                delete state.postComments[key];
              });
            }

            // Limit post likes lists cache
            const likesKeys = Object.keys(state.postLikesList);
            if (likesKeys.length > MAX_ENTRIES) {
              const sortedKeys = likesKeys
                .map(key => ({ key, lastFetched: state.postLikesList[key]?.lastFetched || 0 }))
                .sort((a, b) => b.lastFetched - a.lastFetched)
                .slice(MAX_ENTRIES);

              sortedKeys.forEach(({ key }) => {
                delete state.postLikesList[key];
              });
            }

            // Limit comment replies cache
            const repliesKeys = Object.keys(state.commentReplies);
            if (repliesKeys.length > MAX_ENTRIES) {
              const sortedKeys = repliesKeys
                .map(key => ({ key, lastFetched: state.commentReplies[key]?.lastFetched || 0 }))
                .sort((a, b) => b.lastFetched - a.lastFetched)
                .slice(MAX_ENTRIES);

              sortedKeys.forEach(({ key }) => {
                delete state.commentReplies[key];
              });
            }
          });
        },
      })),
      {
        name: 'like-comment-store',
        partialize: (state: LikeCommentStore) => ({
          postLikes: state.postLikes,
          commentLikes: state.commentLikes,
          // Don't persist comments as they can become stale
        }),
      }
    ),
    {
      name: 'like-comment-store',
    }
  )
);
