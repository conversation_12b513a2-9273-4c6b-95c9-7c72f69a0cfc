import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';
import { 
  extractBearerToken, 
  verifyJWTToken 
} from '@/lib/middleware/jwt';

/**
 * Security middleware wrapper for profile existence check
 */
async function applySecurityMiddleware(req: NextRequest) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'profile_exists_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  return { success: true, jwtPayload: jwtResult.payload };
}

/**
 * GET /api/customer/profile/exists - Check if current user has a customer profile
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Check if customer profile exists
    const { data, error } = await supabase
      .from('customer_profiles')
      .select('id')
      .eq('id', jwtPayload.sub)
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error checking customer profile existence:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to check profile existence' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const exists = !error && !!data;

    return new NextResponse(JSON.stringify({
      exists,
      profile_type: 'customer',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in customer profile exists API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
