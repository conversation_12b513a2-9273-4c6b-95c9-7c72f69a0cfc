import { supabase } from "@/lib/supabase";
import { FeedQueryParams } from "@/lib/types/posts";
import {
  PostCreationState,
  EnhancedFeedResponse,
} from "@/src/utils/feed/postCreationHandler";

export interface UnifiedPost {
  id: string;
  post_source: "business" | "customer";
  author_id: string;
  content: string;
  image_url: string | null;
  created_at: string;
  updated_at: string;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  pincode: string | null;
  product_ids: string[];
  mentioned_business_ids: string[];
  author_name: string | null;
  author_avatar: string | null;
  business_slug: string | null; // Business slug for business posts, null for customer posts
  phone: string | null; // Phone number for business posts, null for customer posts
  whatsapp_number: string | null; // WhatsApp number for business posts, null for customer posts
  business_plan: string | null; // Plan for business posts, null for customer posts
  comment_count: number; // Total number of comments on the post
  total_likes: number; // Total number of likes on the post
  total_comments: number; // Total number of comments on the post
}

export interface UnifiedFeedResponse {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    items: UnifiedPost[];
    totalCount: number;
    hasMore: boolean;
  };
}

/**
 * Get unified feed posts (business + customer posts) with proper pagination
 * Supports post creation state for immediate feedback when user creates a post
 */
export async function getUnifiedFeedPosts(
  params: FeedQueryParams,
  creationState?: PostCreationState
): Promise<EnhancedFeedResponse> {
  // Use the imported supabase client
  const {
    filter = "smart",
    page = 1,
    limit = 10,
    city_slug,
    state_slug,
    locality_slug,
    pincode,
  } = params;

  try {
    // Get current user for smart and subscribed filters
    const {
      data: { session },
    } = await supabase.auth.getSession();

    // Build query parameters for the posts API
    const queryParams = new URLSearchParams();
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('filter', filter);

    // Add location filters
    if (city_slug) {
      queryParams.set('city_slug', city_slug);
    }
    if (state_slug) {
      queryParams.set('state_slug', state_slug);
    }
    if (locality_slug) {
      queryParams.set('locality_slug', locality_slug);
    }
    if (pincode) {
      queryParams.set('pincode', pincode);
    }

    // Use the posts API endpoint
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authentication if available
    if (session) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/posts?${queryParams.toString()}`, {
      method: 'GET',
      headers,
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("Posts API Fetch Error:", result);
      return {
        success: false,
        message: "Failed to fetch posts",
        error: result.error || "Failed to fetch posts",
        data: {
          items: [],
          totalCount: 0,
          hasMore: false,
          hasJustCreatedPost: false,
        },
      };
    }

    const posts = result.posts || [];
    const pagination = result.pagination || {};
    const totalCount = pagination.total || 0;
    const hasMore = pagination.hasMore || false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      success: true,
      message: "Posts fetched successfully",
      data: {
        items: posts,
        totalCount,
        hasMore,
        hasJustCreatedPost: false,
      },
    };
  } catch (error) {
    console.error("Unexpected error in getUnifiedFeedPosts:", error);
    return {
      success: false,
      message: "An unexpected error occurred",
      error: error instanceof Error ? error.message : "Unknown error",
      data: {
        items: [],
        totalCount: 0,
        hasMore: false,
        hasJustCreatedPost: false,
      },
    };
  }
}

/**
 * Get unified feed posts with author information populated
 * Author information is now included directly in the unified_posts view
 */
export async function getUnifiedFeedPostsWithAuthors(
  params: FeedQueryParams,
  creationState?: PostCreationState
): Promise<EnhancedFeedResponse> {
  // Since author information is now included in the unified_posts view,
  // we can just return the result from getUnifiedFeedPosts directly
  return await getUnifiedFeedPosts(params, creationState);
}
