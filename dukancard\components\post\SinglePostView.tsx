'use client';

import { UnifiedPost } from '@/lib/actions/posts/unifiedFeed';
import UnifiedPostCard from '@/components/feed/shared/UnifiedPostCard';
import CommentSection, { useCommentSection } from '@/components/ui/CommentSection';
import PostLikesList from './PostLikesList';
import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useLikeCommentStore } from '@/lib/stores/likeCommentStore';
import { formatIndianNumberShort } from '@/lib/utils/formatNumber';

interface SinglePostViewProps {
  post: UnifiedPost;
}

/**
 * Client component for displaying a single post
 * Uses the same UnifiedPostCard component as the feed for exact consistency
 */
export default function SinglePostView({ post }: SinglePostViewProps) {
  const [currentUserId, setCurrentUserId] = useState<string | undefined>();
  const [authLoading, setAuthLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'comments' | 'likes'>('comments');

  // Get current user ID from Supabase auth
  useEffect(() => {
    const getCurrentUser = async () => {
      try {
        const supabase = createClient();
        const { data: { user } } = await supabase.auth.getUser();
        setCurrentUserId(user?.id);
      } catch (error) {
        console.error('Error getting current user:', error);
        setCurrentUserId(undefined);
      } finally {
        setAuthLoading(false);
      }
    };

    getCurrentUser();
  }, []);

  // Handle URL hash for direct navigation to comments/likes
  useEffect(() => {
    const hash = window.location.hash;
    if (hash === '#comments') {
      setActiveTab('comments');
    } else if (hash === '#likes') {
      setActiveTab('likes');
    }
  }, []);

  // Determine if current user is the post owner
  const isPostOwner = currentUserId === post.author_id;

  // Counts for tabs
  const { commentCount } = useCommentSection(post.id, post.post_source);
  const { postLikesList } = useLikeCommentStore();
  const postKey = `${post.id}:${post.post_source}`;
  const likesData = postLikesList[postKey];
  const likesCount = likesData?.likes?.length ?? post.total_likes ?? 0;

  return (
    <>
      {/* Mobile/Tablet: Full width single column layout */}
      <div className="block xl:hidden bg-white dark:bg-black min-h-screen">
        <div className="w-full">
          {/* Post Card */}
          <div className="px-4 py-4">
            <UnifiedPostCard
              post={post}
              index={0}
              showActualAspectRatio={true}
              disablePostClick={true}
              enableImageFullscreen={true}
              currentUserId={currentUserId}
              requireAuth={!authLoading}
            />
          </div>

          {/* Tab Headers - Full width without card styling */}
          <div className="w-full border-t border-gray-200 dark:border-gray-800">
            <div className="flex">
              <button
                onClick={() => setActiveTab('comments')}
                className={`flex-1 px-6 py-4 text-sm font-medium transition-colors border-b-2 cursor-pointer ${
                  activeTab === 'comments'
                    ? 'text-amber-600 dark:text-amber-400 border-amber-600 dark:border-amber-400 bg-white dark:bg-black'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-transparent bg-white dark:bg-black'
                }`}
              >
                {`Comments (${formatIndianNumberShort(commentCount) ?? 0})`}
              </button>
              <button
                onClick={() => setActiveTab('likes')}
                className={`flex-1 px-6 py-4 text-sm font-medium transition-colors border-b-2 cursor-pointer ${
                  activeTab === 'likes'
                    ? 'text-amber-600 dark:text-amber-400 border-amber-600 dark:border-amber-400 bg-white dark:bg-black'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-transparent bg-white dark:bg-black'
                }`}
              >
                {`Likes (${formatIndianNumberShort(likesCount) ?? 0})`}
              </button>
            </div>
          </div>

          {/* Tab Content - Full width */}
          <div className="w-full min-h-[60vh] bg-white dark:bg-black">
            {activeTab === 'comments' ? (
              <CommentSection
                postId={post.id}
                postSource={post.post_source}
                currentUserId={currentUserId}
                isPostOwner={isPostOwner}
                showInput={true}
                maxHeight="none"
                className="w-full"
                authLoading={authLoading}
                showRefreshControls={false}
              />
            ) : (
              <PostLikesList
                postId={post.id}
                postSource={post.post_source}
                initialLikeCount={0}
                className="w-full"
              />
            )}
          </div>
        </div>
      </div>

      {/* Desktop: Two column layout with proper spacing */}
      <div className="hidden xl:block bg-white dark:bg-black min-h-screen">
        <div className="max-w-[1800px] mx-auto px-8 py-6">
          <div className="grid grid-cols-2 gap-8 h-[calc(100vh-3rem)]">
            {/* Left Column: Post - Sticky */}
            <div className="flex flex-col">
              <div className="sticky top-6 max-h-[calc(100vh-3rem)] overflow-y-auto">
                <UnifiedPostCard
                  post={post}
                  index={0}
                  showActualAspectRatio={true}
                  disablePostClick={true}
                  enableImageFullscreen={true}
                  currentUserId={currentUserId}
                  requireAuth={!authLoading}
                />
              </div>
            </div>

            {/* Right Column: Comments/Likes - Scrollable */}
            <div className="flex flex-col h-full">
              <div className="bg-white dark:bg-black rounded-lg border border-gray-200 dark:border-gray-800 flex flex-col h-full">
                {/* Tab Headers */}
                <div className="flex border-b border-gray-200 dark:border-gray-800 flex-shrink-0">
                  <button
                    onClick={() => setActiveTab('comments')}
                    className={`flex-1 px-6 py-4 text-sm font-medium transition-colors border-b-2 ${
                      activeTab === 'comments'
                        ? 'text-amber-600 dark:text-amber-400 border-amber-600 dark:border-amber-400'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-transparent'
                    }`}
                  >
                    {`Comments (${formatIndianNumberShort(commentCount) ?? 0})`}
                  </button>
                  <button
                    onClick={() => setActiveTab('likes')}
                    className={`flex-1 px-6 py-4 text-sm font-medium transition-colors border-b-2 ${
                      activeTab === 'likes'
                        ? 'text-amber-600 dark:text-amber-400 border-amber-600 dark:border-amber-400'
                        : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 border-transparent'
                    }`}
                  >
                    {`Likes (${formatIndianNumberShort(likesCount) ?? 0})`}
                  </button>
                </div>

                {/* Tab Content - Scrollable */}
                <div className="flex-1 overflow-y-auto">
                  {activeTab === 'comments' ? (
                    <CommentSection
                      postId={post.id}
                      postSource={post.post_source}
                      currentUserId={currentUserId}
                      isPostOwner={isPostOwner}
                      showInput={true}
                      maxHeight="none"
                      className="h-full"
                      authLoading={authLoading}
                      showRefreshControls={false}
                    />
                  ) : (
                    <PostLikesList
                      postId={post.id}
                      postSource={post.post_source}
                      initialLikeCount={0}
                      className="h-full"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}




