import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { z } from 'zod';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';
import { 
  extractBearerToken, 
  verifyJWTToken 
} from '@/lib/middleware/jwt';
import { 
  verifyHMACMiddleware 
} from '@/lib/middleware/hmac';

/**
 * Security middleware wrapper for product API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'product_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature if required
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, requireHMAC);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { success: true, jwtPayload: jwtResult.payload };
}

/**
 * GET /api/products/[id] - Get a specific product
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Apply security middleware (no HMAC required for public product access)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Validate ID format
    if (!id || typeof id !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid product ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Fetch product with business information
    const { data: product, error } = await supabase
      .from('products_services')
      .select(`
        id, business_id, name, description, base_price, discounted_price,
        product_type, is_available, image_url, images, featured_image_index,
        slug, created_at, updated_at,
        business_profiles!business_id(
          id, business_name, business_slug, city, state, pincode, locality,
          logo_url, status, phone, whatsapp_number
        )
      `)
      .eq('id', id)
      .maybeSingle();

    if (error) {
      console.error('Error fetching product:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch product' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!product) {
      return new NextResponse(JSON.stringify({ error: 'Product not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      product,
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in get product API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for updating products
const updateProductSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  base_price: z.number().min(0).optional(),
  discounted_price: z.number().min(0).optional(),
  product_type: z.enum(["physical", "service"]).optional(),
  is_available: z.boolean().optional(),
  image_url: z.string().url().optional(),
  images: z.array(z.string().url()).optional(),
  featured_image_index: z.number().min(0).optional(),
  slug: z.string().optional(),
});

/**
 * PATCH /api/products/[id] - Update a specific product
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate ID format
    if (!id || typeof id !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid product ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = updateProductSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request body',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updateData = validation.data;
    const supabase = await createClient();

    // Update the product (RLS will ensure user can only update their own products)
    const { data: product, error } = await supabase
      .from('products_services')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .eq('business_id', jwtPayload.sub) // Ensure user owns the product
      .select()
      .single();

    if (error) {
      console.error('Error updating product:', error);
      if (error.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ error: 'Product not found or access denied' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      return new NextResponse(JSON.stringify({ error: 'Failed to update product' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      product,
      message: 'Product updated successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in update product API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * DELETE /api/products/[id] - Delete a specific product
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate ID format
    if (!id || typeof id !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid product ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Delete the product (RLS will ensure user can only delete their own products)
    const { error } = await supabase
      .from('products_services')
      .delete()
      .eq('id', id)
      .eq('business_id', jwtPayload.sub); // Ensure user owns the product

    if (error) {
      console.error('Error deleting product:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to delete product' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: 'Product deleted successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in delete product API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
