{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/jwt.ts"], "sourcesContent": ["import { NextRequest } from 'next/server';\nimport jwt from 'jsonwebtoken';\n\nexport interface JWTPayload {\n  sub: string; // user ID\n  user_id: string;\n  roles?: string[];\n  iat: number;\n  exp: number;\n}\n\n/**\n * Extract Bearer token from Authorization header or NextRequest\n */\nexport function extractBearerToken(input: string | NextRequest): string | null {\n  let authHeader: string | null = null;\n\n  if (typeof input === 'string') {\n    authHeader = input;\n  } else {\n    // NextRequest\n    authHeader = input.headers.get('authorization') || input.headers.get('Authorization');\n  }\n\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return null;\n  }\n\n  return authHeader.substring(7); // Remove 'Bearer ' prefix\n}\n\n/**\n * Verify JWT token and return payload\n */\nexport async function verifyJWTToken(token: string): Promise<{\n  success: boolean;\n  payload?: JWTPayload;\n  error?: string;\n}> {\n  try {\n    const secret = process.env.JWT_SECRET;\n    if (!secret) {\n      return { success: false, error: 'JWT secret not configured' };\n    }\n\n    const payload = jwt.verify(token, secret) as JWTPayload;\n    \n    // Ensure user_id is set for backward compatibility\n    if (payload.sub && !payload.user_id) {\n      payload.user_id = payload.sub;\n    }\n\n    return { success: true, payload };\n  } catch (error) {\n    if (error instanceof jwt.TokenExpiredError) {\n      return { success: false, error: 'Token expired' };\n    } else if (error instanceof jwt.JsonWebTokenError) {\n      return { success: false, error: 'Invalid token' };\n    } else {\n      return { success: false, error: 'Token verification failed' };\n    }\n  }\n}\n\n/**\n * Generate JWT token\n */\nexport function generateJWTToken(payload: Omit<JWTPayload, 'iat' | 'exp'>, expiresIn: string = '1h'): string {\n  const secret = process.env.JWT_SECRET;\n  if (!secret) {\n    throw new Error('JWT secret not configured');\n  }\n\n  const now = Math.floor(Date.now() / 1000);\n  const fullPayload: JWTPayload = {\n    ...payload,\n    iat: now,\n    exp: now + (expiresIn === '1h' ? 3600 : parseInt(expiresIn)),\n  };\n\n  return jwt.sign(fullPayload, secret);\n}\n"], "names": [], "mappings": ";;;;;AACA;;AAaO,SAAS,mBAAmB,KAA2B;IAC5D,IAAI,aAA4B;IAEhC,IAAI,OAAO,UAAU,UAAU;QAC7B,aAAa;IACf,OAAO;QACL,cAAc;QACd,aAAa,MAAM,OAAO,CAAC,GAAG,CAAC,oBAAoB,MAAM,OAAO,CAAC,GAAG,CAAC;IACvE;IAEA,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;QACpD,OAAO;IACT;IAEA,OAAO,WAAW,SAAS,CAAC,IAAI,0BAA0B;AAC5D;AAKO,eAAe,eAAe,KAAa;IAKhD,IAAI;QACF,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;QACrC,IAAI,CAAC,QAAQ;YACX,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;QAEA,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAElC,mDAAmD;QACnD,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,OAAO,EAAE;YACnC,QAAQ,OAAO,GAAG,QAAQ,GAAG;QAC/B;QAEA,OAAO;YAAE,SAAS;YAAM;QAAQ;IAClC,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;YAC1C,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD,OAAO,IAAI,iBAAiB,uIAAA,CAAA,UAAG,CAAC,iBAAiB,EAAE;YACjD,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAAgB;QAClD,OAAO;YACL,OAAO;gBAAE,SAAS;gBAAO,OAAO;YAA4B;QAC9D;IACF;AACF;AAKO,SAAS,iBAAiB,OAAwC,EAAE,YAAoB,IAAI;IACjG,MAAM,SAAS,QAAQ,GAAG,CAAC,UAAU;IACrC,IAAI,CAAC,QAAQ;QACX,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACpC,MAAM,cAA0B;QAC9B,GAAG,OAAO;QACV,KAAK;QACL,KAAK,MAAM,CAAC,cAAc,OAAO,OAAO,SAAS,UAAU;IAC7D;IAEA,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,aAAa;AAC/B", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/security/hmac.ts"], "sourcesContent": ["import crypto from 'crypto';\r\n\r\n/**\r\n * Generates an HMAC-SHA256 signature for API request validation\r\n * @param method - HTTP method (GET, POST, etc.)\r\n * @param path - API path (/api/auth/login)\r\n * @param timestamp - Unix timestamp in milliseconds\r\n * @param body - Request body (empty string for GET requests)\r\n * @param deviceSecret - The device secret (plaintext)\r\n * @returns Base64 encoded HMAC signature\r\n */\r\nexport function generateHMACSignature(\r\n  method: string,\r\n  path: string,\r\n  timestamp: string,\r\n  body: string,\r\n  deviceSecret: string\r\n): string {\r\n  // 1. Create SHA256 hash of the request body\r\n  const bodyHash = crypto\r\n    .createHash('sha256')\r\n    .update(body || '')\r\n    .digest('hex');\r\n\r\n  // 2. Create the string to be signed: method + path + timestamp + bodyHash\r\n  const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;\r\n\r\n  // 3. Generate HMAC-SHA256 signature using device secret\r\n  const signature = crypto\r\n    .createHmac('sha256', deviceSecret)\r\n    .update(stringToSign)\r\n    .digest('base64');\r\n\r\n  return signature;\r\n}\r\n\r\n/**\r\n * Verifies an HMAC signature using constant-time comparison\r\n * @param receivedSignature - The signature from the client\r\n * @param expectedSignature - The signature calculated by the server\r\n * @returns true if signatures match, false otherwise\r\n */\r\nexport function verifyHMACSignature(\r\n  receivedSignature: string,\r\n  expectedSignature: string\r\n): boolean {\r\n  // Convert to buffers for constant-time comparison\r\n  const receivedBuffer = Buffer.from(receivedSignature, 'base64');\r\n  const expectedBuffer = Buffer.from(expectedSignature, 'base64');\r\n\r\n  // Ensure both signatures are the same length to prevent timing attacks\r\n  if (receivedBuffer.length !== expectedBuffer.length) {\r\n    return false;\r\n  }\r\n\r\n  // Use crypto.timingSafeEqual for constant-time comparison\r\n  return crypto.timingSafeEqual(receivedBuffer, expectedBuffer);\r\n}\r\n\r\n/**\r\n * Validates timestamp to prevent replay attacks\r\n * @param timestamp - Unix timestamp in milliseconds as string\r\n * @param windowSeconds - Allowed time window in seconds (default: 120)\r\n * @returns true if timestamp is within the allowed window\r\n */\r\nexport function validateTimestamp(\r\n  timestamp: string,\r\n  windowSeconds: number = 120\r\n): boolean {\r\n  const requestTime = parseInt(timestamp, 10);\r\n  const currentTime = Date.now();\r\n  const windowMs = windowSeconds * 1000;\r\n\r\n  // Check if timestamp is a valid number\r\n  if (isNaN(requestTime)) {\r\n    return false;\r\n  }\r\n\r\n  // Check if request is within the allowed time window\r\n  const timeDiff = Math.abs(currentTime - requestTime);\r\n  return timeDiff <= windowMs;\r\n}\r\n\r\n/**\r\n * Extracts required headers for HMAC verification\r\n * @param headers - Request headers\r\n * @returns Object with deviceId, timestamp, and signature, or null if any are missing\r\n */\r\nexport function extractHMACHeaders(headers: Headers | any): {\r\n  deviceId: string;\r\n  timestamp: string;\r\n  signature: string;\r\n} | null {\r\n  const deviceId = typeof headers.get === 'function' \r\n    ? headers.get('x-device-id') \r\n    : headers['x-device-id'];\r\n  const timestamp = typeof headers.get === 'function'\r\n    ? headers.get('x-timestamp')\r\n    : headers['x-timestamp'];\r\n  const signature = typeof headers.get === 'function'\r\n    ? headers.get('x-signature')\r\n    : headers['x-signature'];\r\n\r\n  if (!deviceId || !timestamp || !signature) {\r\n    return null;\r\n  }\r\n\r\n  return { deviceId, timestamp, signature };\r\n}"], "names": [], "mappings": ";;;;;;AAAA;;AAWO,SAAS,sBACd,MAAc,EACd,IAAY,EACZ,SAAiB,EACjB,IAAY,EACZ,YAAoB;IAEpB,4CAA4C;IAC5C,MAAM,WAAW,qGAAA,CAAA,UAAM,CACpB,UAAU,CAAC,UACX,MAAM,CAAC,QAAQ,IACf,MAAM,CAAC;IAEV,0EAA0E;IAC1E,MAAM,eAAe,GAAG,OAAO,WAAW,KAAK,OAAO,YAAY,UAAU;IAE5E,wDAAwD;IACxD,MAAM,YAAY,qGAAA,CAAA,UAAM,CACrB,UAAU,CAAC,UAAU,cACrB,MAAM,CAAC,cACP,MAAM,CAAC;IAEV,OAAO;AACT;AAQO,SAAS,oBACd,iBAAyB,EACzB,iBAAyB;IAEzB,kDAAkD;IAClD,MAAM,iBAAiB,OAAO,IAAI,CAAC,mBAAmB;IACtD,MAAM,iBAAiB,OAAO,IAAI,CAAC,mBAAmB;IAEtD,uEAAuE;IACvE,IAAI,eAAe,MAAM,KAAK,eAAe,MAAM,EAAE;QACnD,OAAO;IACT;IAEA,0DAA0D;IAC1D,OAAO,qGAAA,CAAA,UAAM,CAAC,eAAe,CAAC,gBAAgB;AAChD;AAQO,SAAS,kBACd,SAAiB,EACjB,gBAAwB,GAAG;IAE3B,MAAM,cAAc,SAAS,WAAW;IACxC,MAAM,cAAc,KAAK,GAAG;IAC5B,MAAM,WAAW,gBAAgB;IAEjC,uCAAuC;IACvC,IAAI,MAAM,cAAc;QACtB,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc;IACxC,OAAO,YAAY;AACrB;AAOO,SAAS,mBAAmB,OAAsB;IAKvD,MAAM,WAAW,OAAO,QAAQ,GAAG,KAAK,aACpC,QAAQ,GAAG,CAAC,iBACZ,OAAO,CAAC,cAAc;IAC1B,MAAM,YAAY,OAAO,QAAQ,GAAG,KAAK,aACrC,QAAQ,GAAG,CAAC,iBACZ,OAAO,CAAC,cAAc;IAC1B,MAAM,YAAY,OAAO,QAAQ,GAAG,KAAK,aACrC,QAAQ,GAAG,CAAC,iBACZ,OAAO,CAAC,cAAc;IAE1B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW;QACzC,OAAO;IACT;IAEA,OAAO;QAAE;QAAU;QAAW;IAAU;AAC1C", "debugId": null}}, {"offset": {"line": 291, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/service-role.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport function createServiceRoleClient() {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseServiceRoleKey) {\r\n    throw new Error('Missing required Supabase environment variables for service role');\r\n  }\r\n\r\n  return createClient<Database>(supabaseUrl, supabaseServiceRoleKey, {\r\n    auth: {\r\n      autoRefreshToken: false,\r\n      persistSession: false,\r\n    },\r\n  });\r\n}"], "names": [], "mappings": ";;;AAAA;;AAGO,SAAS;IACd,MAAM;IACN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;IAEpE,IAAI,CAAC,eAAe,CAAC,wBAAwB;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa,wBAAwB;QACjE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/hmac.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { \r\n  generateHMACSignature, \r\n  verifyHMACSignature, \r\n  validateTimestamp, \r\n  extractHMACHeaders \r\n} from '@/lib/security/hmac';\r\nimport { compareSecret } from '@/lib/security/hashing';\r\nimport { createServiceRoleClient } from '@/utils/supabase/service-role';\r\n\r\nexport interface HMACVerificationResult {\r\n  success: boolean;\r\n  error?: string;\r\n  status?: number;\r\n  deviceId?: string;\r\n}\r\n\r\n/**\r\n * Middleware function to verify HMAC signatures on protected API requests\r\n * @param req - Next.js request object\r\n * @param requireHMAC - Whether HMAC verification is required (default: true)\r\n * @returns Result object indicating success/failure and error details\r\n */\r\nexport async function verifyHMACMiddleware(\r\n  req: NextRequest,\r\n  requireHMAC: boolean = true\r\n): Promise<HMACVerificationResult> {\r\n  try {\r\n    // Skip HMAC verification if not required (e.g., for login endpoint)\r\n    if (!requireHMAC) {\r\n      return { success: true };\r\n    }\r\n\r\n    // 1. Extract required headers\r\n    const hmacHeaders = extractHMACHeaders(req.headers);\r\n    if (!hmacHeaders) {\r\n      return {\r\n        success: false,\r\n        error: 'Missing required headers: X-Device-Id, X-Timestamp, or X-Signature',\r\n        status: 400,\r\n      };\r\n    }\r\n\r\n    const { deviceId, timestamp, signature } = hmacHeaders;\r\n\r\n    // 2. Validate timestamp to prevent replay attacks\r\n    if (!validateTimestamp(timestamp)) {\r\n      return {\r\n        success: false,\r\n        error: 'Request has expired',\r\n        status: 408,\r\n      };\r\n    }\r\n\r\n    // 3. Fetch device from database\r\n    const supabase = createServiceRoleClient();\r\n    const { data: device, error: deviceError } = await supabase\r\n      .from('devices')\r\n      .select('device_id, device_secret_hash, hmac_key_hash, revoked')\r\n      .eq('device_id', deviceId)\r\n      .single();\r\n\r\n    if (deviceError || !device) {\r\n      return {\r\n        success: false,\r\n        error: 'Invalid device ID',\r\n        status: 403,\r\n      };\r\n    }\r\n\r\n    // 4. Check if device is revoked\r\n    if (device.revoked) {\r\n      return {\r\n        success: false,\r\n        error: 'Device has been revoked',\r\n        status: 403,\r\n      };\r\n    }\r\n\r\n    // 5. Get request body for signature verification\r\n    let requestBody = '';\r\n    try {\r\n      // Clone the request to read the body without consuming it\r\n      const clonedReq = req.clone();\r\n      requestBody = await clonedReq.text();\r\n    } catch (error) {\r\n      requestBody = '';\r\n    }\r\n\r\n    // 6. Generate expected signature using stored HMAC key\r\n    const method = req.method;\r\n    const path = new URL(req.url).pathname;\r\n    \r\n    const expectedSignature = generateHMACSignature(\r\n      method,\r\n      path,\r\n      timestamp,\r\n      requestBody,\r\n      device.hmac_key_hash || '' // Use the stored HMAC key\r\n    );\r\n\r\n    // 7. Verify signature using constant-time comparison\r\n    const isValidSignature = verifyHMACSignature(signature, expectedSignature);\r\n\r\n    if (!isValidSignature) {\r\n      return {\r\n        success: false,\r\n        error: 'Invalid signature',\r\n        status: 403,\r\n      };\r\n    }\r\n\r\n    // 8. HMAC verification successful\r\n    return {\r\n      success: true,\r\n      deviceId: deviceId,\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error in HMAC verification:', error);\r\n    return {\r\n      success: false,\r\n      error: 'Internal Server Error',\r\n      status: 500,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Next.js middleware wrapper for HMAC verification\r\n * @param req - Next.js request object\r\n * @param requireHMAC - Whether HMAC verification is required\r\n * @returns NextResponse or null to continue\r\n */\r\nexport async function hmacMiddleware(\r\n  req: NextRequest,\r\n  requireHMAC: boolean = true\r\n): Promise<NextResponse | null> {\r\n  const result = await verifyHMACMiddleware(req, requireHMAC);\r\n  \r\n  if (!result.success) {\r\n    return new NextResponse(JSON.stringify({ error: result.error }), {\r\n      status: result.status || 500,\r\n      headers: { 'Content-Type': 'application/json' },\r\n    });\r\n  }\r\n  \r\n  return null; // Continue to next middleware/handler\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAOA;;;;AAeO,eAAe,qBACpB,GAAgB,EAChB,cAAuB,IAAI;IAE3B,IAAI;QACF,oEAAoE;QACpE,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,8BAA8B;QAC9B,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,OAAO;QAClD,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;QAE3C,kDAAkD;QAClD,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,gCAAgC;QAChC,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD;QACvC,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,yDACP,EAAE,CAAC,aAAa,UAChB,MAAM;QAET,IAAI,eAAe,CAAC,QAAQ;YAC1B,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,gCAAgC;QAChC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,iDAAiD;QACjD,IAAI,cAAc;QAClB,IAAI;YACF,0DAA0D;YAC1D,MAAM,YAAY,IAAI,KAAK;YAC3B,cAAc,MAAM,UAAU,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,cAAc;QAChB;QAEA,uDAAuD;QACvD,MAAM,SAAS,IAAI,MAAM;QACzB,MAAM,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ;QAEtC,MAAM,oBAAoB,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAC5C,QACA,MACA,WACA,aACA,OAAO,aAAa,IAAI,GAAG,0BAA0B;;QAGvD,qDAAqD;QACrD,MAAM,mBAAmB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;QAExD,IAAI,CAAC,kBAAkB;YACrB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,UAAU;QACZ;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAQO,eAAe,eACpB,GAAgB,EAChB,cAAuB,IAAI;IAE3B,MAAM,SAAS,MAAM,qBAAqB,KAAK;IAE/C,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO,OAAO,KAAK;QAAC,IAAI;YAC/D,QAAQ,OAAO,MAAM,IAAI;YACzB,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,OAAO,MAAM,sCAAsC;AACrD", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/rateLimiter.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { Redis } from '@upstash/redis';\r\n\r\n// Initialize Redis client\r\nconst redis = new Redis({\r\n  url: process.env.UPSTASH_REDIS_REST_URL!,\r\n  token: process.env.UPSTASH_REDIS_REST_TOKEN!,\r\n});\r\n\r\nexport interface RateLimitConfig {\r\n  /** Maximum requests allowed */\r\n  maxRequests: number;\r\n  /** Time window in seconds */\r\n  windowSeconds: number;\r\n  /** Identifier for this rate limit (e.g., 'ip', 'device', 'user') */\r\n  identifier: string;\r\n}\r\n\r\nexport interface RateLimitResult {\r\n  success: boolean;\r\n  error?: string;\r\n  status?: number;\r\n  limit?: number;\r\n  remaining?: number;\r\n  reset?: number;\r\n  retryAfter?: number;\r\n}\r\n\r\n/**\r\n * Get client IP address from request\r\n */\r\nfunction getClientIP(req: NextRequest): string {\r\n  const forwarded = req.headers.get('x-forwarded-for');\r\n  const realIP = req.headers.get('x-real-ip');\r\n  const remoteAddress = req.headers.get('x-remote-address');\r\n  \r\n  if (forwarded) {\r\n    return forwarded.split(',')[0].trim();\r\n  }\r\n  \r\n  return realIP || remoteAddress || 'unknown';\r\n}\r\n\r\n/**\r\n * Generate Redis key for rate limiting\r\n */\r\nfunction generateRateLimitKey(identifier: string, value: string, windowStart: number): string {\r\n  return `ratelimit:${identifier}:${value}:${windowStart}`;\r\n}\r\n\r\n/**\r\n * Get the start of the current time window\r\n */\r\nfunction getWindowStart(windowSeconds: number): number {\r\n  const now = Math.floor(Date.now() / 1000);\r\n  return Math.floor(now / windowSeconds) * windowSeconds;\r\n}\r\n\r\n/**\r\n * Check rate limit for a specific identifier and value\r\n */\r\nasync function checkRateLimit(\r\n  identifier: string,\r\n  value: string,\r\n  config: RateLimitConfig\r\n): Promise<RateLimitResult> {\r\n  try {\r\n    const windowStart = getWindowStart(config.windowSeconds);\r\n    const key = generateRateLimitKey(identifier, value, windowStart);\r\n    const windowEnd = windowStart + config.windowSeconds;\r\n\r\n    // Get current count\r\n    const currentCount = await redis.get<number>(key) || 0;\r\n\r\n    if (currentCount >= config.maxRequests) {\r\n      // Rate limit exceeded\r\n      const retryAfter = windowEnd - Math.floor(Date.now() / 1000);\r\n      return {\r\n        success: false,\r\n        error: 'Rate limit exceeded',\r\n        status: 429,\r\n        limit: config.maxRequests,\r\n        remaining: 0,\r\n        reset: windowEnd,\r\n        retryAfter: Math.max(retryAfter, 0),\r\n      };\r\n    }\r\n\r\n    // Increment counter\r\n    const newCount = await redis.incr(key);\r\n    \r\n    // Set expiration if this is the first request in the window\r\n    if (newCount === 1) {\r\n      await redis.expire(key, config.windowSeconds);\r\n    }\r\n\r\n    // Calculate remaining requests\r\n    const remaining = Math.max(config.maxRequests - newCount, 0);\r\n\r\n    return {\r\n      success: true,\r\n      limit: config.maxRequests,\r\n      remaining,\r\n      reset: windowEnd,\r\n    };\r\n  } catch (error) {\r\n    console.error(`Rate limit check failed for ${identifier}:${value}:`, error);\r\n    \r\n    // In case of Redis error, allow the request but log the error\r\n    return {\r\n      success: true,\r\n      limit: config.maxRequests,\r\n      remaining: config.maxRequests,\r\n      reset: Math.floor(Date.now() / 1000) + config.windowSeconds,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Rate limiting middleware with multiple strategies\r\n */\r\nexport async function rateLimitMiddleware(\r\n  req: NextRequest,\r\n  strategies: Array<{ \r\n    identifier: string; \r\n    getValue: (req: NextRequest) => string | null; \r\n    config: RateLimitConfig \r\n  }> = []\r\n): Promise<RateLimitResult> {\r\n  // Default strategies if none provided\r\n  if (strategies.length === 0) {\r\n    const defaultMaxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);\r\n    const defaultWindowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || '60', 10);\r\n\r\n    strategies = [\r\n      {\r\n        identifier: 'ip',\r\n        getValue: (req) => getClientIP(req),\r\n        config: {\r\n          maxRequests: defaultMaxRequests,\r\n          windowSeconds: defaultWindowSeconds,\r\n          identifier: 'ip',\r\n        },\r\n      },\r\n    ];\r\n  }\r\n\r\n  let finalResult: RateLimitResult = {\r\n    success: true,\r\n    limit: strategies[0]?.config.maxRequests || 100,\r\n    remaining: strategies[0]?.config.maxRequests || 100,\r\n    reset: Math.floor(Date.now() / 1000) + (strategies[0]?.config.windowSeconds || 60),\r\n  };\r\n\r\n  // Apply strategies in order\r\n  for (const strategy of strategies) {\r\n    const value = strategy.getValue(req);\r\n    \r\n    // Skip strategy if value cannot be determined\r\n    if (!value || value === 'unknown') {\r\n      continue;\r\n    }\r\n\r\n    const result = await checkRateLimit(strategy.identifier, value, strategy.config);\r\n    \r\n    if (!result.success) {\r\n      return result;\r\n    }\r\n\r\n    // Update final result with the most restrictive limits\r\n    if (result.remaining !== undefined && result.remaining < (finalResult.remaining || Infinity)) {\r\n      finalResult = {\r\n        ...finalResult,\r\n        limit: result.limit,\r\n        remaining: result.remaining,\r\n        reset: result.reset,\r\n      };\r\n    }\r\n  }\r\n\r\n  return finalResult;\r\n}\r\n\r\n/**\r\n * Apply rate limiting strategies with common configurations\r\n */\r\nexport async function applyRateLimiting(\r\n  req: NextRequest,\r\n  options: {\r\n    deviceId?: string;\r\n    userId?: string;\r\n    byIP?: boolean;\r\n    byDevice?: boolean;\r\n    byUser?: boolean;\r\n    customLimits?: {\r\n      ip?: RateLimitConfig;\r\n      device?: RateLimitConfig;\r\n      user?: RateLimitConfig;\r\n    };\r\n  } = {}\r\n): Promise<RateLimitResult> {\r\n  const strategies: Array<{\r\n    identifier: string;\r\n    getValue: (req: NextRequest) => string | null;\r\n    config: RateLimitConfig;\r\n  }> = [];\r\n\r\n  // Default configurations\r\n  const defaultIPLimit: RateLimitConfig = {\r\n    maxRequests: parseInt(process.env.RATE_LIMIT_IP_MAX_REQUESTS || '100', 10),\r\n    windowSeconds: parseInt(process.env.RATE_LIMIT_IP_WINDOW_SECONDS || '60', 10),\r\n    identifier: 'ip',\r\n  };\r\n\r\n  const defaultDeviceLimit: RateLimitConfig = {\r\n    maxRequests: parseInt(process.env.RATE_LIMIT_DEVICE_MAX_REQUESTS || '200', 10),\r\n    windowSeconds: parseInt(process.env.RATE_LIMIT_DEVICE_WINDOW_SECONDS || '60', 10),\r\n    identifier: 'device',\r\n  };\r\n\r\n  const defaultUserLimit: RateLimitConfig = {\r\n    maxRequests: parseInt(process.env.RATE_LIMIT_USER_MAX_REQUESTS || '500', 10),\r\n    windowSeconds: parseInt(process.env.RATE_LIMIT_USER_WINDOW_SECONDS || '60', 10),\r\n    identifier: 'user',\r\n  };\r\n\r\n  // Add IP rate limiting (enabled by default)\r\n  if (options.byIP !== false) {\r\n    strategies.push({\r\n      identifier: 'ip',\r\n      getValue: (req) => getClientIP(req),\r\n      config: options.customLimits?.ip || defaultIPLimit,\r\n    });\r\n  }\r\n\r\n  // Add device rate limiting\r\n  if (options.byDevice && options.deviceId) {\r\n    strategies.push({\r\n      identifier: 'device',\r\n      getValue: () => options.deviceId!,\r\n      config: options.customLimits?.device || defaultDeviceLimit,\r\n    });\r\n  }\r\n\r\n  // Add user rate limiting\r\n  if (options.byUser && options.userId) {\r\n    strategies.push({\r\n      identifier: 'user',\r\n      getValue: () => options.userId!,\r\n      config: options.customLimits?.user || defaultUserLimit,\r\n    });\r\n  }\r\n\r\n  return rateLimitMiddleware(req, strategies);\r\n}\r\n\r\n/**\r\n * Next.js middleware wrapper for rate limiting\r\n */\r\nexport async function rateLimitingMiddleware(\r\n  req: NextRequest,\r\n  options: Parameters<typeof applyRateLimiting>[1] = {}\r\n): Promise<NextResponse | null> {\r\n  const result = await applyRateLimiting(req, options);\r\n\r\n  if (!result.success) {\r\n    const response = new NextResponse(JSON.stringify({ error: result.error }), {\r\n      status: result.status || 429,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'X-RateLimit-Limit': result.limit?.toString() || '',\r\n        'X-RateLimit-Remaining': result.remaining?.toString() || '0',\r\n        'X-RateLimit-Reset': result.reset?.toString() || '',\r\n        'Retry-After': result.retryAfter?.toString() || '',\r\n      },\r\n    });\r\n\r\n    return response;\r\n  }\r\n\r\n  // Add rate limit headers to successful responses\r\n  // Note: This will be handled by the calling code since we're returning null to continue\r\n  return null;\r\n}\r\n\r\n/**\r\n * Add rate limit headers to response\r\n */\r\nexport function addRateLimitHeaders(\r\n  response: NextResponse,\r\n  result: RateLimitResult\r\n): NextResponse {\r\n  if (result.limit !== undefined) {\r\n    response.headers.set('X-RateLimit-Limit', result.limit.toString());\r\n  }\r\n  if (result.remaining !== undefined) {\r\n    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());\r\n  }\r\n  if (result.reset !== undefined) {\r\n    response.headers.set('X-RateLimit-Reset', result.reset.toString());\r\n  }\r\n  \r\n  return response;\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AAEA,0BAA0B;AAC1B,MAAM,QAAQ,IAAI,+JAAA,CAAA,QAAK,CAAC;IACtB,KAAK,QAAQ,GAAG,CAAC,sBAAsB;IACvC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;AAC7C;AAqBA;;CAEC,GACD,SAAS,YAAY,GAAgB;IACnC,MAAM,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC;IAClC,MAAM,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IAC/B,MAAM,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC;IAEtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU,iBAAiB;AACpC;AAEA;;CAEC,GACD,SAAS,qBAAqB,UAAkB,EAAE,KAAa,EAAE,WAAmB;IAClF,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,EAAE,aAAa;AAC1D;AAEA;;CAEC,GACD,SAAS,eAAe,aAAqB;IAC3C,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACpC,OAAO,KAAK,KAAK,CAAC,MAAM,iBAAiB;AAC3C;AAEA;;CAEC,GACD,eAAe,eACb,UAAkB,EAClB,KAAa,EACb,MAAuB;IAEvB,IAAI;QACF,MAAM,cAAc,eAAe,OAAO,aAAa;QACvD,MAAM,MAAM,qBAAqB,YAAY,OAAO;QACpD,MAAM,YAAY,cAAc,OAAO,aAAa;QAEpD,oBAAoB;QACpB,MAAM,eAAe,MAAM,MAAM,GAAG,CAAS,QAAQ;QAErD,IAAI,gBAAgB,OAAO,WAAW,EAAE;YACtC,sBAAsB;YACtB,MAAM,aAAa,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YACvD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,OAAO,OAAO,WAAW;gBACzB,WAAW;gBACX,OAAO;gBACP,YAAY,KAAK,GAAG,CAAC,YAAY;YACnC;QACF;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC;QAElC,4DAA4D;QAC5D,IAAI,aAAa,GAAG;YAClB,MAAM,MAAM,MAAM,CAAC,KAAK,OAAO,aAAa;QAC9C;QAEA,+BAA+B;QAC/B,MAAM,YAAY,KAAK,GAAG,CAAC,OAAO,WAAW,GAAG,UAAU;QAE1D,OAAO;YACL,SAAS;YACT,OAAO,OAAO,WAAW;YACzB;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;QAErE,8DAA8D;QAC9D,OAAO;YACL,SAAS;YACT,OAAO,OAAO,WAAW;YACzB,WAAW,OAAO,WAAW;YAC7B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,OAAO,aAAa;QAC7D;IACF;AACF;AAKO,eAAe,oBACpB,GAAgB,EAChB,aAIK,EAAE;IAEP,sCAAsC;IACtC,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,MAAM,qBAAqB,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI,OAAO;QAClF,MAAM,uBAAuB,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI,MAAM;QAErF,aAAa;YACX;gBACE,YAAY;gBACZ,UAAU,CAAC,MAAQ,YAAY;gBAC/B,QAAQ;oBACN,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;SACD;IACH;IAEA,IAAI,cAA+B;QACjC,SAAS;QACT,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,eAAe;QAC5C,WAAW,UAAU,CAAC,EAAE,EAAE,OAAO,eAAe;QAChD,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,iBAAiB,EAAE;IACnF;IAEA,4BAA4B;IAC5B,KAAK,MAAM,YAAY,WAAY;QACjC,MAAM,QAAQ,SAAS,QAAQ,CAAC;QAEhC,8CAA8C;QAC9C,IAAI,CAAC,SAAS,UAAU,WAAW;YACjC;QACF;QAEA,MAAM,SAAS,MAAM,eAAe,SAAS,UAAU,EAAE,OAAO,SAAS,MAAM;QAE/E,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,IAAI,OAAO,SAAS,KAAK,aAAa,OAAO,SAAS,GAAG,CAAC,YAAY,SAAS,IAAI,QAAQ,GAAG;YAC5F,cAAc;gBACZ,GAAG,WAAW;gBACd,OAAO,OAAO,KAAK;gBACnB,WAAW,OAAO,SAAS;gBAC3B,OAAO,OAAO,KAAK;YACrB;QACF;IACF;IAEA,OAAO;AACT;AAKO,eAAe,kBACpB,GAAgB,EAChB,UAWI,CAAC,CAAC;IAEN,MAAM,aAID,EAAE;IAEP,yBAAyB;IACzB,MAAM,iBAAkC;QACtC,aAAa,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI,OAAO;QACvE,eAAe,SAAS,QAAQ,GAAG,CAAC,4BAA4B,IAAI,MAAM;QAC1E,YAAY;IACd;IAEA,MAAM,qBAAsC;QAC1C,aAAa,SAAS,QAAQ,GAAG,CAAC,8BAA8B,IAAI,OAAO;QAC3E,eAAe,SAAS,QAAQ,GAAG,CAAC,gCAAgC,IAAI,MAAM;QAC9E,YAAY;IACd;IAEA,MAAM,mBAAoC;QACxC,aAAa,SAAS,QAAQ,GAAG,CAAC,4BAA4B,IAAI,OAAO;QACzE,eAAe,SAAS,QAAQ,GAAG,CAAC,8BAA8B,IAAI,MAAM;QAC5E,YAAY;IACd;IAEA,4CAA4C;IAC5C,IAAI,QAAQ,IAAI,KAAK,OAAO;QAC1B,WAAW,IAAI,CAAC;YACd,YAAY;YACZ,UAAU,CAAC,MAAQ,YAAY;YAC/B,QAAQ,QAAQ,YAAY,EAAE,MAAM;QACtC;IACF;IAEA,2BAA2B;IAC3B,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,EAAE;QACxC,WAAW,IAAI,CAAC;YACd,YAAY;YACZ,UAAU,IAAM,QAAQ,QAAQ;YAChC,QAAQ,QAAQ,YAAY,EAAE,UAAU;QAC1C;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,EAAE;QACpC,WAAW,IAAI,CAAC;YACd,YAAY;YACZ,UAAU,IAAM,QAAQ,MAAM;YAC9B,QAAQ,QAAQ,YAAY,EAAE,QAAQ;QACxC;IACF;IAEA,OAAO,oBAAoB,KAAK;AAClC;AAKO,eAAe,uBACpB,GAAgB,EAChB,UAAmD,CAAC,CAAC;IAErD,MAAM,SAAS,MAAM,kBAAkB,KAAK;IAE5C,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,WAAW,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO,OAAO,KAAK;QAAC,IAAI;YACzE,QAAQ,OAAO,MAAM,IAAI;YACzB,SAAS;gBACP,gBAAgB;gBAChB,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,yBAAyB,OAAO,SAAS,EAAE,cAAc;gBACzD,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,eAAe,OAAO,UAAU,EAAE,cAAc;YAClD;QACF;QAEA,OAAO;IACT;IAEA,iDAAiD;IACjD,wFAAwF;IACxF,OAAO;AACT;AAKO,SAAS,oBACd,QAAsB,EACtB,MAAuB;IAEvB,IAAI,OAAO,KAAK,KAAK,WAAW;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,KAAK,CAAC,QAAQ;IACjE;IACA,IAAI,OAAO,SAAS,KAAK,WAAW;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,SAAS,CAAC,QAAQ;IACzE;IACA,IAAI,OAAO,KAAK,KAAK,WAAW;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,KAAK,CAAC,QAAQ;IACjE;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/bruteForceProtection.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { applyRateLimiting, RateLimitResult, RateLimitConfig } from './rateLimiter';\r\n\r\nexport interface BruteForceProtectionConfig {\r\n  /** Maximum login attempts per IP per hour (default: 10) */\r\n  maxLoginAttemptsPerIP?: number;\r\n  /** Time window for login attempts in seconds (default: 3600 = 1 hour) */\r\n  loginWindowSeconds?: number;\r\n  /** Maximum login attempts per email per hour (default: 5) */\r\n  maxLoginAttemptsPerEmail?: number;\r\n  /** Maximum token refresh attempts per device per hour (default: 20) */\r\n  maxRefreshAttemptsPerDevice?: number;\r\n  /** Time window for token refresh in seconds (default: 3600 = 1 hour) */\r\n  refreshWindowSeconds?: number;\r\n  /** Whether to enable progressive delays (default: true) */\r\n  enableProgressiveDelays?: boolean;\r\n}\r\n\r\nexport interface BruteForceContext {\r\n  /** IP address of the request */\r\n  ipAddress?: string;\r\n  /** Email being used for login (if applicable) */\r\n  email?: string;\r\n  /** Device ID for token refresh (if applicable) */\r\n  deviceId?: string;\r\n  /** User ID (if available) */\r\n  userId?: string;\r\n  /** Type of authentication operation */\r\n  operation: 'login' | 'refresh' | 'device_register' | 'send_otp' | 'verify_otp' |\r\n             'business_api' | 'business_access_check' | 'business_search' | 'sitemap_generation' |\r\n             'profile_exists_api' | 'customer_profile_api' | 'likes_api' | 'location_api' |\r\n             'posts_api' | 'product_api' | 'products_api' | 'storage_api';\r\n}\r\n\r\n/**\r\n * Calculate progressive delay based on attempt count\r\n * Implements exponential backoff with jitter\r\n */\r\nexport function calculateProgressiveDelay(attemptCount: number): number {\r\n  if (attemptCount <= 3) return 0;\r\n  \r\n  // Base delay starts at 1 second for 4th attempt\r\n  const baseDelay = Math.pow(2, attemptCount - 4) * 1000;\r\n  \r\n  // Cap at 30 seconds\r\n  const cappedDelay = Math.min(baseDelay, 30000);\r\n  \r\n  // Add jitter (±20%) but ensure result doesn't exceed cap\r\n  const jitter = cappedDelay * 0.2 * (Math.random() - 0.5);\r\n  const finalDelay = cappedDelay + jitter;\r\n  \r\n  // Ensure final delay doesn't exceed the cap and is not negative\r\n  return Math.floor(Math.max(0, Math.min(finalDelay, 30000)));\r\n}\r\n\r\n/**\r\n * Apply brute-force protection for authentication endpoints\r\n */\r\nexport async function applyBruteForceProtection(\r\n  req: NextRequest,\r\n  context: BruteForceContext,\r\n  config: BruteForceProtectionConfig = {}\r\n): Promise<RateLimitResult> {\r\n  const {\r\n    maxLoginAttemptsPerIP = parseInt(process.env.BRUTE_FORCE_LOGIN_IP_LIMIT || '10', 10),\r\n    loginWindowSeconds = parseInt(process.env.BRUTE_FORCE_LOGIN_WINDOW || '3600', 10),\r\n    maxLoginAttemptsPerEmail = parseInt(process.env.BRUTE_FORCE_EMAIL_LIMIT || '5', 10),\r\n    maxRefreshAttemptsPerDevice = parseInt(process.env.BRUTE_FORCE_REFRESH_LIMIT || '20', 10),\r\n    refreshWindowSeconds = parseInt(process.env.BRUTE_FORCE_REFRESH_WINDOW || '3600', 10),\r\n    enableProgressiveDelays = true,\r\n  } = config;\r\n\r\n  const strategies: Parameters<typeof applyRateLimiting>[1] = {};\r\n\r\n  switch (context.operation) {\r\n    case 'login':\r\n      // For login, we apply both IP-based and email-based rate limiting\r\n      strategies.byIP = true;\r\n      strategies.customLimits = {\r\n        ip: {\r\n          maxRequests: maxLoginAttemptsPerIP,\r\n          windowSeconds: loginWindowSeconds,\r\n          identifier: 'login_ip',\r\n        } as RateLimitConfig,\r\n      };\r\n\r\n      // If we have an email, add email-based rate limiting\r\n      if (context.email) {\r\n        strategies.byUser = true;\r\n        strategies.userId = `email:${context.email}`;\r\n        strategies.customLimits.user = {\r\n          maxRequests: maxLoginAttemptsPerEmail,\r\n          windowSeconds: loginWindowSeconds,\r\n          identifier: 'login_email',\r\n        } as RateLimitConfig;\r\n      }\r\n      break;\r\n\r\n    case 'refresh':\r\n      // For token refresh, we apply device-based and IP-based rate limiting\r\n      strategies.byIP = true;\r\n      strategies.customLimits = {\r\n        ip: {\r\n          maxRequests: maxRefreshAttemptsPerDevice * 2, // More lenient for IP\r\n          windowSeconds: refreshWindowSeconds,\r\n          identifier: 'refresh_ip',\r\n        } as RateLimitConfig,\r\n      };\r\n\r\n      if (context.deviceId) {\r\n        strategies.byDevice = true;\r\n        strategies.deviceId = context.deviceId;\r\n        strategies.customLimits.device = {\r\n          maxRequests: maxRefreshAttemptsPerDevice,\r\n          windowSeconds: refreshWindowSeconds,\r\n          identifier: 'refresh_device',\r\n        } as RateLimitConfig;\r\n      }\r\n      break;\r\n\r\n    case 'device_register':\r\n      // For device registration, apply IP-based rate limiting\r\n      strategies.byIP = true;\r\n      strategies.customLimits = {\r\n        ip: {\r\n          maxRequests: 5, // Very restrictive for device registration\r\n          windowSeconds: 3600,\r\n          identifier: 'device_register_ip',\r\n        } as RateLimitConfig,\r\n      };\r\n      break;\r\n\r\n    default:\r\n      return {\r\n        success: false,\r\n        error: 'Invalid operation type',\r\n        status: 400,\r\n      };\r\n  }\r\n\r\n  const result = await applyRateLimiting(req, strategies);\r\n\r\n  // If rate limiting failed and progressive delays are enabled,\r\n  // add delay information to the result\r\n  if (!result.success && enableProgressiveDelays) {\r\n    const attemptCount = (result.limit || 0) - (result.remaining || 0);\r\n    const delay = calculateProgressiveDelay(attemptCount);\r\n    \r\n    if (delay > 0) {\r\n      result.retryAfter = Math.max(result.retryAfter || 0, Math.ceil(delay / 1000));\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Middleware wrapper for brute-force protection\r\n * Returns a NextResponse if the request should be blocked, null if it should continue\r\n */\r\nexport async function bruteForceProtectionMiddleware(\r\n  req: NextRequest,\r\n  context: BruteForceContext,\r\n  config: BruteForceProtectionConfig = {}\r\n): Promise<NextResponse | null> {\r\n  const result = await applyBruteForceProtection(req, context, config);\r\n\r\n  if (!result.success) {\r\n    const response = new NextResponse(JSON.stringify({ \r\n      error: result.error,\r\n      retryAfter: result.retryAfter,\r\n      message: 'Too many attempts. Please try again later.',\r\n    }), {\r\n      status: result.status || 429,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'X-RateLimit-Limit': result.limit?.toString() || '',\r\n        'X-RateLimit-Remaining': result.remaining?.toString() || '0',\r\n        'X-RateLimit-Reset': result.reset?.toString() || '',\r\n        'Retry-After': result.retryAfter?.toString() || '',\r\n      },\r\n    });\r\n\r\n    return response;\r\n  }\r\n\r\n  return null; // Continue to next middleware/handler\r\n}\r\n\r\n/**\r\n * Extract email from login request body safely\r\n */\r\nexport function extractEmailFromLoginRequest(body: any): string | undefined {\r\n  try {\r\n    if (typeof body === 'object' && body !== null && typeof body.email === 'string') {\r\n      return body.email.toLowerCase().trim();\r\n    }\r\n  } catch (error) {\r\n    // Ignore parsing errors\r\n  }\r\n  return undefined;\r\n}\r\n\r\n/**\r\n * Extract device ID from request body safely\r\n */\r\nexport function extractDeviceIdFromRequest(body: any): string | undefined {\r\n  try {\r\n    if (typeof body === 'object' && body !== null && typeof body.deviceId === 'string') {\r\n      return body.deviceId;\r\n    }\r\n  } catch (error) {\r\n    // Ignore parsing errors\r\n  }\r\n  return undefined;\r\n}\r\n\r\n/**\r\n * Get client IP address from request (same as rate limiter)\r\n */\r\nexport function getClientIP(req: NextRequest): string {\r\n  const forwarded = req.headers.get('x-forwarded-for');\r\n  const realIP = req.headers.get('x-real-ip');\r\n  const remoteAddress = req.headers.get('x-remote-address');\r\n  \r\n  if (forwarded) {\r\n    return forwarded.split(',')[0].trim();\r\n  }\r\n  \r\n  return realIP || remoteAddress || 'unknown';\r\n}"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAqCO,SAAS,0BAA0B,YAAoB;IAC5D,IAAI,gBAAgB,GAAG,OAAO;IAE9B,gDAAgD;IAChD,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,eAAe,KAAK;IAElD,oBAAoB;IACpB,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW;IAExC,yDAAyD;IACzD,MAAM,SAAS,cAAc,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG;IACvD,MAAM,aAAa,cAAc;IAEjC,gEAAgE;IAChE,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY;AACrD;AAKO,eAAe,0BACpB,GAAgB,EAChB,OAA0B,EAC1B,SAAqC,CAAC,CAAC;IAEvC,MAAM,EACJ,wBAAwB,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI,MAAM,GAAG,EACpF,qBAAqB,SAAS,QAAQ,GAAG,CAAC,wBAAwB,IAAI,QAAQ,GAAG,EACjF,2BAA2B,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI,KAAK,GAAG,EACnF,8BAA8B,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI,MAAM,GAAG,EACzF,uBAAuB,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI,QAAQ,GAAG,EACrF,0BAA0B,IAAI,EAC/B,GAAG;IAEJ,MAAM,aAAsD,CAAC;IAE7D,OAAQ,QAAQ,SAAS;QACvB,KAAK;YACH,kEAAkE;YAClE,WAAW,IAAI,GAAG;YAClB,WAAW,YAAY,GAAG;gBACxB,IAAI;oBACF,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YAEA,qDAAqD;YACrD,IAAI,QAAQ,KAAK,EAAE;gBACjB,WAAW,MAAM,GAAG;gBACpB,WAAW,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBAC5C,WAAW,YAAY,CAAC,IAAI,GAAG;oBAC7B,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YACA;QAEF,KAAK;YACH,sEAAsE;YACtE,WAAW,IAAI,GAAG;YAClB,WAAW,YAAY,GAAG;gBACxB,IAAI;oBACF,aAAa,8BAA8B;oBAC3C,eAAe;oBACf,YAAY;gBACd;YACF;YAEA,IAAI,QAAQ,QAAQ,EAAE;gBACpB,WAAW,QAAQ,GAAG;gBACtB,WAAW,QAAQ,GAAG,QAAQ,QAAQ;gBACtC,WAAW,YAAY,CAAC,MAAM,GAAG;oBAC/B,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YACA;QAEF,KAAK;YACH,wDAAwD;YACxD,WAAW,IAAI,GAAG;YAClB,WAAW,YAAY,GAAG;gBACxB,IAAI;oBACF,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YACA;QAEF;YACE,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;IACJ;IAEA,MAAM,SAAS,MAAM,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;IAE5C,8DAA8D;IAC9D,sCAAsC;IACtC,IAAI,CAAC,OAAO,OAAO,IAAI,yBAAyB;QAC9C,MAAM,eAAe,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,IAAI,CAAC;QACjE,MAAM,QAAQ,0BAA0B;QAExC,IAAI,QAAQ,GAAG;YACb,OAAO,UAAU,GAAG,KAAK,GAAG,CAAC,OAAO,UAAU,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ;QACzE;IACF;IAEA,OAAO;AACT;AAMO,eAAe,+BACpB,GAAgB,EAChB,OAA0B,EAC1B,SAAqC,CAAC,CAAC;IAEvC,MAAM,SAAS,MAAM,0BAA0B,KAAK,SAAS;IAE7D,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,WAAW,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAC/C,OAAO,OAAO,KAAK;YACnB,YAAY,OAAO,UAAU;YAC7B,SAAS;QACX,IAAI;YACF,QAAQ,OAAO,MAAM,IAAI;YACzB,SAAS;gBACP,gBAAgB;gBAChB,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,yBAAyB,OAAO,SAAS,EAAE,cAAc;gBACzD,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,eAAe,OAAO,UAAU,EAAE,cAAc;YAClD;QACF;QAEA,OAAO;IACT;IAEA,OAAO,MAAM,sCAAsC;AACrD;AAKO,SAAS,6BAA6B,IAAS;IACpD,IAAI;QACF,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,KAAK,KAAK,UAAU;YAC/E,OAAO,KAAK,KAAK,CAAC,WAAW,GAAG,IAAI;QACtC;IACF,EAAE,OAAO,OAAO;IACd,wBAAwB;IAC1B;IACA,OAAO;AACT;AAKO,SAAS,2BAA2B,IAAS;IAClD,IAAI;QACF,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,QAAQ,KAAK,UAAU;YAClF,OAAO,KAAK,QAAQ;QACtB;IACF,EAAE,OAAO,OAAO;IACd,wBAAwB;IAC1B;IACA,OAAO;AACT;AAKO,SAAS,YAAY,GAAgB;IAC1C,MAAM,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC;IAClC,MAAM,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IAC/B,MAAM,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC;IAEtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU,iBAAiB;AACpC", "debugId": null}}, {"offset": {"line": 803, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/api/business/me/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';\nimport { verifyHMACMiddleware } from '@/lib/middleware/hmac';\nimport { createServiceRoleClient } from '@/utils/supabase/service-role';\nimport { \n  bruteForceProtectionMiddleware, \n  getClientIP \n} from '@/lib/middleware/bruteForceProtection';\n\n/**\n * Security middleware wrapper for business API routes\n */\nasync function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {\n  // 1. Apply rate limiting and brute force protection\n  const ipAddress = getClientIP(req);\n  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {\n    operation: 'business_api',\n    ipAddress,\n  });\n\n  if (bruteForceCheck) {\n    return bruteForceCheck;\n  }\n\n  // 2. Verify JWT token\n  const token = extractBearerToken(req);\n  if (!token) {\n    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {\n      status: 401,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n\n  const jwtResult = await verifyJWTToken(token);\n  if (!jwtResult.success) {\n    return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {\n      status: 401,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n\n  const jwtPayload = jwtResult.payload;\n\n  if (!jwtPayload) {\n    return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {\n      status: 401,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n\n  // 3. Verify HMAC signature (if required)\n  if (requireHMAC) {\n    const hmacResult = await verifyHMACMiddleware(req, true);\n    if (!hmacResult.success) {\n      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {\n        status: hmacResult.status || 403,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n  }\n\n  return { jwtPayload };\n}\n\n/**\n * GET /api/business/me - Get the current user's business profile\n */\nexport async function GET(req: NextRequest) {\n  try {\n    // Apply security middleware\n    const securityResult = await applySecurityMiddleware(req, true);\n    if (securityResult instanceof NextResponse) {\n      return securityResult;\n    }\n\n    const { jwtPayload } = securityResult;\n\n    const supabase = createServiceRoleClient();\n\n    // Parse query parameters for additional data\n    const url = new URL(req.url);\n    const includeProducts = url.searchParams.get('include_products') === 'true';\n    const includeGallery = url.searchParams.get('include_gallery') === 'true';\n    const includeMetrics = url.searchParams.get('include_metrics') === 'true';\n\n    // Build the select query based on what's requested\n    let selectQuery = `\n      id, business_name, business_slug, contact_email, member_name, title,\n      business_category, phone, whatsapp_number, address_line, city, state,\n      pincode, locality, about_bio, status, logo_url, instagram_url,\n      facebook_url, established_year, delivery_info, business_hours,\n      latitude, longitude, created_at, updated_at\n    `;\n\n    // Add metrics if requested\n    if (includeMetrics) {\n      selectQuery += `, total_likes, total_subscriptions, average_rating, total_visits, today_visits, yesterday_visits`;\n    }\n\n    // Add products if requested\n    if (includeProducts) {\n      selectQuery += `, products_services (\n        id, name, description, base_price, discounted_price, is_available, \n        image_url, images, featured_image_index, product_type, slug, created_at, updated_at\n      )`;\n    }\n\n    // Add gallery if requested\n    if (includeGallery) {\n      selectQuery += `, gallery`;\n    }\n\n    // Fetch the user's business profile\n    const { data: business, error } = await supabase\n      .from('business_profiles')\n      .select(selectQuery)\n      .eq('id', jwtPayload.user_id)\n      .maybeSingle();\n\n    if (error) {\n      console.error('Error fetching user business profile:', error);\n      return new NextResponse(JSON.stringify({ error: 'Failed to fetch business profile' }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    if (!business) {\n      return new NextResponse(JSON.stringify({ error: 'Business profile not found' }), {\n        status: 404,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    return NextResponse.json({ business });\n\n  } catch (error) {\n    console.error('Unexpected error in GET /api/business/me:', error);\n    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAKA;;CAEC,GACD,eAAe,wBAAwB,GAAgB,EAAE,cAAuB,IAAI;IAClF,oDAAoD;IACpD,MAAM,YAAY,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM,kBAAkB,MAAM,CAAA,GAAA,2IAAA,CAAA,iCAA8B,AAAD,EAAE,KAAK;QAChE,WAAW;QACX;IACF;IAEA,IAAI,iBAAiB;QACnB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,QAAQ,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;IACjC,IAAI,CAAC,OAAO;QACV,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAA+B,IAAI;YACjF,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,MAAM,YAAY,MAAM,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE;IACvC,IAAI,CAAC,UAAU,OAAO,EAAE;QACtB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAA2B,IAAI;YAC7E,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,MAAM,aAAa,UAAU,OAAO;IAEpC,IAAI,CAAC,YAAY;QACf,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAAI;YAC1E,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,yCAAyC;IACzC,IAAI,aAAa;QACf,MAAM,aAAa,MAAM,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK;QACnD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBAAE,OAAO,WAAW,KAAK;YAAC,IAAI;gBACnE,QAAQ,WAAW,MAAM,IAAI;gBAC7B,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;IACF;IAEA,OAAO;QAAE;IAAW;AACtB;AAKO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,4BAA4B;QAC5B,MAAM,iBAAiB,MAAM,wBAAwB,KAAK;QAC1D,IAAI,0BAA0B,gIAAA,CAAA,eAAY,EAAE;YAC1C,OAAO;QACT;QAEA,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD;QAEvC,6CAA6C;QAC7C,MAAM,MAAM,IAAI,IAAI,IAAI,GAAG;QAC3B,MAAM,kBAAkB,IAAI,YAAY,CAAC,GAAG,CAAC,wBAAwB;QACrE,MAAM,iBAAiB,IAAI,YAAY,CAAC,GAAG,CAAC,uBAAuB;QACnE,MAAM,iBAAiB,IAAI,YAAY,CAAC,GAAG,CAAC,uBAAuB;QAEnE,mDAAmD;QACnD,IAAI,cAAc,CAAC;;;;;;IAMnB,CAAC;QAED,2BAA2B;QAC3B,IAAI,gBAAgB;YAClB,eAAe,CAAC,gGAAgG,CAAC;QACnH;QAEA,4BAA4B;QAC5B,IAAI,iBAAiB;YACnB,eAAe,CAAC;;;OAGf,CAAC;QACJ;QAEA,2BAA2B;QAC3B,IAAI,gBAAgB;YAClB,eAAe,CAAC,SAAS,CAAC;QAC5B;QAEA,oCAAoC;QACpC,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,qBACL,MAAM,CAAC,aACP,EAAE,CAAC,MAAM,WAAW,OAAO,EAC3B,WAAW;QAEd,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBAAE,OAAO;YAAmC,IAAI;gBACrF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,IAAI,CAAC,UAAU;YACb,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA6B,IAAI;gBAC/E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAS;IAEtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAAI;YAC1E,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;AACF", "debugId": null}}]}