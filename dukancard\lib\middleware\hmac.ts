import { NextRequest, NextResponse } from 'next/server';
import { 
  generateHMACSignature, 
  verifyHMACSignature, 
  validateTimestamp, 
  extractHMACHeaders 
} from '@/lib/security/hmac';

import { createServiceRoleClient } from '@/utils/supabase/service-role';

export interface HMACVerificationResult {
  success: boolean;
  error?: string;
  status?: number;
  deviceId?: string;
}

/**
 * Middleware function to verify HMAC signatures on protected API requests
 * @param req - Next.js request object
 * @param requireHMAC - Whether HMAC verification is required (default: true)
 * @returns Result object indicating success/failure and error details
 */
export async function verifyHMACMiddleware(
  req: NextRequest,
  requireHMAC: boolean = true
): Promise<HMACVerificationResult> {
  try {
    // Skip HMAC verification if not required (e.g., for login endpoint)
    if (!requireHMAC) {
      return { success: true };
    }

    // Skip HMAC verification for internal calls
    const isInternalCall = req.headers.get('X-Internal-Call') === 'true';
    if (isInternalCall) {
      return { success: true };
    }

    // 1. Extract required headers
    const hmacHeaders = extractHMACHeaders(req.headers);
    if (!hmacHeaders) {
      return {
        success: false,
        error: 'Missing required headers: X-Device-Id, X-Timestamp, or X-Signature',
        status: 400,
      };
    }

    const { deviceId, timestamp, signature } = hmacHeaders;

    // 2. Validate timestamp to prevent replay attacks
    if (!validateTimestamp(timestamp)) {
      return {
        success: false,
        error: 'Request has expired',
        status: 408,
      };
    }

    // 3. Fetch device from database
    const supabase = createServiceRoleClient();
    const { data: device, error: deviceError } = await supabase
      .from('devices')
      .select('device_id, device_secret_hash, hmac_key_hash, revoked')
      .eq('device_id', deviceId)
      .single();

    if (deviceError || !device) {
      return {
        success: false,
        error: 'Invalid device ID',
        status: 403,
      };
    }

    // 4. Check if device is revoked
    if (device.revoked) {
      return {
        success: false,
        error: 'Device has been revoked',
        status: 403,
      };
    }

    // 5. Get request body for signature verification
    let requestBody = '';
    try {
      // Clone the request to read the body without consuming it
      const clonedReq = req.clone();
      requestBody = await clonedReq.text();
    } catch (_error) {
      requestBody = '';
    }

    // 6. Generate expected signature using stored HMAC key
    const method = req.method;
    const path = new URL(req.url).pathname;
    
    const expectedSignature = generateHMACSignature(
      method,
      path,
      timestamp,
      requestBody,
      device.hmac_key_hash || '' // Use the stored HMAC key
    );

    // 7. Verify signature using constant-time comparison
    const isValidSignature = verifyHMACSignature(signature, expectedSignature);

    if (!isValidSignature) {
      return {
        success: false,
        error: 'Invalid signature',
        status: 403,
      };
    }

    // 8. HMAC verification successful
    return {
      success: true,
      deviceId: deviceId,
    };
    
  } catch (_error) {
    console.error('Unexpected error in HMAC verification:', _error);
    return {
      success: false,
      error: 'Internal Server Error',
      status: 500,
    };
  }
}

/**
 * Next.js middleware wrapper for HMAC verification
 * @param req - Next.js request object
 * @param requireHMAC - Whether HMAC verification is required
 * @returns NextResponse or null to continue
 */
export async function hmacMiddleware(
  req: NextRequest,
  requireHMAC: boolean = true
): Promise<NextResponse | null> {
  const result = await verifyHMACMiddleware(req, requireHMAC);
  
  if (!result.success) {
    return new NextResponse(JSON.stringify({ error: result.error }), {
      status: result.status || 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
  
  return null; // Continue to next middleware/handler
}