{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  let headersList: Headers | null = null;\r\n  let cookieStore: any = null;\r\n\r\n  try {\r\n    // Dynamically import next/headers to avoid issues in edge runtime\r\n    const { headers, cookies } = await import('next/headers');\r\n    headersList = await headers();\r\n    cookieStore = await cookies();\r\n  } catch (error) {\r\n    // If next/headers is not available (e.g., in edge runtime), continue without it\r\n    console.warn('next/headers not available in this context, using fallback');\r\n  }\r\n\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    (headersList && headersList.get('x-playwright-testing') === 'true');\r\n\r\n  if (isTestEnvironment && headersList) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  // If cookies are not available, create a basic server client\r\n  if (!cookieStore) {\r\n    return createServerClient(\r\n      supabaseUrl,\r\n      supabaseAnonKey,\r\n      {\r\n        cookies: {\r\n          getAll() {\r\n            return [];\r\n          },\r\n          setAll() {\r\n            // No-op when cookies are not available\r\n          },\r\n        },\r\n      }\r\n    ) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAIO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,IAAI,cAA8B;IAClC,IAAI,cAAmB;IAEvB,IAAI;QACF,kEAAkE;QAClE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,cAAc,MAAM;QACpB,cAAc,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,gFAAgF;QAChF,QAAQ,IAAI,CAAC;IACf;IAEA,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAClC,eAAe,YAAY,GAAG,CAAC,4BAA4B;IAE9D,IAAI,qBAAqB,aAAa;QACpC,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,6DAA6D;IAC7D,IAAI,CAAC,aAAa;QAChB,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;YACE,SAAS;gBACP;oBACE,OAAO,EAAE;gBACX;gBACA;gBACE,uCAAuC;gBACzC;YACF;QACF;IAEJ;IAEA,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/rateLimiter.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { Redis } from '@upstash/redis';\r\n\r\n// Initialize Redis client\r\nconst redis = new Redis({\r\n  url: process.env.UPSTASH_REDIS_REST_URL!,\r\n  token: process.env.UPSTASH_REDIS_REST_TOKEN!,\r\n});\r\n\r\nexport interface RateLimitConfig {\r\n  /** Maximum requests allowed */\r\n  maxRequests: number;\r\n  /** Time window in seconds */\r\n  windowSeconds: number;\r\n  /** Identifier for this rate limit (e.g., 'ip', 'device', 'user') */\r\n  identifier: string;\r\n}\r\n\r\nexport interface RateLimitResult {\r\n  success: boolean;\r\n  error?: string;\r\n  status?: number;\r\n  limit?: number;\r\n  remaining?: number;\r\n  reset?: number;\r\n  retryAfter?: number;\r\n}\r\n\r\n/**\r\n * Get client IP address from request\r\n */\r\nfunction getClientIP(req: NextRequest): string {\r\n  const forwarded = req.headers.get('x-forwarded-for');\r\n  const realIP = req.headers.get('x-real-ip');\r\n  const remoteAddress = req.headers.get('x-remote-address');\r\n  \r\n  if (forwarded) {\r\n    return forwarded.split(',')[0].trim();\r\n  }\r\n  \r\n  return realIP || remoteAddress || 'unknown';\r\n}\r\n\r\n/**\r\n * Generate Redis key for rate limiting\r\n */\r\nfunction generateRateLimitKey(identifier: string, value: string, windowStart: number): string {\r\n  return `ratelimit:${identifier}:${value}:${windowStart}`;\r\n}\r\n\r\n/**\r\n * Get the start of the current time window\r\n */\r\nfunction getWindowStart(windowSeconds: number): number {\r\n  const now = Math.floor(Date.now() / 1000);\r\n  return Math.floor(now / windowSeconds) * windowSeconds;\r\n}\r\n\r\n/**\r\n * Check rate limit for a specific identifier and value\r\n */\r\nasync function checkRateLimit(\r\n  identifier: string,\r\n  value: string,\r\n  config: RateLimitConfig\r\n): Promise<RateLimitResult> {\r\n  try {\r\n    const windowStart = getWindowStart(config.windowSeconds);\r\n    const key = generateRateLimitKey(identifier, value, windowStart);\r\n    const windowEnd = windowStart + config.windowSeconds;\r\n\r\n    // Get current count\r\n    const currentCount = await redis.get<number>(key) || 0;\r\n\r\n    if (currentCount >= config.maxRequests) {\r\n      // Rate limit exceeded\r\n      const retryAfter = windowEnd - Math.floor(Date.now() / 1000);\r\n      return {\r\n        success: false,\r\n        error: 'Rate limit exceeded',\r\n        status: 429,\r\n        limit: config.maxRequests,\r\n        remaining: 0,\r\n        reset: windowEnd,\r\n        retryAfter: Math.max(retryAfter, 0),\r\n      };\r\n    }\r\n\r\n    // Increment counter\r\n    const newCount = await redis.incr(key);\r\n    \r\n    // Set expiration if this is the first request in the window\r\n    if (newCount === 1) {\r\n      await redis.expire(key, config.windowSeconds);\r\n    }\r\n\r\n    // Calculate remaining requests\r\n    const remaining = Math.max(config.maxRequests - newCount, 0);\r\n\r\n    return {\r\n      success: true,\r\n      limit: config.maxRequests,\r\n      remaining,\r\n      reset: windowEnd,\r\n    };\r\n  } catch (error) {\r\n    console.error(`Rate limit check failed for ${identifier}:${value}:`, error);\r\n    \r\n    // In case of Redis error, allow the request but log the error\r\n    return {\r\n      success: true,\r\n      limit: config.maxRequests,\r\n      remaining: config.maxRequests,\r\n      reset: Math.floor(Date.now() / 1000) + config.windowSeconds,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Rate limiting middleware with multiple strategies\r\n */\r\nexport async function rateLimitMiddleware(\r\n  req: NextRequest,\r\n  strategies: Array<{ \r\n    identifier: string; \r\n    getValue: (req: NextRequest) => string | null; \r\n    config: RateLimitConfig \r\n  }> = []\r\n): Promise<RateLimitResult> {\r\n  // Default strategies if none provided\r\n  if (strategies.length === 0) {\r\n    const defaultMaxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);\r\n    const defaultWindowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || '60', 10);\r\n\r\n    strategies = [\r\n      {\r\n        identifier: 'ip',\r\n        getValue: (req) => getClientIP(req),\r\n        config: {\r\n          maxRequests: defaultMaxRequests,\r\n          windowSeconds: defaultWindowSeconds,\r\n          identifier: 'ip',\r\n        },\r\n      },\r\n    ];\r\n  }\r\n\r\n  let finalResult: RateLimitResult = {\r\n    success: true,\r\n    limit: strategies[0]?.config.maxRequests || 100,\r\n    remaining: strategies[0]?.config.maxRequests || 100,\r\n    reset: Math.floor(Date.now() / 1000) + (strategies[0]?.config.windowSeconds || 60),\r\n  };\r\n\r\n  // Apply strategies in order\r\n  for (const strategy of strategies) {\r\n    const value = strategy.getValue(req);\r\n    \r\n    // Skip strategy if value cannot be determined\r\n    if (!value || value === 'unknown') {\r\n      continue;\r\n    }\r\n\r\n    const result = await checkRateLimit(strategy.identifier, value, strategy.config);\r\n    \r\n    if (!result.success) {\r\n      return result;\r\n    }\r\n\r\n    // Update final result with the most restrictive limits\r\n    if (result.remaining !== undefined && result.remaining < (finalResult.remaining || Infinity)) {\r\n      finalResult = {\r\n        ...finalResult,\r\n        limit: result.limit,\r\n        remaining: result.remaining,\r\n        reset: result.reset,\r\n      };\r\n    }\r\n  }\r\n\r\n  return finalResult;\r\n}\r\n\r\n/**\r\n * Apply rate limiting strategies with common configurations\r\n */\r\nexport async function applyRateLimiting(\r\n  req: NextRequest,\r\n  options: {\r\n    deviceId?: string;\r\n    userId?: string;\r\n    byIP?: boolean;\r\n    byDevice?: boolean;\r\n    byUser?: boolean;\r\n    customLimits?: {\r\n      ip?: RateLimitConfig;\r\n      device?: RateLimitConfig;\r\n      user?: RateLimitConfig;\r\n    };\r\n  } = {}\r\n): Promise<RateLimitResult> {\r\n  const strategies: Array<{\r\n    identifier: string;\r\n    getValue: (req: NextRequest) => string | null;\r\n    config: RateLimitConfig;\r\n  }> = [];\r\n\r\n  // Default configurations\r\n  const defaultIPLimit: RateLimitConfig = {\r\n    maxRequests: parseInt(process.env.RATE_LIMIT_IP_MAX_REQUESTS || '100', 10),\r\n    windowSeconds: parseInt(process.env.RATE_LIMIT_IP_WINDOW_SECONDS || '60', 10),\r\n    identifier: 'ip',\r\n  };\r\n\r\n  const defaultDeviceLimit: RateLimitConfig = {\r\n    maxRequests: parseInt(process.env.RATE_LIMIT_DEVICE_MAX_REQUESTS || '200', 10),\r\n    windowSeconds: parseInt(process.env.RATE_LIMIT_DEVICE_WINDOW_SECONDS || '60', 10),\r\n    identifier: 'device',\r\n  };\r\n\r\n  const defaultUserLimit: RateLimitConfig = {\r\n    maxRequests: parseInt(process.env.RATE_LIMIT_USER_MAX_REQUESTS || '500', 10),\r\n    windowSeconds: parseInt(process.env.RATE_LIMIT_USER_WINDOW_SECONDS || '60', 10),\r\n    identifier: 'user',\r\n  };\r\n\r\n  // Add IP rate limiting (enabled by default)\r\n  if (options.byIP !== false) {\r\n    strategies.push({\r\n      identifier: 'ip',\r\n      getValue: (req) => getClientIP(req),\r\n      config: options.customLimits?.ip || defaultIPLimit,\r\n    });\r\n  }\r\n\r\n  // Add device rate limiting\r\n  if (options.byDevice && options.deviceId) {\r\n    strategies.push({\r\n      identifier: 'device',\r\n      getValue: () => options.deviceId!,\r\n      config: options.customLimits?.device || defaultDeviceLimit,\r\n    });\r\n  }\r\n\r\n  // Add user rate limiting\r\n  if (options.byUser && options.userId) {\r\n    strategies.push({\r\n      identifier: 'user',\r\n      getValue: () => options.userId!,\r\n      config: options.customLimits?.user || defaultUserLimit,\r\n    });\r\n  }\r\n\r\n  return rateLimitMiddleware(req, strategies);\r\n}\r\n\r\n/**\r\n * Next.js middleware wrapper for rate limiting\r\n */\r\nexport async function rateLimitingMiddleware(\r\n  req: NextRequest,\r\n  options: Parameters<typeof applyRateLimiting>[1] = {}\r\n): Promise<NextResponse | null> {\r\n  const result = await applyRateLimiting(req, options);\r\n\r\n  if (!result.success) {\r\n    const response = new NextResponse(JSON.stringify({ error: result.error }), {\r\n      status: result.status || 429,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'X-RateLimit-Limit': result.limit?.toString() || '',\r\n        'X-RateLimit-Remaining': result.remaining?.toString() || '0',\r\n        'X-RateLimit-Reset': result.reset?.toString() || '',\r\n        'Retry-After': result.retryAfter?.toString() || '',\r\n      },\r\n    });\r\n\r\n    return response;\r\n  }\r\n\r\n  // Add rate limit headers to successful responses\r\n  // Note: This will be handled by the calling code since we're returning null to continue\r\n  return null;\r\n}\r\n\r\n/**\r\n * Add rate limit headers to response\r\n */\r\nexport function addRateLimitHeaders(\r\n  response: NextResponse,\r\n  result: RateLimitResult\r\n): NextResponse {\r\n  if (result.limit !== undefined) {\r\n    response.headers.set('X-RateLimit-Limit', result.limit.toString());\r\n  }\r\n  if (result.remaining !== undefined) {\r\n    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());\r\n  }\r\n  if (result.reset !== undefined) {\r\n    response.headers.set('X-RateLimit-Reset', result.reset.toString());\r\n  }\r\n  \r\n  return response;\r\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;;;AAEA,0BAA0B;AAC1B,MAAM,QAAQ,IAAI,+JAAA,CAAA,QAAK,CAAC;IACtB,KAAK,QAAQ,GAAG,CAAC,sBAAsB;IACvC,OAAO,QAAQ,GAAG,CAAC,wBAAwB;AAC7C;AAqBA;;CAEC,GACD,SAAS,YAAY,GAAgB;IACnC,MAAM,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC;IAClC,MAAM,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IAC/B,MAAM,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC;IAEtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU,iBAAiB;AACpC;AAEA;;CAEC,GACD,SAAS,qBAAqB,UAAkB,EAAE,KAAa,EAAE,WAAmB;IAClF,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,EAAE,aAAa;AAC1D;AAEA;;CAEC,GACD,SAAS,eAAe,aAAqB;IAC3C,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;IACpC,OAAO,KAAK,KAAK,CAAC,MAAM,iBAAiB;AAC3C;AAEA;;CAEC,GACD,eAAe,eACb,UAAkB,EAClB,KAAa,EACb,MAAuB;IAEvB,IAAI;QACF,MAAM,cAAc,eAAe,OAAO,aAAa;QACvD,MAAM,MAAM,qBAAqB,YAAY,OAAO;QACpD,MAAM,YAAY,cAAc,OAAO,aAAa;QAEpD,oBAAoB;QACpB,MAAM,eAAe,MAAM,MAAM,GAAG,CAAS,QAAQ;QAErD,IAAI,gBAAgB,OAAO,WAAW,EAAE;YACtC,sBAAsB;YACtB,MAAM,aAAa,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YACvD,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;gBACR,OAAO,OAAO,WAAW;gBACzB,WAAW;gBACX,OAAO;gBACP,YAAY,KAAK,GAAG,CAAC,YAAY;YACnC;QACF;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC;QAElC,4DAA4D;QAC5D,IAAI,aAAa,GAAG;YAClB,MAAM,MAAM,MAAM,CAAC,KAAK,OAAO,aAAa;QAC9C;QAEA,+BAA+B;QAC/B,MAAM,YAAY,KAAK,GAAG,CAAC,OAAO,WAAW,GAAG,UAAU;QAE1D,OAAO;YACL,SAAS;YACT,OAAO,OAAO,WAAW;YACzB;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;QAErE,8DAA8D;QAC9D,OAAO;YACL,SAAS;YACT,OAAO,OAAO,WAAW;YACzB,WAAW,OAAO,WAAW;YAC7B,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,OAAO,aAAa;QAC7D;IACF;AACF;AAKO,eAAe,oBACpB,GAAgB,EAChB,aAIK,EAAE;IAEP,sCAAsC;IACtC,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,MAAM,qBAAqB,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI,OAAO;QAClF,MAAM,uBAAuB,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI,MAAM;QAErF,aAAa;YACX;gBACE,YAAY;gBACZ,UAAU,CAAC,MAAQ,YAAY;gBAC/B,QAAQ;oBACN,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;SACD;IACH;IAEA,IAAI,cAA+B;QACjC,SAAS;QACT,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,eAAe;QAC5C,WAAW,UAAU,CAAC,EAAE,EAAE,OAAO,eAAe;QAChD,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,QAAQ,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,iBAAiB,EAAE;IACnF;IAEA,4BAA4B;IAC5B,KAAK,MAAM,YAAY,WAAY;QACjC,MAAM,QAAQ,SAAS,QAAQ,CAAC;QAEhC,8CAA8C;QAC9C,IAAI,CAAC,SAAS,UAAU,WAAW;YACjC;QACF;QAEA,MAAM,SAAS,MAAM,eAAe,SAAS,UAAU,EAAE,OAAO,SAAS,MAAM;QAE/E,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,OAAO;QACT;QAEA,uDAAuD;QACvD,IAAI,OAAO,SAAS,KAAK,aAAa,OAAO,SAAS,GAAG,CAAC,YAAY,SAAS,IAAI,QAAQ,GAAG;YAC5F,cAAc;gBACZ,GAAG,WAAW;gBACd,OAAO,OAAO,KAAK;gBACnB,WAAW,OAAO,SAAS;gBAC3B,OAAO,OAAO,KAAK;YACrB;QACF;IACF;IAEA,OAAO;AACT;AAKO,eAAe,kBACpB,GAAgB,EAChB,UAWI,CAAC,CAAC;IAEN,MAAM,aAID,EAAE;IAEP,yBAAyB;IACzB,MAAM,iBAAkC;QACtC,aAAa,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI,OAAO;QACvE,eAAe,SAAS,QAAQ,GAAG,CAAC,4BAA4B,IAAI,MAAM;QAC1E,YAAY;IACd;IAEA,MAAM,qBAAsC;QAC1C,aAAa,SAAS,QAAQ,GAAG,CAAC,8BAA8B,IAAI,OAAO;QAC3E,eAAe,SAAS,QAAQ,GAAG,CAAC,gCAAgC,IAAI,MAAM;QAC9E,YAAY;IACd;IAEA,MAAM,mBAAoC;QACxC,aAAa,SAAS,QAAQ,GAAG,CAAC,4BAA4B,IAAI,OAAO;QACzE,eAAe,SAAS,QAAQ,GAAG,CAAC,8BAA8B,IAAI,MAAM;QAC5E,YAAY;IACd;IAEA,4CAA4C;IAC5C,IAAI,QAAQ,IAAI,KAAK,OAAO;QAC1B,WAAW,IAAI,CAAC;YACd,YAAY;YACZ,UAAU,CAAC,MAAQ,YAAY;YAC/B,QAAQ,QAAQ,YAAY,EAAE,MAAM;QACtC;IACF;IAEA,2BAA2B;IAC3B,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,EAAE;QACxC,WAAW,IAAI,CAAC;YACd,YAAY;YACZ,UAAU,IAAM,QAAQ,QAAQ;YAChC,QAAQ,QAAQ,YAAY,EAAE,UAAU;QAC1C;IACF;IAEA,yBAAyB;IACzB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,EAAE;QACpC,WAAW,IAAI,CAAC;YACd,YAAY;YACZ,UAAU,IAAM,QAAQ,MAAM;YAC9B,QAAQ,QAAQ,YAAY,EAAE,QAAQ;QACxC;IACF;IAEA,OAAO,oBAAoB,KAAK;AAClC;AAKO,eAAe,uBACpB,GAAgB,EAChB,UAAmD,CAAC,CAAC;IAErD,MAAM,SAAS,MAAM,kBAAkB,KAAK;IAE5C,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,WAAW,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO,OAAO,KAAK;QAAC,IAAI;YACzE,QAAQ,OAAO,MAAM,IAAI;YACzB,SAAS;gBACP,gBAAgB;gBAChB,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,yBAAyB,OAAO,SAAS,EAAE,cAAc;gBACzD,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,eAAe,OAAO,UAAU,EAAE,cAAc;YAClD;QACF;QAEA,OAAO;IACT;IAEA,iDAAiD;IACjD,wFAAwF;IACxF,OAAO;AACT;AAKO,SAAS,oBACd,QAAsB,EACtB,MAAuB;IAEvB,IAAI,OAAO,KAAK,KAAK,WAAW;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,KAAK,CAAC,QAAQ;IACjE;IACA,IAAI,OAAO,SAAS,KAAK,WAAW;QAClC,SAAS,OAAO,CAAC,GAAG,CAAC,yBAAyB,OAAO,SAAS,CAAC,QAAQ;IACzE;IACA,IAAI,OAAO,KAAK,KAAK,WAAW;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,KAAK,CAAC,QAAQ;IACjE;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/bruteForceProtection.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { applyRateLimiting, RateLimitResult, RateLimitConfig } from './rateLimiter';\r\n\r\nexport interface BruteForceProtectionConfig {\r\n  /** Maximum login attempts per IP per hour (default: 10) */\r\n  maxLoginAttemptsPerIP?: number;\r\n  /** Time window for login attempts in seconds (default: 3600 = 1 hour) */\r\n  loginWindowSeconds?: number;\r\n  /** Maximum login attempts per email per hour (default: 5) */\r\n  maxLoginAttemptsPerEmail?: number;\r\n  /** Maximum token refresh attempts per device per hour (default: 20) */\r\n  maxRefreshAttemptsPerDevice?: number;\r\n  /** Time window for token refresh in seconds (default: 3600 = 1 hour) */\r\n  refreshWindowSeconds?: number;\r\n  /** Whether to enable progressive delays (default: true) */\r\n  enableProgressiveDelays?: boolean;\r\n}\r\n\r\nexport interface BruteForceContext {\r\n  /** IP address of the request */\r\n  ipAddress?: string;\r\n  /** Email being used for login (if applicable) */\r\n  email?: string;\r\n  /** Device ID for token refresh (if applicable) */\r\n  deviceId?: string;\r\n  /** User ID (if available) */\r\n  userId?: string;\r\n  /** Type of authentication operation */\r\n  operation: 'login' | 'refresh' | 'device_register';\r\n}\r\n\r\n/**\r\n * Calculate progressive delay based on attempt count\r\n * Implements exponential backoff with jitter\r\n */\r\nexport function calculateProgressiveDelay(attemptCount: number): number {\r\n  if (attemptCount <= 3) return 0;\r\n  \r\n  // Base delay starts at 1 second for 4th attempt\r\n  const baseDelay = Math.pow(2, attemptCount - 4) * 1000;\r\n  \r\n  // Cap at 30 seconds\r\n  const cappedDelay = Math.min(baseDelay, 30000);\r\n  \r\n  // Add jitter (±20%) but ensure result doesn't exceed cap\r\n  const jitter = cappedDelay * 0.2 * (Math.random() - 0.5);\r\n  const finalDelay = cappedDelay + jitter;\r\n  \r\n  // Ensure final delay doesn't exceed the cap and is not negative\r\n  return Math.floor(Math.max(0, Math.min(finalDelay, 30000)));\r\n}\r\n\r\n/**\r\n * Apply brute-force protection for authentication endpoints\r\n */\r\nexport async function applyBruteForceProtection(\r\n  req: NextRequest,\r\n  context: BruteForceContext,\r\n  config: BruteForceProtectionConfig = {}\r\n): Promise<RateLimitResult> {\r\n  const {\r\n    maxLoginAttemptsPerIP = parseInt(process.env.BRUTE_FORCE_LOGIN_IP_LIMIT || '10', 10),\r\n    loginWindowSeconds = parseInt(process.env.BRUTE_FORCE_LOGIN_WINDOW || '3600', 10),\r\n    maxLoginAttemptsPerEmail = parseInt(process.env.BRUTE_FORCE_EMAIL_LIMIT || '5', 10),\r\n    maxRefreshAttemptsPerDevice = parseInt(process.env.BRUTE_FORCE_REFRESH_LIMIT || '20', 10),\r\n    refreshWindowSeconds = parseInt(process.env.BRUTE_FORCE_REFRESH_WINDOW || '3600', 10),\r\n    enableProgressiveDelays = true,\r\n  } = config;\r\n\r\n  const strategies: Parameters<typeof applyRateLimiting>[1] = {};\r\n\r\n  switch (context.operation) {\r\n    case 'login':\r\n      // For login, we apply both IP-based and email-based rate limiting\r\n      strategies.byIP = true;\r\n      strategies.customLimits = {\r\n        ip: {\r\n          maxRequests: maxLoginAttemptsPerIP,\r\n          windowSeconds: loginWindowSeconds,\r\n          identifier: 'login_ip',\r\n        } as RateLimitConfig,\r\n      };\r\n\r\n      // If we have an email, add email-based rate limiting\r\n      if (context.email) {\r\n        strategies.byUser = true;\r\n        strategies.userId = `email:${context.email}`;\r\n        strategies.customLimits.user = {\r\n          maxRequests: maxLoginAttemptsPerEmail,\r\n          windowSeconds: loginWindowSeconds,\r\n          identifier: 'login_email',\r\n        } as RateLimitConfig;\r\n      }\r\n      break;\r\n\r\n    case 'refresh':\r\n      // For token refresh, we apply device-based and IP-based rate limiting\r\n      strategies.byIP = true;\r\n      strategies.customLimits = {\r\n        ip: {\r\n          maxRequests: maxRefreshAttemptsPerDevice * 2, // More lenient for IP\r\n          windowSeconds: refreshWindowSeconds,\r\n          identifier: 'refresh_ip',\r\n        } as RateLimitConfig,\r\n      };\r\n\r\n      if (context.deviceId) {\r\n        strategies.byDevice = true;\r\n        strategies.deviceId = context.deviceId;\r\n        strategies.customLimits.device = {\r\n          maxRequests: maxRefreshAttemptsPerDevice,\r\n          windowSeconds: refreshWindowSeconds,\r\n          identifier: 'refresh_device',\r\n        } as RateLimitConfig;\r\n      }\r\n      break;\r\n\r\n    case 'device_register':\r\n      // For device registration, apply IP-based rate limiting\r\n      strategies.byIP = true;\r\n      strategies.customLimits = {\r\n        ip: {\r\n          maxRequests: 5, // Very restrictive for device registration\r\n          windowSeconds: 3600,\r\n          identifier: 'device_register_ip',\r\n        } as RateLimitConfig,\r\n      };\r\n      break;\r\n\r\n    default:\r\n      return {\r\n        success: false,\r\n        error: 'Invalid operation type',\r\n        status: 400,\r\n      };\r\n  }\r\n\r\n  const result = await applyRateLimiting(req, strategies);\r\n\r\n  // If rate limiting failed and progressive delays are enabled,\r\n  // add delay information to the result\r\n  if (!result.success && enableProgressiveDelays) {\r\n    const attemptCount = (result.limit || 0) - (result.remaining || 0);\r\n    const delay = calculateProgressiveDelay(attemptCount);\r\n    \r\n    if (delay > 0) {\r\n      result.retryAfter = Math.max(result.retryAfter || 0, Math.ceil(delay / 1000));\r\n    }\r\n  }\r\n\r\n  return result;\r\n}\r\n\r\n/**\r\n * Middleware wrapper for brute-force protection\r\n * Returns a NextResponse if the request should be blocked, null if it should continue\r\n */\r\nexport async function bruteForceProtectionMiddleware(\r\n  req: NextRequest,\r\n  context: BruteForceContext,\r\n  config: BruteForceProtectionConfig = {}\r\n): Promise<NextResponse | null> {\r\n  const result = await applyBruteForceProtection(req, context, config);\r\n\r\n  if (!result.success) {\r\n    const response = new NextResponse(JSON.stringify({ \r\n      error: result.error,\r\n      retryAfter: result.retryAfter,\r\n      message: 'Too many attempts. Please try again later.',\r\n    }), {\r\n      status: result.status || 429,\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'X-RateLimit-Limit': result.limit?.toString() || '',\r\n        'X-RateLimit-Remaining': result.remaining?.toString() || '0',\r\n        'X-RateLimit-Reset': result.reset?.toString() || '',\r\n        'Retry-After': result.retryAfter?.toString() || '',\r\n      },\r\n    });\r\n\r\n    return response;\r\n  }\r\n\r\n  return null; // Continue to next middleware/handler\r\n}\r\n\r\n/**\r\n * Extract email from login request body safely\r\n */\r\nexport function extractEmailFromLoginRequest(body: any): string | undefined {\r\n  try {\r\n    if (typeof body === 'object' && body !== null && typeof body.email === 'string') {\r\n      return body.email.toLowerCase().trim();\r\n    }\r\n  } catch (error) {\r\n    // Ignore parsing errors\r\n  }\r\n  return undefined;\r\n}\r\n\r\n/**\r\n * Extract device ID from request body safely\r\n */\r\nexport function extractDeviceIdFromRequest(body: any): string | undefined {\r\n  try {\r\n    if (typeof body === 'object' && body !== null && typeof body.deviceId === 'string') {\r\n      return body.deviceId;\r\n    }\r\n  } catch (error) {\r\n    // Ignore parsing errors\r\n  }\r\n  return undefined;\r\n}\r\n\r\n/**\r\n * Get client IP address from request (same as rate limiter)\r\n */\r\nexport function getClientIP(req: NextRequest): string {\r\n  const forwarded = req.headers.get('x-forwarded-for');\r\n  const realIP = req.headers.get('x-real-ip');\r\n  const remoteAddress = req.headers.get('x-remote-address');\r\n  \r\n  if (forwarded) {\r\n    return forwarded.split(',')[0].trim();\r\n  }\r\n  \r\n  return realIP || remoteAddress || 'unknown';\r\n}"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAkCO,SAAS,0BAA0B,YAAoB;IAC5D,IAAI,gBAAgB,GAAG,OAAO;IAE9B,gDAAgD;IAChD,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,eAAe,KAAK;IAElD,oBAAoB;IACpB,MAAM,cAAc,KAAK,GAAG,CAAC,WAAW;IAExC,yDAAyD;IACzD,MAAM,SAAS,cAAc,MAAM,CAAC,KAAK,MAAM,KAAK,GAAG;IACvD,MAAM,aAAa,cAAc;IAEjC,gEAAgE;IAChE,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY;AACrD;AAKO,eAAe,0BACpB,GAAgB,EAChB,OAA0B,EAC1B,SAAqC,CAAC,CAAC;IAEvC,MAAM,EACJ,wBAAwB,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI,MAAM,GAAG,EACpF,qBAAqB,SAAS,QAAQ,GAAG,CAAC,wBAAwB,IAAI,QAAQ,GAAG,EACjF,2BAA2B,SAAS,QAAQ,GAAG,CAAC,uBAAuB,IAAI,KAAK,GAAG,EACnF,8BAA8B,SAAS,QAAQ,GAAG,CAAC,yBAAyB,IAAI,MAAM,GAAG,EACzF,uBAAuB,SAAS,QAAQ,GAAG,CAAC,0BAA0B,IAAI,QAAQ,GAAG,EACrF,0BAA0B,IAAI,EAC/B,GAAG;IAEJ,MAAM,aAAsD,CAAC;IAE7D,OAAQ,QAAQ,SAAS;QACvB,KAAK;YACH,kEAAkE;YAClE,WAAW,IAAI,GAAG;YAClB,WAAW,YAAY,GAAG;gBACxB,IAAI;oBACF,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YAEA,qDAAqD;YACrD,IAAI,QAAQ,KAAK,EAAE;gBACjB,WAAW,MAAM,GAAG;gBACpB,WAAW,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK,EAAE;gBAC5C,WAAW,YAAY,CAAC,IAAI,GAAG;oBAC7B,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YACA;QAEF,KAAK;YACH,sEAAsE;YACtE,WAAW,IAAI,GAAG;YAClB,WAAW,YAAY,GAAG;gBACxB,IAAI;oBACF,aAAa,8BAA8B;oBAC3C,eAAe;oBACf,YAAY;gBACd;YACF;YAEA,IAAI,QAAQ,QAAQ,EAAE;gBACpB,WAAW,QAAQ,GAAG;gBACtB,WAAW,QAAQ,GAAG,QAAQ,QAAQ;gBACtC,WAAW,YAAY,CAAC,MAAM,GAAG;oBAC/B,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YACA;QAEF,KAAK;YACH,wDAAwD;YACxD,WAAW,IAAI,GAAG;YAClB,WAAW,YAAY,GAAG;gBACxB,IAAI;oBACF,aAAa;oBACb,eAAe;oBACf,YAAY;gBACd;YACF;YACA;QAEF;YACE,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;IACJ;IAEA,MAAM,SAAS,MAAM,CAAA,GAAA,kIAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;IAE5C,8DAA8D;IAC9D,sCAAsC;IACtC,IAAI,CAAC,OAAO,OAAO,IAAI,yBAAyB;QAC9C,MAAM,eAAe,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,SAAS,IAAI,CAAC;QACjE,MAAM,QAAQ,0BAA0B;QAExC,IAAI,QAAQ,GAAG;YACb,OAAO,UAAU,GAAG,KAAK,GAAG,CAAC,OAAO,UAAU,IAAI,GAAG,KAAK,IAAI,CAAC,QAAQ;QACzE;IACF;IAEA,OAAO;AACT;AAMO,eAAe,+BACpB,GAAgB,EAChB,OAA0B,EAC1B,SAAqC,CAAC,CAAC;IAEvC,MAAM,SAAS,MAAM,0BAA0B,KAAK,SAAS;IAE7D,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,WAAW,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAC/C,OAAO,OAAO,KAAK;YACnB,YAAY,OAAO,UAAU;YAC7B,SAAS;QACX,IAAI;YACF,QAAQ,OAAO,MAAM,IAAI;YACzB,SAAS;gBACP,gBAAgB;gBAChB,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,yBAAyB,OAAO,SAAS,EAAE,cAAc;gBACzD,qBAAqB,OAAO,KAAK,EAAE,cAAc;gBACjD,eAAe,OAAO,UAAU,EAAE,cAAc;YAClD;QACF;QAEA,OAAO;IACT;IAEA,OAAO,MAAM,sCAAsC;AACrD;AAKO,SAAS,6BAA6B,IAAS;IACpD,IAAI;QACF,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,KAAK,KAAK,UAAU;YAC/E,OAAO,KAAK,KAAK,CAAC,WAAW,GAAG,IAAI;QACtC;IACF,EAAE,OAAO,OAAO;IACd,wBAAwB;IAC1B;IACA,OAAO;AACT;AAKO,SAAS,2BAA2B,IAAS;IAClD,IAAI;QACF,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,QAAQ,KAAK,UAAU;YAClF,OAAO,KAAK,QAAQ;QACtB;IACF,EAAE,OAAO,OAAO;IACd,wBAAwB;IAC1B;IACA,OAAO;AACT;AAKO,SAAS,YAAY,GAAgB;IAC1C,MAAM,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC;IAClC,MAAM,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC;IAC/B,MAAM,gBAAgB,IAAI,OAAO,CAAC,GAAG,CAAC;IAEtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,OAAO,UAAU,iBAAiB;AACpC", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/security/hmac.ts"], "sourcesContent": ["import crypto from 'crypto';\r\n\r\n/**\r\n * Generates an HMAC-SHA256 signature for API request validation\r\n * @param method - HTTP method (GET, POST, etc.)\r\n * @param path - API path (/api/auth/login)\r\n * @param timestamp - Unix timestamp in milliseconds\r\n * @param body - Request body (empty string for GET requests)\r\n * @param deviceSecret - The device secret (plaintext)\r\n * @returns Base64 encoded HMAC signature\r\n */\r\nexport function generateHMACSignature(\r\n  method: string,\r\n  path: string,\r\n  timestamp: string,\r\n  body: string,\r\n  deviceSecret: string\r\n): string {\r\n  // 1. Create SHA256 hash of the request body\r\n  const bodyHash = crypto\r\n    .createHash('sha256')\r\n    .update(body || '')\r\n    .digest('hex');\r\n\r\n  // 2. Create the string to be signed: method + path + timestamp + bodyHash\r\n  const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;\r\n\r\n  // 3. Generate HMAC-SHA256 signature using device secret\r\n  const signature = crypto\r\n    .createHmac('sha256', deviceSecret)\r\n    .update(stringToSign)\r\n    .digest('base64');\r\n\r\n  return signature;\r\n}\r\n\r\n/**\r\n * Verifies an HMAC signature using constant-time comparison\r\n * @param receivedSignature - The signature from the client\r\n * @param expectedSignature - The signature calculated by the server\r\n * @returns true if signatures match, false otherwise\r\n */\r\nexport function verifyHMACSignature(\r\n  receivedSignature: string,\r\n  expectedSignature: string\r\n): boolean {\r\n  // Convert to buffers for constant-time comparison\r\n  const receivedBuffer = Buffer.from(receivedSignature, 'base64');\r\n  const expectedBuffer = Buffer.from(expectedSignature, 'base64');\r\n\r\n  // Ensure both signatures are the same length to prevent timing attacks\r\n  if (receivedBuffer.length !== expectedBuffer.length) {\r\n    return false;\r\n  }\r\n\r\n  // Use crypto.timingSafeEqual for constant-time comparison\r\n  return crypto.timingSafeEqual(receivedBuffer, expectedBuffer);\r\n}\r\n\r\n/**\r\n * Validates timestamp to prevent replay attacks\r\n * @param timestamp - Unix timestamp in milliseconds as string\r\n * @param windowSeconds - Allowed time window in seconds (default: 120)\r\n * @returns true if timestamp is within the allowed window\r\n */\r\nexport function validateTimestamp(\r\n  timestamp: string,\r\n  windowSeconds: number = 120\r\n): boolean {\r\n  const requestTime = parseInt(timestamp, 10);\r\n  const currentTime = Date.now();\r\n  const windowMs = windowSeconds * 1000;\r\n\r\n  // Check if timestamp is a valid number\r\n  if (isNaN(requestTime)) {\r\n    return false;\r\n  }\r\n\r\n  // Check if request is within the allowed time window\r\n  const timeDiff = Math.abs(currentTime - requestTime);\r\n  return timeDiff <= windowMs;\r\n}\r\n\r\n/**\r\n * Extracts required headers for HMAC verification\r\n * @param headers - Request headers\r\n * @returns Object with deviceId, timestamp, and signature, or null if any are missing\r\n */\r\nexport function extractHMACHeaders(headers: Headers | any): {\r\n  deviceId: string;\r\n  timestamp: string;\r\n  signature: string;\r\n} | null {\r\n  const deviceId = typeof headers.get === 'function' \r\n    ? headers.get('x-device-id') \r\n    : headers['x-device-id'];\r\n  const timestamp = typeof headers.get === 'function'\r\n    ? headers.get('x-timestamp')\r\n    : headers['x-timestamp'];\r\n  const signature = typeof headers.get === 'function'\r\n    ? headers.get('x-signature')\r\n    : headers['x-signature'];\r\n\r\n  if (!deviceId || !timestamp || !signature) {\r\n    return null;\r\n  }\r\n\r\n  return { deviceId, timestamp, signature };\r\n}"], "names": [], "mappings": ";;;;;;AAAA;;AAWO,SAAS,sBACd,MAAc,EACd,IAAY,EACZ,SAAiB,EACjB,IAAY,EACZ,YAAoB;IAEpB,4CAA4C;IAC5C,MAAM,WAAW,qGAAA,CAAA,UAAM,CACpB,UAAU,CAAC,UACX,MAAM,CAAC,QAAQ,IACf,MAAM,CAAC;IAEV,0EAA0E;IAC1E,MAAM,eAAe,GAAG,OAAO,WAAW,KAAK,OAAO,YAAY,UAAU;IAE5E,wDAAwD;IACxD,MAAM,YAAY,qGAAA,CAAA,UAAM,CACrB,UAAU,CAAC,UAAU,cACrB,MAAM,CAAC,cACP,MAAM,CAAC;IAEV,OAAO;AACT;AAQO,SAAS,oBACd,iBAAyB,EACzB,iBAAyB;IAEzB,kDAAkD;IAClD,MAAM,iBAAiB,OAAO,IAAI,CAAC,mBAAmB;IACtD,MAAM,iBAAiB,OAAO,IAAI,CAAC,mBAAmB;IAEtD,uEAAuE;IACvE,IAAI,eAAe,MAAM,KAAK,eAAe,MAAM,EAAE;QACnD,OAAO;IACT;IAEA,0DAA0D;IAC1D,OAAO,qGAAA,CAAA,UAAM,CAAC,eAAe,CAAC,gBAAgB;AAChD;AAQO,SAAS,kBACd,SAAiB,EACjB,gBAAwB,GAAG;IAE3B,MAAM,cAAc,SAAS,WAAW;IACxC,MAAM,cAAc,KAAK,GAAG;IAC5B,MAAM,WAAW,gBAAgB;IAEjC,uCAAuC;IACvC,IAAI,MAAM,cAAc;QACtB,OAAO;IACT;IAEA,qDAAqD;IACrD,MAAM,WAAW,KAAK,GAAG,CAAC,cAAc;IACxC,OAAO,YAAY;AACrB;AAOO,SAAS,mBAAmB,OAAsB;IAKvD,MAAM,WAAW,OAAO,QAAQ,GAAG,KAAK,aACpC,QAAQ,GAAG,CAAC,iBACZ,OAAO,CAAC,cAAc;IAC1B,MAAM,YAAY,OAAO,QAAQ,GAAG,KAAK,aACrC,QAAQ,GAAG,CAAC,iBACZ,OAAO,CAAC,cAAc;IAC1B,MAAM,YAAY,OAAO,QAAQ,GAAG,KAAK,aACrC,QAAQ,GAAG,CAAC,iBACZ,OAAO,CAAC,cAAc;IAE1B,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,WAAW;QACzC,OAAO;IACT;IAEA,OAAO;QAAE;QAAU;QAAW;IAAU;AAC1C", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/service-role.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport function createServiceRoleClient() {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseServiceRoleKey) {\r\n    throw new Error('Missing required Supabase environment variables for service role');\r\n  }\r\n\r\n  return createClient<Database>(supabaseUrl, supabaseServiceRoleKey, {\r\n    auth: {\r\n      autoRefreshToken: false,\r\n      persistSession: false,\r\n    },\r\n  });\r\n}"], "names": [], "mappings": ";;;AAAA;;AAGO,SAAS;IACd,MAAM;IACN,MAAM,yBAAyB,QAAQ,GAAG,CAAC,yBAAyB;IAEpE,IAAI,CAAC,eAAe,CAAC,wBAAwB;QAC3C,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAY,aAAa,wBAAwB;QACjE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AACF", "debugId": null}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/middleware/hmac.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\r\nimport { \r\n  generateHMACSignature, \r\n  verifyHMACSignature, \r\n  validateTimestamp, \r\n  extractHMACHeaders \r\n} from '@/lib/security/hmac';\r\nimport { compareSecret } from '@/lib/security/hashing';\r\nimport { createServiceRoleClient } from '@/utils/supabase/service-role';\r\n\r\nexport interface HMACVerificationResult {\r\n  success: boolean;\r\n  error?: string;\r\n  status?: number;\r\n  deviceId?: string;\r\n}\r\n\r\n/**\r\n * Middleware function to verify HMAC signatures on protected API requests\r\n * @param req - Next.js request object\r\n * @param requireHMAC - Whether HMAC verification is required (default: true)\r\n * @returns Result object indicating success/failure and error details\r\n */\r\nexport async function verifyHMACMiddleware(\r\n  req: NextRequest,\r\n  requireHMAC: boolean = true\r\n): Promise<HMACVerificationResult> {\r\n  try {\r\n    // Skip HMAC verification if not required (e.g., for login endpoint)\r\n    if (!requireHMAC) {\r\n      return { success: true };\r\n    }\r\n\r\n    // 1. Extract required headers\r\n    const hmacHeaders = extractHMACHeaders(req.headers);\r\n    if (!hmacHeaders) {\r\n      return {\r\n        success: false,\r\n        error: 'Missing required headers: X-Device-Id, X-Timestamp, or X-Signature',\r\n        status: 400,\r\n      };\r\n    }\r\n\r\n    const { deviceId, timestamp, signature } = hmacHeaders;\r\n\r\n    // 2. Validate timestamp to prevent replay attacks\r\n    if (!validateTimestamp(timestamp)) {\r\n      return {\r\n        success: false,\r\n        error: 'Request has expired',\r\n        status: 408,\r\n      };\r\n    }\r\n\r\n    // 3. Fetch device from database\r\n    const supabase = createServiceRoleClient();\r\n    const { data: device, error: deviceError } = await supabase\r\n      .from('devices')\r\n      .select('device_id, device_secret_hash, hmac_key_hash, revoked')\r\n      .eq('device_id', deviceId)\r\n      .single();\r\n\r\n    if (deviceError || !device) {\r\n      return {\r\n        success: false,\r\n        error: 'Invalid device ID',\r\n        status: 403,\r\n      };\r\n    }\r\n\r\n    // 4. Check if device is revoked\r\n    if (device.revoked) {\r\n      return {\r\n        success: false,\r\n        error: 'Device has been revoked',\r\n        status: 403,\r\n      };\r\n    }\r\n\r\n    // 5. Get request body for signature verification\r\n    let requestBody = '';\r\n    try {\r\n      // Clone the request to read the body without consuming it\r\n      const clonedReq = req.clone();\r\n      requestBody = await clonedReq.text();\r\n    } catch (error) {\r\n      requestBody = '';\r\n    }\r\n\r\n    // 6. Generate expected signature using stored HMAC key\r\n    const method = req.method;\r\n    const path = new URL(req.url).pathname;\r\n    \r\n    const expectedSignature = generateHMACSignature(\r\n      method,\r\n      path,\r\n      timestamp,\r\n      requestBody,\r\n      device.hmac_key_hash // Use the stored HMAC key\r\n    );\r\n\r\n    // 7. Verify signature using constant-time comparison\r\n    const isValidSignature = verifyHMACSignature(signature, expectedSignature);\r\n\r\n    if (!isValidSignature) {\r\n      return {\r\n        success: false,\r\n        error: 'Invalid signature',\r\n        status: 403,\r\n      };\r\n    }\r\n\r\n    // 8. HMAC verification successful\r\n    return {\r\n      success: true,\r\n      deviceId: deviceId,\r\n    };\r\n    \r\n  } catch (error) {\r\n    console.error('Unexpected error in HMAC verification:', error);\r\n    return {\r\n      success: false,\r\n      error: 'Internal Server Error',\r\n      status: 500,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Next.js middleware wrapper for HMAC verification\r\n * @param req - Next.js request object\r\n * @param requireHMAC - Whether HMAC verification is required\r\n * @returns NextResponse or null to continue\r\n */\r\nexport async function hmacMiddleware(\r\n  req: NextRequest,\r\n  requireHMAC: boolean = true\r\n): Promise<NextResponse | null> {\r\n  const result = await verifyHMACMiddleware(req, requireHMAC);\r\n  \r\n  if (!result.success) {\r\n    return new NextResponse(JSON.stringify({ error: result.error }), {\r\n      status: result.status || 500,\r\n      headers: { 'Content-Type': 'application/json' },\r\n    });\r\n  }\r\n  \r\n  return null; // Continue to next middleware/handler\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAOA;;;;AAeO,eAAe,qBACpB,GAAgB,EAChB,cAAuB,IAAI;IAE3B,IAAI;QACF,oEAAoE;QACpE,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,SAAS;YAAK;QACzB;QAEA,8BAA8B;QAC9B,MAAM,cAAc,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,OAAO;QAClD,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;QAE3C,kDAAkD;QAClD,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YACjC,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,gCAAgC;QAChC,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD;QACvC,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,yDACP,EAAE,CAAC,aAAa,UAChB,MAAM;QAET,IAAI,eAAe,CAAC,QAAQ;YAC1B,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,gCAAgC;QAChC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,iDAAiD;QACjD,IAAI,cAAc;QAClB,IAAI;YACF,0DAA0D;YAC1D,MAAM,YAAY,IAAI,KAAK;YAC3B,cAAc,MAAM,UAAU,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,cAAc;QAChB;QAEA,uDAAuD;QACvD,MAAM,SAAS,IAAI,MAAM;QACzB,MAAM,OAAO,IAAI,IAAI,IAAI,GAAG,EAAE,QAAQ;QAEtC,MAAM,oBAAoB,CAAA,GAAA,yHAAA,CAAA,wBAAqB,AAAD,EAC5C,QACA,MACA,WACA,aACA,OAAO,aAAa,CAAC,0BAA0B;;QAGjD,qDAAqD;QACrD,MAAM,mBAAmB,CAAA,GAAA,yHAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;QAExD,IAAI,CAAC,kBAAkB;YACrB,OAAO;gBACL,SAAS;gBACT,OAAO;gBACP,QAAQ;YACV;QACF;QAEA,kCAAkC;QAClC,OAAO;YACL,SAAS;YACT,UAAU;QACZ;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;QACV;IACF;AACF;AAQO,eAAe,eACpB,GAAgB,EAChB,cAAuB,IAAI;IAE3B,MAAM,SAAS,MAAM,qBAAqB,KAAK;IAE/C,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO,OAAO,KAAK;QAAC,IAAI;YAC/D,QAAQ,OAAO,MAAM,IAAI;YACzB,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,OAAO,MAAM,sCAAsC;AACrD", "debugId": null}}, {"offset": {"line": 1018, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/api/products/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { createClient } from '@/utils/supabase/server';\nimport { z } from 'zod';\nimport { \n  bruteForceProtectionMiddleware, \n  getClientIP \n} from '@/lib/middleware/bruteForceProtection';\nimport { \n  extractBearerToken, \n  verifyJWTToken \n} from '@/lib/middleware/jwt';\nimport { \n  verifyHMACMiddleware \n} from '@/lib/middleware/hmac';\n\n/**\n * Security middleware wrapper for products API routes\n */\nasync function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {\n  // 1. Apply rate limiting and brute force protection\n  const ipAddress = getClientIP(req);\n  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {\n    operation: 'products_api',\n    ipAddress,\n  });\n\n  if (bruteForceCheck) {\n    return bruteForceCheck;\n  }\n\n  // 2. Verify JWT token\n  const token = extractBearerToken(req);\n  if (!token) {\n    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {\n      status: 401,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n\n  const jwtResult = await verifyJWTToken(token);\n  if (!jwtResult.success) {\n    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {\n      status: 401,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n\n  // 3. Verify HMAC signature if required\n  if (requireHMAC) {\n    const hmacResult = await verifyHMACMiddleware(req, requireHMAC);\n    if (!hmacResult.success) {\n      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {\n        status: hmacResult.status || 500,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n  }\n\n  return { success: true, jwtPayload: jwtResult.payload };\n}\n\n// Schema for listing products\nconst listProductsSchema = z.object({\n  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),\n  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),\n  search: z.string().optional(),\n  category: z.string().optional(),\n  business_id: z.string().optional(),\n  product_type: z.enum([\"physical\", \"service\"]).optional(),\n  is_available: z.string().transform(val => val === 'true').optional(),\n  sort_by: z.enum([\"name_asc\", \"name_desc\", \"created_asc\", \"created_desc\", \"price_asc\", \"price_desc\"]).optional(),\n  pincode: z.string().optional(),\n  locality: z.string().optional(),\n  city: z.string().optional(),\n  state: z.string().optional(),\n});\n\n/**\n * GET /api/products - List products with filtering and pagination\n */\nexport async function GET(req: NextRequest) {\n  try {\n    // Apply security middleware (no HMAC required for public product listing)\n    const securityResult = await applySecurityMiddleware(req, false);\n    if (securityResult instanceof NextResponse) {\n      return securityResult;\n    }\n\n    // Parse and validate query parameters\n    const url = new URL(req.url);\n    const queryParams = Object.fromEntries(url.searchParams.entries());\n    \n    const validation = listProductsSchema.safeParse(queryParams);\n    if (!validation.success) {\n      return new NextResponse(JSON.stringify({ \n        error: 'Invalid query parameters',\n        details: validation.error.errors \n      }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    const {\n      page = 1,\n      limit = 20,\n      search,\n      category,\n      business_id,\n      product_type,\n      is_available = true,\n      sort_by = \"created_desc\",\n      pincode,\n      locality,\n      city,\n      state\n    } = validation.data;\n    const offset = (page - 1) * limit;\n\n    // Build query\n    const supabase = await createClient();\n\n    let query = supabase\n      .from('products_services')\n      .select(`\n        id, business_id, name, description, base_price, discounted_price,\n        product_type, is_available, image_url, images, featured_image_index,\n        slug, created_at, updated_at,\n        business_profiles!business_id(\n          id, business_name, business_slug, city, state, pincode, locality,\n          logo_url, status\n        )\n      `, { count: 'exact' })\n      .eq('is_available', is_available);\n\n    // Add filters\n    if (search) {\n      query = query.ilike('name', `%${search}%`);\n    }\n    if (business_id) {\n      query = query.eq('business_id', business_id);\n    }\n    if (product_type) {\n      query = query.eq('product_type', product_type);\n    }\n\n    // Location filters (filter by business location)\n    if (pincode) {\n      query = query.eq('business_profiles.pincode', pincode);\n    }\n    if (locality) {\n      query = query.eq('business_profiles.locality', locality);\n    }\n    if (city) {\n      query = query.eq('business_profiles.city', city);\n    }\n    if (state) {\n      query = query.eq('business_profiles.state', state);\n    }\n\n    // Only show products from online businesses\n    query = query.eq('business_profiles.status', 'online');\n\n    // Add sorting\n    const [sortField, sortDirection] = sort_by.split('_');\n    const ascending = sortDirection === 'asc';\n    \n    let orderColumn = sortField;\n    if (sortField === 'created') {\n      orderColumn = 'created_at';\n    } else if (sortField === 'price') {\n      orderColumn = 'base_price';\n    }\n\n    query = query.order(orderColumn, { ascending });\n\n    // Add pagination\n    query = query.range(offset, offset + limit - 1);\n\n    const { data: products, error, count } = await query;\n\n    if (error) {\n      console.error('Error fetching products:', error);\n      return new NextResponse(JSON.stringify({ error: 'Failed to fetch products' }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    const totalCount = count || 0;\n    const hasMore = offset + limit < totalCount;\n    const nextPage = hasMore ? page + 1 : null;\n\n    return new NextResponse(JSON.stringify({\n      products: products || [],\n      pagination: {\n        page,\n        limit,\n        total: totalCount,\n        hasMore,\n        nextPage,\n      },\n    }), {\n      status: 200,\n      headers: { 'Content-Type': 'application/json' },\n    });\n\n  } catch (error) {\n    console.error('Unexpected error in products API:', error);\n    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n}\n\n// Schema for creating products\nconst createProductSchema = z.object({\n  name: z.string().min(1).max(255),\n  description: z.string().optional(),\n  base_price: z.number().min(0),\n  discounted_price: z.number().min(0).optional(),\n  product_type: z.enum([\"physical\", \"service\"]),\n  is_available: z.boolean().optional().default(true),\n  image_url: z.string().url().optional(),\n  images: z.array(z.string().url()).optional(),\n  featured_image_index: z.number().min(0).optional(),\n  slug: z.string().optional(),\n});\n\n/**\n * POST /api/products - Create a new product\n */\nexport async function POST(req: NextRequest) {\n  try {\n    // Apply security middleware (HMAC required for mutations)\n    const securityResult = await applySecurityMiddleware(req, true);\n    if (securityResult instanceof NextResponse) {\n      return securityResult;\n    }\n\n    const { jwtPayload } = securityResult;\n\n    // Parse and validate request body\n    const body = await req.json();\n    const validation = createProductSchema.safeParse(body);\n    \n    if (!validation.success) {\n      return new NextResponse(JSON.stringify({ \n        error: 'Invalid request body',\n        details: validation.error.errors \n      }), {\n        status: 400,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    const productData = validation.data;\n    const supabase = await createClient();\n\n    // Create the product\n    const { data: product, error } = await supabase\n      .from('products_services')\n      .insert({\n        ...productData,\n        business_id: jwtPayload.sub, // Use user ID from JWT\n      })\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating product:', error);\n      return new NextResponse(JSON.stringify({ error: 'Failed to create product' }), {\n        status: 500,\n        headers: { 'Content-Type': 'application/json' },\n      });\n    }\n\n    return new NextResponse(JSON.stringify({\n      product,\n      message: 'Product created successfully',\n    }), {\n      status: 201,\n      headers: { 'Content-Type': 'application/json' },\n    });\n\n  } catch (error) {\n    console.error('Unexpected error in create product API:', error);\n    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {\n      status: 500,\n      headers: { 'Content-Type': 'application/json' },\n    });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAQA;;;;;;;AAIA;;CAEC,GACD,eAAe,wBAAwB,GAAgB,EAAE,cAAuB,IAAI;IAClF,oDAAoD;IACpD,MAAM,YAAY,CAAA,GAAA,2IAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM,kBAAkB,MAAM,CAAA,GAAA,2IAAA,CAAA,iCAA8B,AAAD,EAAE,KAAK;QAChE,WAAW;QACX;IACF;IAEA,IAAI,iBAAiB;QACnB,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,QAAQ,mBAAmB;IACjC,IAAI,CAAC,OAAO;QACV,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAA+B,IAAI;YACjF,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,MAAM,YAAY,MAAM,eAAe;IACvC,IAAI,CAAC,UAAU,OAAO,EAAE;QACtB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO,UAAU,KAAK;QAAC,IAAI;YAClE,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;IAEA,uCAAuC;IACvC,IAAI,aAAa;QACf,MAAM,aAAa,MAAM,CAAA,GAAA,2HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK;QACnD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBAAE,OAAO,WAAW,KAAK;YAAC,IAAI;gBACnE,QAAQ,WAAW,MAAM,IAAI;gBAC7B,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;IACF;IAEA,OAAO;QAAE,SAAS;QAAM,YAAY,UAAU,OAAO;IAAC;AACxD;AAEA,8BAA8B;AAC9B,MAAM,qBAAqB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,SAAS,KAAK,KAAK,IAAI,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,QAAQ;IACrF,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,SAAS,KAAK,KAAK,IAAI,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,QAAQ;IAC/F,QAAQ,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,cAAc,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;KAAU,EAAE,QAAQ;IACtD,cAAc,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAA,MAAO,QAAQ,QAAQ,QAAQ;IAClE,SAAS,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAa;QAAe;QAAgB;QAAa;KAAa,EAAE,QAAQ;IAC7G,SAAS,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC5B,UAAU,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACzB,OAAO,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAKO,eAAe,IAAI,GAAgB;IACxC,IAAI;QACF,0EAA0E;QAC1E,MAAM,iBAAiB,MAAM,wBAAwB,KAAK;QAC1D,IAAI,0BAA0B,gIAAA,CAAA,eAAY,EAAE;YAC1C,OAAO;QACT;QAEA,sCAAsC;QACtC,MAAM,MAAM,IAAI,IAAI,IAAI,GAAG;QAC3B,MAAM,cAAc,OAAO,WAAW,CAAC,IAAI,YAAY,CAAC,OAAO;QAE/D,MAAM,aAAa,mBAAmB,SAAS,CAAC;QAChD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBACrC,OAAO;gBACP,SAAS,WAAW,KAAK,CAAC,MAAM;YAClC,IAAI;gBACF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,EACJ,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,MAAM,EACN,QAAQ,EACR,WAAW,EACX,YAAY,EACZ,eAAe,IAAI,EACnB,UAAU,cAAc,EACxB,OAAO,EACP,QAAQ,EACR,IAAI,EACJ,KAAK,EACN,GAAG,WAAW,IAAI;QACnB,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;QAE5B,cAAc;QACd,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,IAAI,QAAQ,SACT,IAAI,CAAC,qBACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EAAE;YAAE,OAAO;QAAQ,GACnB,EAAE,CAAC,gBAAgB;QAEtB,cAAc;QACd,IAAI,QAAQ;YACV,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QAC3C;QACA,IAAI,aAAa;YACf,QAAQ,MAAM,EAAE,CAAC,eAAe;QAClC;QACA,IAAI,cAAc;YAChB,QAAQ,MAAM,EAAE,CAAC,gBAAgB;QACnC;QAEA,iDAAiD;QACjD,IAAI,SAAS;YACX,QAAQ,MAAM,EAAE,CAAC,6BAA6B;QAChD;QACA,IAAI,UAAU;YACZ,QAAQ,MAAM,EAAE,CAAC,8BAA8B;QACjD;QACA,IAAI,MAAM;YACR,QAAQ,MAAM,EAAE,CAAC,0BAA0B;QAC7C;QACA,IAAI,OAAO;YACT,QAAQ,MAAM,EAAE,CAAC,2BAA2B;QAC9C;QAEA,4CAA4C;QAC5C,QAAQ,MAAM,EAAE,CAAC,4BAA4B;QAE7C,cAAc;QACd,MAAM,CAAC,WAAW,cAAc,GAAG,QAAQ,KAAK,CAAC;QACjD,MAAM,YAAY,kBAAkB;QAEpC,IAAI,cAAc;QAClB,IAAI,cAAc,WAAW;YAC3B,cAAc;QAChB,OAAO,IAAI,cAAc,SAAS;YAChC,cAAc;QAChB;QAEA,QAAQ,MAAM,KAAK,CAAC,aAAa;YAAE;QAAU;QAE7C,iBAAiB;QACjB,QAAQ,MAAM,KAAK,CAAC,QAAQ,SAAS,QAAQ;QAE7C,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM;QAE/C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA2B,IAAI;gBAC7E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,aAAa,SAAS;QAC5B,MAAM,UAAU,SAAS,QAAQ;QACjC,MAAM,WAAW,UAAU,OAAO,IAAI;QAEtC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YACrC,UAAU,YAAY,EAAE;YACxB,YAAY;gBACV;gBACA;gBACA,OAAO;gBACP;gBACA;YACF;QACF,IAAI;YACF,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAAI;YAC1E,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;AACF;AAEA,+BAA+B;AAC/B,MAAM,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAC5B,aAAa,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IAC3B,kBAAkB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC5C,cAAc,sIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAY;KAAU;IAC5C,cAAc,sIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC7C,WAAW,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACpC,QAAQ,sIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,IAAI,QAAQ;IAC1C,sBAAsB,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,QAAQ;IAChD,MAAM,sIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC3B;AAKO,eAAe,KAAK,GAAgB;IACzC,IAAI;QACF,0DAA0D;QAC1D,MAAM,iBAAiB,MAAM,wBAAwB,KAAK;QAC1D,IAAI,0BAA0B,gIAAA,CAAA,eAAY,EAAE;YAC1C,OAAO;QACT;QAEA,MAAM,EAAE,UAAU,EAAE,GAAG;QAEvB,kCAAkC;QAClC,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,MAAM,aAAa,oBAAoB,SAAS,CAAC;QAEjD,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBACrC,OAAO;gBACP,SAAS,WAAW,KAAK,CAAC,MAAM;YAClC,IAAI;gBACF,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,cAAc,WAAW,IAAI;QACnC,MAAM,WAAW,MAAM,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;QAElC,qBAAqB;QACrB,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,qBACL,MAAM,CAAC;YACN,GAAG,WAAW;YACd,aAAa,WAAW,GAAG;QAC7B,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA2B,IAAI;gBAC7E,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YACrC;YACA,SAAS;QACX,IAAI;YACF,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAAI;YAC1E,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;AACF", "debugId": null}}]}