"use server";

import { createClient } from "@/utils/supabase/server";

// Define types for the customer profile data
type CustomerProfilePublicData = {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
  created_at: string | null;
  updated_at: string | null;
};

/**
 * Securely fetch a customer profile by ID using the service role key
 * This bypasses RLS and ensures sensitive data is not exposed to the client
 */
export async function getSecureCustomerProfileById(
  userId: string
): Promise<{
  data?: CustomerProfilePublicData;
  error?: string;
}> {
  if (!userId) {
    return { error: "User ID is required." };
  }

  try {
    // Use the public view which only exposes safe data
    const supabase = await createClient();

    // Fetch the customer profile from public view
    const { data, error } = await supabase
      .from("customer_profiles_public")
      .select("*")
      .eq("id", userId)
      .maybeSingle();

    if (error) {
      console.error("Secure Fetch Error:", error);
      return { error: `Failed to fetch customer profile: ${error.message}` };
    }

    if (!data) {
      return { error: "Profile not found." };
    }

    // Data from public view is already safe
    const safeData: CustomerProfilePublicData = {
      id: data.id!,
      name: data.name,
      email: null, // Not available in public view
      avatar_url: data.avatar_url,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return { data: safeData };
  } catch (e) {
    console.error("Exception in getSecureCustomerProfileById:", e);
    return { error: "An unexpected error occurred." };
  }
}

interface CustomerProfileForReview {
  id: string;
  name: string | null;
  avatar_url: string | null;
}

interface BusinessProfileForReview {
  id: string;
  business_name: string | null;
  logo_url: string | null;
}

/**
 * Securely fetch multiple customer profiles by IDs using the service role key
 */
export async function getSecureCustomerProfilesByIds(
  userIds: string[]
): Promise<{
  data?: CustomerProfilePublicData[];
  error?: string;
}> {
  if (!userIds || userIds.length === 0) {
    return { data: [] };
  }

  try {
    // Use the public view which only exposes safe data
    const supabase = await createClient();

    // Fetch the customer profiles from public view
    const { data, error } = await supabase
      .from("customer_profiles_public")
      .select("id, name, avatar_url, created_at, updated_at")
      .in("id", userIds);

    if (error) {
      console.error("Secure Fetch Error:", error);
      return { error: `Failed to fetch customer profiles: ${error.message}` };
    }

    const safeData = data?.map((profile) => ({
      ...profile,
      email: null // Not available in public view
    })) || [];

    return { data: safeData as CustomerProfilePublicData[] };
  } catch (e) {
    console.error("Exception in getSecureCustomerProfilesByIds:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely fetch all customer profiles using the service role key
 * This is primarily for testing purposes
 */
export async function getAllSecureCustomerProfiles(): Promise<{
  data?: CustomerProfilePublicData[];
  error?: string;
}> {
  try {
    // Use the public view which only exposes safe data
    const supabase = await createClient();

    // Fetch all customer profiles from public view
    const { data, error } = await supabase
      .from("customer_profiles_public")
      .select("id, name, avatar_url, created_at, updated_at");

    if (error) {
      console.error("Secure Fetch Error:", error);
      return { error: `Failed to fetch customer profiles: ${error.message}` };
    }

    const safeData = data?.map((profile) => ({
      ...profile,
      email: null // Not available in public view
    })) || [];

    return { data: safeData as CustomerProfilePublicData[] };
  } catch (e) {
    console.error("Exception in getAllSecureCustomerProfiles:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Securely check if a user has access to their own customer profile
 * This is used for authenticated users to access their own profile
 */
export async function checkUserCustomerProfileAccess(
  userId: string
): Promise<{
  hasAccess: boolean;
  error?: string;
}> {
  if (!userId) {
    return { hasAccess: false, error: "User ID is required." };
  }

  try {
    // Use regular client for authenticated user
    const supabase = await createClient();

    // Check if the user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return { hasAccess: false, error: "User not authenticated." };
    }

    // Verify the requested userId matches the authenticated user
    if (user.id !== userId) {
      return { hasAccess: false, error: "Unauthorized access attempt." };
    }

    // Check if the user has a customer profile
    const { data, error } = await supabase
      .from("customer_profiles")
      .select("id")
      .eq("id", userId)
      .maybeSingle();

    if (error) {
      console.error("Profile Access Check Error:", error);
      return { hasAccess: false, error: "Database error checking access." };
    }

    return { hasAccess: !!data };
  } catch (e) {
    console.error("Exception in checkUserCustomerProfileAccess:", e);
    return { hasAccess: false, error: "An unexpected error occurred." };
  }
}

/**
 * Get user profile data (either business or customer) for reviews
 * This function will check both tables and return the appropriate profile data
 */
export async function getUserProfileForReview(
  userId: string
): Promise<{
  data?: {
    id: string;
    name: string | null;
    avatar_url: string | null;
    is_business: boolean;
  };
  error?: string;
}> {
  if (!userId) {
    return { error: "User ID is required." };
  }

  try {
    // Use the public view which only exposes safe data
    const supabase = await createClient();

    // First check customer_profiles_public
    const { data: customerProfile, error: customerError } = await supabase
      .from("customer_profiles_public")
      .select("id, name, avatar_url")
      .eq("id", userId)
      .maybeSingle();

    if (customerError) {
      console.error("Error fetching customer profile:", customerError);
      return { error: `Failed to fetch customer profile: ${customerError.message}` };
    }

    if (customerProfile) {
      return {
        data: {
          id: customerProfile.id!,
          name: customerProfile.name,
          avatar_url: customerProfile.avatar_url,
          is_business: false
        }
      };
    }

    // If not found in customer_profiles, check business_profiles
    const { data: businessProfile, error: businessError } = await supabase
      .from("business_profiles")
      .select("id, business_name, logo_url")
      .eq("id", userId)
      .maybeSingle();

    if (businessError) {
      console.error("Error fetching business profile:", businessError);
      return { error: `Failed to fetch business profile: ${businessError.message}` };
    }

    if (businessProfile) {
      return {
        data: {
          id: businessProfile.id,
          name: businessProfile.business_name,
          avatar_url: businessProfile.logo_url,
          is_business: true
        }
      };
    }

    return { error: "User profile not found in either customer or business profiles." };
  } catch (e) {
    console.error("Exception in getUserProfileForReview:", e);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Get multiple user profiles (either business or customer) for reviews
 */
export async function getUserProfilesForReviews(
  userIds: string[]
): Promise<{
  data?: {
    [key: string]: {
      id: string;
      name: string | null;
      avatar_url: string | null;
      is_business: boolean;
    }
  };
  error?: string;
}> {
  if (!userIds || userIds.length === 0) {
    return { data: {} };
  }

  try {
    // Use the public view which only exposes safe data
    const supabase = await createClient();

    // Fetch customer profiles from public view
    const { data: customerProfiles, error: customerError } = await supabase
      .from("customer_profiles_public")
      .select("id, name, avatar_url")
      .in("id", userIds);

    if (customerError) {
      console.error("Error fetching customer profiles:", customerError);
      return { error: `Failed to fetch customer profiles: ${customerError.message}` };
    }

    // Fetch business profiles
    const { data: businessProfiles, error: businessError } = await supabase
      .from("business_profiles")
      .select("id, business_name, logo_url")
      .in("id", userIds);

    if (businessError) {
      console.error("Error fetching business profiles:", businessError);
      return { error: `Failed to fetch business profiles: ${businessError.message}` };
    }

    // Combine the results into a map of user ID to profile data
    const profilesMap: {
      [key: string]: {
        id: string;
        name: string | null;
        avatar_url: string | null;
        is_business: boolean;
      }
    } = {};

    // Add customer profiles to the map
    customerProfiles?.forEach((profile) => {
      if (profile.id) {
        profilesMap[profile.id] = {
          id: profile.id,
          name: profile.name,
          avatar_url: profile.avatar_url,
          is_business: false
        };
      }
    });

    // Add business profiles to the map
    businessProfiles?.forEach((profile: BusinessProfileForReview) => {
      // Only add if not already in the map (customer profiles take precedence)
      if (!profilesMap[profile.id]) {
        profilesMap[profile.id] = {
          id: profile.id,
          name: profile.business_name,
          avatar_url: profile.logo_url,
          is_business: true
        };
      }
    });

    return { data: profilesMap };
  } catch (e) {
    console.error("Exception in getUserProfilesForReviews:", e);
    return { error: "An unexpected error occurred." };
  }
}
