import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { applySecurityMiddleware } from "@/lib/middleware/security";
import { getGalleryLimit } from "@/app/(dashboard)/dashboard/business/gallery/utils";
import { z } from "zod";

// Types
interface GalleryImage {
  id: string;
  url: string;
  path: string;
  created_at: string;
}

const querySchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).optional().default("1"),
  limit: z.string().transform(val => parseInt(val, 10)).optional().default("12"),
  tab: z.enum(["true", "false"]).optional().default("false"), // For gallery tab (max 4 images)
});

/**
 * GET /api/gallery/public/[businessSlug] - Get public gallery images for a business
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ businessSlug: string }> }
) {
  try {
    // Apply security middleware (no HMAC required for public gallery)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { businessSlug } = await params;
    const supabase = await createClient();

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = querySchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { page, limit, tab } = validation.data;
    const isTabView = tab === "true";

    if (!businessSlug) {
      return new NextResponse(JSON.stringify({ 
        error: "Business slug is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // First, get the business ID from the slug
    const { data: business, error: businessError } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", businessSlug)
      .eq("status", "online")
      .single();

    if (businessError || !business) {
      console.error("Error fetching business profile:", businessError);
      return new NextResponse(JSON.stringify({ 
        error: "Business not found" 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Get the business profile with gallery data
    const { data: profileData, error: galleryError } = await supabase
      .from("business_profiles")
      .select("gallery")
      .eq("id", business.id)
      .single();

    if (galleryError) {
      console.error("Error fetching business gallery:", galleryError);
      return new NextResponse(JSON.stringify({ 
        error: `Failed to fetch gallery images: ${galleryError.message}` 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const allImages = (Array.isArray(profileData?.gallery) ? profileData.gallery : []) as GalleryImage[];
    
    if (!Array.isArray(allImages) || allImages.length === 0) {
      const emptyResponse = {
        images: [],
        totalCount: 0,
        ...(isTabView ? {} : {
          totalPages: 0,
          currentPage: page,
          hasNextPage: false,
          hasPrevPage: false,
        })
      };
      
      return new NextResponse(JSON.stringify(emptyResponse), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Apply plan limit first
    const galleryLimit = getGalleryLimit();
    const planLimitedImages = allImages.slice(0, galleryLimit);

    if (isTabView) {
      // For gallery tab, limit to max 4 images
      const finalLimit = Math.min(planLimitedImages.length, 4);
      const limitedImages = planLimitedImages.slice(0, finalLimit);
      const totalCount = planLimitedImages.length;

      return new NextResponse(JSON.stringify({
        images: limitedImages,
        totalCount
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    } else {
      // For full gallery view with pagination
      const totalCount = planLimitedImages.length;
      const totalPages = Math.ceil(totalCount / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;

      // Get paginated images
      const paginatedImages = planLimitedImages.slice(startIndex, endIndex);

      return new NextResponse(JSON.stringify({
        images: paginatedImages,
        totalCount,
        totalPages,
        currentPage: page,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1,
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      });
    }

  } catch (error) {
    console.error("Unexpected error in public gallery GET:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
