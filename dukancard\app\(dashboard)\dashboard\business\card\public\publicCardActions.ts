"use server";

import { BusinessCardData } from "../schema";
import { ProductServiceData } from "../../products/actions";
import { mapPublicCardData } from "../data/businessCardMapper";

/**
 * Fetches public card data by business slug
 * @param slug - The business slug to fetch data for
 * @returns Public card data with products/services or error
 */
export async function getPublicCardDataBySlug(slug: string): Promise<{
  data?: BusinessCardData & { products_services?: ProductServiceData[] };
  error?: string;
}> {
  if (!slug) {
    return { error: "Business slug is required." };
  }

  try {
    // Use the business profile API endpoint for public access by slug
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}?include_products=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profile",
      };
    }

    const data = result.business;

    if (!data) {
      return { error: "Profile not found or is not online." };
    }

    // Map data using the shared mapper
    const mappedData = mapPublicCardData(data);
    return { data: mappedData };
  } catch (e) {
    console.error("Exception in getPublicCardDataBySlug:", e);
    return { error: "An unexpected error occurred." };
  }
}
