import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import ModernBusinessFeedList from '@/components/feed/ModernBusinessFeedList';
import { getUnifiedFeedPostsWithAuthors } from '@/lib/actions/posts/unifiedFeed';
import { getCurrentUserBusinessProfileId } from '@/lib/actions/businessProfiles/access';

export const metadata: Metadata = {
  title: "Feed",
  description: "View your business feed and stay updated",
};

export default async function BusinessDashboardPage() {
  // Check if user has business profile access using existing server action
  const { profileId: businessProfileId, error: accessError } = await getCurrentUserBusinessProfileId();

  if (accessError || !businessProfileId) {
    redirect('/login?message=Please log in to view your dashboard');
  }

  // For now, we'll use default values since we don't have the full profile data
  // The feed component will handle fetching its own data
  const businessProfile = {
    city_slug: undefined,
    state_slug: undefined,
    locality_slug: undefined,
    pincode: undefined,
    business_name: 'Business Owner'
  };

  // Get initial posts using the unified feed algorithm
  const initialFeedResult = await getUnifiedFeedPostsWithAuthors({
    filter: 'smart',
    page: 1,
    limit: 10,
    city_slug: businessProfile?.city_slug || undefined,
    state_slug: businessProfile?.state_slug || undefined,
    locality_slug: businessProfile?.locality_slug || undefined,
    pincode: businessProfile?.pincode || undefined
  });

  const posts = initialFeedResult.success ? initialFeedResult.data?.items || [] : [];
  const hasMore = initialFeedResult.success ? initialFeedResult.data?.hasMore || false : false;

  if (!initialFeedResult.success) {
    console.error('Error fetching initial posts:', initialFeedResult.error);
  }

  return (
    <ModernBusinessFeedList
      initialPosts={posts}
      initialTotalCount={0} // Not needed for infinite scroll
      initialHasMore={hasMore}
      initialFilter="smart"
      citySlug={businessProfile?.city_slug || undefined}
      stateSlug={businessProfile?.state_slug || undefined}
      localitySlug={businessProfile?.locality_slug || undefined}
      pincode={businessProfile?.pincode || undefined}
      businessName={businessProfile?.business_name || 'Business Owner'}
    />
  );
}
