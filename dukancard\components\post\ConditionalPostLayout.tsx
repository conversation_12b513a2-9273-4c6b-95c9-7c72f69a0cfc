'use client';

import React, { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { BusinessAppSidebar } from '@/components/sidebar/BusinessAppSidebar';
import { CustomerAppSidebar } from '@/components/sidebar/CustomerAppSidebar';
import MinimalHeader from '@/app/components/MinimalHeader';
import { ThemeToggle } from '@/app/components/ThemeToggle';
import BottomNav from '@/app/components/BottomNav';
import { cn } from '@/lib/utils';
import { useAuthStore } from '@/lib/stores/authStore';
import { useBusinessProfileStore } from '@/lib/stores/businessProfileStore';
import { useCustomerProfileStore } from '@/lib/stores/customerProfileStore';
// Removed PaymentMethodLimitationsProvider import as we're now free for all users

interface ConditionalPostLayoutProps {
  children: React.ReactNode;
}

interface UserProfile {
  business_name?: string;
  logo_url?: string;
  avatar_url?: string;
}

/**
 * Conditional layout component that shows:
 * - Dashboard layout (header + sidebar) for authenticated users
 * - Simple public layout for non-authenticated users
 */
export default function ConditionalPostLayout({ children }: ConditionalPostLayoutProps) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [userType, setUserType] = useState<'business' | 'customer' | null>(null);
  const [loading, setLoading] = useState(true);

  const { user: authUser, isAuthenticated } = useAuthStore();
  const { getCurrentUserProfile: getBusinessProfile } = useBusinessProfileStore();
  const { getCurrentUserProfile: getCustomerProfile } = useCustomerProfileStore();

  useEffect(() => {
    const fetchUserProfiles = async () => {
      try {
        setUser(authUser as User);

        if (authUser && isAuthenticated) {
          // Try to get business profile first
          const business = await getBusinessProfile();

          if (business) {
            setProfile({
              business_name: business.business_name,
              logo_url: business.logo_url || undefined
            });
            setUserType('business');
          } else {
            // Try to get customer profile
            const customer = await getCustomerProfile();

            if (customer) {
              setProfile({
                avatar_url: customer.avatar_url || undefined
              });
              setUserType('customer');
            }
          }
        }
      } catch (error) {
        console.error('Error fetching user profiles:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserProfiles();
  }, [authUser, isAuthenticated, getBusinessProfile, getCustomerProfile]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-white dark:bg-black">
        <div className="w-full">
          {children}
        </div>
      </div>
    );
  }

  // If user is not authenticated, show simple public layout with bottom nav
  if (!user) {
    return (
      <div className="min-h-screen bg-white dark:bg-black">
        <div className="w-full pb-16 md:pb-6">
          {children}
        </div>
        <BottomNav />
      </div>
    );
  }

  // If user is authenticated, show dashboard layout
  if (userType === 'business' && profile) {
    // Business dashboard layout
    return (
      // Removed PaymentMethodLimitationsProvider as we're now free for all users
      <SidebarProvider>
          <BusinessAppSidebar
            businessName={profile.business_name || null}
            logoUrl={profile.logo_url || null}
            memberName={user.user_metadata?.full_name || user.user_metadata?.name || null}
          />
          <SidebarInset>
            <MinimalHeader
              businessName={profile.business_name || null}
              logoUrl={profile.logo_url || null}
              userName={user.user_metadata?.full_name || user.user_metadata?.name || null}
            >
              <SidebarTrigger className="ml-auto md:ml-0" />
              <ThemeToggle variant="dashboard" />
            </MinimalHeader>

            <main className={cn(
              "flex-grow pb-16 md:pb-6",
              "bg-white dark:bg-black"
            )}>
              {children}
            </main>
            <BottomNav />
          </SidebarInset>
        </SidebarProvider>
      // Removed PaymentMethodLimitationsProvider as we're now free for all users
    );
  } else if (userType === 'customer') {
    // Customer dashboard layout
    return (
      <SidebarProvider>
        <CustomerAppSidebar
          userName={user.user_metadata?.full_name || user.user_metadata?.name || null}
          userAvatarUrl={profile?.avatar_url || null}
        />
        <SidebarInset>
          <MinimalHeader
            userName={user.user_metadata?.full_name || user.user_metadata?.name || null}
            businessName={null}
            logoUrl={profile?.avatar_url || null}
          >
            <SidebarTrigger className="ml-auto md:ml-0" />
            <ThemeToggle variant="dashboard" />
          </MinimalHeader>

          <main className={cn(
            "flex-grow pb-16 md:pb-6",
            "bg-white dark:bg-black"
          )}>
            {children}
          </main>
          <BottomNav />
        </SidebarInset>
      </SidebarProvider>
    );
  }

  // Fallback to public layout if user type is unknown
  return (
    <div className="min-h-screen bg-white dark:bg-black">
      <div className="w-full pb-16 md:pb-6">
        {children}
      </div>
      <BottomNav />
    </div>
  );
}
