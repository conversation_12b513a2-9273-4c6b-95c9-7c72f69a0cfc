import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@/utils/supabase/server';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Apply security middleware for subscriptions API
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  try {
    // 1. Rate limiting
    const ipAddress = getClientIP(req);
    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'business_api',
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck;
    }

    // 2. JWT verification
    const token = extractBearerToken(req);
    if (!token) {
      return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
      return new NextResponse(JSON.stringify({ error: jwtResult.error || 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // 3. HMAC verification (for external calls)
    if (requireHMAC) {
      const hmacResult = await verifyHMACMiddleware(req, true);
      if (!hmacResult.success) {
        return new NextResponse(JSON.stringify({ error: hmacResult.error || 'HMAC verification failed' }), {
          status: hmacResult.status || 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      return {
        success: true,
        jwtPayload: jwtResult.payload,
        deviceId: hmacResult.deviceId,
      };
    }

    return {
      success: true,
      jwtPayload: jwtResult.payload,
    };

  } catch (error) {
    console.error('Unexpected error in security middleware:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal security error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for listing subscriptions
const listSubscriptionsSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  user_id: z.string().uuid().optional(),
  business_id: z.string().uuid().optional(),
});

/**
 * GET /api/subscriptions - List subscriptions
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for listing)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listSubscriptionsSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      page = 1,
      limit = 20,
      user_id,
      business_id
    } = validation.data;
    const offset = (page - 1) * limit;

    const supabase = await createClient();

    // Build query for subscriptions
    let query = supabase
      .from('subscriptions')
      .select(`
        id, user_id, business_profile_id, created_at,
        business_profiles!business_profile_id(
          id, business_name, business_slug, logo_url, city, state, pincode, locality
        )
      `, { count: 'exact' });

    // Apply filters
    if (user_id) {
      // Ensure user can only access their own subscriptions
      if (user_id !== jwtPayload?.sub) {
        return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      query = query.eq('user_id', user_id);
    } else if (business_id) {
      // For business subscriptions, ensure user owns the business
      if (business_id !== jwtPayload?.sub) {
        return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      query = query.eq('business_profile_id', business_id);
    } else {
      // Default to current user's subscriptions
      if (!jwtPayload) {
        return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      query = query.eq('user_id', jwtPayload.sub);
    }

    // Add sorting and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: subscriptions, error, count } = await query;

    if (error) {
      console.error('Error fetching subscriptions:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch subscriptions' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      subscriptions: subscriptions || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in subscriptions API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for creating subscriptions
const createSubscriptionSchema = z.object({
  business_profile_id: z.string().uuid(),
});

/**
 * POST /api/subscriptions - Create a new subscription
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = createSubscriptionSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { business_profile_id } = validation.data;

    // Prevent self-subscription
    if (jwtPayload.sub === business_profile_id) {
      return new NextResponse(JSON.stringify({ 
        error: 'You cannot subscribe to your own business' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Create the subscription
    const { data: subscription, error } = await supabase
      .from('subscriptions')
      .insert({
        user_id: jwtPayload.sub,
        business_profile_id,
      })
      .select(`
        id, user_id, business_profile_id, created_at,
        business_profiles!business_profile_id(
          id, business_name, business_slug, logo_url
        )
      `)
      .single();

    if (error) {
      // Handle duplicate subscription
      if (error.code === '23505') {
        return new NextResponse(JSON.stringify({ 
          error: 'Already subscribed to this business' 
        }), {
          status: 409,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      console.error('Error creating subscription:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to create subscription' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      subscription,
      message: 'Subscription created successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create subscription API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for deleting subscriptions
const deleteSubscriptionSchema = z.object({
  business_profile_id: z.string().uuid(),
});

/**
 * DELETE /api/subscriptions - Remove a subscription
 */
export async function DELETE(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = deleteSubscriptionSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { business_profile_id } = validation.data;

    const supabase = await createClient();

    // Delete the subscription
    const { error } = await supabase
      .from('subscriptions')
      .delete()
      .eq('user_id', jwtPayload.sub)
      .eq('business_profile_id', business_profile_id);

    if (error) {
      console.error('Error deleting subscription:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to delete subscription' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: 'Subscription deleted successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in delete subscription API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
