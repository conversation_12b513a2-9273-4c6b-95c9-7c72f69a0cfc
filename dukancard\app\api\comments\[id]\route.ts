import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@/utils/supabase/server';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Apply security middleware for comments API
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  try {
    // 1. Rate limiting
    const ipAddress = getClientIP(req);
    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'posts_api',
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck;
    }

    // 2. JWT verification
    const token = extractBearerToken(req);
    if (!token) {
      return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
      return new NextResponse(JSON.stringify({ error: jwtResult.error || 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // 3. HMAC verification (for external calls)
    if (requireHMAC) {
      const hmacResult = await verifyHMACMiddleware(req, true);
      if (!hmacResult.success) {
        return new NextResponse(JSON.stringify({ error: hmacResult.error || 'HMAC verification failed' }), {
          status: hmacResult.status || 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      return {
        success: true,
        jwtPayload: jwtResult.payload,
        deviceId: hmacResult.deviceId,
      };
    }

    return {
      success: true,
      jwtPayload: jwtResult.payload,
    };

  } catch (error) {
    console.error('Unexpected error in security middleware:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal security error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/comments/[id] - Get a specific comment
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    // Apply security middleware (no HMAC required for reading)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { id } = params;

    // Validate UUID
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!UUID_REGEX.test(id)) {
      return new NextResponse(JSON.stringify({ error: 'Invalid comment ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    const { data: comment, error } = await supabase
      .from('post_comments')
      .select(`
        id, content, created_at, updated_at, is_pinned,
        user_id, post_id, post_source, parent_comment_id,
        customer_profiles!user_id(id, name, avatar_url),
        business_profiles!user_id(id, business_name, logo_url)
      `)
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ error: 'Comment not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      console.error('Error fetching comment:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch comment' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({ comment }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in get comment API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for updating comments
const updateCommentSchema = z.object({
  content: z.string().min(1).max(1000).optional(),
  is_pinned: z.boolean().optional(),
});

/**
 * PATCH /api/comments/[id] - Update a comment
 */
export async function PATCH(req: NextRequest, { params }: RouteParams) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;
    const { id } = params;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate UUID
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!UUID_REGEX.test(id)) {
      return new NextResponse(JSON.stringify({ error: 'Invalid comment ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = updateCommentSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updateData = validation.data;

    const supabase = await createClient();

    // Check if comment exists and user owns it
    const { data: existingComment, error: fetchError } = await supabase
      .from('post_comments')
      .select('user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ error: 'Comment not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      console.error('Error fetching comment for update:', fetchError);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch comment' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check ownership
    if (existingComment.user_id !== jwtPayload.sub) {
      return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Update the comment
    const { data: comment, error } = await supabase
      .from('post_comments')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select(`
        id, content, created_at, updated_at, is_pinned,
        user_id, post_id, post_source, parent_comment_id,
        customer_profiles!user_id(id, name, avatar_url),
        business_profiles!user_id(id, business_name, logo_url)
      `)
      .single();

    if (error) {
      console.error('Error updating comment:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to update comment' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      comment,
      message: 'Comment updated successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in update comment API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * DELETE /api/comments/[id] - Delete a comment
 */
export async function DELETE(req: NextRequest, { params }: RouteParams) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;
    const { id } = params;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate UUID
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!UUID_REGEX.test(id)) {
      return new NextResponse(JSON.stringify({ error: 'Invalid comment ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Check if comment exists and user owns it
    const { data: existingComment, error: fetchError } = await supabase
      .from('post_comments')
      .select('user_id')
      .eq('id', id)
      .single();

    if (fetchError) {
      if (fetchError.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ error: 'Comment not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      console.error('Error fetching comment for deletion:', fetchError);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch comment' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check ownership
    if (existingComment.user_id !== jwtPayload.sub) {
      return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Delete the comment
    const { error } = await supabase
      .from('post_comments')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting comment:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to delete comment' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: 'Comment deleted successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in delete comment API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
