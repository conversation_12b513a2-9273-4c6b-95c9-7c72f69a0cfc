import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface UploadedFile {
  path: string;
  fullPath: string;
  publicUrl: string;
  bucket: string;
  size: number;
  type: string;
  originalName: string;
}

export interface UploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
}

export interface StorageState {
  uploads: UploadProgress[];
  recentFiles: UploadedFile[];
  loading: boolean;
  error: string | null;
}

export interface StorageActions {
  // Upload a single file
  uploadFile: (
    _file: File,
    _bucket: 'business' | 'customers',
    _folder?: string,
    _options?: {
      upsert?: boolean;
      contentType?: string;
    }
  ) => Promise<UploadedFile | null>;

  // Upload multiple files
  uploadFiles: (
    _files: File[],
    _bucket: 'business' | 'customers',
    _folder?: string,
    _options?: {
      upsert?: boolean;
      contentType?: string;
    }
  ) => Promise<UploadedFile[]>;

  // Delete a file
  deleteFile: (
    _bucket: 'business' | 'customers',
    _filePath: string
  ) => Promise<boolean>;

  // Get public URL for a file
  getPublicUrl: (
    _bucket: 'business' | 'customers',
    _filePath: string
  ) => string;

  // Clear upload progress
  clearUploads: () => void;

  // Clear error
  clearError: () => void;

  // Remove upload progress for a specific file
  removeUpload: (_fileId: string) => void;
}

type StorageStore = StorageState & StorageActions;

const initialState: StorageState = {
  uploads: [],
  recentFiles: [],
  loading: false,
  error: null,
};

export const useStorageStore = create<StorageStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      uploadFile: async (file, bucket, folder = 'general', options = {}) => {
        const fileId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        
        // Add upload progress
        set({
          uploads: [
            ...get().uploads,
            {
              fileId,
              fileName: file.name,
              progress: 0,
              status: 'uploading',
            },
          ],
          loading: true,
          error: null,
        });

        try {
          // Get auth token
          const { createClient } = await import('@/utils/supabase/client');
          const supabase = createClient();
          const { data: { session } } = await supabase.auth.getSession();

          if (!session) {
            throw new Error('Authentication required');
          }

          // Create FormData
          const formData = new FormData();
          formData.append('file', file);
          formData.append('bucket', bucket);
          formData.append('folder', folder);
          if (options.upsert) {
            formData.append('upsert', 'true');
          }

          // Upload with progress tracking
          const response = await fetch('/api/storage/upload', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: formData,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to upload file');
          }

          const uploadedFile = result.file;

          // Update upload progress to completed
          set({
            uploads: get().uploads.map(upload =>
              upload.fileId === fileId
                ? { ...upload, progress: 100, status: 'completed' }
                : upload
            ),
            recentFiles: [uploadedFile, ...get().recentFiles.slice(0, 9)], // Keep last 10 files
            loading: false,
            error: null,
          });

          return uploadedFile;
        } catch (error) {
          console.error('Error uploading file:', error);
          const errorMessage = error instanceof Error ? error.message : 'Failed to upload file';

          // Update upload progress to error
          set({
            uploads: get().uploads.map(upload =>
              upload.fileId === fileId
                ? { ...upload, status: 'error', error: errorMessage }
                : upload
            ),
            loading: false,
            error: errorMessage,
          });

          return null;
        }
      },

      uploadFiles: async (files, bucket, folder = 'general', options = {}) => {
        const uploadPromises = files.map(file => 
          get().uploadFile(file, bucket, folder, options)
        );

        const results = await Promise.allSettled(uploadPromises);
        
        return results
          .filter((result): result is PromiseFulfilledResult<UploadedFile> => 
            result.status === 'fulfilled' && result.value !== null
          )
          .map(result => result.value);
      },

      deleteFile: async (bucket, filePath) => {
        set({ loading: true, error: null });

        try {
          // Get auth token
          const { createClient } = await import('@/utils/supabase/client');
          const supabase = createClient();
          const { data: { session } } = await supabase.auth.getSession();

          if (!session) {
            throw new Error('Authentication required');
          }

          const response = await fetch('/api/storage/upload', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: JSON.stringify({
              bucket,
              filePath,
            }),
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to delete file');
          }

          // Remove from recent files if it exists
          set({
            recentFiles: get().recentFiles.filter(file => file.path !== filePath),
            loading: false,
            error: null,
          });

          return true;
        } catch (error) {
          console.error('Error deleting file:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to delete file',
          });
          return false;
        }
      },

      getPublicUrl: (bucket, filePath) => {
        // Construct the public URL based on Supabase storage URL pattern
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        return `${supabaseUrl}/storage/v1/object/public/${bucket}/${filePath}`;
      },

      clearUploads: () => {
        set({ uploads: [] });
      },

      clearError: () => {
        set({ error: null });
      },

      removeUpload: (fileId) => {
        set({
          uploads: get().uploads.filter(upload => upload.fileId !== fileId),
        });
      },
    }),
    {
      name: 'storage-store',
    }
  )
);
