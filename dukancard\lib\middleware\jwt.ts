import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

export interface JWTPayload {
  sub: string; // user ID
  user_id: string;
  roles?: string[];
  iat: number;
  exp: number;
}

/**
 * Extract Bearer token from Authorization header or NextRequest
 */
export function extractBearerToken(input: string | NextRequest): string | null {
  let authHeader: string | null = null;

  if (typeof input === 'string') {
    authHeader = input;
  } else {
    // NextRequest
    authHeader = input.headers.get('authorization') || input.headers.get('Authorization');
  }

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  return authHeader.substring(7); // Remove 'Bearer ' prefix
}

/**
 * Verify JWT token and return payload
 */
export async function verifyJWTToken(token: string): Promise<{
  success: boolean;
  payload?: JWTPayload;
  error?: string;
}> {
  try {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      return { success: false, error: 'JWT secret not configured' };
    }

    const payload = jwt.verify(token, secret) as JWTPayload;
    
    // Ensure user_id is set for backward compatibility
    if (payload.sub && !payload.user_id) {
      payload.user_id = payload.sub;
    }

    return { success: true, payload };
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      return { success: false, error: 'Token expired' };
    } else if (error instanceof jwt.JsonWebTokenError) {
      return { success: false, error: 'Invalid token' };
    } else {
      return { success: false, error: 'Token verification failed' };
    }
  }
}

/**
 * Generate JWT token
 */
export function generateJWTToken(payload: Omit<JWTPayload, 'iat' | 'exp'>, expiresIn: string = '1h'): string {
  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT secret not configured');
  }

  const now = Math.floor(Date.now() / 1000);
  const fullPayload: JWTPayload = {
    ...payload,
    iat: now,
    exp: now + (expiresIn === '1h' ? 3600 : parseInt(expiresIn)),
  };

  return jwt.sign(fullPayload, secret);
}
