/**
 * Storage Service for React Native
 *
 * Handles all file storage operations using direct Supabase storage client calls
 * with Row Level Security (RLS) policies for security.
 *
 * This service replaces the need for Next.js proxy API routes by leveraging:
 * - RLS policies for security (users can only access their own files)
 * - Public read access for file downloads
 * - Direct Supabase storage client calls for better performance
 * - Proper file path structure for RLS enforcement
 */

import { supabase } from "@/lib/supabase";
import { getCustomerAvatarPath } from "@/backend/supabase/utils/storage-paths";

// Types for storage operations
export interface UploadResult {
  success: boolean;
  data?: {
    path: string;
    fullPath: string;
    publicUrl: string;
  };
  error?: string;
}

export interface DeleteResult {
  success: boolean;
  error?: string;
}

export interface DownloadResult {
  success: boolean;
  data?: {
    publicUrl: string;
    signedUrl?: string;
  };
  error?: string;
}

/**
 * Generate a unique file path for user-specific storage
 * Format: users/{prefix}/{midfix}/{userId}/{folder}/{filename}
 * This format matches the RLS policy requirements
 */
function generateUserFilePath(
  userId: string,
  folder: string,
  filename: string
): string {
  // Create prefix and midfix from user ID for better distribution
  const prefix = userId.substring(0, 2);
  const midfix = userId.substring(2, 4);

  return `users/${prefix}/${midfix}/${userId}/${folder}/${filename}`;
}

/**
 * Generate a unique filename with timestamp
 */
function generateUniqueFilename(originalFilename: string): string {
  const timestamp = Date.now();
  const extension = originalFilename.split(".").pop();
  const nameWithoutExt = originalFilename.replace(/\.[^/.]+$/, "");

  return `${nameWithoutExt}_${timestamp}.${extension}`;
}

/**
 * Upload a file to Supabase storage
 * Uses RLS policies to ensure users can only upload to their own folders
 */
export async function uploadFile(
  file: File | Blob,
  bucket: "business" | "customers",
  folder: string,
  filename: string,
  options?: {
    upsert?: boolean;
    contentType?: string;
  }
): Promise<UploadResult> {
  try {
    // Get current session for authentication
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      return { success: false, error: "Authentication required" };
    }

    // Create FormData for the API request
    const formData = new FormData();
    formData.append('file', file, filename);
    formData.append('bucket', bucket);
    formData.append('folder', folder);
    if (options?.upsert) {
      formData.append('upsert', 'true');
    }

    // Use the storage upload API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/storage/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        // Note: Don't set Content-Type for FormData, let the browser set it
      },
      body: formData,
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("[STORAGE_SERVICE] API Upload Error:", result);
      return {
        success: false,
        error: result.error || "Failed to upload file"
      };
    }

    return {
      success: true,
      data: {
        path: result.file.path,
        fullPath: result.file.fullPath,
        publicUrl: result.file.publicUrl,
      },
    };
  } catch (error) {
    console.error("[STORAGE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Delete a file from Supabase storage
 * Uses RLS policies to ensure users can only delete their own files
 */
export async function deleteFile(
  bucket: "business" | "customers",
  filePath: string
): Promise<DeleteResult> {
  try {
    // Get current session for authentication
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      return { success: false, error: "Authentication required" };
    }

    // Use the storage delete API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/storage/upload`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
      body: JSON.stringify({
        bucket,
        filePath,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("[STORAGE_SERVICE] API Delete Error:", result);
      return {
        success: false,
        error: result.error || "Failed to delete file"
      };
    }

    return { success: true };
  } catch (error) {
    console.error("[STORAGE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get public URL for a file
 * Uses public read access for file downloads
 */
export async function getFileUrl(
  bucket: "business" | "customers",
  filePath: string,
  options?: {
    download?: boolean;
    transform?: {
      width?: number;
      height?: number;
      resize?: "cover" | "contain" | "fill";
      format?: "origin";
      quality?: number;
    };
  }
): Promise<DownloadResult> {
  try {
    // Get public URL (no authentication required due to public read access)
    const { data: publicUrlData } = supabase.storage
      .from(bucket)
      .getPublicUrl(filePath, {
        download: options?.download,
        transform: options?.transform,
      });

    return {
      success: true,
      data: {
        publicUrl: publicUrlData.publicUrl,
      },
    };
  } catch (error) {
    console.error("[STORAGE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Get a signed URL for private file access (if needed)
 * This is typically not needed due to public read access, but available for special cases
 */
export async function getSignedUrl(
  bucket: "business" | "customers",
  filePath: string,
  expiresIn: number = 3600 // 1 hour default
): Promise<DownloadResult> {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .createSignedUrl(filePath, expiresIn);

    if (error) {
      console.error("[STORAGE_SERVICE] Error creating signed URL:", error);
      return { success: false, error: "Failed to create signed URL" };
    }

    return {
      success: true,
      data: {
        publicUrl: data.signedUrl,
        signedUrl: data.signedUrl,
      },
    };
  } catch (error) {
    console.error("[STORAGE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * List files in a user's folder
 * Uses RLS policies to ensure users can only list their own files
 */
export async function listUserFiles(
  bucket: "business" | "customers",
  folder: string,
  options?: {
    limit?: number;
    offset?: number;
    sortBy?: { column: string; order: "asc" | "desc" };
  }
): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { success: false, error: "Authentication required" };
    }

    // Generate user-specific folder path
    const prefix = user.id.substring(0, 2);
    const midfix = user.id.substring(2, 4);
    const userFolderPath = `users/${prefix}/${midfix}/${user.id}/${folder}`;

    const { data, error } = await supabase.storage
      .from(bucket)
      .list(userFolderPath, {
        limit: options?.limit,
        offset: options?.offset,
        sortBy: options?.sortBy,
      });

    if (error) {
      console.error("[STORAGE_SERVICE] Error listing files:", error);
      return { success: false, error: "Failed to list files" };
    }

    return { success: true, data: data || [] };
  } catch (error) {
    console.error("[STORAGE_SERVICE] Unexpected error:", error);
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Upload profile image (business logo or customer avatar)
 * Convenience method for profile image uploads
 */
export async function uploadProfileImage(
  file: File | Blob,
  type: "business" | "customer",
  filename: string,
  options?: {
    contentType?: string;
  }
): Promise<UploadResult> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error("[STORAGE_SERVICE] Authentication failed:", {
        authError,
        hasUser: !!user,
      });
      return { success: false, error: "Authentication required" };
    }

    console.log("[STORAGE_SERVICE] User authenticated:", {
      userId: user.id,
      email: user.email,
    });

    const bucket = type === "business" ? "business" : "customers";

    // Test bucket access
    try {
      const { data: buckets, error: bucketsError } =
        await supabase.storage.listBuckets();
      console.log(
        "[STORAGE_SERVICE] Available buckets:",
        buckets?.map((b) => b.name)
      );
      if (bucketsError) {
        console.error("[STORAGE_SERVICE] Error listing buckets:", bucketsError);
      }
    } catch (bucketTestError) {
      console.error("[STORAGE_SERVICE] Bucket test failed:", bucketTestError);
    }

    // For customer avatars, use direct upload with proper path generation
    if (type === "customer") {
      const timestamp = Date.now() + Math.floor(Math.random() * 1000);
      const filePath = getCustomerAvatarPath(user.id, timestamp);

      console.log("[STORAGE_SERVICE] Uploading customer avatar:", {
        userId: user.id,
        filePath,
        bucket,
        fileSize: file.size,
        fileType: file.type || options?.contentType,
      });

      // Upload file directly to storage with the full path
      console.log(
        "[STORAGE_SERVICE] About to call supabase.storage.upload with:",
        {
          bucket,
          filePath,
          fileSize: file.size,
          contentType: options?.contentType || "image/webp",
          upsert: true,
        }
      );

      const { data, error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          upsert: true,
          contentType: options?.contentType || "image/webp",
        });

      console.log("[STORAGE_SERVICE] Upload response:", { data, error });

      if (error) {
        console.error("[STORAGE_SERVICE] Error uploading customer avatar:", {
          error,
          message: error.message,
          filePath,
          bucket,
        });
        return {
          success: false,
          error: `Failed to upload avatar: ${error.message || error}`,
        };
      }

      console.log("[STORAGE_SERVICE] Upload successful:", data);

      // Get public URL for the uploaded file
      const { data: publicUrlData } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return {
        success: true,
        data: {
          path: data.path,
          fullPath: data.fullPath,
          publicUrl: publicUrlData.publicUrl,
        },
      };
    }

    // For business logos, use the regular upload function
    const folder = "logos";
    return uploadFile(file, bucket, folder, filename, {
      upsert: true, // Allow overwriting existing profile images
      contentType: options?.contentType,
    });
  } catch (error) {
    console.error(
      "[STORAGE_SERVICE] Unexpected error in uploadProfileImage:",
      error
    );
    return { success: false, error: "An unexpected error occurred" };
  }
}

/**
 * Delete profile image
 * Convenience method for profile image deletion
 */
export async function deleteProfileImage(
  type: "business" | "customer",
  filePath: string
): Promise<DeleteResult> {
  const bucket = type === "business" ? "business" : "customers";
  return deleteFile(bucket, filePath);
}

/**
 * Extract file path from URL
 * Helper function to get file path from public URL for deletion
 */
export function extractFilePathFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split("/");

    // Find the bucket name and extract everything after it
    const bucketIndex = pathParts.findIndex(
      (part) => part === "business" || part === "customers"
    );

    if (bucketIndex === -1 || bucketIndex === pathParts.length - 1) {
      return null;
    }

    return pathParts.slice(bucketIndex + 1).join("/");
  } catch (error) {
    console.error(
      "[STORAGE_SERVICE] Error extracting file path from URL:",
      error
    );
    return null;
  }
}
