import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { z } from 'zod';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';
import { 
  extractBearerToken, 
  verifyJWTToken 
} from '@/lib/middleware/jwt';
import { 
  verifyHMACMiddleware 
} from '@/lib/middleware/hmac';
import { TablesInsert } from '@/types/supabase';

/**
 * Security middleware wrapper for likes API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'likes_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature if required
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, requireHMAC);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { success: true, jwtPayload: jwtResult.payload };
}

// Schema for listing likes
const listLikesSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  business_id: z.string().optional(),
  customer_id: z.string().optional(),
  like_type: z.enum(["business", "post"]).optional(),
});

/**
 * GET /api/likes - List likes with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for listing likes)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listLikesSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      page = 1,
      limit = 20,
      business_id,
      customer_id,
      like_type
    } = validation.data;
    const offset = (page - 1) * limit;

    const supabase = await createClient();

    // Build query based on like type
    let query;
    
    if (like_type === "business" || (!like_type && business_id)) {
      // Business likes
      query = supabase
        .from('likes')
        .select(`
          id, customer_id, business_id, created_at,
          customer_profiles!customer_id(id, name, avatar_url),
          business_profiles!business_id(id, business_name, logo_url)
        `, { count: 'exact' });

      if (business_id) {
        query = query.eq('business_id', business_id);
      }
      if (customer_id) {
        query = query.eq('customer_id', customer_id);
      }
    } else if (like_type === "post") {
      // Post likes
      query = supabase
        .from('post_likes')
        .select(`
          id, customer_id, post_id, post_type, created_at,
          customer_profiles!customer_id(id, name, avatar_url)
        `, { count: 'exact' });

      if (customer_id) {
        query = query.eq('customer_id', customer_id);
      }
    } else {
      // Default to business likes if no specific type
      query = supabase
        .from('likes')
        .select(`
          id, customer_id, business_id, created_at,
          customer_profiles!customer_id(id, name, avatar_url),
          business_profiles!business_id(id, business_name, logo_url)
        `, { count: 'exact' });
    }

    // Add sorting and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: likes, error, count } = await query;

    if (error) {
      console.error('Error fetching likes:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch likes' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const totalCount = count || 0;
    const hasMore = offset + limit < totalCount;
    const nextPage = hasMore ? page + 1 : null;

    return new NextResponse(JSON.stringify({
      likes: likes || [],
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
        nextPage,
      },
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in likes API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for creating likes
const createLikeSchema = z.object({
  like_type: z.enum(["business", "post"]),
  business_id: z.string().optional(),
  post_id: z.string().optional(),
  post_type: z.enum(["business", "customer"]).optional(),
});

/**
 * POST /api/likes - Create a new like (like a business or post)
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = createLikeSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request body',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { like_type, business_id, post_id, post_type } = validation.data;
    const supabase = await createClient();

    let likeData;
    let tableName;

    if (like_type === "business") {
      if (!business_id) {
        return new NextResponse(JSON.stringify({ error: 'business_id is required for business likes' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      tableName = 'likes';
      likeData = {
        customer_id: jwtPayload.sub,
        business_id,
      };
    } else if (like_type === "post") {
      if (!post_id || !post_type) {
        return new NextResponse(JSON.stringify({ error: 'post_id and post_type are required for post likes' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }

            tableName = 'post_likes';
      likeData = {
        user_id: jwtPayload.sub,
        post_id,
        post_source: post_type,
      } as TablesInsert<'post_likes'>;
    } else {
      return new NextResponse(JSON.stringify({ error: 'Invalid like_type' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Check if like already exists
    const { data: existingLike } = await supabase
      .from(tableName as 'likes' | 'post_likes')
      .select('id')
      .match(likeData as TablesInsert<'likes'> | TablesInsert<'post_likes'>)
      .maybeSingle();

    if (existingLike) {
      return new NextResponse(JSON.stringify({ error: 'Already liked' }), {
        status: 409,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Create the like
    const { data: like, error } = await supabase
      .from(tableName as 'likes' | 'post_likes')
      .insert(likeData as TablesInsert<'likes'> | TablesInsert<'post_likes'>)
      .select()
      .single();

    if (error) {
      console.error('Error creating like:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to create like' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      like,
      message: 'Like created successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create like API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for deleting likes
const deleteLikeSchema = z.object({
  like_type: z.enum(["business", "post"]),
  business_id: z.string().optional(),
  post_id: z.string().optional(),
  post_type: z.enum(["business", "customer"]).optional(),
});

/**
 * DELETE /api/likes - Remove a like (unlike a business or post)
 */
export async function DELETE(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = deleteLikeSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request body',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { like_type, business_id, post_id, post_type } = validation.data;
    const supabase = await createClient();

    let whereClause;
    let tableName;

    if (like_type === "business") {
      if (!business_id) {
        return new NextResponse(JSON.stringify({ error: 'business_id is required for business likes' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }

            tableName = 'likes';
      whereClause = {
        user_id: jwtPayload.sub,
        business_profile_id: business_id,
      };
    } else if (like_type === "post") {
      if (!post_id || !post_type) {
        return new NextResponse(JSON.stringify({ error: 'post_id and post_type are required for post likes' }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      tableName = 'post_likes';
      whereClause = {
        user_id: jwtPayload.sub,
        post_id,
        post_source: post_type,
      };
    } else {
      return new NextResponse(JSON.stringify({ error: 'Invalid like_type' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Delete the like
    const { error } = await supabase
      .from(tableName as 'likes' | 'post_likes')
      .delete()
      .match(whereClause as TablesInsert<'likes'> | TablesInsert<'post_likes'>);

    if (error) {
      console.error('Error deleting like:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to delete like' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: 'Like removed successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in delete like API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
