import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Security middleware wrapper for sitemap API routes
 * Sitemap is public but still needs rate limiting
 */
async function applySecurityMiddleware(req: NextRequest) {
  // Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'sitemap_generation',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  return { success: true };
}

/**
 * GET /api/business/sitemap - Get business profiles for sitemap generation
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const supabase = await createClient();

    // Fetch all business profiles with status "online"
    const { data: profiles, error: profilesError } = await supabase
      .from('business_profiles')
      .select('business_slug, updated_at')
      .eq('status', 'online') // Only fetch online profiles
      .not('business_slug', 'is', null); // Ensure business_slug is not null

    if (profilesError) {
      console.error('Error fetching profiles for sitemap:', profilesError);
      return new NextResponse(JSON.stringify({ error: 'Database error fetching profiles' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // If there are no profiles, return empty array
    if (!profiles || profiles.length === 0) {
      return NextResponse.json({
        profiles: [],
      });
    }

    // Create a map to deduplicate by business_slug
    const uniqueProfiles = new Map<
      string,
      { business_slug: string; updated_at: string }
    >();

    // Add all profiles to the map (this automatically deduplicates by business_slug)
    profiles.forEach((profile) => {
      if (profile.business_slug) {
        uniqueProfiles.set(profile.business_slug, {
          business_slug: profile.business_slug,
          updated_at: profile.updated_at,
        });
      }
    });

    // Convert map values to array
    const combinedProfiles = Array.from(uniqueProfiles.values());

    return NextResponse.json({
      profiles: combinedProfiles,
    });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/sitemap:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
