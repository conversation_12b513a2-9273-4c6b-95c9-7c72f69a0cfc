import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
// Removed getPlanLimit import as we're now free for all users
import ProductsPageClient from "./components/ProductsPageClient";
import { internalGet } from "@/lib/utils/internalApiClient";

// Add metadata
export const metadata: Metadata = {
  title: "Manage Products",
  robots: "noindex, nofollow",
};

export default async function ProductsPage() {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect("/login"); // Redirect to login if not authenticated
  }

  // Fetch initial products data using internal API client
  let initialData: any[] = [];
  let initialCount = 0;
  let error: string | undefined;

  try {
    const response = await internalGet('/api/products?page=1&limit=10', { userId: user.id });

    if (response.success && response.data) {
      initialData = response.data.products || [];
      initialCount = response.data.pagination?.total || 0;
    } else {
      error = response.error || 'Failed to fetch products';
    }
  } catch (err) {
    console.error('Error fetching products:', err);
    error = 'Failed to fetch products';
  }

  // Platform is now free for all users - no product limits
  return (
    <ProductsPageClient
      initialData={initialData}
      initialCount={initialCount}
      error={error}
    />
  );
}
