const CHUNK_PUBLIC_PATH = "server/app/api/products/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_29e1b139._.js");
runtime.loadChunk("server/chunks/node_modules_next_04ac0c63._.js");
runtime.loadChunk("server/chunks/node_modules_tr46_816df9d9._.js");
runtime.loadChunk("server/chunks/node_modules_ws_686b49a1._.js");
runtime.loadChunk("server/chunks/node_modules_@supabase_auth-js_dist_module_4e4e8dc6._.js");
runtime.loadChunk("server/chunks/node_modules_zod_lib_index_mjs_a0234164._.js");
runtime.loadChunk("server/chunks/node_modules_@upstash_redis_008e28e7._.js");
runtime.loadChunk("server/chunks/node_modules_731ecb91._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__637cbcb2._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/products/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/products/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
