"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import {
  GalleryImage,
  UploadGalleryImageResponse,
  DeleteGalleryImageResponse,
  GetGalleryImagesResponse
} from "./types";
import { internalPost, internalDelete, internalPatch, internalGet } from "@/lib/utils/internalApiClient";


/**
 * Upload a gallery image
 */
export async function uploadGalleryImage(
  formData: FormData
): Promise<UploadGalleryImageResponse> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to upload gallery image
    const response = await internalPost('/api/gallery', formData, { userId: user.id });

    if (!response.success) {
      return { success: false, error: response.error || "Failed to upload gallery image." };
    }

    // Revalidate the gallery page
    revalidatePath("/dashboard/business/gallery");

    return {
      success: true,
      image: response.data?.image
    };
  } catch (error) {
    console.error("Unexpected error during gallery image upload:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Update gallery images order
 */
export async function updateGalleryOrder(
  orderedImages: GalleryImage[]
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to update gallery order
    const response = await internalPatch('/api/gallery', { orderedImages }, { userId: user.id });

    if (!response.success) {
      return { success: false, error: response.error || "Failed to update gallery order." };
    }

    // Revalidate the gallery page
    revalidatePath("/dashboard/business/gallery");
    return { success: true };
  } catch (error) {
    console.error("Error updating gallery order:", error);
    return {
      success: false,
      error: "An unexpected error occurred while updating gallery order.",
    };
  }
}

export async function deleteGalleryImage(
  imageId: string
): Promise<DeleteGalleryImageResponse> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to delete gallery image
    const response = await internalDelete(`/api/gallery?imageId=${imageId}`, { userId: user.id });

    if (!response.success) {
      return { success: false, error: response.error || "Failed to delete gallery image." };
    }

    // Revalidate the gallery page
    revalidatePath("/dashboard/business/gallery");

    return { success: true };
  } catch (error) {
    console.error("Unexpected error during gallery image deletion:", error);
    return {
      success: false,
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Get all gallery images for the current user
 */
export async function getGalleryImages(): Promise<GetGalleryImagesResponse> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { images: [], error: "User not authenticated." };
  }

  try {
    // Use internal API client to get gallery images
    const response = await internalGet("/api/gallery", { userId: user.id });

    if (!response.success) {
      return { images: [], error: response.error || "Failed to get gallery images." };
    }

    return { images: response.data?.images || [] };
  } catch (error) {
    console.error("Unexpected error fetching gallery images:", error);
    return {
      images: [],
      error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}