"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { updateProductFormSchema, ProductServiceData } from "./schemas";
import { handleBaseProductImageUpload } from "./imageHandlers";
import { internalPatch, internalGet } from "@/lib/utils/internalApiClient";


// Update an existing product/service
export async function updateProductService(
  itemId: string,
  formData: FormData
): Promise<{ success: boolean; error?: string; data?: ProductServiceData }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  // Extract form values and image data
  // Define a more specific type for form values before validation
  type RawFormValues = Record<
    string,
    string | File | undefined | boolean | number | null | string[]
  >; // Allow string[] for categories/tags
  const formValues: RawFormValues = {};
  const productImages: (File | null)[] = [];
  let featuredImageIndex = 0;
  const removedImageIndices: number[] = [];

  for (const [key, value] of formData.entries()) {
    if (key.startsWith("productImage_")) {
      // Multi-image upload format: productImage_0, productImage_1, etc.
      const index = parseInt(key.split('_')[1], 10);
      if (value instanceof File && value.size > 0) {
        // Ensure array has enough slots
        while (productImages.length <= index) {
          productImages.push(null);
        }
        productImages[index] = value;
      }
    } else if (key === "featuredImageIndex") {
      featuredImageIndex = parseInt(value as string, 10) || 0;
    } else if (key === "removedImageIndices") {
      // Parse the JSON array of removed indices
      try {
        const indices = JSON.parse(value as string);
        if (Array.isArray(indices)) {
          indices.forEach(index => {
            if (typeof index === 'number') {
              removedImageIndices.push(index);
            }
          });
        }
      } catch (error) {
        console.error(`Error parsing removedImageIndices:`, error);
      }
    } else {
      formValues[key] = value;
    }
  }

  // categories/tags assignment removed

  // Coerce numeric/boolean fields (simplified)
  if (formValues.product_type && typeof formValues.product_type !== "string") {
    formValues.product_type = String(formValues.product_type);
  }
  if (formValues.base_price)
    formValues.base_price = Number(formValues.base_price);
  // offering_price removed
  if (formValues.discounted_price)
    // Coerce discounted_price
    formValues.discounted_price = Number(formValues.discounted_price);
  // inventory_count removed
  // display_order removed
  formValues.is_available =
    formValues.is_available === "true" || formValues.is_available === "on";

  // Validate extracted form values (simplified schema)
  const validatedFields = updateProductFormSchema.safeParse(formValues);
  if (!validatedFields.success) {
    console.error(
      "Update Product Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    const errors = validatedFields.error.flatten().fieldErrors;
    // Safely join error messages
    const errorMessages = Object.entries(errors)
      .map(
        ([field, messages]) =>
          `${field}: ${
            Array.isArray(messages) ? messages.join(", ") : messages
          }`
      )
      .join("; ");
    return { success: false, error: `Invalid data: ${errorMessages}` };
  }

  // Note: Product availability limit enforcement is now handled by database triggers
  // The check_product_limit() trigger function will automatically enforce limits
  // when products are updated

  // Prepare data for update (simplified)
  // Use validatedFields.data directly, but ensure non-updatable fields are excluded before calling supabase.update
  // Start with validated data, explicitly type as Partial<ProductServiceData> with updated_at
  const dataToUpdate: Partial<ProductServiceData> & { updated_at?: string } = {
    ...validatedFields.data,
  };

  // Add updated_at timestamp
  dataToUpdate.updated_at = new Date().toISOString();

  // Remove fields that should not be updated directly in the DB table
  delete dataToUpdate.id;
  delete dataToUpdate.business_id;
  delete dataToUpdate.created_at;
  // image_url is handled separately below, so remove it from the initial update object if present
  delete dataToUpdate.image_url;

  // 1. Handle Image Upload/Removal
  let imageUrlToUpdate: string | null | undefined = undefined;
  let imagesToUpdate: string[] | undefined = undefined;

  // First, get the current product to get existing images and name
  const currentProductResponse = await internalGet(`/api/products/${itemId}`, { userId: user.id });

  if (!currentProductResponse.success) {
    return {
      success: false,
      error: `Failed to fetch current product data: ${currentProductResponse.error}`,
    };
  }

  const currentProduct = currentProductResponse.data?.product;
  if (!currentProduct) {
    return {
      success: false,
      error: "Product not found",
    };
  }

  // Get the product name - use the updated name if provided, otherwise use the current name
  const _productName = validatedFields.data.name || currentProduct.name;

  // Handle multi-image upload
  if (productImages.length > 0 || removedImageIndices.length > 0) {
    const existingImages = (currentProduct?.images as string[]) || [];

    const uploadResult = await handleBaseProductImageUpload(
      user.id,
      itemId,
      productImages,
      existingImages,
      removedImageIndices
    );

    if (uploadResult.error) {
      return {
        success: false,
        error: `Failed to update images: ${uploadResult.error}`,
      };
    }

    imagesToUpdate = uploadResult.urls;

    // Set the featured image URL as the main image_url
    if (Array.isArray(imagesToUpdate) && imagesToUpdate.length > 0) {
      const featuredIdx = Math.min(featuredImageIndex, imagesToUpdate.length - 1);
      imageUrlToUpdate = imagesToUpdate[featuredIdx];

      // Update the data object
      dataToUpdate.image_url = imageUrlToUpdate;
      dataToUpdate.images = imagesToUpdate;
      dataToUpdate.featured_image_index = featuredIdx;
    } else {
      // No images left
      dataToUpdate.image_url = null;
      dataToUpdate.images = [];
      dataToUpdate.featured_image_index = 0;
    }
  }

  // 2. Update products_services table

  try {
    const updateResponse = await internalPatch(`/api/products/${itemId}`, dataToUpdate, { userId: user.id });

    if (!updateResponse.success) {
      console.error(`Failed to update product ${itemId}:`, updateResponse.error);

      // Check if this is a product limit error from the database trigger
      if (updateResponse.error?.includes('Cannot make product available') &&
          updateResponse.error?.includes('reached the limit')) {
        return {
          success: false,
          error: updateResponse.error, // Use the API error message directly
        };
      }

      if (updateResponse.error?.includes('not found') || updateResponse.error?.includes('permission'))
        return {
          success: false,
          error:
            "Product/Service not found or you don't have permission to edit it.",
        };
      return {
        success: false,
        error: `Failed to update product/service: ${updateResponse.error}`,
      };
    }

    const updatedProduct = updateResponse.data?.product;
    if (!updatedProduct) {
      return {
        success: false,
        error: "Failed to get updated product data",
      };
    }

    // Revalidate the path to refresh the UI
    revalidatePath("/dashboard/business/products");

    return { success: true, data: updatedProduct as unknown as ProductServiceData };
  } catch (error) {
    console.error(`Unexpected error updating product ${itemId}:`, error);
    return {
      success: false,
      error: `Unexpected error updating product: ${error instanceof Error ? error.message : String(error)}`,
    };
  }

  // 3. Handle Categories - Removed
  // 4. Handle Tags - Removed
}