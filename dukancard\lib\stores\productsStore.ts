import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface Product {
  id: string;
  business_id: string;
  name: string;
  description?: string;
  base_price: number;
  discounted_price?: number;
  product_type: 'physical' | 'service';
  is_available: boolean;
  image_url?: string;
  images?: string[];
  featured_image_index?: number;
  slug?: string;
  created_at: string;
  updated_at: string;
  business_profiles?: {
    id: string;
    business_name: string;
    business_slug: string;
    city: string;
    state: string;
    pincode: string;
    locality: string;
    logo_url?: string;
    status: string;
  };
}

export interface ProductsState {
  products: Product[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  filters: {
    search?: string;
    category?: string;
    business_id?: string;
    product_type?: 'physical' | 'service';
    is_available?: boolean;
    sort_by?: string;
    pincode?: string;
    locality?: string;
    city?: string;
    state?: string;
  };
}

export interface ProductsActions {
  // Fetch products with filters
  fetchProducts: (_filters?: Partial<ProductsState['filters']>, _page?: number) => Promise<void>;

  // Load more products (for infinite scroll)
  loadMoreProducts: () => Promise<void>;

  // Create a new product
  createProduct: (_productData: Omit<Product, 'id' | 'created_at' | 'updated_at' | 'business_profiles'>) => Promise<Product | null>;

  // Update a product
  updateProduct: (_id: string, _productData: Partial<Product>) => Promise<Product | null>;

  // Delete a product
  deleteProduct: (_id: string) => Promise<boolean>;

  // Get a single product
  getProduct: (_id: string) => Promise<Product | null>;
  
  // Set filters
  setFilters: (_filters: Partial<ProductsState['filters']>) => void;
  
  // Clear filters
  clearFilters: () => void;
  
  // Reset state
  reset: () => void;
  
  // Clear error
  clearError: () => void;
}

type ProductsStore = ProductsState & ProductsActions;

const initialState: ProductsState = {
  products: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
    nextPage: null,
  },
  filters: {},
};

export const useProductsStore = create<ProductsStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      fetchProducts: async (filters = {}, page = 1) => {
        set({ loading: true, error: null });

        try {
          // Merge with existing filters
          const currentFilters = get().filters;
          const newFilters = { ...currentFilters, ...filters };

          // Build query parameters
          const queryParams = new URLSearchParams();
          queryParams.set('page', page.toString());
          queryParams.set('limit', get().pagination.limit.toString());

          // Add filters to query params
          Object.entries(newFilters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.set(key, value.toString());
            }
          });

          // Get auth token if available
          const headers: Record<string, string> = {
            'Content-Type': 'application/json',
          };

          // Try to get session from Supabase (this is just for auth, not direct DB access)
          try {
            const { createClient } = await import('@/utils/supabase/client');
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();
            
            if (session) {
              headers['Authorization'] = `Bearer ${session.access_token}`;
            }
          } catch (authError) {
            console.warn('Could not get auth session:', authError);
          }

          const response = await fetch(`/api/products?${queryParams.toString()}`, {
            method: 'GET',
            headers,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to fetch products');
          }

          const products = result.products || [];
          const pagination = result.pagination || {};

          set({
            products: page === 1 ? products : [...get().products, ...products],
            pagination: {
              page,
              limit: pagination.limit || get().pagination.limit,
              total: pagination.total || 0,
              hasMore: pagination.hasMore || false,
              nextPage: pagination.nextPage || null,
            },
            filters: newFilters,
            loading: false,
            error: null,
          });
        } catch (error) {
          console.error('Error fetching products:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch products',
          });
        }
      },

      loadMoreProducts: async () => {
        const { pagination, filters } = get();
        if (!pagination.hasMore || get().loading) return;

        await get().fetchProducts(filters, pagination.page + 1);
      },

      createProduct: async (productData) => {
        set({ loading: true, error: null });

        try {
          // Get auth token
          const { createClient } = await import('@/utils/supabase/client');
          const supabase = createClient();
          const { data: { session } } = await supabase.auth.getSession();

          if (!session) {
            throw new Error('Authentication required');
          }

          const response = await fetch('/api/products', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: JSON.stringify(productData),
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to create product');
          }

          const newProduct = result.product;

          // Add to the beginning of the products list
          set({
            products: [newProduct, ...get().products],
            loading: false,
            error: null,
          });

          return newProduct;
        } catch (error) {
          console.error('Error creating product:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to create product',
          });
          return null;
        }
      },

      updateProduct: async (id, productData) => {
        set({ loading: true, error: null });

        try {
          // Get auth token
          const { createClient } = await import('@/utils/supabase/client');
          const supabase = createClient();
          const { data: { session } } = await supabase.auth.getSession();

          if (!session) {
            throw new Error('Authentication required');
          }

          const response = await fetch(`/api/products/${id}`, {
            method: 'PATCH',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: JSON.stringify(productData),
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to update product');
          }

          const updatedProduct = result.product;

          // Update the product in the list
          set({
            products: get().products.map(p => p.id === id ? updatedProduct : p),
            loading: false,
            error: null,
          });

          return updatedProduct;
        } catch (error) {
          console.error('Error updating product:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to update product',
          });
          return null;
        }
      },

      deleteProduct: async (id) => {
        set({ loading: true, error: null });

        try {
          // Get auth token
          const { createClient } = await import('@/utils/supabase/client');
          const supabase = createClient();
          const { data: { session } } = await supabase.auth.getSession();

          if (!session) {
            throw new Error('Authentication required');
          }

          const response = await fetch(`/api/products/${id}`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to delete product');
          }

          // Remove the product from the list
          set({
            products: get().products.filter(p => p.id !== id),
            loading: false,
            error: null,
          });

          return true;
        } catch (error) {
          console.error('Error deleting product:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to delete product',
          });
          return false;
        }
      },

      getProduct: async (id) => {
        try {
          // Get auth token if available
          const headers: Record<string, string> = {
            'Content-Type': 'application/json',
          };

          try {
            const { createClient } = await import('@/utils/supabase/client');
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();
            
            if (session) {
              headers['Authorization'] = `Bearer ${session.access_token}`;
            }
          } catch (authError) {
            console.warn('Could not get auth session:', authError);
          }

          const response = await fetch(`/api/products/${id}`, {
            method: 'GET',
            headers,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to fetch product');
          }

          return result.product;
        } catch (error) {
          console.error('Error fetching product:', error);
          set({ error: error instanceof Error ? error.message : 'Failed to fetch product' });
          return null;
        }
      },

      setFilters: (filters) => {
        set({ filters: { ...get().filters, ...filters } });
      },

      clearFilters: () => {
        set({ filters: {} });
      },

      reset: () => {
        set(initialState);
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'products-store',
    }
  )
);
