{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/combinedActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\nimport { DiscoverSearchResult } from \"./types\";\r\nimport { fetchBusinessesBySearch } from \"./businessActions\";\r\nimport { fetchAllProducts, fetchProductsByBusinessIds } from \"./productActions\";\r\nimport { searchDiscoverData } from \"./locationActions\";\r\n\r\n// Combined search function that handles both business and product searches\r\nexport async function searchDiscoverCombined(params: {\r\n  businessName?: string | null;\r\n  productName?: string | null;\r\n  pincode?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  category?: string | null;\r\n  viewType: \"cards\" | \"products\";\r\n  page?: number;\r\n  limit?: number;\r\n  businessSort?: BusinessSortBy | string;\r\n  productSort?: BusinessSortBy | string;\r\n  productType?: \"physical\" | \"service\" | null;\r\n}): Promise<{\r\n  data?: DiscoverSearchResult;\r\n  error?: string;\r\n}> {\r\n  const {\r\n    businessName,\r\n    productName,\r\n    pincode,\r\n    city,\r\n    locality,\r\n    category,\r\n    viewType,\r\n    page = 1,\r\n    limit = viewType === \"products\" ? 20 : 5,\r\n    businessSort = \"created_desc\",\r\n    productSort = \"created_desc\",\r\n    productType = null,\r\n  } = params;\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Check Authentication\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n\r\n  try {\r\n    // Case 1: Search by product name (only for products view)\r\n    if (\r\n      viewType === \"products\" &&\r\n      productName &&\r\n      productName.trim().length > 0\r\n    ) {\r\n      // Fetch products by name\r\n      const productsResult = await fetchAllProducts({\r\n        page,\r\n        limit,\r\n        sortBy: productSort as BusinessSortBy,\r\n        productType,\r\n        pincode,\r\n        locality,\r\n        productName,\r\n        category,\r\n      });\r\n\r\n      if (productsResult.error) {\r\n        return { error: productsResult.error };\r\n      }\r\n\r\n      return {\r\n        data: {\r\n          products: productsResult.data?.products || [],\r\n          isAuthenticated,\r\n          totalCount: productsResult.data?.totalCount || 0,\r\n          hasMore: productsResult.data?.hasMore || false,\r\n          nextPage: productsResult.data?.nextPage || null,\r\n        },\r\n      };\r\n    }\r\n    // Case 2: Search by business name\r\n    else if (businessName && businessName.trim().length > 0) {\r\n      // First, search for businesses by name\r\n      const businessResult = await fetchBusinessesBySearch({\r\n        businessName,\r\n        pincode,\r\n        locality,\r\n        page,\r\n        limit,\r\n        sortBy: businessSort as BusinessSortBy,\r\n        category,\r\n      });\r\n\r\n      if (businessResult.error) {\r\n        return { error: businessResult.error };\r\n      }\r\n\r\n      if (viewType === \"cards\") {\r\n        // Return businesses directly\r\n        return {\r\n          data: {\r\n            businesses: businessResult.data?.businesses || [],\r\n            isAuthenticated,\r\n            totalCount: businessResult.data?.totalCount || 0,\r\n            hasMore: businessResult.data?.hasMore || false,\r\n            nextPage: businessResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      } else {\r\n        // viewType === \"products\"\r\n        // Get business IDs from the search results\r\n        const businessIds = businessResult.data?.businesses.map(\r\n          (business) => business.id\r\n        );\r\n\r\n        if (!businessIds || businessIds.length === 0) {\r\n          return {\r\n            data: {\r\n              products: [],\r\n              isAuthenticated,\r\n              totalCount: 0,\r\n              hasMore: false,\r\n              nextPage: null,\r\n            },\r\n          };\r\n        }\r\n\r\n        // Fetch products for these businesses\r\n        const productsResult = await fetchProductsByBusinessIds({\r\n          businessIds: businessIds as string[],\r\n          page,\r\n          limit,\r\n          sortBy: productSort as BusinessSortBy,\r\n          productType,\r\n        });\r\n\r\n        if (productsResult.error) {\r\n          return { error: productsResult.error };\r\n        }\r\n\r\n        return {\r\n          data: {\r\n            products: productsResult.data?.products || [],\r\n            isAuthenticated,\r\n            totalCount: productsResult.data?.totalCount || 0,\r\n            hasMore: productsResult.data?.hasMore || false,\r\n            nextPage: productsResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      }\r\n    }\r\n    // Case 3: Search by location (pincode or city)\r\n    else if (pincode || city) {\r\n      return await searchDiscoverData({\r\n        pincode: pincode || undefined,\r\n        city: city || undefined,\r\n        locality,\r\n        viewType,\r\n        page,\r\n        limit,\r\n        sortBy: (viewType === \"products\"\r\n          ? productSort\r\n          : businessSort) as BusinessSortBy,\r\n        productType: viewType === \"products\" ? productType : null,\r\n        category,\r\n      });\r\n    }\r\n    // Case 4: No search criteria - show all businesses or products\r\n    else {\r\n      if (viewType === \"cards\") {\r\n        // Fetch all businesses\r\n        const businessResult = await fetchBusinessesBySearch({\r\n          page,\r\n          limit,\r\n          sortBy: businessSort as BusinessSortBy,\r\n          category,\r\n        });\r\n\r\n        if (businessResult.error) {\r\n          return { error: businessResult.error };\r\n        }\r\n\r\n        return {\r\n          data: {\r\n            businesses: businessResult.data?.businesses || [],\r\n            isAuthenticated,\r\n            totalCount: businessResult.data?.totalCount || 0,\r\n            hasMore: businessResult.data?.hasMore || false,\r\n            nextPage: businessResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      } else {\r\n        // viewType === \"products\"\r\n        // Fetch all products\r\n\r\n        const productsResult = await fetchAllProducts({\r\n          page,\r\n          limit,\r\n          sortBy: productSort as BusinessSortBy,\r\n          productType,\r\n          pincode: pincode || undefined,\r\n          locality,\r\n          category,\r\n        });\r\n\r\n        if (productsResult.error) {\r\n          return { error: productsResult.error };\r\n        }\r\n\r\n        return {\r\n          data: {\r\n            products: productsResult.data?.products || [],\r\n            isAuthenticated,\r\n            totalCount: productsResult.data?.totalCount || 0,\r\n            hasMore: productsResult.data?.hasMore || false,\r\n            nextPage: productsResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.error(\"Search Discover Combined Exception:\", e);\r\n    return { error: \"An unexpected error occurred during the search.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAWsB,yBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/constants/paginationConstants.ts"], "sourcesContent": ["/**\r\n * Constants for pagination in the discover page\r\n */\r\n\r\n// Number of businesses to fetch per page\r\nexport const DISCOVER_BUSINESSES_PER_PAGE = 20;\r\n\r\n// Number of products to fetch per page\r\nexport const DISCOVER_PRODUCTS_PER_PAGE = 20;\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,yCAAyC;;;;;AAClC,MAAM,+BAA+B;AAGrC,MAAM,6BAA6B", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/utils/sortMappings.ts"], "sourcesContent": ["import { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { BusinessSortOption, ProductSortOption } from \"../context/types\";\r\n\r\n/**\r\n * Gets the sorting column from a sort option\r\n */\r\nexport function getSortingColumn(sortBy: string): string {\r\n  // Extract the column name from the sort option (e.g., \"created_desc\" -> \"created_at\")\r\n  const parts = sortBy.split('_');\r\n  if (parts.length < 2) return 'created_at';\r\n\r\n  // Special cases\r\n  if (parts[0] === 'likes') return 'total_likes';\r\n  if (parts[0] === 'subscriptions') return 'total_subscriptions';\r\n  if (parts[0] === 'rating') return 'average_rating';\r\n  if (parts[0] === 'newest') return 'created_at'; // Handle 'newest' as a special case\r\n\r\n  // Standard cases\r\n  if (parts[0] === 'created') return 'created_at';\r\n  if (parts[0] === 'name') return 'business_name';\r\n  if (parts[0] === 'price') return 'price';\r\n\r\n  return parts[0];\r\n}\r\n\r\n/**\r\n * Gets the sorting direction from a sort option\r\n */\r\nexport function getSortingDirection(sortBy: string): boolean {\r\n  // Extract the direction from the sort option (e.g., \"created_desc\" -> false for descending)\r\n  return !sortBy.endsWith('_desc');\r\n}\r\n\r\n/**\r\n * Maps a product sort option from the UI to the backend API sort parameter\r\n */\r\nexport function mapProductSortToBackend(\r\n  sortOption: ProductSortOption\r\n): BusinessSortBy | \"price_asc\" | \"price_desc\" {\r\n  switch (sortOption) {\r\n    case \"newest\":\r\n      return \"created_desc\";\r\n    case \"name_asc\":\r\n      return \"name_asc\";\r\n    case \"name_desc\":\r\n      return \"name_desc\";\r\n    case \"price_low\":\r\n      return \"price_asc\"; // Map to price_asc for proper sorting\r\n    case \"price_high\":\r\n      return \"price_desc\"; // Map to price_desc for proper sorting\r\n    default:\r\n      return \"created_desc\";\r\n  }\r\n}\r\n\r\n/**\r\n * Maps a business sort option from the UI to the backend API sort parameter\r\n */\r\nexport function mapBusinessSortToBackend(\r\n  sortOption: BusinessSortOption\r\n): BusinessSortBy {\r\n  switch (sortOption) {\r\n    case \"newest\":\r\n      return \"created_desc\";\r\n    case \"name_asc\":\r\n      return \"name_asc\";\r\n    case \"name_desc\":\r\n      return \"name_desc\";\r\n    case \"most_liked\":\r\n      return \"likes_desc\";\r\n    case \"most_subscribed\":\r\n      return \"subscriptions_desc\";\r\n    case \"highest_rated\":\r\n      return \"rating_desc\";\r\n    default:\r\n      return \"created_desc\";\r\n  }\r\n}\r\n\r\n/**\r\n * Maps a backend API sort parameter to a product sort option for the UI\r\n */\r\nexport function mapBackendToProductSort(\r\n  backendSort: string\r\n): ProductSortOption {\r\n  switch (backendSort) {\r\n    case \"created_desc\":\r\n      return \"newest\";\r\n    case \"name_asc\":\r\n      return \"name_asc\";\r\n    case \"name_desc\":\r\n      return \"name_desc\";\r\n    case \"price_asc\":\r\n      return \"price_low\";\r\n    case \"price_desc\":\r\n      return \"price_high\";\r\n    default:\r\n      return \"newest\";\r\n  }\r\n}\r\n\r\n/**\r\n * Maps a backend API sort parameter to a business sort option for the UI\r\n */\r\nexport function mapBackendToBusinessSort(\r\n  backendSort: BusinessSortBy\r\n): BusinessSortOption {\r\n  switch (backendSort) {\r\n    case \"created_desc\":\r\n      return \"newest\";\r\n    case \"name_asc\":\r\n      return \"name_asc\";\r\n    case \"name_desc\":\r\n      return \"name_desc\";\r\n    case \"likes_desc\":\r\n      return \"most_liked\";\r\n    case \"subscriptions_desc\":\r\n      return \"most_subscribed\";\r\n    case \"rating_desc\":\r\n      return \"highest_rated\";\r\n    default:\r\n      return \"newest\";\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAMO,SAAS,iBAAiB,MAAc;IAC7C,sFAAsF;IACtF,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,MAAM,MAAM,GAAG,GAAG,OAAO;IAE7B,gBAAgB;IAChB,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,OAAO;IACjC,IAAI,KAAK,CAAC,EAAE,KAAK,iBAAiB,OAAO;IACzC,IAAI,KAAK,CAAC,EAAE,KAAK,UAAU,OAAO;IAClC,IAAI,KAAK,CAAC,EAAE,KAAK,UAAU,OAAO,cAAc,oCAAoC;IAEpF,iBAAiB;IACjB,IAAI,KAAK,CAAC,EAAE,KAAK,WAAW,OAAO;IACnC,IAAI,KAAK,CAAC,EAAE,KAAK,QAAQ,OAAO;IAChC,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,OAAO;IAEjC,OAAO,KAAK,CAAC,EAAE;AACjB;AAKO,SAAS,oBAAoB,MAAc;IAChD,4FAA4F;IAC5F,OAAO,CAAC,OAAO,QAAQ,CAAC;AAC1B;AAKO,SAAS,wBACd,UAA6B;IAE7B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,aAAa,sCAAsC;QAC5D,KAAK;YACH,OAAO,cAAc,uCAAuC;QAC9D;YACE,OAAO;IACX;AACF;AAKO,SAAS,yBACd,UAA8B;IAE9B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,wBACd,WAAmB;IAEnB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,yBACd,WAA2B;IAE3B,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/constants/urlParamConstants.ts"], "sourcesContent": ["/**\r\n * Constants for URL parameters in the discover page\r\n */\r\n\r\n// Business tab URL parameters\r\nexport const BUSINESS_NAME_PARAM = \"businessName\";\r\nexport const BUSINESS_SORT_PARAM = \"businessSort\";\r\nexport const BUSINESS_FILTER_PARAM = \"businessFilter\";\r\n\r\n// Product tab URL parameters\r\nexport const PRODUCT_NAME_PARAM = \"productName\";\r\nexport const PRODUCT_SORT_PARAM = \"productSort\";\r\nexport const PRODUCT_TYPE_PARAM = \"productType\";\r\n\r\n// Common URL parameters\r\nexport const PINCODE_PARAM = \"pincode\";\r\nexport const CITY_PARAM = \"city\";\r\nexport const LOCALITY_PARAM = \"locality\";\r\nexport const VIEW_TYPE_PARAM = \"view\";\r\nexport const CATEGORY_PARAM = \"category\";\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,8BAA8B;;;;;;;;;;;;;;AACvB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,wBAAwB;AAG9B,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAG3B,MAAM,gBAAgB;AACtB,MAAM,aAAa;AACnB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB", "debugId": null}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/businessActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport {\r\n  BusinessSortBy,\r\n  getSecureBusinessProfiles,\r\n} from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"./combinedActions\";\r\n\r\n// Function to fetch more business cards for infinite scroll\r\nexport async function fetchMoreBusinessCardsCombined(params: {\r\n  businessName?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  page: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n}): Promise<{\r\n  data?: {\r\n    businesses: BusinessCardData[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    nextPage: number | null;\r\n  };\r\n  error?: string;\r\n}> {\r\n  // Reuse the searchDiscoverCombined function with viewType set to \"cards\"\r\n  const result = await searchDiscoverCombined({\r\n    ...params,\r\n    viewType: \"cards\",\r\n  });\r\n\r\n  if (result.error) {\r\n    return { error: result.error };\r\n  }\r\n\r\n  if (!result.data?.businesses) {\r\n    return { error: \"No business data found\" };\r\n  }\r\n\r\n  return {\r\n    data: {\r\n      businesses: result.data.businesses,\r\n      totalCount: result.data.totalCount,\r\n      hasMore: result.data.hasMore,\r\n      nextPage: result.data.nextPage,\r\n    },\r\n  };\r\n}\r\n\r\n// Function to fetch businesses by search criteria\r\nexport async function fetchBusinessesBySearch(params: {\r\n  businessName?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n  category?: string | null;\r\n}): Promise<{\r\n  data?: {\r\n    businesses: BusinessCardData[];\r\n    location?: { city: string; state: string } | null;\r\n    isAuthenticated: boolean;\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    nextPage: number | null;\r\n  };\r\n  error?: string;\r\n}> {\r\n  const {\r\n    businessName,\r\n    pincode,\r\n    locality,\r\n    page = 1,\r\n    limit = 20,\r\n    sortBy = \"created_desc\",\r\n    category = null,\r\n  } = params;\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Check Authentication\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n\r\n  try {\r\n    // Use the secure method to fetch business profiles\r\n    const {\r\n      data: businessesData,\r\n      count,\r\n      error: businessesError,\r\n    } = await getSecureBusinessProfiles(\r\n      businessName,\r\n      pincode,\r\n      locality,\r\n      page,\r\n      limit,\r\n      sortBy,\r\n      category\r\n    );\r\n\r\n    if (businessesError) {\r\n      console.error(\"Search Businesses By Name Error:\", businessesError);\r\n      return { error: businessesError };\r\n    }\r\n\r\n    const totalCount = count || 0;\r\n    // Calculate if there are more pages\r\n    const hasMore =\r\n      totalCount > (page - 1) * limit + (businessesData?.length || 0);\r\n    const nextPage = hasMore ? page + 1 : null;\r\n\r\n    // Map raw data to BusinessCardData, handling potential nulls\r\n    const businesses: BusinessCardData[] =\r\n      businessesData?.map((data) => {\r\n        // Use the actual data from the database\r\n        return {\r\n          id: data.id,\r\n          business_name: data.business_name ?? \"\",\r\n          contact_email: \"\", // Not included in secure data\r\n          // Subscription fields removed - all users now have access to all features\r\n          created_at: data.created_at ?? undefined,\r\n          updated_at: data.updated_at ?? undefined,\r\n          logo_url: data.logo_url ?? \"\",\r\n          member_name: data.member_name ?? \"\",\r\n          title: data.title ?? \"\",\r\n          address_line: data.address_line ?? \"\",\r\n          city: data.city ?? \"\",\r\n          state: data.state ?? \"\",\r\n          pincode: data.pincode ?? \"\",\r\n          locality: data.locality ?? \"\",\r\n          phone: data.phone ?? \"\",\r\n          business_category: data.business_category ?? \"\",\r\n          instagram_url: data.instagram_url ?? \"\",\r\n          facebook_url: data.facebook_url ?? \"\",\r\n          whatsapp_number: data.whatsapp_number ?? \"\",\r\n          about_bio: data.about_bio ?? \"\",\r\n          status: data.status === \"online\" ? \"online\" : \"offline\",\r\n          business_slug: data.business_slug ?? \"\",\r\n\r\n          // Include metrics data\r\n          total_likes: data.total_likes ?? 0,\r\n          total_subscriptions: data.total_subscriptions ?? 0,\r\n          average_rating: data.average_rating ?? 0,\r\n\r\n          // Use actual data if available, otherwise use defaults\r\n          // theme_color field removed - using default styling\r\n          delivery_info: data.delivery_info ?? \"\",\r\n          business_hours: data.business_hours,\r\n\r\n          established_year: data.established_year ?? null,\r\n\r\n          // Add default values for fields required by BusinessCardData but not in our query\r\n          website_url: \"\",\r\n          linkedin_url: \"\",\r\n          twitter_url: \"\",\r\n          youtube_url: \"\",\r\n          call_number: \"\", // This field doesn't exist in the database\r\n        };\r\n      }) ?? [];\r\n\r\n    return {\r\n      data: {\r\n        businesses,\r\n        isAuthenticated,\r\n        totalCount,\r\n        hasMore,\r\n        nextPage,\r\n      },\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Search Businesses Exception:\", e);\r\n    return { error: \"An unexpected error occurred during the search.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAWsB,iCAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/context/businessContextFunctions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useTransition } from \"react\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"../actions/combinedActions\";\r\nimport { fetchMoreBusinessCardsCombined } from \"../actions/businessActions\";\r\nimport { DISCOVER_BUSINESSES_PER_PAGE } from \"../constants/paginationConstants\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport {\r\n  BUSINESS_NAME_PARAM,\r\n  BUSINESS_SORT_PARAM,\r\n  PINCODE_PARAM,\r\n  LOCALITY_PARAM,\r\n} from \"../constants/urlParamConstants\";\r\nimport { DiscoverSearchResult, ViewType } from \"./types\";\r\n\r\n// Business context functions\r\nexport function useBusinessContextFunctions(\r\n  viewType: ViewType,\r\n  setIsSearching: (_value: boolean) => void,\r\n  setSearchResult: (_value: DiscoverSearchResult | null) => void,\r\n  setIsAuthenticated: (_value: boolean) => void,\r\n  setBusinesses: (_value: BusinessCardData[]) => void,\r\n  setHasMore: (_value: boolean) => void,\r\n  setTotalCount: (_value: number) => void,\r\n  setCurrentPage: (_value: number) => void,\r\n  setSortBy: (_value: BusinessSortBy) => void,\r\n  setSearchError: (_value: string | null) => void,\r\n  businesses: BusinessCardData[],\r\n  sortBy: BusinessSortBy\r\n) {\r\n  const searchParams = useSearchParams();\r\n  const [, startSearchTransition] = useTransition();\r\n\r\n  // Handle business sort change\r\n  const handleBusinessSortChange = (sortOption: BusinessSortBy) => {\r\n    if (sortOption !== sortBy) {\r\n      setSortBy(sortOption);\r\n      setIsSearching(true);\r\n\r\n      // Update URL to include the sort option\r\n      if (typeof window !== \"undefined\") {\r\n        const url = new URL(window.location.href);\r\n        // Set the new parameter and remove the old one\r\n        url.searchParams.set(BUSINESS_SORT_PARAM, sortOption);\r\n        url.searchParams.delete(\"sortBy\");\r\n        window.history.replaceState({}, \"\", url.toString());\r\n      }\r\n\r\n      // If we already have search results, perform the search again with the new sort\r\n      if (viewType === \"cards\") {\r\n        const businessName = searchParams.get(BUSINESS_NAME_PARAM) || null;\r\n        const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n        let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n        // Handle \"_any\" locality value\r\n        if (locality === \"_any\") {\r\n          locality = \"\";\r\n        }\r\n\r\n        startSearchTransition(async () => {\r\n          const result = await searchDiscoverCombined({\r\n            businessName,\r\n            pincode,\r\n            locality,\r\n            viewType,\r\n            page: 1,\r\n            limit: DISCOVER_BUSINESSES_PER_PAGE,\r\n            businessSort: sortOption,\r\n          });\r\n\r\n          if (result.data) {\r\n            setSearchResult(result.data);\r\n            setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n            if (result.data.businesses) {\r\n              setBusinesses(result.data.businesses);\r\n            }\r\n\r\n            setHasMore(result.data.hasMore);\r\n            setTotalCount(result.data.totalCount);\r\n            setCurrentPage(1);\r\n          } else {\r\n            setSearchError(result.error || \"Failed to fetch results.\");\r\n          }\r\n          setIsSearching(false);\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle business search\r\n  const handleBusinessSearch = (term: string) => {\r\n    // Prevent unnecessary API calls\r\n    if (viewType !== \"cards\") {\r\n      return;\r\n    }\r\n\r\n    setIsSearching(true);\r\n\r\n    // Add a flag to prevent duplicate calls\r\n    const currentSearchTerm = term;\r\n\r\n    // Update URL to include the search term\r\n    if (typeof window !== \"undefined\") {\r\n      const url = new URL(window.location.href);\r\n      if (currentSearchTerm) {\r\n        url.searchParams.set(BUSINESS_NAME_PARAM, currentSearchTerm);\r\n      } else {\r\n        url.searchParams.delete(BUSINESS_NAME_PARAM);\r\n      }\r\n      window.history.replaceState({}, \"\", url.toString());\r\n    }\r\n\r\n    // If search term is empty, reset to initial state\r\n    if (!currentSearchTerm) {\r\n      // If we have pincode/locality, search with those parameters\r\n      const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n      let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n      // Handle \"_any\" locality value\r\n      if (locality === \"_any\") {\r\n        locality = \"\";\r\n      }\r\n\r\n      startSearchTransition(async () => {\r\n        // Add a small delay to prevent race conditions\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n        const result = await searchDiscoverCombined({\r\n          businessName: null,\r\n          pincode,\r\n          locality,\r\n          viewType,\r\n          page: 1,\r\n          limit: DISCOVER_BUSINESSES_PER_PAGE,\r\n          businessSort: sortBy,\r\n        });\r\n\r\n        if (result.data) {\r\n          setSearchResult(result.data);\r\n          setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n          if (result.data.businesses) {\r\n            setBusinesses(result.data.businesses);\r\n          }\r\n\r\n          setHasMore(result.data.hasMore);\r\n          setTotalCount(result.data.totalCount);\r\n          setCurrentPage(1);\r\n        } else {\r\n          setSearchError(result.error || \"Failed to fetch results.\");\r\n        }\r\n        setIsSearching(false);\r\n      });\r\n      return;\r\n    }\r\n\r\n    // If search term is provided, search with it\r\n    const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n    let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n    // Handle \"_any\" locality value\r\n    if (locality === \"_any\") {\r\n      locality = \"\";\r\n    }\r\n\r\n    // Reset state before new search\r\n    setSearchError(null);\r\n    setBusinesses([]);\r\n    setCurrentPage(1);\r\n    setHasMore(false);\r\n    setTotalCount(0);\r\n\r\n    startSearchTransition(async () => {\r\n      // Add a small delay to prevent race conditions\r\n      await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n      const result = await searchDiscoverCombined({\r\n        businessName: currentSearchTerm,\r\n        pincode,\r\n        locality,\r\n        viewType,\r\n        page: 1,\r\n        limit: DISCOVER_BUSINESSES_PER_PAGE,\r\n        businessSort: sortBy,\r\n      });\r\n\r\n      if (result.data) {\r\n        setSearchResult(result.data);\r\n        setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n        if (result.data.businesses) {\r\n          setBusinesses(result.data.businesses);\r\n        }\r\n\r\n        setHasMore(result.data.hasMore);\r\n        setTotalCount(result.data.totalCount);\r\n        setCurrentPage(1);\r\n      } else {\r\n        setSearchError(result.error || \"Failed to fetch results.\");\r\n      }\r\n      setIsSearching(false);\r\n    });\r\n  };\r\n\r\n  // Load more businesses\r\n  const loadMoreBusinesses = async (\r\n    nextPage: number,\r\n    isLoadingMore: boolean,\r\n    setIsLoadingMore: (_value: boolean) => void\r\n  ) => {\r\n    if (isLoadingMore) {\r\n      return;\r\n    }\r\n\r\n    const businessName = searchParams.get(BUSINESS_NAME_PARAM) || null;\r\n    const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n    let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n    // Handle \"_any\" locality value\r\n    if (locality === \"_any\") {\r\n      locality = \"\";\r\n    }\r\n\r\n    setIsLoadingMore(true);\r\n\r\n    try {\r\n      // Add a small delay to prevent race conditions\r\n      await new Promise((resolve) => setTimeout(resolve, 300));\r\n\r\n      const result = await fetchMoreBusinessCardsCombined({\r\n        businessName,\r\n        pincode,\r\n        locality,\r\n        page: nextPage,\r\n        limit: DISCOVER_BUSINESSES_PER_PAGE,\r\n        sortBy,\r\n      });\r\n\r\n      if (result.data && result.data.businesses) {\r\n        if (result.data.businesses.length > 0) {\r\n          const newBusinesses = result.data.businesses;\r\n\r\n          // Filter out any businesses that already exist in the current list to avoid duplicates\r\n          const existingIds = new Set(businesses.map((b) => b.id));\r\n          const uniqueNewBusinesses = newBusinesses.filter(\r\n            (business) => !existingIds.has(business.id)\r\n          );\r\n\r\n          if (uniqueNewBusinesses.length > 0) {\r\n            // Combine previous businesses with new ones\r\n            const updatedBusinesses = [...businesses, ...uniqueNewBusinesses];\r\n            setBusinesses(updatedBusinesses);\r\n            // Use the hasMore flag from the server\r\n            setHasMore(result.data.hasMore);\r\n            setCurrentPage(nextPage);\r\n            return true;\r\n          } else {\r\n            // If all new businesses were duplicates, stop loading more\r\n            console.warn(\r\n              \"All new businesses were duplicates, stopping infinite scroll\"\r\n            );\r\n            setHasMore(false);\r\n            return false;\r\n          }\r\n        } else {\r\n          setHasMore(false);\r\n          return false;\r\n        }\r\n      } else {\r\n        setHasMore(false);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading more businesses:\", error);\r\n      setHasMore(false);\r\n      return false;\r\n    } finally {\r\n      setIsLoadingMore(false);\r\n    }\r\n  };\r\n\r\n  return {\r\n    handleBusinessSortChange,\r\n    handleBusinessSearch,\r\n    loadMoreBusinesses,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AACA;AACA;AAEA;AATA;;;;;;;AAkBO,SAAS,4BACd,QAAkB,EAClB,cAAyC,EACzC,eAA8D,EAC9D,kBAA6C,EAC7C,aAAmD,EACnD,UAAqC,EACrC,aAAuC,EACvC,cAAwC,EACxC,SAA2C,EAC3C,cAA+C,EAC/C,UAA8B,EAC9B,MAAsB;IAEtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,GAAG,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IAE9C,8BAA8B;IAC9B,MAAM,2BAA2B,CAAC;QAChC,IAAI,eAAe,QAAQ;YACzB,UAAU;YACV,eAAe;YAEf,wCAAwC;YACxC,uCAAmC;;YAMnC;YAEA,gFAAgF;YAChF,IAAI,aAAa,SAAS;gBACxB,MAAM,eAAe,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB,KAAK;gBAC9D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;gBACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;gBAEnD,+BAA+B;gBAC/B,IAAI,aAAa,QAAQ;oBACvB,WAAW;gBACb;gBAEA,sBAAsB;oBACpB,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;wBAC1C;wBACA;wBACA;wBACA;wBACA,MAAM;wBACN,OAAO,+JAAA,CAAA,+BAA4B;wBACnC,cAAc;oBAChB;oBAEA,IAAI,OAAO,IAAI,EAAE;wBACf,gBAAgB,OAAO,IAAI;wBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;wBAE9C,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;4BAC1B,cAAc,OAAO,IAAI,CAAC,UAAU;wBACtC;wBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;wBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;wBACpC,eAAe;oBACjB,OAAO;wBACL,eAAe,OAAO,KAAK,IAAI;oBACjC;oBACA,eAAe;gBACjB;YACF;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,uBAAuB,CAAC;QAC5B,gCAAgC;QAChC,IAAI,aAAa,SAAS;YACxB;QACF;QAEA,eAAe;QAEf,wCAAwC;QACxC,MAAM,oBAAoB;QAE1B,wCAAwC;QACxC,uCAAmC;;QAQnC;QAEA,kDAAkD;QAClD,IAAI,CAAC,mBAAmB;YACtB,4DAA4D;YAC5D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;YACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;YAEnD,+BAA+B;YAC/B,IAAI,aAAa,QAAQ;gBACvB,WAAW;YACb;YAEA,sBAAsB;gBACpB,+CAA+C;gBAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC1C,cAAc;oBACd;oBACA;oBACA;oBACA,MAAM;oBACN,OAAO,+JAAA,CAAA,+BAA4B;oBACnC,cAAc;gBAChB;gBAEA,IAAI,OAAO,IAAI,EAAE;oBACf,gBAAgB,OAAO,IAAI;oBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;oBAE9C,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;wBAC1B,cAAc,OAAO,IAAI,CAAC,UAAU;oBACtC;oBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;oBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;oBACpC,eAAe;gBACjB,OAAO;oBACL,eAAe,OAAO,KAAK,IAAI;gBACjC;gBACA,eAAe;YACjB;YACA;QACF;QAEA,6CAA6C;QAC7C,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;QACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;QAEnD,+BAA+B;QAC/B,IAAI,aAAa,QAAQ;YACvB,WAAW;QACb;QAEA,gCAAgC;QAChC,eAAe;QACf,cAAc,EAAE;QAChB,eAAe;QACf,WAAW;QACX,cAAc;QAEd,sBAAsB;YACpB,+CAA+C;YAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC1C,cAAc;gBACd;gBACA;gBACA;gBACA,MAAM;gBACN,OAAO,+JAAA,CAAA,+BAA4B;gBACnC,cAAc;YAChB;YAEA,IAAI,OAAO,IAAI,EAAE;gBACf,gBAAgB,OAAO,IAAI;gBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;gBAE9C,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;oBAC1B,cAAc,OAAO,IAAI,CAAC,UAAU;gBACtC;gBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;gBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;gBACpC,eAAe;YACjB,OAAO;gBACL,eAAe,OAAO,KAAK,IAAI;YACjC;YACA,eAAe;QACjB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,OACzB,UACA,eACA;QAEA,IAAI,eAAe;YACjB;QACF;QAEA,MAAM,eAAe,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB,KAAK;QAC9D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;QACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;QAEnD,+BAA+B;QAC/B,IAAI,aAAa,QAAQ;YACvB,WAAW;QACb;QAEA,iBAAiB;QAEjB,IAAI;YACF,+CAA+C;YAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,iCAA8B,AAAD,EAAE;gBAClD;gBACA;gBACA;gBACA,MAAM;gBACN,OAAO,+JAAA,CAAA,+BAA4B;gBACnC;YACF;YAEA,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;gBACzC,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG;oBACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,UAAU;oBAE5C,uFAAuF;oBACvF,MAAM,cAAc,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;oBACtD,MAAM,sBAAsB,cAAc,MAAM,CAC9C,CAAC,WAAa,CAAC,YAAY,GAAG,CAAC,SAAS,EAAE;oBAG5C,IAAI,oBAAoB,MAAM,GAAG,GAAG;wBAClC,4CAA4C;wBAC5C,MAAM,oBAAoB;+BAAI;+BAAe;yBAAoB;wBACjE,cAAc;wBACd,uCAAuC;wBACvC,WAAW,OAAO,IAAI,CAAC,OAAO;wBAC9B,eAAe;wBACf,OAAO;oBACT,OAAO;wBACL,2DAA2D;wBAC3D,QAAQ,IAAI,CACV;wBAEF,WAAW;wBACX,OAAO;oBACT;gBACF,OAAO;oBACL,WAAW;oBACX,OAAO;gBACT;YACF,OAAO;gBACL,WAAW;gBACX,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,WAAW;YACX,OAAO;QACT,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/productActions.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\n\n\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\nimport { NearbyProduct, getSortingColumn, getSortingDirection } from \"./types\";\nimport { searchDiscoverCombined } from \"./combinedActions\";\n\n// Define a type for the product result from Supabase\n// Unused type kept for reference\n/* type ProductResult = {\n  id: string;\n  business_id: string | null;\n  name: string | null;\n  description: string | null;\n  base_price: number | null;\n  discounted_price: number | null;\n  product_type: \"physical\" | \"service\" | null;\n  is_available: boolean | null;\n  image_url: string | null;\n  created_at: string | null;\n  updated_at: string | null;\n  business_profiles: {\n    business_slug: string | null;\n  } | null;\n}; */\n\n// Helper function to convert any product result to NearbyProduct\nfunction convertToNearbyProduct(\n  product: Record<string, unknown>\n): NearbyProduct {\n  // Extract business_slug from the joined business_profiles\n  let business_slug = null;\n  if (product.business_profiles) {\n    // Handle both object and array formats\n    if (Array.isArray(product.business_profiles)) {\n      business_slug = product.business_profiles[0]?.business_slug || null;\n    } else if (\n      product.business_profiles &&\n      typeof product.business_profiles === \"object\"\n    ) {\n      business_slug =\n        ((product.business_profiles as Record<string, unknown>)\n          .business_slug as string) || null;\n    }\n  }\n\n  return {\n    id: product.id as string,\n    business_id: product.business_id as string | undefined,\n    name: (product.name as string) || \"\",\n    description: (product.description as string) || \"\",\n    base_price: Number(product.base_price) || 0,\n    discounted_price: product.discounted_price\n      ? Number(product.discounted_price)\n      : undefined,\n    product_type:\n      (product.product_type as \"physical\" | \"service\") || \"physical\",\n    is_available: Boolean(product.is_available) || false,\n    image_url: product.image_url as string | undefined,\n    created_at: product.created_at as string | undefined,\n    updated_at: product.updated_at as string | undefined,\n    slug: product.slug as string | undefined,\n    business_slug: business_slug,\n    featured_image_index: 0, // Default value for NearbyProduct\n    images: [], // Default empty array for images\n  };\n}\n\n// Function to fetch more products with combined search for infinite scroll\nexport async function fetchMoreProductsCombined(params: {\n  businessName?: string | null;\n  productName?: string | null;\n  pincode?: string | null;\n  locality?: string | null;\n  page: number;\n  limit?: number;\n  sortBy?: BusinessSortBy;\n  productSort?: string;\n  productType?: \"physical\" | \"service\" | null;\n}): Promise<{\n  data?: {\n    products: NearbyProduct[];\n    totalCount: number;\n    hasMore: boolean;\n    nextPage: number | null;\n  };\n  error?: string;\n}> {\n  // Reuse the searchDiscoverCombined function with viewType set to \"products\"\n  const result = await searchDiscoverCombined({\n    ...params,\n    viewType: \"products\",\n  });\n\n  if (result.error) {\n    return { error: result.error };\n  }\n\n  if (!result.data?.products) {\n    return { error: \"No product data found\" };\n  }\n\n  return {\n    data: {\n      products: result.data.products,\n      totalCount: result.data.totalCount,\n      hasMore: result.data.hasMore,\n      nextPage: result.data.nextPage,\n    },\n  };\n}\n\n// Function to fetch all products\nexport async function fetchAllProducts(params: {\n  page?: number;\n  limit?: number;\n  sortBy?: BusinessSortBy;\n  productType?: \"physical\" | \"service\" | null;\n  pincode?: string | null;\n  locality?: string | null;\n  productName?: string | null;\n  category?: string | null;\n}): Promise<{\n  data?: {\n    products: NearbyProduct[];\n    isAuthenticated: boolean;\n    totalCount: number;\n    hasMore: boolean;\n    nextPage: number | null;\n  };\n  error?: string;\n}> {\n  const {\n    page = 1,\n    limit = 20,\n    sortBy = \"created_desc\",\n    productType = null,\n    pincode = null,\n    locality = null,\n    productName = null,\n    category = null,\n  } = params;\n\n  // Check Authentication\n  const supabase = await createClient();\n  const {\n    data: { session },\n  } = await supabase.auth.getSession();\n  const isAuthenticated = !!session;\n\n  try {\n    // Build query parameters for the products API\n    const queryParams = new URLSearchParams();\n    queryParams.set('page', page.toString());\n    queryParams.set('limit', limit.toString());\n    queryParams.set('is_available', 'true');\n\n    // Add filters\n    if (productType) {\n      queryParams.set('product_type', productType);\n    }\n    if (pincode) {\n      queryParams.set('pincode', pincode);\n    }\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n    if (productName) {\n      queryParams.set('search', productName);\n    }\n    if (category) {\n      queryParams.set('category', category);\n    }\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n      case 'likes_desc':\n      case 'subscriptions_asc':\n      case 'subscriptions_desc':\n      case 'rating_asc':\n      case 'rating_desc':\n        // These sorts are not supported for products, fall back to created_desc\n        apiSortBy = 'created_desc';\n        break;\n      default:\n        apiSortBy = 'created_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    // Use the products API endpoint\n    const headers: Record<string, string> = {\n      'Content-Type': 'application/json',\n    };\n\n    // Add authentication if available\n    if (session) {\n      headers['Authorization'] = `Bearer ${session.access_token}`;\n    }\n\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/products?${queryParams.toString()}`, {\n      method: 'GET',\n      headers,\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"Products API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch products\",\n      };\n    }\n\n    const products = result.products || [];\n    const pagination = result.pagination || {};\n    const totalCount = pagination.total || 0;\n\n    // Build the query for counting products - count products from valid businesses\n    let countQuery = supabase\n      .from(\"products_services\")\n      .select(\"id\", { count: \"exact\" })\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      countQuery = countQuery.eq(\"product_type\", productType);\n    }\n\n    // Add product name filter if provided\n    if (productName && productName.trim().length > 0) {\n      countQuery = countQuery.ilike(\"name\", `%${productName.trim()}%`);\n    }\n\n    // Get total count\n    const { count, error: countError } = await countQuery;\n\n    if (countError) {\n      console.error(\"Error counting products:\", countError);\n      return { error: \"Failed to count products\" };\n    }\n\n    // Build the query for fetching products from valid businesses\n    let productsQuery = supabase\n      .from(\"products_services\")\n      .select(\n        `\n        id, business_id, name, description, base_price, discounted_price, product_type,\n        is_available, image_url, created_at, updated_at, slug,\n        business_profiles!business_id(business_slug)\n      `\n      )\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      productsQuery = productsQuery.eq(\"product_type\", productType);\n    }\n\n    // Add product name filter if provided\n    if (productName && productName.trim().length > 0) {\n      productsQuery = productsQuery.ilike(\"name\", `%${productName.trim()}%`);\n    }\n\n    // Add sorting\n    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view\n    const sortAscending = getSortingDirection(sortBy);\n\n    // Special handling for price sorting to use discounted_price when available, otherwise base_price\n    if (sortColumn === \"price\") {\n      if (sortAscending) {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: true, nullsFirst: false })\n          .order(\"base_price\", { ascending: true, nullsFirst: false });\n      } else {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: false, nullsFirst: false })\n          .order(\"base_price\", { ascending: false, nullsFirst: false });\n      }\n    } else {\n      productsQuery = productsQuery.order(sortColumn, {\n        ascending: sortAscending,\n      });\n    }\n\n    // Add pagination\n    productsQuery = productsQuery.range(offset, offset + limit - 1);\n\n    // Execute the query\n    const { data: productsData, error: productsError } = await productsQuery;\n\n    if (productsError) {\n      console.error(\"Error fetching products:\", productsError);\n      return { error: \"Failed to fetch products\" };\n    }\n\n    // Process the products data to include business_slug\n    const products = productsData.map(convertToNearbyProduct);\n\n    // Calculate pagination info\n    const totalCount = count || 0;\n    const hasMore = totalCount > offset + products.length;\n    const nextPage = hasMore ? page + 1 : null;\n\n    return {\n      data: {\n        products,\n        isAuthenticated,\n        totalCount,\n        hasMore,\n        nextPage,\n      },\n    };\n  } catch (error) {\n    console.error(\"Unexpected error in fetchAllProducts:\", error);\n    return { error: \"An unexpected error occurred\" };\n  }\n}\n\n// Function to fetch products by business IDs\nexport async function fetchProductsByBusinessIds(params: {\n  businessIds: string[];\n  page?: number;\n  limit?: number;\n  sortBy?: BusinessSortBy;\n  productType?: \"physical\" | \"service\" | null;\n}): Promise<{\n  data?: {\n    products: NearbyProduct[];\n    isAuthenticated: boolean;\n    totalCount: number;\n    hasMore: boolean;\n    nextPage: number | null;\n  };\n  error?: string;\n}> {\n  const {\n    businessIds,\n    page = 1,\n    limit = 20,\n    sortBy = \"created_desc\",\n    productType = null,\n  } = params;\n\n  if (!businessIds || businessIds.length === 0) {\n    return {\n      error: \"No business IDs provided\",\n    };\n  }\n\n  // Check Authentication\n  const supabase = await createClient();\n  const {\n    data: { user },\n  } = await supabase.auth.getUser();\n  const isAuthenticated = !!user;\n\n  try {\n    const offset = (page - 1) * limit;\n    \n\n    // Filter the business IDs to only include online ones\n    const { data: validBusinesses, error: businessError } = await supabase\n      .from(\"business_profiles\")\n      .select(\"id\")\n      .in(\"id\", businessIds)\n      .eq(\"status\", \"online\");\n\n    if (businessError) {\n      console.error(\"Error filtering valid businesses:\", businessError);\n      return { error: \"Failed to filter valid businesses\" };\n    }\n\n    // If no valid businesses found, return empty result\n    if (!validBusinesses || validBusinesses.length === 0) {\n      return {\n        data: {\n          products: [],\n          isAuthenticated,\n          totalCount: 0,\n          hasMore: false,\n          nextPage: null,\n        },\n      };\n    }\n\n    // Get the IDs of valid businesses\n    const validBusinessIds = validBusinesses.map((b: { id: string; }) => b.id);\n\n    // Build the query for counting products\n    let countQuery = supabase\n      .from(\"products_services\")\n      .select(\"id\", { count: \"exact\" })\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      countQuery = countQuery.eq(\"product_type\", productType);\n    }\n\n    // Get total count\n    const { count, error: countError } = await countQuery;\n\n    if (countError) {\n      console.error(\"Error counting products:\", countError);\n      return { error: \"Failed to count products\" };\n    }\n\n    // Build the query for fetching products\n    let productsQuery = supabase\n      .from(\"products_services\")\n      .select(\n        `\n        id, business_id, name, description, base_price, discounted_price, product_type,\n        is_available, image_url, created_at, updated_at, slug,\n        business_profiles!business_id(business_slug)\n      `\n      )\n      .in(\"business_id\", validBusinessIds)\n      .eq(\"is_available\", true);\n\n    // Add product type filter if provided\n    if (productType) {\n      productsQuery = productsQuery.eq(\"product_type\", productType);\n    }\n\n    // Add sorting\n    const sortColumn = getSortingColumn(sortBy, true); // true indicates product view\n    const sortAscending = getSortingDirection(sortBy);\n\n    // Special handling for price sorting to use discounted_price when available, otherwise base_price\n    if (sortColumn === \"price\") {\n      if (sortAscending) {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: true, nullsFirst: false })\n          .order(\"base_price\", { ascending: true, nullsFirst: false });\n      } else {\n        productsQuery = productsQuery\n          .order(\"discounted_price\", { ascending: false, nullsFirst: false })\n          .order(\"base_price\", { ascending: false, nullsFirst: false });\n      }\n    } else {\n      productsQuery = productsQuery.order(sortColumn, {\n        ascending: sortAscending,\n      });\n    }\n\n    // Add pagination\n    productsQuery = productsQuery.range(offset, offset + limit - 1);\n\n    // Execute the query\n    const { data: productsData, error: productsError } = await productsQuery;\n\n    if (productsError) {\n      console.error(\"Error fetching products:\", productsError);\n      return { error: \"Failed to fetch products\" };\n    }\n\n    // Process the products data to include business_slug\n    const products = productsData.map(convertToNearbyProduct);\n\n    // Calculate pagination info\n    const totalCount = count || 0;\n    const hasMore = totalCount > offset + products.length;\n    const nextPage = hasMore ? page + 1 : null;\n\n    return {\n      data: {\n        products,\n        isAuthenticated,\n        totalCount,\n        hasMore,\n        nextPage,\n      },\n    };\n  } catch (error) {\n    console.error(\"Unexpected error in fetchProductsByBusinessIds:\", error);\n    return { error: \"An unexpected error occurred\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;IAuEsB,4BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/context/productContextFunctions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useTransition } from \"react\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"../actions/combinedActions\";\r\nimport { fetchMoreProductsCombined } from \"../actions/productActions\";\r\nimport { DISCOVER_PRODUCTS_PER_PAGE } from \"../constants/paginationConstants\";\r\nimport { NearbyProduct } from \"../actions/types\";\r\nimport {\r\n  PRODUCT_NAME_PARAM,\r\n  PRODUCT_SORT_PARAM,\r\n  PRODUCT_TYPE_PARAM,\r\n  PINCODE_PARAM,\r\n  LOCALITY_PARAM,\r\n} from \"../constants/urlParamConstants\";\r\nimport {\r\n  DiscoverSearchResult,\r\n  ProductFilterOption,\r\n  ProductSortOption,\r\n  ViewType,\r\n} from \"./types\";\r\nimport { mapProductSortToBackend } from \"../utils/sortMappings\";\r\n\r\n// Product context functions\r\nexport function useProductContextFunctions(\r\n  viewType: ViewType,\r\n  setIsSearching: (_value: boolean) => void,\r\n  setSearchResult: (_value: DiscoverSearchResult | null) => void,\r\n  setIsAuthenticated: (_value: boolean) => void,\r\n  setProducts: (_value: NearbyProduct[]) => void,\r\n  setHasMore: (_value: boolean) => void,\r\n  setTotalCount: (_value: number) => void,\r\n  setCurrentPage: (_value: number) => void,\r\n  setProductSortBy: (_value: ProductSortOption) => void,\r\n  setProductFilterBy: (_value: ProductFilterOption) => void,\r\n  setSearchError: (_value: string | null) => void,\r\n  products: NearbyProduct[],\r\n  sortBy: BusinessSortBy,\r\n  productSortBy: ProductSortOption,\r\n  productFilterBy: ProductFilterOption\r\n) {\r\n  const searchParams = useSearchParams();\r\n  const [, startSearchTransition] = useTransition();\r\n\r\n  // Handle product sort change\r\n  const handleProductSortChange = (sortOption: ProductSortOption) => {\r\n    if (sortOption !== productSortBy) {\r\n      setProductSortBy(sortOption);\r\n      setIsSearching(true);\r\n\r\n      // Update URL to include the sort option\r\n      if (typeof window !== \"undefined\") {\r\n        const url = new URL(window.location.href);\r\n        // Set the new parameter and remove the old one\r\n        url.searchParams.set(PRODUCT_SORT_PARAM, sortOption);\r\n        url.searchParams.delete(\"sortBy\");\r\n        window.history.replaceState({}, \"\", url.toString());\r\n      }\r\n\r\n      // Map the product sort option to a backend sort value\r\n      const mappedSortBy = mapProductSortToBackend(sortOption);\r\n\r\n      // If we already have search results, perform the search again with the new sort\r\n      if (viewType === \"products\") {\r\n        const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;\r\n        const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n        let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n        // Handle \"_any\" locality value\r\n        if (locality === \"_any\") {\r\n          locality = \"\";\r\n        }\r\n\r\n        startSearchTransition(async () => {\r\n          const result = await searchDiscoverCombined({\r\n            businessName: null,\r\n            productName,\r\n            pincode,\r\n            locality,\r\n            viewType,\r\n            page: 1,\r\n            limit: DISCOVER_PRODUCTS_PER_PAGE,\r\n            productSort: mappedSortBy,\r\n            productType: productFilterBy === \"all\" ? null : productFilterBy,\r\n          });\r\n\r\n          if (result.data) {\r\n            setSearchResult(result.data);\r\n            setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n            if (viewType === \"products\" && result.data.products) {\r\n              setProducts(result.data.products);\r\n            }\r\n\r\n            setHasMore(result.data.hasMore);\r\n            setTotalCount(result.data.totalCount);\r\n            setCurrentPage(1);\r\n          } else {\r\n            setSearchError(result.error || \"Failed to fetch results.\");\r\n          }\r\n          setIsSearching(false);\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  // Handle product search\r\n  const handleProductSearch = (term: string) => {\r\n    // Prevent unnecessary API calls\r\n    if (viewType !== \"products\") {\r\n      return;\r\n    }\r\n\r\n    setIsSearching(true);\r\n\r\n    // Add a flag to prevent duplicate calls\r\n    const currentSearchTerm = term;\r\n\r\n    // Update URL to include the search term\r\n    if (typeof window !== \"undefined\") {\r\n      const url = new URL(window.location.href);\r\n      if (currentSearchTerm) {\r\n        url.searchParams.set(PRODUCT_NAME_PARAM, currentSearchTerm);\r\n      } else {\r\n        url.searchParams.delete(PRODUCT_NAME_PARAM);\r\n      }\r\n      window.history.replaceState({}, \"\", url.toString());\r\n    }\r\n\r\n    // If search term is empty, reset to initial state\r\n    if (!currentSearchTerm) {\r\n      // If we have pincode/locality, search with those parameters\r\n      const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n      let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n      // Handle \"_any\" locality value\r\n      if (locality === \"_any\") {\r\n        locality = \"\";\r\n      }\r\n\r\n      startSearchTransition(async () => {\r\n        // Add a small delay to prevent race conditions\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n        // Map the product sort option to a backend sort value\r\n        const mappedSortBy = mapProductSortToBackend(productSortBy);\r\n\r\n        const result = await searchDiscoverCombined({\r\n          businessName: null,\r\n          productName: null,\r\n          pincode,\r\n          locality,\r\n          viewType,\r\n          page: 1,\r\n          limit: DISCOVER_PRODUCTS_PER_PAGE,\r\n          productSort: mappedSortBy,\r\n          productType: productFilterBy === \"all\" ? null : productFilterBy,\r\n        });\r\n\r\n        if (result.data) {\r\n          setSearchResult(result.data);\r\n          setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n          if (viewType === \"products\" && result.data.products) {\r\n            setProducts(result.data.products);\r\n          }\r\n\r\n          setHasMore(result.data.hasMore);\r\n          setTotalCount(result.data.totalCount);\r\n          setCurrentPage(1);\r\n        } else {\r\n          setSearchError(result.error || \"Failed to fetch results.\");\r\n        }\r\n        setIsSearching(false);\r\n      });\r\n      return;\r\n    }\r\n\r\n    // If search term is provided, search with it\r\n    const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n    let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n    // Handle \"_any\" locality value\r\n    if (locality === \"_any\") {\r\n      locality = \"\";\r\n    }\r\n\r\n    // Reset state before new search\r\n    setSearchError(null);\r\n    setProducts([]);\r\n    setCurrentPage(1);\r\n    setHasMore(false);\r\n    setTotalCount(0);\r\n\r\n    startSearchTransition(async () => {\r\n      // Add a small delay to prevent race conditions\r\n      await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n      // Map the product sort option to a backend sort value\r\n      const mappedSortBy = mapProductSortToBackend(productSortBy);\r\n\r\n      const result = await searchDiscoverCombined({\r\n        businessName: null,\r\n        productName: currentSearchTerm,\r\n        pincode,\r\n        locality,\r\n        viewType,\r\n        page: 1,\r\n        limit: DISCOVER_PRODUCTS_PER_PAGE,\r\n        productSort: mappedSortBy,\r\n        productType: productFilterBy === \"all\" ? null : productFilterBy,\r\n      });\r\n\r\n      if (result.data) {\r\n        setSearchResult(result.data);\r\n        setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n        if (viewType === \"products\" && result.data.products) {\r\n          setProducts(result.data.products);\r\n        }\r\n\r\n        setHasMore(result.data.hasMore);\r\n        setTotalCount(result.data.totalCount);\r\n        setCurrentPage(1);\r\n      } else {\r\n        setSearchError(result.error || \"Failed to fetch results.\");\r\n      }\r\n      setIsSearching(false);\r\n    });\r\n  };\r\n\r\n  // Handle product filter change\r\n  const handleProductFilterChange = (filter: ProductFilterOption) => {\r\n    if (filter !== productFilterBy) {\r\n      setProductFilterBy(filter);\r\n      setIsSearching(true);\r\n\r\n      // Update URL to include the filter option\r\n      if (typeof window !== \"undefined\") {\r\n        const url = new URL(window.location.href);\r\n        url.searchParams.set(PRODUCT_TYPE_PARAM, filter);\r\n        window.history.replaceState({}, \"\", url.toString());\r\n      }\r\n\r\n      // If we already have search results, perform the search again with the new filter\r\n      if (viewType === \"products\") {\r\n        const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;\r\n        const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n        let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n        // Handle \"_any\" locality value\r\n        if (locality === \"_any\") {\r\n          locality = \"\";\r\n        }\r\n\r\n        startSearchTransition(async () => {\r\n          // Map the product sort option to a backend sort value\r\n          const mappedSortBy = mapProductSortToBackend(productSortBy);\r\n\r\n          const result = await searchDiscoverCombined({\r\n            businessName: null,\r\n            productName,\r\n            pincode,\r\n            locality,\r\n            viewType,\r\n            page: 1,\r\n            limit: DISCOVER_PRODUCTS_PER_PAGE,\r\n            productSort: mappedSortBy,\r\n            productType: filter === \"all\" ? null : filter,\r\n          });\r\n\r\n          if (result.data) {\r\n            setSearchResult(result.data);\r\n            setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n            if (viewType === \"products\" && result.data.products) {\r\n              setProducts(result.data.products);\r\n            }\r\n\r\n            setHasMore(result.data.hasMore);\r\n            setTotalCount(result.data.totalCount);\r\n            setCurrentPage(1);\r\n          } else {\r\n            setSearchError(result.error || \"Failed to fetch results.\");\r\n          }\r\n          setIsSearching(false);\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  // Load more products\r\n  const loadMoreProducts = async (\r\n    nextPage: number,\r\n    isLoadingMore: boolean,\r\n    setIsLoadingMore: (_value: boolean) => void\r\n  ) => {\r\n    if (isLoadingMore) {\r\n      return;\r\n    }\r\n\r\n    const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;\r\n    const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n    let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n    // Handle \"_any\" locality value\r\n    if (locality === \"_any\") {\r\n      locality = \"\";\r\n    }\r\n\r\n    setIsLoadingMore(true);\r\n\r\n    try {\r\n      // Add a small delay to prevent race conditions\r\n      await new Promise((resolve) => setTimeout(resolve, 300));\r\n\r\n      // Map the product sort option to a backend sort value\r\n      const mappedSortBy = mapProductSortToBackend(productSortBy);\r\n\r\n      const result = await fetchMoreProductsCombined({\r\n        businessName: null,\r\n        productName,\r\n        pincode,\r\n        locality,\r\n        page: nextPage,\r\n        limit: DISCOVER_PRODUCTS_PER_PAGE,\r\n        productSort: mappedSortBy,\r\n        productType: productFilterBy === \"all\" ? null : productFilterBy,\r\n      });\r\n\r\n      if (result.data && result.data.products) {\r\n        if (result.data.products.length > 0) {\r\n          const newProducts = result.data.products;\r\n\r\n          // Filter out any products that already exist in the current list to avoid duplicates\r\n          const existingIds = new Set(products.map((p) => p.id));\r\n          const uniqueNewProducts = newProducts.filter(\r\n            (product) => !existingIds.has(product.id)\r\n          );\r\n\r\n          if (uniqueNewProducts.length > 0) {\r\n            // Combine previous products with new ones\r\n            const updatedProducts = [...products, ...uniqueNewProducts];\r\n            setProducts(updatedProducts);\r\n            // Use the hasMore flag from the server\r\n            setHasMore(result.data.hasMore);\r\n            setCurrentPage(nextPage);\r\n            return true;\r\n          } else {\r\n            // If all new products were duplicates, stop loading more\r\n            console.warn(\r\n              \"All new products were duplicates, stopping infinite scroll\"\r\n            );\r\n            setHasMore(false);\r\n            return false;\r\n          }\r\n        } else {\r\n          setHasMore(false);\r\n          return false;\r\n        }\r\n      } else {\r\n        setHasMore(false);\r\n        return false;\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading more products:\", error);\r\n      setHasMore(false);\r\n      return false;\r\n    } finally {\r\n      setIsLoadingMore(false);\r\n    }\r\n  };\r\n\r\n  return {\r\n    handleProductSortChange,\r\n    handleProductSearch,\r\n    handleProductFilterChange,\r\n    loadMoreProducts,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAaA;AAtBA;;;;;;;;AAyBO,SAAS,2BACd,QAAkB,EAClB,cAAyC,EACzC,eAA8D,EAC9D,kBAA6C,EAC7C,WAA8C,EAC9C,UAAqC,EACrC,aAAuC,EACvC,cAAwC,EACxC,gBAAqD,EACrD,kBAAyD,EACzD,cAA+C,EAC/C,QAAyB,EACzB,MAAsB,EACtB,aAAgC,EAChC,eAAoC;IAEpC,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,GAAG,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IAE9C,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,IAAI,eAAe,eAAe;YAChC,iBAAiB;YACjB,eAAe;YAEf,wCAAwC;YACxC,uCAAmC;;YAMnC;YAEA,sDAAsD;YACtD,MAAM,eAAe,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE;YAE7C,gFAAgF;YAChF,IAAI,aAAa,YAAY;gBAC3B,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB,KAAK;gBAC5D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;gBACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;gBAEnD,+BAA+B;gBAC/B,IAAI,aAAa,QAAQ;oBACvB,WAAW;gBACb;gBAEA,sBAAsB;oBACpB,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;wBAC1C,cAAc;wBACd;wBACA;wBACA;wBACA;wBACA,MAAM;wBACN,OAAO,+JAAA,CAAA,6BAA0B;wBACjC,aAAa;wBACb,aAAa,oBAAoB,QAAQ,OAAO;oBAClD;oBAEA,IAAI,OAAO,IAAI,EAAE;wBACf,gBAAgB,OAAO,IAAI;wBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;wBAE9C,IAAI,aAAa,cAAc,OAAO,IAAI,CAAC,QAAQ,EAAE;4BACnD,YAAY,OAAO,IAAI,CAAC,QAAQ;wBAClC;wBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;wBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;wBACpC,eAAe;oBACjB,OAAO;wBACL,eAAe,OAAO,KAAK,IAAI;oBACjC;oBACA,eAAe;gBACjB;YACF;QACF;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC;QAC3B,gCAAgC;QAChC,IAAI,aAAa,YAAY;YAC3B;QACF;QAEA,eAAe;QAEf,wCAAwC;QACxC,MAAM,oBAAoB;QAE1B,wCAAwC;QACxC,uCAAmC;;QAQnC;QAEA,kDAAkD;QAClD,IAAI,CAAC,mBAAmB;YACtB,4DAA4D;YAC5D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;YACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;YAEnD,+BAA+B;YAC/B,IAAI,aAAa,QAAQ;gBACvB,WAAW;YACb;YAEA,sBAAsB;gBACpB,+CAA+C;gBAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,sDAAsD;gBACtD,MAAM,eAAe,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE;gBAE7C,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC1C,cAAc;oBACd,aAAa;oBACb;oBACA;oBACA;oBACA,MAAM;oBACN,OAAO,+JAAA,CAAA,6BAA0B;oBACjC,aAAa;oBACb,aAAa,oBAAoB,QAAQ,OAAO;gBAClD;gBAEA,IAAI,OAAO,IAAI,EAAE;oBACf,gBAAgB,OAAO,IAAI;oBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;oBAE9C,IAAI,aAAa,cAAc,OAAO,IAAI,CAAC,QAAQ,EAAE;wBACnD,YAAY,OAAO,IAAI,CAAC,QAAQ;oBAClC;oBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;oBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;oBACpC,eAAe;gBACjB,OAAO;oBACL,eAAe,OAAO,KAAK,IAAI;gBACjC;gBACA,eAAe;YACjB;YACA;QACF;QAEA,6CAA6C;QAC7C,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;QACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;QAEnD,+BAA+B;QAC/B,IAAI,aAAa,QAAQ;YACvB,WAAW;QACb;QAEA,gCAAgC;QAChC,eAAe;QACf,YAAY,EAAE;QACd,eAAe;QACf,WAAW;QACX,cAAc;QAEd,sBAAsB;YACpB,+CAA+C;YAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,sDAAsD;YACtD,MAAM,eAAe,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE;YAE7C,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;gBAC1C,cAAc;gBACd,aAAa;gBACb;gBACA;gBACA;gBACA,MAAM;gBACN,OAAO,+JAAA,CAAA,6BAA0B;gBACjC,aAAa;gBACb,aAAa,oBAAoB,QAAQ,OAAO;YAClD;YAEA,IAAI,OAAO,IAAI,EAAE;gBACf,gBAAgB,OAAO,IAAI;gBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;gBAE9C,IAAI,aAAa,cAAc,OAAO,IAAI,CAAC,QAAQ,EAAE;oBACnD,YAAY,OAAO,IAAI,CAAC,QAAQ;gBAClC;gBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;gBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;gBACpC,eAAe;YACjB,OAAO;gBACL,eAAe,OAAO,KAAK,IAAI;YACjC;YACA,eAAe;QACjB;IACF;IAEA,+BAA+B;IAC/B,MAAM,4BAA4B,CAAC;QACjC,IAAI,WAAW,iBAAiB;YAC9B,mBAAmB;YACnB,eAAe;YAEf,0CAA0C;YAC1C,uCAAmC;;YAInC;YAEA,kFAAkF;YAClF,IAAI,aAAa,YAAY;gBAC3B,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB,KAAK;gBAC5D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;gBACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;gBAEnD,+BAA+B;gBAC/B,IAAI,aAAa,QAAQ;oBACvB,WAAW;gBACb;gBAEA,sBAAsB;oBACpB,sDAAsD;oBACtD,MAAM,eAAe,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE;oBAE7C,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;wBAC1C,cAAc;wBACd;wBACA;wBACA;wBACA;wBACA,MAAM;wBACN,OAAO,+JAAA,CAAA,6BAA0B;wBACjC,aAAa;wBACb,aAAa,WAAW,QAAQ,OAAO;oBACzC;oBAEA,IAAI,OAAO,IAAI,EAAE;wBACf,gBAAgB,OAAO,IAAI;wBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;wBAE9C,IAAI,aAAa,cAAc,OAAO,IAAI,CAAC,QAAQ,EAAE;4BACnD,YAAY,OAAO,IAAI,CAAC,QAAQ;wBAClC;wBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;wBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;wBACpC,eAAe;oBACjB,OAAO;wBACL,eAAe,OAAO,KAAK,IAAI;oBACjC;oBACA,eAAe;gBACjB;YACF;QACF;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB,OACvB,UACA,eACA;QAEA,IAAI,eAAe;YACjB;QACF;QAEA,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB,KAAK;QAC5D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;QACnD,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;QAEnD,+BAA+B;QAC/B,IAAI,aAAa,QAAQ;YACvB,WAAW;QACb;QAEA,iBAAiB;QAEjB,IAAI;YACF,+CAA+C;YAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,sDAAsD;YACtD,MAAM,eAAe,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE;YAE7C,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,4BAAyB,AAAD,EAAE;gBAC7C,cAAc;gBACd;gBACA;gBACA;gBACA,MAAM;gBACN,OAAO,+JAAA,CAAA,6BAA0B;gBACjC,aAAa;gBACb,aAAa,oBAAoB,QAAQ,OAAO;YAClD;YAEA,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE;gBACvC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;oBACnC,MAAM,cAAc,OAAO,IAAI,CAAC,QAAQ;oBAExC,qFAAqF;oBACrF,MAAM,cAAc,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE;oBACpD,MAAM,oBAAoB,YAAY,MAAM,CAC1C,CAAC,UAAY,CAAC,YAAY,GAAG,CAAC,QAAQ,EAAE;oBAG1C,IAAI,kBAAkB,MAAM,GAAG,GAAG;wBAChC,0CAA0C;wBAC1C,MAAM,kBAAkB;+BAAI;+BAAa;yBAAkB;wBAC3D,YAAY;wBACZ,uCAAuC;wBACvC,WAAW,OAAO,IAAI,CAAC,OAAO;wBAC9B,eAAe;wBACf,OAAO;oBACT,OAAO;wBACL,yDAAyD;wBACzD,QAAQ,IAAI,CACV;wBAEF,WAAW;wBACX,OAAO;oBACT;gBACF,OAAO;oBACL,WAAW;oBACX,OAAO;gBACT;YACF,OAAO;gBACL,WAAW;gBACX,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,WAAW;YACX,OAAO;QACT,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 721, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/context/commonContextFunctions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { useTransition } from \"react\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"../actions/combinedActions\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { NearbyProduct } from \"../actions/types\";\r\nimport {\r\n  DISCOVER_BUSINESSES_PER_PAGE,\r\n  DISCOVER_PRODUCTS_PER_PAGE,\r\n} from \"../constants/paginationConstants\";\r\nimport {\r\n  VIEW_TYPE_PARAM,\r\n  BUSINESS_NAME_PARAM,\r\n  PRODUCT_NAME_PARAM,\r\n  PINCODE_PARAM,\r\n  CITY_PARAM,\r\n  LOCALITY_PARAM,\r\n  CATEGORY_PARAM,\r\n} from \"../constants/urlParamConstants\";\r\nimport {\r\n  CombinedSearchFormData,\r\n  DiscoverSearchResult,\r\n  ProductFilterOption,\r\n  ProductSortOption,\r\n  ViewType,\r\n} from \"./types\";\r\n\r\n// Common context functions\r\nexport function useCommonContextFunctions(\r\n  viewType: ViewType,\r\n  setViewType: (_value: ViewType) => void,\r\n  setIsSearching: (_value: boolean) => void,\r\n  setSearchResult: (_value: DiscoverSearchResult | null) => void,\r\n  setIsAuthenticated: (_value: boolean) => void,\r\n  setBusinesses: (_value: BusinessCardData[]) => void,\r\n  setProducts: (_value: NearbyProduct[]) => void,\r\n  setHasMore: (_value: boolean) => void,\r\n  setTotalCount: (_value: number) => void,\r\n  setCurrentPage: (_value: number) => void,\r\n  setSearchError: (_value: string | null) => void,\r\n  businessSort: BusinessSortBy,\r\n  productSort: ProductSortOption,\r\n  productFilterBy: ProductFilterOption,\r\n  selectedCategory: string | null\r\n) {\r\n  const searchParams = useSearchParams();\r\n  const [isPending, startSearchTransition] = useTransition();\r\n\r\n  // Handle view change\r\n  const handleViewChange = (view: ViewType) => {\r\n    if (view !== viewType) {\r\n      // First set the view type and loading state\r\n      setViewType(view);\r\n      setIsSearching(true);\r\n\r\n      // Clear existing data for the new view to ensure we show skeletons\r\n      if (view === \"products\") {\r\n        setProducts([]);\r\n      } else {\r\n        setBusinesses([]);\r\n      }\r\n\r\n      // Update URL to include the view type\r\n      if (typeof window !== \"undefined\") {\r\n        const url = new URL(window.location.href);\r\n        url.searchParams.set(VIEW_TYPE_PARAM, view);\r\n        window.history.replaceState({}, \"\", url.toString());\r\n      }\r\n\r\n      // Always perform a search when switching views to ensure data is loaded\r\n      const businessName = searchParams.get(BUSINESS_NAME_PARAM) || null;\r\n      const productName = searchParams.get(PRODUCT_NAME_PARAM) || null;\r\n      const pincode = searchParams.get(PINCODE_PARAM) || null;\r\n      const city = searchParams.get(CITY_PARAM) || null;\r\n      let locality = searchParams.get(LOCALITY_PARAM) || null;\r\n\r\n      // Handle \"_any\" locality value\r\n      if (locality === \"_any\") {\r\n        locality = \"\";\r\n      }\r\n\r\n      startSearchTransition(async () => {\r\n        try {\r\n          // Add a small delay to prevent race conditions\r\n          await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n          // If switching to products view, clear businesses\r\n          if (view === \"products\") {\r\n            setBusinesses([]);\r\n          }\r\n          // If switching to businesses view, clear products\r\n          else {\r\n            setProducts([]);\r\n          }\r\n\r\n          const result = await searchDiscoverCombined({\r\n            businessName: view === \"cards\" ? businessName : null,\r\n            productName: view === \"products\" ? productName : null,\r\n            pincode,\r\n            city,\r\n            locality,\r\n            viewType: view,\r\n            page: 1,\r\n            limit:\r\n              view === \"products\"\r\n                ? DISCOVER_PRODUCTS_PER_PAGE\r\n                : DISCOVER_BUSINESSES_PER_PAGE,\r\n            businessSort,\r\n            productSort,\r\n            productType:\r\n              view === \"products\" && productFilterBy !== \"all\"\r\n                ? productFilterBy\r\n                : null,\r\n            category: selectedCategory,\r\n          });\r\n\r\n          if (result.data) {\r\n            setSearchResult(result.data);\r\n            setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n            if (view === \"cards\" && result.data.businesses) {\r\n              setBusinesses(result.data.businesses);\r\n            } else if (view === \"products\" && result.data.products) {\r\n              setProducts(result.data.products);\r\n            }\r\n\r\n            setHasMore(result.data.hasMore);\r\n            setTotalCount(result.data.totalCount);\r\n            setCurrentPage(1);\r\n          } else {\r\n            setSearchError(result.error || \"Failed to fetch results.\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error changing view:\", error);\r\n          setSearchError(\"An unexpected error occurred.\");\r\n        } finally {\r\n          setIsSearching(false);\r\n        }\r\n      });\r\n    }\r\n  };\r\n\r\n  // Perform search\r\n  const performSearch = (data: CombinedSearchFormData) => {\r\n    // First set loading state and clear errors\r\n    setIsSearching(true);\r\n    setSearchError(null);\r\n\r\n    // Reset state before new search to ensure we show skeletons\r\n    if (viewType === \"cards\") {\r\n      setBusinesses([]);\r\n    } else {\r\n      setProducts([]);\r\n    }\r\n\r\n    // Reset pagination state\r\n    setCurrentPage(1);\r\n    setHasMore(false);\r\n    setTotalCount(0);\r\n\r\n    // Extract values from data\r\n    const { businessName, pincode, city, locality, category } = data;\r\n\r\n    // Update URL parameters based on search type\r\n    if (typeof window !== \"undefined\") {\r\n      const url = new URL(window.location.href);\r\n\r\n      // Clear existing search parameters\r\n      url.searchParams.delete(BUSINESS_NAME_PARAM);\r\n      url.searchParams.delete(PRODUCT_NAME_PARAM);\r\n      url.searchParams.delete(PINCODE_PARAM);\r\n      url.searchParams.delete(CITY_PARAM);\r\n      url.searchParams.delete(LOCALITY_PARAM);\r\n      url.searchParams.delete(CATEGORY_PARAM);\r\n\r\n      // Set business name or product name based on view type\r\n      if (businessName) {\r\n        if (viewType === \"cards\") {\r\n          url.searchParams.set(BUSINESS_NAME_PARAM, businessName);\r\n        } else {\r\n          url.searchParams.set(PRODUCT_NAME_PARAM, businessName);\r\n        }\r\n      }\r\n\r\n      // Set location parameters based on what's provided\r\n      if (pincode) {\r\n        url.searchParams.set(PINCODE_PARAM, pincode);\r\n        if (locality && locality !== \"_any\") {\r\n          url.searchParams.set(LOCALITY_PARAM, locality);\r\n        }\r\n      } else if (city) {\r\n        url.searchParams.set(CITY_PARAM, city);\r\n      }\r\n\r\n      // Set category parameter\r\n      if (category) {\r\n        url.searchParams.set(CATEGORY_PARAM, category);\r\n      }\r\n\r\n      // Update the URL without reloading the page\r\n      window.history.replaceState({}, \"\", url.toString());\r\n    }\r\n\r\n    startSearchTransition(async () => {\r\n      try {\r\n        // Add a small delay to prevent race conditions\r\n        await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n        const result = await searchDiscoverCombined({\r\n          businessName: viewType === \"cards\" ? businessName : null,\r\n          productName: viewType === \"products\" ? businessName : null, // Reuse businessName for productName for now\r\n          pincode,\r\n          city, // Now correctly passing the city parameter\r\n          locality: locality === \"_any\" ? \"\" : locality,\r\n          category: category !== undefined ? category : selectedCategory, // Use category from search data or current selected category\r\n          viewType,\r\n          page: 1,\r\n          limit:\r\n            viewType === \"products\"\r\n              ? DISCOVER_PRODUCTS_PER_PAGE\r\n              : DISCOVER_BUSINESSES_PER_PAGE,\r\n          businessSort,\r\n          productSort,\r\n          productType:\r\n            viewType === \"products\" && productFilterBy !== \"all\"\r\n              ? productFilterBy\r\n              : null,\r\n        });\r\n\r\n        if (result.data) {\r\n          setSearchResult(result.data);\r\n          setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n          if (viewType === \"cards\" && result.data.businesses) {\r\n            setBusinesses(result.data.businesses);\r\n          } else if (viewType === \"products\" && result.data.products) {\r\n            setProducts(result.data.products);\r\n          }\r\n\r\n          setHasMore(result.data.hasMore);\r\n          setTotalCount(result.data.totalCount);\r\n        } else {\r\n          setSearchError(result.error || \"Failed to fetch results.\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Unexpected error in performSearch:\", error);\r\n        setSearchError(\"An unexpected error occurred. Please try again.\");\r\n      } finally {\r\n        setIsSearching(false);\r\n      }\r\n    });\r\n  };\r\n\r\n  // Load more items\r\n  const loadMore = async (\r\n    loadMoreBusinesses: (\r\n      _nextPage: number,\r\n      _isLoadingMore: boolean,\r\n      _setIsLoadingMore: (_value: boolean) => void\r\n    ) => Promise<boolean | undefined>,\r\n    loadMoreProducts: (\r\n      _nextPage: number,\r\n      _isLoadingMore: boolean,\r\n      _setIsLoadingMore: (_value: boolean) => void\r\n    ) => Promise<boolean | undefined>,\r\n    currentPage: number,\r\n    isLoadingMore: boolean,\r\n    setIsLoadingMore: (_value: boolean) => void\r\n  ) => {\r\n    if (isLoadingMore || isPending) {\r\n      return;\r\n    }\r\n\r\n    const nextPage = currentPage + 1;\r\n\r\n    if (viewType === \"cards\") {\r\n      await loadMoreBusinesses(nextPage, isLoadingMore, setIsLoadingMore);\r\n    } else {\r\n      await loadMoreProducts(nextPage, isLoadingMore, setIsLoadingMore);\r\n    }\r\n  };\r\n\r\n  return {\r\n    isPending,\r\n    handleViewChange,\r\n    performSearch,\r\n    loadMore,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AAGA;AAIA;AAZA;;;;;;AA8BO,SAAS,0BACd,QAAkB,EAClB,WAAuC,EACvC,cAAyC,EACzC,eAA8D,EAC9D,kBAA6C,EAC7C,aAAmD,EACnD,WAA8C,EAC9C,UAAqC,EACrC,aAAuC,EACvC,cAAwC,EACxC,cAA+C,EAC/C,YAA4B,EAC5B,WAA8B,EAC9B,eAAoC,EACpC,gBAA+B;IAE/B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IAEvD,qBAAqB;IACrB,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,UAAU;YACrB,4CAA4C;YAC5C,YAAY;YACZ,eAAe;YAEf,mEAAmE;YACnE,IAAI,SAAS,YAAY;gBACvB,YAAY,EAAE;YAChB,OAAO;gBACL,cAAc,EAAE;YAClB;YAEA,sCAAsC;YACtC,uCAAmC;;YAInC;YAEA,wEAAwE;YACxE,MAAM,eAAe,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB,KAAK;YAC9D,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB,KAAK;YAC5D,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;YACnD,MAAM,OAAO,aAAa,GAAG,CAAC,6JAAA,CAAA,aAAU,KAAK;YAC7C,IAAI,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;YAEnD,+BAA+B;YAC/B,IAAI,aAAa,QAAQ;gBACvB,WAAW;YACb;YAEA,sBAAsB;gBACpB,IAAI;oBACF,+CAA+C;oBAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;oBAEnD,kDAAkD;oBAClD,IAAI,SAAS,YAAY;wBACvB,cAAc,EAAE;oBAClB,OAEK;wBACH,YAAY,EAAE;oBAChB;oBAEA,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;wBAC1C,cAAc,SAAS,UAAU,eAAe;wBAChD,aAAa,SAAS,aAAa,cAAc;wBACjD;wBACA;wBACA;wBACA,UAAU;wBACV,MAAM;wBACN,OACE,SAAS,aACL,+JAAA,CAAA,6BAA0B,GAC1B,+JAAA,CAAA,+BAA4B;wBAClC;wBACA;wBACA,aACE,SAAS,cAAc,oBAAoB,QACvC,kBACA;wBACN,UAAU;oBACZ;oBAEA,IAAI,OAAO,IAAI,EAAE;wBACf,gBAAgB,OAAO,IAAI;wBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;wBAE9C,IAAI,SAAS,WAAW,OAAO,IAAI,CAAC,UAAU,EAAE;4BAC9C,cAAc,OAAO,IAAI,CAAC,UAAU;wBACtC,OAAO,IAAI,SAAS,cAAc,OAAO,IAAI,CAAC,QAAQ,EAAE;4BACtD,YAAY,OAAO,IAAI,CAAC,QAAQ;wBAClC;wBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;wBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;wBACpC,eAAe;oBACjB,OAAO;wBACL,eAAe,OAAO,KAAK,IAAI;oBACjC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACtC,eAAe;gBACjB,SAAU;oBACR,eAAe;gBACjB;YACF;QACF;IACF;IAEA,iBAAiB;IACjB,MAAM,gBAAgB,CAAC;QACrB,2CAA2C;QAC3C,eAAe;QACf,eAAe;QAEf,4DAA4D;QAC5D,IAAI,aAAa,SAAS;YACxB,cAAc,EAAE;QAClB,OAAO;YACL,YAAY,EAAE;QAChB;QAEA,yBAAyB;QACzB,eAAe;QACf,WAAW;QACX,cAAc;QAEd,2BAA2B;QAC3B,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;QAE5D,6CAA6C;QAC7C,uCAAmC;;QAqCnC;QAEA,sBAAsB;YACpB,IAAI;gBACF,+CAA+C;gBAC/C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;gBAEnD,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;oBAC1C,cAAc,aAAa,UAAU,eAAe;oBACpD,aAAa,aAAa,aAAa,eAAe;oBACtD;oBACA;oBACA,UAAU,aAAa,SAAS,KAAK;oBACrC,UAAU,aAAa,YAAY,WAAW;oBAC9C;oBACA,MAAM;oBACN,OACE,aAAa,aACT,+JAAA,CAAA,6BAA0B,GAC1B,+JAAA,CAAA,+BAA4B;oBAClC;oBACA;oBACA,aACE,aAAa,cAAc,oBAAoB,QAC3C,kBACA;gBACR;gBAEA,IAAI,OAAO,IAAI,EAAE;oBACf,gBAAgB,OAAO,IAAI;oBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;oBAE9C,IAAI,aAAa,WAAW,OAAO,IAAI,CAAC,UAAU,EAAE;wBAClD,cAAc,OAAO,IAAI,CAAC,UAAU;oBACtC,OAAO,IAAI,aAAa,cAAc,OAAO,IAAI,CAAC,QAAQ,EAAE;wBAC1D,YAAY,OAAO,IAAI,CAAC,QAAQ;oBAClC;oBAEA,WAAW,OAAO,IAAI,CAAC,OAAO;oBAC9B,cAAc,OAAO,IAAI,CAAC,UAAU;gBACtC,OAAO;oBACL,eAAe,OAAO,KAAK,IAAI;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,eAAe;YACjB,SAAU;gBACR,eAAe;YACjB;QACF;IACF;IAEA,kBAAkB;IAClB,MAAM,WAAW,OACf,oBAKA,kBAKA,aACA,eACA;QAEA,IAAI,iBAAiB,WAAW;YAC9B;QACF;QAEA,MAAM,WAAW,cAAc;QAE/B,IAAI,aAAa,SAAS;YACxB,MAAM,mBAAmB,UAAU,eAAe;QACpD,OAAO;YACL,MAAM,iBAAiB,UAAU,eAAe;QAClD;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/context/DiscoverContext.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createContext, useContext, useState, useEffect } from \"react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"../actions/combinedActions\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { NearbyProduct } from \"../actions/types\";\r\nimport { DISCOVER_PRODUCTS_PER_PAGE } from \"../constants/paginationConstants\";\r\nimport { mapProductSortToBackend } from \"../utils/sortMappings\";\r\nimport {\r\n  BUSINESS_NAME_PARAM,\r\n  BUSINESS_SORT_PARAM,\r\n  PINCODE_PARAM,\r\n  CITY_PARAM,\r\n  LOCALITY_PARAM,\r\n  VIEW_TYPE_PARAM,\r\n  PRODUCT_SORT_PARAM,\r\n  PRODUCT_TYPE_PARAM,\r\n  CATEGORY_PARAM,\r\n} from \"../constants/urlParamConstants\";\r\nimport {\r\n  DiscoverContextType,\r\n  DiscoverSearchResult,\r\n  ProductFilterOption,\r\n  ProductSortOption,\r\n  ViewType,\r\n} from \"./types\";\r\nimport { useBusinessContextFunctions } from \"./businessContextFunctions\";\r\nimport { useProductContextFunctions } from \"./productContextFunctions\";\r\nimport { useCommonContextFunctions } from \"./commonContextFunctions\";\r\n\r\n// Create the context\r\nconst DiscoverContext = createContext<DiscoverContextType | undefined>(\r\n  undefined\r\n);\r\n\r\n// Provider component\r\nexport function DiscoverProvider({ children }: { children: React.ReactNode }) {\r\n  const searchParams = useSearchParams();\r\n\r\n  // Get initial values from URL\r\n  const initialBusinessName = searchParams.get(BUSINESS_NAME_PARAM) || null;\r\n  const initialPincode = searchParams.get(PINCODE_PARAM) || null;\r\n  const initialCity = searchParams.get(CITY_PARAM) || null;\r\n  const initialLocality = searchParams.get(LOCALITY_PARAM) || null;\r\n  const initialCategory = searchParams.get(CATEGORY_PARAM) || null;\r\n  const initialViewType =\r\n    (searchParams.get(VIEW_TYPE_PARAM) as ViewType) || \"products\";\r\n\r\n  // Get initial sort values from URL\r\n  let initialBusinessSort = (searchParams.get(BUSINESS_SORT_PARAM) ||\r\n    \"created_desc\") as BusinessSortBy;\r\n  let initialProductSort = (searchParams.get(PRODUCT_SORT_PARAM) ||\r\n    \"newest\") as ProductSortOption;\r\n\r\n  // Handle migration from old sortBy parameter to new parameters\r\n  const oldSortBy = searchParams.get(\"sortBy\");\r\n  if (oldSortBy) {\r\n    // If we have the old parameter, use it for the appropriate view type and remove it\r\n    if (initialViewType === \"cards\") {\r\n      initialBusinessSort = oldSortBy as BusinessSortBy;\r\n    } else if (initialViewType === \"products\") {\r\n      initialProductSort = oldSortBy as ProductSortOption;\r\n    }\r\n\r\n    // Update URL to remove the old parameter and set the new ones\r\n    if (typeof window !== \"undefined\") {\r\n      const url = new URL(window.location.href);\r\n      url.searchParams.delete(\"sortBy\");\r\n      url.searchParams.set(BUSINESS_SORT_PARAM, initialBusinessSort);\r\n      url.searchParams.set(PRODUCT_SORT_PARAM, initialProductSort);\r\n      window.history.replaceState({}, \"\", url.toString());\r\n    }\r\n  }\r\n  const initialProductFilterBy =\r\n    (searchParams.get(PRODUCT_TYPE_PARAM) as ProductFilterOption) || \"all\";\r\n\r\n  // State\r\n  const [viewType, setViewType] = useState<ViewType>(initialViewType);\r\n  const [sortBy, setSortBy] = useState<BusinessSortBy>(initialBusinessSort);\r\n  const [selectedCategory, setSelectedCategory] = useState<string | null>(\r\n    initialCategory\r\n  );\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const [isLoadingMore, setIsLoadingMore] = useState(false);\r\n  const [searchError, setSearchError] = useState<string | null>(null);\r\n  const [productFilterBy, setProductFilterBy] = useState<ProductFilterOption>(\r\n    initialProductFilterBy\r\n  );\r\n  const [productSortBy, setProductSortBy] =\r\n    useState<ProductSortOption>(initialProductSort);\r\n  const [searchResult, setSearchResult] = useState<DiscoverSearchResult | null>(\r\n    null\r\n  );\r\n  const [businesses, setBusinesses] = useState<BusinessCardData[]>([]);\r\n  const [products, setProducts] = useState<NearbyProduct[]>([]);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [hasMore, setHasMore] = useState(false);\r\n  const [totalCount, setTotalCount] = useState(0);\r\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\r\n\r\n  // Get business context functions\r\n  const { handleBusinessSortChange, handleBusinessSearch, loadMoreBusinesses } =\r\n    useBusinessContextFunctions(\r\n      viewType,\r\n      setIsSearching,\r\n      setSearchResult,\r\n      setIsAuthenticated,\r\n      setBusinesses,\r\n      setHasMore,\r\n      setTotalCount,\r\n      setCurrentPage,\r\n      setSortBy,\r\n      setSearchError,\r\n      businesses,\r\n      sortBy\r\n    );\r\n\r\n  // Get product context functions\r\n  const {\r\n    handleProductSortChange,\r\n    handleProductSearch,\r\n    handleProductFilterChange,\r\n    loadMoreProducts,\r\n  } = useProductContextFunctions(\r\n    viewType,\r\n    setIsSearching,\r\n    setSearchResult,\r\n    setIsAuthenticated,\r\n    setProducts,\r\n    setHasMore,\r\n    setTotalCount,\r\n    setCurrentPage,\r\n    setProductSortBy,\r\n    setProductFilterBy,\r\n    setSearchError,\r\n    products,\r\n    sortBy,\r\n    productSortBy,\r\n    productFilterBy\r\n  );\r\n\r\n  // Category change handler\r\n  const handleCategoryChange = (category: string | null) => {\r\n    setSelectedCategory(category);\r\n\r\n    // Update URL parameter\r\n    if (typeof window !== \"undefined\") {\r\n      const url = new URL(window.location.href);\r\n      if (category) {\r\n        url.searchParams.set(CATEGORY_PARAM, category);\r\n      } else {\r\n        url.searchParams.delete(CATEGORY_PARAM);\r\n      }\r\n      window.history.replaceState({}, \"\", url.toString());\r\n    }\r\n\r\n    // Reset pagination and trigger immediate search\r\n    setCurrentPage(1);\r\n    setHasMore(false);\r\n\r\n    // Get current search parameters to maintain existing search context\r\n    const currentBusinessName = searchParams.get(BUSINESS_NAME_PARAM);\r\n    const currentPincode = searchParams.get(PINCODE_PARAM);\r\n    const currentCity = searchParams.get(CITY_PARAM);\r\n    const currentLocality = searchParams.get(LOCALITY_PARAM);\r\n\r\n    // Trigger search with the new category value while maintaining other search parameters\r\n    performSearch({\r\n      businessName: currentBusinessName, // Use businessName for both cards and products\r\n      pincode: currentPincode,\r\n      city: currentCity,\r\n      locality: currentLocality,\r\n      category, // Use the new category value (null when deselecting)\r\n    });\r\n  };\r\n\r\n  // Get common context functions\r\n  const {\r\n    isPending,\r\n    handleViewChange,\r\n    performSearch,\r\n    loadMore: commonLoadMore,\r\n  } = useCommonContextFunctions(\r\n    viewType,\r\n    setViewType,\r\n    setIsSearching,\r\n    setSearchResult,\r\n    setIsAuthenticated,\r\n    setBusinesses,\r\n    setProducts,\r\n    setHasMore,\r\n    setTotalCount,\r\n    setCurrentPage,\r\n    setSearchError,\r\n    sortBy,\r\n    productSortBy,\r\n    productFilterBy,\r\n    selectedCategory\r\n  );\r\n\r\n  // Wrapper for loadMore to pass the correct functions\r\n  const loadMore = async () => {\r\n    await commonLoadMore(\r\n      loadMoreBusinesses,\r\n      loadMoreProducts,\r\n      currentPage,\r\n      isLoadingMore,\r\n      setIsLoadingMore\r\n    );\r\n  };\r\n\r\n  // Load initial data on mount\r\n  useEffect(() => {\r\n    // Always set isSearching to true initially to show skeleton loaders\r\n    setIsSearching(true);\r\n\r\n    // If URL has search params, perform search with those params\r\n    if (\r\n      initialBusinessName ||\r\n      initialPincode ||\r\n      initialCity ||\r\n      initialCategory\r\n    ) {\r\n      // Handle \"_any\" locality value\r\n      const normalizedLocality =\r\n        initialLocality === \"_any\" ? \"\" : initialLocality;\r\n\r\n      // Use only the properties that exist in CombinedSearchFormData\r\n      performSearch({\r\n        businessName: initialBusinessName,\r\n        pincode: initialPincode,\r\n        city: initialCity,\r\n        locality: normalizedLocality,\r\n        category: initialCategory,\r\n      });\r\n    } else {\r\n      // Otherwise, load all businesses or products based on viewType\r\n\r\n      const fetchInitialData = async () => {\r\n        try {\r\n          // Load products by default for the initial load\r\n          const initialViewType = \"products\";\r\n          console.log(\"Initial load: Fetching products\");\r\n\r\n          const result = await searchDiscoverCombined({\r\n            viewType: initialViewType,\r\n            page: 1,\r\n            limit: DISCOVER_PRODUCTS_PER_PAGE,\r\n            productSort: mapProductSortToBackend(productSortBy),\r\n            productType: productFilterBy === \"all\" ? null : productFilterBy,\r\n          });\r\n\r\n          if (result.data) {\r\n            setSearchResult(result.data);\r\n            setIsAuthenticated(result.data.isAuthenticated);\r\n\r\n            if (result.data.products) {\r\n              setProducts(result.data.products);\r\n              // Use the hasMore flag from the server\r\n              setHasMore(result.data.hasMore);\r\n            } else {\r\n              setHasMore(false);\r\n            }\r\n\r\n            setTotalCount(result.data.totalCount);\r\n          } else {\r\n            setSearchError(result.error || \"Failed to fetch results.\");\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Unexpected error loading initial data:\", error);\r\n          setSearchError(\"An unexpected error occurred. Please try again.\");\r\n        } finally {\r\n          setIsSearching(false);\r\n        }\r\n      };\r\n\r\n      fetchInitialData();\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  // Create the context value\r\n  const contextValue: DiscoverContextType = {\r\n    viewType,\r\n    sortBy,\r\n    selectedCategory,\r\n    isSearching,\r\n    isPending,\r\n    isLoadingMore,\r\n    searchError,\r\n    productFilterBy,\r\n    productSortBy,\r\n    searchResult,\r\n    businesses,\r\n    products,\r\n    currentPage,\r\n    hasMore,\r\n    totalCount,\r\n    isAuthenticated,\r\n    performSearch,\r\n    handleViewChange,\r\n    handleBusinessSortChange,\r\n    handleBusinessSearch,\r\n    handleProductSearch,\r\n    handleProductSortChange,\r\n    handleProductFilterChange,\r\n    handleCategoryChange,\r\n    loadMore,\r\n  };\r\n\r\n  return (\r\n    <DiscoverContext.Provider value={contextValue}>\r\n      {children}\r\n    </DiscoverContext.Provider>\r\n  );\r\n}\r\n\r\n// Create a custom hook to use the context\r\nexport function useDiscoverContext() {\r\n  const context = useContext(DiscoverContext);\r\n  if (context === undefined) {\r\n    throw new Error(\r\n      \"useDiscoverContext must be used within a DiscoverProvider\"\r\n    );\r\n  }\r\n  return context;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AAGA;AACA;AACA;AAkBA;AACA;AACA;AA9BA;;;;;;;;;;;AAgCA,qBAAqB;AACrB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAClC;AAIK,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IAC1E,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,8BAA8B;IAC9B,MAAM,sBAAsB,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB,KAAK;IACrE,MAAM,iBAAiB,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;IAC1D,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,aAAU,KAAK;IACpD,MAAM,kBAAkB,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;IAC5D,MAAM,kBAAkB,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;IAC5D,MAAM,kBACJ,AAAC,aAAa,GAAG,CAAC,6JAAA,CAAA,kBAAe,KAAkB;IAErD,mCAAmC;IACnC,IAAI,sBAAuB,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB,KAC7D;IACF,IAAI,qBAAsB,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB,KAC3D;IAEF,+DAA+D;IAC/D,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,IAAI,WAAW;QACb,mFAAmF;QACnF,IAAI,oBAAoB,SAAS;YAC/B,sBAAsB;QACxB,OAAO,IAAI,oBAAoB,YAAY;YACzC,qBAAqB;QACvB;QAEA,8DAA8D;QAC9D,uCAAmC;;QAMnC;IACF;IACA,MAAM,yBACJ,AAAC,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB,KAA6B;IAEnE,QAAQ;IACR,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrD;IAEF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnD;IAEF,MAAM,CAAC,eAAe,iBAAiB,GACrC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAC9B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,iCAAiC;IACjC,MAAM,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,GAC1E,CAAA,GAAA,kKAAA,CAAA,8BAA2B,AAAD,EACxB,UACA,gBACA,iBACA,oBACA,eACA,YACA,eACA,gBACA,WACA,gBACA,YACA;IAGJ,gCAAgC;IAChC,MAAM,EACJ,uBAAuB,EACvB,mBAAmB,EACnB,yBAAyB,EACzB,gBAAgB,EACjB,GAAG,CAAA,GAAA,iKAAA,CAAA,6BAA0B,AAAD,EAC3B,UACA,gBACA,iBACA,oBACA,aACA,YACA,eACA,gBACA,kBACA,oBACA,gBACA,UACA,QACA,eACA;IAGF,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QAEpB,uBAAuB;QACvB,uCAAmC;;QAQnC;QAEA,gDAAgD;QAChD,eAAe;QACf,WAAW;QAEX,oEAAoE;QACpE,MAAM,sBAAsB,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB;QAChE,MAAM,iBAAiB,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa;QACrD,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,aAAU;QAC/C,MAAM,kBAAkB,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc;QAEvD,uFAAuF;QACvF,cAAc;YACZ,cAAc;YACd,SAAS;YACT,MAAM;YACN,UAAU;YACV;QACF;IACF;IAEA,+BAA+B;IAC/B,MAAM,EACJ,SAAS,EACT,gBAAgB,EAChB,aAAa,EACb,UAAU,cAAc,EACzB,GAAG,CAAA,GAAA,gKAAA,CAAA,4BAAyB,AAAD,EAC1B,UACA,aACA,gBACA,iBACA,oBACA,eACA,aACA,YACA,eACA,gBACA,gBACA,QACA,eACA,iBACA;IAGF,qDAAqD;IACrD,MAAM,WAAW;QACf,MAAM,eACJ,oBACA,kBACA,aACA,eACA;IAEJ;IAEA,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oEAAoE;QACpE,eAAe;QAEf,6DAA6D;QAC7D,IACE,uBACA,kBACA,eACA,iBACA;YACA,+BAA+B;YAC/B,MAAM,qBACJ,oBAAoB,SAAS,KAAK;YAEpC,+DAA+D;YAC/D,cAAc;gBACZ,cAAc;gBACd,SAAS;gBACT,MAAM;gBACN,UAAU;gBACV,UAAU;YACZ;QACF,OAAO;YACL,+DAA+D;YAE/D,MAAM,mBAAmB;gBACvB,IAAI;oBACF,gDAAgD;oBAChD,MAAM,kBAAkB;oBACxB,QAAQ,GAAG,CAAC;oBAEZ,MAAM,SAAS,MAAM,CAAA,GAAA,8KAAA,CAAA,yBAAsB,AAAD,EAAE;wBAC1C,UAAU;wBACV,MAAM;wBACN,OAAO,+JAAA,CAAA,6BAA0B;wBACjC,aAAa,CAAA,GAAA,oJAAA,CAAA,0BAAuB,AAAD,EAAE;wBACrC,aAAa,oBAAoB,QAAQ,OAAO;oBAClD;oBAEA,IAAI,OAAO,IAAI,EAAE;wBACf,gBAAgB,OAAO,IAAI;wBAC3B,mBAAmB,OAAO,IAAI,CAAC,eAAe;wBAE9C,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE;4BACxB,YAAY,OAAO,IAAI,CAAC,QAAQ;4BAChC,uCAAuC;4BACvC,WAAW,OAAO,IAAI,CAAC,OAAO;wBAChC,OAAO;4BACL,WAAW;wBACb;wBAEA,cAAc,OAAO,IAAI,CAAC,UAAU;oBACtC,OAAO;wBACL,eAAe,OAAO,KAAK,IAAI;oBACjC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,eAAe;gBACjB,SAAU;oBACR,eAAe;gBACjB;YACF;YAEA;QACF;IACA,uDAAuD;IACzD,GAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,eAAoC;QACxC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;kBAC9B;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1105, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/locationSchemas.ts"], "sourcesContent": ["import * as z from \"zod\";\r\n\r\n// Schema for pincode-based search\r\nexport const pincodeSchema = z.object({\r\n  pincode: z\r\n    .string()\r\n    .regex(/^\\d{6}$/, { message: \"Pincode must be exactly 6 digits.\" }),\r\n  locality: z.string().optional().nullable(), // Optional locality\r\n});\r\n\r\n// Schema for city-based search\r\nexport const citySchema = z.object({\r\n  city: z\r\n    .string()\r\n    .min(2, { message: \"City name must be at least 2 characters.\" }),\r\n  locality: z.string().optional().nullable(), // Optional locality\r\n});\r\n\r\n// Schema for business name search\r\nexport const businessNameSchema = z.object({\r\n  businessName: z.string().min(1, { message: \"Business name is required.\" }),\r\n});\r\n\r\n// Schema for combined search\r\nexport const combinedSearchSchema = z.object({\r\n  businessName: z.string().optional().nullable(),\r\n  pincode: z\r\n    .string()\r\n    .regex(/^\\d{6}$/, { message: \"Pincode must be exactly 6 digits.\" })\r\n    .optional()\r\n    .nullable(),\r\n  city: z.string().optional().nullable(),\r\n  locality: z.string().optional().nullable(),\r\n  category: z.string().optional().nullable(),\r\n});\r\n\r\n// Schema for pagination parameters\r\nexport const paginationSchema = z.object({\r\n  page: z.number().int().positive().default(1),\r\n  limit: z.number().int().positive().max(50).default(20), // Max 50 items per page\r\n});\r\n\r\n// Schema for sorting options\r\nexport const sortingSchema = z.object({\r\n  sortBy: z\r\n    .enum([\r\n      \"name_asc\",\r\n      \"name_desc\",\r\n      \"created_asc\",\r\n      \"created_desc\",\r\n      \"likes_asc\",\r\n      \"likes_desc\",\r\n      \"subscriptions_asc\",\r\n      \"subscriptions_desc\",\r\n      \"rating_asc\",\r\n      \"rating_desc\",\r\n    ])\r\n    .default(\"created_desc\"),\r\n});\r\n\r\n// Combined schema for pincode-based discovery search with pagination\r\nexport const discoverySearchSchema = pincodeSchema.extend({\r\n  viewType: z.enum([\"cards\", \"products\"]),\r\n  ...paginationSchema.shape,\r\n  ...sortingSchema.shape,\r\n});\r\n\r\n// Combined schema for business name search with pagination\r\nexport const businessNameSearchSchema = businessNameSchema.extend({\r\n  viewType: z.enum([\"cards\", \"products\"]),\r\n  ...paginationSchema.shape,\r\n  ...sortingSchema.shape,\r\n});\r\n\r\n// Combined schema for combined search with pagination\r\nexport const combinedSearchParamsSchema = combinedSearchSchema.extend({\r\n  viewType: z.enum([\"cards\", \"products\"]),\r\n  ...paginationSchema.shape,\r\n  ...sortingSchema.shape,\r\n});\r\n\r\nexport type LocationSearchFormData = z.infer<typeof pincodeSchema>;\r\nexport type CitySearchFormData = z.infer<typeof citySchema>;\r\nexport type BusinessNameSearchFormData = z.infer<typeof businessNameSchema>;\r\nexport type PaginationParams = z.infer<typeof paginationSchema>;\r\nexport type SortingParams = z.infer<typeof sortingSchema>;\r\nexport type DiscoverySearchParams = z.infer<typeof discoverySearchSchema>;\r\nexport type BusinessNameSearchParams = z.infer<typeof businessNameSearchSchema>;\r\nexport type CombinedSearchParams = z.infer<typeof combinedSearchParamsSchema>;\r\nexport type CombinedSearchFormData = z.infer<typeof combinedSearchSchema>;\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAGO,MAAM,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACpC,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,KAAK,CAAC,WAAW;QAAE,SAAS;IAAoC;IACnE,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACjC,MAAM,CAAA,GAAA,oIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C;IAChE,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;AAC1E;AAGO,MAAM,uBAAuB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IAC3C,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC5C,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,KAAK,CAAC,WAAW;QAAE,SAAS;IAAoC,GAChE,QAAQ,GACR,QAAQ;IACX,MAAM,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IACpC,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IACxC,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,mBAAmB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACvC,MAAM,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC1C,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AACrD;AAGO,MAAM,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACpC,QAAQ,CAAA,GAAA,oIAAA,CAAA,OACD,AAAD,EAAE;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,EACA,OAAO,CAAC;AACb;AAGO,MAAM,wBAAwB,cAAc,MAAM,CAAC;IACxD,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;KAAW;IACtC,GAAG,iBAAiB,KAAK;IACzB,GAAG,cAAc,KAAK;AACxB;AAGO,MAAM,2BAA2B,mBAAmB,MAAM,CAAC;IAChE,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;KAAW;IACtC,GAAG,iBAAiB,KAAK;IACzB,GAAG,cAAc,KAAK;AACxB;AAGO,MAAM,6BAA6B,qBAAqB,MAAM,CAAC;IACpE,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;KAAW;IACtC,GAAG,iBAAiB,KAAK;IACzB,GAAG,cAAc,KAAK;AACxB", "debugId": null}}, {"offset": {"line": 1192, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1443, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-pointer items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,8OAAC,sIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,8OAAC,2HAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,8OAAC,2HAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,8OAAC,2HAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,8OAAC,2HAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,8OAAC,2HAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,8OAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;;0BAEV,8OAAC,0MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,sIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/location.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { Database } from \"@/types/supabase\";\n\n\n// --- Pincode Lookup Action ---\nexport async function getPincodeDetails(pincode: string): Promise<{\n  data?: {\n    city: string;\n    state: string;\n    localities: string[];\n  };\n  city?: string;\n  state?: string;\n  localities?: string[];\n  error?: string;\n}> {\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\n    return { error: \"Invalid Pincode format.\" };\n  }\n\n  const supabase = await createClient();\n  try {\n    // First get city and state from pincodes table\n    const { data: pincodeData, error: pincodeError } = await supabase\n      .from(\"pincodes\")\n      .select(\"OfficeName, DivisionName, StateName\")\n      .eq(\"Pincode\", pincode) // Updated column name to match database\n      .order(\"OfficeName\");\n\n    if (pincodeError) {\n      console.error(\"Pincode Fetch Error:\", pincodeError);\n      return { error: \"Database error fetching pincode details.\" };\n    }\n\n    if (!pincodeData || pincodeData.length === 0) {\n      return { error: \"Pincode not found.\" };\n    }\n\n    // State names are already in title case format in the database\n    const state = pincodeData[0].StateName;\n\n    // Use DivisionName as the city (already cleaned)\n    const city = pincodeData[0].DivisionName;\n\n    // Get unique localities from post office names\n    const localities = [\n      ...new Set(pincodeData.map((item: { OfficeName: string }) => item.OfficeName)),\n    ] as string[];\n\n    return {\n      data: { city, state, localities },\n      city,\n      state,\n      localities\n    };\n  } catch (e) {\n    console.error(\"Pincode Lookup Exception:\", e);\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\n  }\n}\n// --- End Pincode Lookup ---\n\n// --- City Lookup Action ---\nexport async function getCityDetails(city: string): Promise<{\n  data?: {\n    pincodes: string[];\n    state: string;\n    localities: string[];\n  };\n  pincodes?: string[];\n  state?: string;\n  localities?: string[];\n  error?: string;\n}> {\n  if (!city || city.length < 2) {\n    return { error: \"City name must be at least 2 characters.\" };\n  }\n\n  const supabase = await createClient();\n  try {\n    // Get pincodes and state for the city - DivisionName is the city column\n    const { data: cityData, error: cityError } = await supabase\n      .from(\"pincodes\")\n      .select(\"Pincode, OfficeName, StateName, DivisionName\")\n      .ilike(\"DivisionName\", `%${city}%`)\n      .order(\"Pincode\");\n\n    if (cityError) {\n      console.error(\"City Fetch Error:\", cityError);\n      return { error: \"Database error fetching city details.\" };\n    }\n\n    if (!cityData || cityData.length === 0) {\n      return { error: \"City not found.\" };\n    }\n\n    // State names are already in title case format in the database\n    const state = cityData[0].StateName;\n\n    // Get unique pincodes\n    const pincodes = [...new Set(cityData.map((item: { Pincode: string }) => item.Pincode))] as string[];\n\n    // Get unique localities from post office names\n    const localities = [\n      ...new Set(cityData.map((item: { OfficeName: string }) => item.OfficeName)),\n    ] as string[];\n\n    return {\n      data: { pincodes, state, localities },\n      pincodes,\n      state,\n      localities\n    };\n  } catch (e) {\n    console.error(\"City Lookup Exception:\", e);\n    return { error: \"An unexpected error occurred during city lookup.\" };\n  }\n}\n// --- End City Lookup ---\n\n// --- City Autocomplete Action ---\n/**\n * Get city suggestions based on a search query\n *\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\n * The PostgreSQL function is defined as:\n *\n * ```sql\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\n * RETURNS TABLE(city TEXT) AS $$\n * BEGIN\n *   RETURN QUERY\n *   SELECT DISTINCT \"DivisionName\" as city\n *   FROM pincodes\n *   WHERE \"DivisionName\" ILIKE search_query\n *   ORDER BY \"DivisionName\"\n *   LIMIT result_limit;\n * END;\n * $$ LANGUAGE plpgsql;\n * ```\n *\n * @param query The search query (minimum 2 characters)\n * @returns Array of up to 5 unique city suggestions\n */\nexport async function getCitySuggestions(query: string): Promise<{\n  data?: {\n    cities: string[];\n  };\n  cities?: string[];\n  error?: string;\n}> {\n  if (!query || query.length < 2) {\n    return { error: \"Query must be at least 2 characters.\" };\n  }\n\n  const supabase = (await createClient()) as SupabaseClient<Database>;\n  try {\n    // Use the PostgreSQL function to get distinct cities (up to 5)\n    const { data: cityData, error: cityError } = await supabase\n      .rpc('get_distinct_cities', {\n        search_query: `%${query}%`,\n        result_limit: 5\n      });\n\n    if (cityError) {\n      console.error(\"City Suggestions Error:\", cityError);\n\n      // Fallback to regular query if RPC fails\n      try {\n        // Use a regular query as fallback\n        const { data: fallbackData, error: fallbackError } = await supabase\n          .from(\"pincodes\")\n          .select(\"DivisionName\")\n          .ilike(\"DivisionName\", `%${query}%`)\n          .order(\"DivisionName\")\n          .limit(100);\n\n        if (fallbackError) {\n          throw fallbackError;\n        }\n\n        if (!fallbackData || fallbackData.length === 0) {\n          return { data: { cities: [] }, cities: [] };\n        }\n\n        // Get unique cities and format them\n        const cities = [...new Set(fallbackData.map((item: { DivisionName: string }) =>\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\n        ))] as string[];\n\n        const topCities = cities.slice(0, 5);\n\n        return {\n          data: { cities: topCities },\n          cities: topCities\n        };\n      } catch (fallbackErr) {\n        console.error(\"Fallback City Query Error:\", fallbackErr);\n        return { error: \"Database error fetching city suggestions.\" };\n      }\n    }\n\n    if (!cityData || cityData.length === 0) {\n      return { data: { cities: [] }, cities: [] };\n    }\n\n    // Format the city names to Title Case\n    const cities = cityData.map((item: { city: string }) =>\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\n    );\n\n    return {\n      data: { cities },\n      cities\n    };\n  } catch (e) {\n    console.error(\"City Suggestions Exception:\", e);\n    return { error: \"An unexpected error occurred while fetching city suggestions.\" };\n  }\n}\n// --- End City Autocomplete ---\n"], "names": [], "mappings": ";;;;;;IAQsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 1639, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/client/locationUtils.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { createClient } from \"@/utils/supabase/client\";\r\n\r\n/**\r\n * Get city suggestions based on a search query\r\n * @param query The search query (minimum 2 characters)\r\n * @returns Array of city suggestions\r\n */\r\n/**\r\n * Get city suggestions based on a search query\r\n *\r\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\r\n * The PostgreSQL function is defined as:\r\n *\r\n * ```sql\r\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\r\n * RETURNS TABLE(city TEXT) AS $$\r\n * BEGIN\r\n *   RETURN QUERY\r\n *   SELECT DISTINCT \"DivisionName\" as city\r\n *   FROM pincodes\r\n *   WHERE \"DivisionName\" ILIKE search_query\r\n *   ORDER BY \"DivisionName\"\r\n *   LIMIT result_limit;\r\n * END;\r\n * $$ LANGUAGE plpgsql;\r\n * ```\r\n *\r\n * @param query The search query (minimum 2 characters)\r\n * @returns Array of up to 5 unique city suggestions\r\n */\r\nexport async function getCitySuggestionsClient(query: string): Promise<{\r\n  cities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!query || query.length < 2) {\r\n    return { error: \"Query must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = createClient();\r\n  try {\r\n    // Use the PostgreSQL function to get distinct cities (up to 5)\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .rpc('get_distinct_cities', {\r\n        search_query: `%${query}%`,\r\n        result_limit: 5\r\n      });\r\n\r\n    if (cityError) {\r\n      console.error(\"City Suggestions Error:\", cityError);\r\n\r\n      // Fallback to regular query if RPC fails (in case the function doesn't exist)\r\n      try {\r\n        // Use DISTINCT ON to get unique city names directly from the database\r\n        const { data: fallbackData, error: fallbackError } = await supabase\r\n          .from(\"pincodes\")\r\n          .select(\"DivisionName\")\r\n          .ilike(\"DivisionName\", `%${query}%`)\r\n          .order(\"DivisionName\")\r\n          .limit(100);\r\n\r\n        if (fallbackError) {\r\n          throw fallbackError;\r\n        }\r\n\r\n        if (!fallbackData || fallbackData.length === 0) {\r\n          return { cities: [] };\r\n        }\r\n\r\n        // Get unique cities and format them\r\n        const cities = [...new Set(fallbackData.map((item) =>\r\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n        ))] as string[];\r\n\r\n        return { cities: cities.slice(0, 5) };\r\n      } catch (fallbackErr) {\r\n        console.error(\"Fallback City Query Error:\", fallbackErr);\r\n        return { error: \"Database error fetching city suggestions.\" };\r\n      }\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      return { cities: [] };\r\n    }\r\n\r\n    // Format the city names to Title Case\r\n    // The response from get_distinct_cities is an array of objects with a 'city' property\r\n    const cities = cityData.map((item: { city: string }) =>\r\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\r\n    );\r\n\r\n    return { cities };\r\n  } catch (e) {\r\n    console.error(\"City Suggestions Exception:\", e);\r\n    return { error: \"An unexpected error occurred during city suggestions.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get pincode details including city, state, and localities\r\n * @param pincode The 6-digit pincode\r\n * @returns Pincode details including city, state, and localities\r\n */\r\nexport async function getPincodeDetailsClient(pincode: string): Promise<{\r\n  city?: string;\r\n  state?: string;\r\n  localities?: string[];\r\n  error?: string;\r\n}> {\r\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\r\n    return { error: \"Invalid Pincode format.\" };\r\n  }\r\n\r\n  const supabase = createClient();\r\n  try {\r\n    // First get city and state from pincodes table\r\n    const { data: pincodeData, error: pincodeError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"OfficeName, DivisionName, StateName\")\r\n      .eq(\"Pincode\", pincode)\r\n      .order(\"OfficeName\");\r\n\r\n    if (pincodeError) {\r\n      console.error(\"Pincode Fetch Error:\", pincodeError);\r\n      return { error: \"Database error fetching pincode details.\" };\r\n    }\r\n\r\n    if (!pincodeData || pincodeData.length === 0) {\r\n      return { error: \"Pincode not found.\" };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = pincodeData[0].StateName;\r\n\r\n    // Format city name (DivisionName) to Title Case\r\n    const city = pincodeData[0].DivisionName\r\n      .toLowerCase()\r\n      .replace(/\\b\\w/g, (char: string) => char.toUpperCase());\r\n\r\n    // Get unique localities from post office names\r\n    const localities = [\r\n      ...new Set(\r\n        pincodeData.map((item) =>\r\n          item.OfficeName.replace(\" B.O\", \"\").trim()\r\n        )\r\n      ),\r\n    ] as string[];\r\n\r\n    return {\r\n      city,\r\n      state,\r\n      localities\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Pincode Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get city details including state and check if it belongs to a specific state\r\n * @param city The city name\r\n * @param expectedState Optional state name to validate against\r\n * @returns City details including state and validation result\r\n */\r\nexport async function getCityDetailsClient(city: string, expectedState?: string): Promise<{\r\n  state?: string;\r\n  isValidState?: boolean;\r\n  error?: string;\r\n}> {\r\n  if (!city || city.length < 2) {\r\n    return { error: \"City name must be at least 2 characters.\" };\r\n  }\r\n\r\n  const supabase = createClient();\r\n  try {\r\n    // Get state for the city - DivisionName is the city column\r\n    // Use exact matching for city name to ensure we get the correct city\r\n    const { data: cityData, error: cityError } = await supabase\r\n      .from(\"pincodes\")\r\n      .select(\"StateName, DivisionName\")\r\n      .eq(\"DivisionName\", city)\r\n      .limit(1);\r\n\r\n    if (cityError) {\r\n      console.error(\"City Fetch Error:\", cityError);\r\n      return { error: \"Database error fetching city details.\" };\r\n    }\r\n\r\n    if (!cityData || cityData.length === 0) {\r\n      // Try with case-insensitive search as fallback\r\n      const { data: fallbackData, error: fallbackError } = await supabase\r\n        .from(\"pincodes\")\r\n        .select(\"StateName, DivisionName\")\r\n        .ilike(\"DivisionName\", city)\r\n        .limit(1);\r\n\r\n      if (fallbackError || !fallbackData || fallbackData.length === 0) {\r\n        return { error: \"City not found.\" };\r\n      }\r\n\r\n      // State names are already in title case format in the database\r\n      const state = fallbackData[0].StateName;\r\n\r\n      // Check if the city belongs to the expected state\r\n      const isValidState = expectedState ? state === expectedState : true;\r\n\r\n      return {\r\n        state,\r\n        isValidState\r\n      };\r\n    }\r\n\r\n    // State names are already in title case format in the database\r\n    const state = cityData[0].StateName;\r\n\r\n    // Check if the city belongs to the expected state\r\n    const isValidState = expectedState ? state === expectedState : true;\r\n\r\n    return {\r\n      state,\r\n      isValidState\r\n    };\r\n  } catch (e) {\r\n    console.error(\"City Lookup Exception:\", e);\r\n    return { error: \"An unexpected error occurred during city lookup.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAgCO,eAAe,yBAAyB,KAAa;IAI1D,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO;YAAE,OAAO;QAAuC;IACzD;IAEA,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAC5B,IAAI;QACF,+DAA+D;QAC/D,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,GAAG,CAAC,uBAAuB;YAC1B,cAAc,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,cAAc;QAChB;QAEF,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,8EAA8E;YAC9E,IAAI;gBACF,sEAAsE;gBACtE,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,gBACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAClC,KAAK,CAAC,gBACN,KAAK,CAAC;gBAET,IAAI,eAAe;oBACjB,MAAM;gBACR;gBAEA,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;oBAC9C,OAAO;wBAAE,QAAQ,EAAE;oBAAC;gBACtB;gBAEA,oCAAoC;gBACpC,MAAM,SAAS;uBAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,OAC3C,KAAK,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;iBAClF;gBAEH,OAAO;oBAAE,QAAQ,OAAO,KAAK,CAAC,GAAG;gBAAG;YACtC,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,OAAO;oBAAE,OAAO;gBAA4C;YAC9D;QACF;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,QAAQ,EAAE;YAAC;QACtB;QAEA,sCAAsC;QACtC,sFAAsF;QACtF,MAAM,SAAS,SAAS,GAAG,CAAC,CAAC,OAC3B,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;QAG7E,OAAO;YAAE;QAAO;IAClB,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;QAAwD;IAC1E;AACF;AAOO,eAAe,wBAAwB,OAAe;IAM3D,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,UAAU;QACxC,OAAO;YAAE,OAAO;QAA0B;IAC5C;IAEA,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAC5B,IAAI;QACF,+CAA+C;QAC/C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,uCACP,EAAE,CAAC,WAAW,SACd,KAAK,CAAC;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE,OAAO;YAA2C;QAC7D;QAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC5C,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,WAAW,CAAC,EAAE,CAAC,SAAS;QAEtC,gDAAgD;QAChD,MAAM,OAAO,WAAW,CAAC,EAAE,CAAC,YAAY,CACrC,WAAW,GACX,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;QAEtD,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IACL,YAAY,GAAG,CAAC,CAAC,OACf,KAAK,UAAU,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI;SAG7C;QAED,OAAO;YACL;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,OAAO;QAAsD;IACxE;AACF;AAQO,eAAe,qBAAqB,IAAY,EAAE,aAAsB;IAK7E,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;QAC5B,OAAO;YAAE,OAAO;QAA2C;IAC7D;IAEA,MAAM,WAAW,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAC5B,IAAI;QACF,2DAA2D;QAC3D,qEAAqE;QACrE,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,YACL,MAAM,CAAC,2BACP,EAAE,CAAC,gBAAgB,MACnB,KAAK,CAAC;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAwC;QAC1D;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,+CAA+C;YAC/C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,2BACP,KAAK,CAAC,gBAAgB,MACtB,KAAK,CAAC;YAET,IAAI,iBAAiB,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;gBAC/D,OAAO;oBAAE,OAAO;gBAAkB;YACpC;YAEA,+DAA+D;YAC/D,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,SAAS;YAEvC,kDAAkD;YAClD,MAAM,eAAe,gBAAgB,UAAU,gBAAgB;YAE/D,OAAO;gBACL;gBACA;YACF;QACF;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC,SAAS;QAEnC,kDAAkD;QAClD,MAAM,eAAe,gBAAgB,UAAU,gBAAgB;QAE/D,OAAO;YACL;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,OAAO;QAAmD;IACrE;AACF", "debugId": null}}, {"offset": {"line": 1802, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/CitySearchSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nexport default function CitySearchSkeleton() {\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      exit={{ opacity: 0 }}\r\n      className=\"absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg p-1\"\r\n    >\r\n      <div className=\"py-1 px-2\">\r\n        {Array.from({ length: 5 }).map((_, i) => (\r\n          <div key={i} className=\"flex items-center py-1.5 px-2\">\r\n            <Skeleton className=\"h-4 w-4 mr-2 rounded-full\" />\r\n            <Skeleton className=\"h-4 w-full max-w-[180px]\" />\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;QACnB,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;oBAAY,WAAU;;sCACrB,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;mBAFZ;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 1962, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/config/categories.ts"], "sourcesContent": ["import { LucideIcon } from \"lucide-react\";\r\nimport {\r\n  Utensils,\r\n  Briefcase,\r\n  GraduationCap,\r\n  Car,\r\n  Store,\r\n  Stethoscope,\r\n  Scissors,\r\n  Wrench,\r\n  ShoppingBag,\r\n  Laptop,\r\n  Home,\r\n  Dumbbell,\r\n  Plane,\r\n  Coffee,\r\n  Music,\r\n  Camera,\r\n  Pen,\r\n  PenTool,\r\n  Code,\r\n  Shirt,\r\n  Truck,\r\n  Building,\r\n  Landmark,\r\n  Hammer,\r\n  Leaf,\r\n  Palette,\r\n  BookOpen,\r\n  Users,\r\n  HeartPulse,\r\n  Sparkles,\r\n  Gem,\r\n  Smartphone,\r\n  Megaphone,\r\n  Banknote,\r\n  Gavel,\r\n  Scroll,\r\n  Handshake,\r\n  Warehouse,\r\n  Factory,\r\n  Tractor,\r\n  Cake,\r\n  Bed,\r\n  Luggage,\r\n  Bus,\r\n  Flower,\r\n  ShoppingCart,\r\n  Brush,\r\n  Mic,\r\n  Film,\r\n  Gamepad2,\r\n  Printer,\r\n  Cog,\r\n  Boxes,\r\n  Wallet,\r\n  BadgeIndianRupee,\r\n  Building2,\r\n  Presentation,\r\n  Zap,\r\n  Droplets,\r\n  Recycle,\r\n  Microscope,\r\n  Pill,\r\n  Baby,\r\n  Glasses,\r\n  Footprints,\r\n  Bike,\r\n  Sofa,\r\n  Stamp,\r\n  ShieldCheck,\r\n  Bug,\r\n} from \"lucide-react\";\r\n\r\nexport interface BusinessCategory {\r\n  name: string;\r\n  icon: LucideIcon;\r\n  slug?: string;\r\n  description?: string;\r\n  isPopular?: boolean;\r\n}\r\n\r\n/**\r\n * Shared business categories used across the application\r\n * This centralized list ensures consistency across different components\r\n * Some categories are marked as popular for display on homepage and discovery page\r\n */\r\nexport const BUSINESS_CATEGORIES: BusinessCategory[] = [\r\n  // Food & Dining\r\n  { name: \"Restaurants\", icon: Utensils, slug: \"restaurants\", description: \"Restaurants and dining establishments\", isPopular: true },\r\n  { name: \"Cafes & Bakeries\", icon: Coffee, slug: \"cafes-bakeries\", description: \"Coffee shops, bakeries, and dessert places\", isPopular: true },\r\n  { name: \"Food Delivery\", icon: Truck, slug: \"food-delivery\", description: \"Food delivery services\" },\r\n  { name: \"Catering\", icon: Utensils, slug: \"catering\", description: \"Catering services for events\" },\r\n  { name: \"Sweet Shops\", icon: Cake, slug: \"sweet-shops\", description: \"Traditional sweet shops and confectioneries\" },\r\n  { name: \"Street Food\", icon: Utensils, slug: \"street-food\", description: \"Street food vendors and stalls\" },\r\n  { name: \"Cloud Kitchen\", icon: Utensils, slug: \"cloud-kitchen\", description: \"Delivery-only food businesses\" },\r\n  { name: \"Tiffin Services\", icon: Utensils, slug: \"tiffin-services\", description: \"Home-cooked meal delivery services\" },\r\n\r\n  // Retail & Shopping\r\n  { name: \"Retail Stores\", icon: Store, slug: \"retail\", description: \"General retail and shops\", isPopular: true },\r\n  { name: \"Grocery & Supermarkets\", icon: ShoppingCart, slug: \"grocery\", description: \"Grocery stores and supermarkets\", isPopular: true },\r\n  { name: \"Fashion & Clothing\", icon: Shirt, slug: \"fashion\", description: \"Clothing and fashion retailers\", isPopular: true },\r\n  { name: \"Electronics\", icon: Smartphone, slug: \"electronics\", description: \"Electronics and gadget stores\" },\r\n  { name: \"Home Decor\", icon: Sofa, slug: \"home-decor\", description: \"Home decor and furnishing stores\" },\r\n  { name: \"Jewelry\", icon: Gem, slug: \"jewelry\", description: \"Jewelry and accessory stores\" },\r\n  { name: \"Bookstores\", icon: BookOpen, slug: \"bookstores\", description: \"Book shops and stationers\" },\r\n  { name: \"Footwear\", icon: Footprints, slug: \"footwear\", description: \"Shoe and footwear stores\" },\r\n  { name: \"Gift Shops\", icon: ShoppingBag, slug: \"gift-shops\", description: \"Gift and souvenir shops\" },\r\n  { name: \"Eyewear\", icon: Glasses, slug: \"eyewear\", description: \"Optical shops and eyewear retailers\" },\r\n  { name: \"Mobile Shops\", icon: Smartphone, slug: \"mobile-shops\", description: \"Mobile phone retailers and repair shops\" },\r\n\r\n  // Professional Services\r\n  { name: \"Legal Services\", icon: Gavel, slug: \"legal\", description: \"Lawyers and legal consultants\", isPopular: true },\r\n  { name: \"Financial Services\", icon: Banknote, slug: \"financial\", description: \"Financial advisors and services\", isPopular: true },\r\n  { name: \"Accounting\", icon: BadgeIndianRupee, slug: \"accounting\", description: \"Accounting and tax services\" },\r\n  { name: \"Consulting\", icon: Briefcase, slug: \"consulting\", description: \"Business and management consulting\" },\r\n  { name: \"Insurance\", icon: Wallet, slug: \"insurance\", description: \"Insurance services and agents\" },\r\n  { name: \"HR Services\", icon: Users, slug: \"hr-services\", description: \"Human resources and recruitment services\" },\r\n  { name: \"Tax Consultants\", icon: BadgeIndianRupee, slug: \"tax-consultants\", description: \"Tax filing and consultation services\" },\r\n  { name: \"Notary Services\", icon: Stamp, slug: \"notary\", description: \"Notary and document verification services\" },\r\n  { name: \"Translation Services\", icon: Scroll, slug: \"translation\", description: \"Language translation and interpretation services\" },\r\n\r\n  // Healthcare\r\n  { name: \"Medical Clinics\", icon: Stethoscope, slug: \"medical\", description: \"Medical clinics and doctors\", isPopular: true },\r\n  { name: \"Dental Care\", icon: Sparkles, slug: \"dental\", description: \"Dental clinics and services\" },\r\n  { name: \"Pharmacy\", icon: Pill, slug: \"pharmacy\", description: \"Pharmacies and medical supplies\" },\r\n  { name: \"Mental Health\", icon: HeartPulse, slug: \"mental-health\", description: \"Mental health services and counseling\" },\r\n  { name: \"Alternative Medicine\", icon: Leaf, slug: \"alternative-medicine\", description: \"Ayurveda, homeopathy, and alternative treatments\" },\r\n  { name: \"Diagnostic Centers\", icon: Microscope, slug: \"diagnostic\", description: \"Medical testing and diagnostic centers\" },\r\n  { name: \"Physiotherapy\", icon: HeartPulse, slug: \"physiotherapy\", description: \"Physiotherapy and rehabilitation services\" },\r\n  { name: \"Veterinary\", icon: HeartPulse, slug: \"veterinary\", description: \"Veterinary clinics and pet healthcare\" },\r\n  { name: \"Elder Care\", icon: HeartPulse, slug: \"elder-care\", description: \"Elder care and assisted living services\" },\r\n  { name: \"Maternity Care\", icon: Baby, slug: \"maternity\", description: \"Maternity and childcare services\" },\r\n\r\n  // Beauty & Wellness\r\n  { name: \"Salon & Spa\", icon: Scissors, slug: \"salon-spa\", description: \"Beauty salons and spa services\", isPopular: true },\r\n  { name: \"Fitness\", icon: Dumbbell, slug: \"fitness\", description: \"Gyms and fitness centers\", isPopular: true },\r\n  { name: \"Yoga & Meditation\", icon: Users, slug: \"yoga\", description: \"Yoga studios and meditation centers\" },\r\n  { name: \"Cosmetics\", icon: Sparkles, slug: \"cosmetics\", description: \"Cosmetics and beauty products\" },\r\n  { name: \"Barber Shops\", icon: Scissors, slug: \"barber\", description: \"Men's grooming and barber shops\" },\r\n  { name: \"Wellness Centers\", icon: Sparkles, slug: \"wellness\", description: \"Wellness and holistic health centers\" },\r\n  { name: \"Massage Therapy\", icon: Sparkles, slug: \"massage\", description: \"Massage and bodywork services\" },\r\n  { name: \"Skin Care\", icon: Sparkles, slug: \"skin-care\", description: \"Skin care clinics and dermatology\" },\r\n\r\n  // Education & Training\r\n  { name: \"Schools\", icon: GraduationCap, slug: \"schools\", description: \"Schools and educational institutions\", isPopular: true },\r\n  { name: \"Coaching Centers\", icon: BookOpen, slug: \"coaching\", description: \"Coaching and tutoring centers\" },\r\n  { name: \"Vocational Training\", icon: Presentation, slug: \"vocational\", description: \"Vocational and skill training\" },\r\n  { name: \"Online Education\", icon: Laptop, slug: \"online-education\", description: \"Online courses and e-learning\" },\r\n  { name: \"Language Schools\", icon: BookOpen, slug: \"language\", description: \"Language learning and training centers\" },\r\n  { name: \"Music Classes\", icon: Music, slug: \"music-classes\", description: \"Music schools and training\" },\r\n  { name: \"Dance Classes\", icon: Music, slug: \"dance-classes\", description: \"Dance schools and training\" },\r\n  { name: \"Art Schools\", icon: Palette, slug: \"art-schools\", description: \"Art and craft education\" },\r\n  { name: \"Driving Schools\", icon: Car, slug: \"driving-education\", description: \"Driving training and education\" },\r\n  { name: \"Playschools\", icon: Baby, slug: \"playschools\", description: \"Preschools and early education\" },\r\n  { name: \"Tuition Centers\", icon: BookOpen, slug: \"tuition\", description: \"Private tutoring and academic support\" },\r\n\r\n  // Technology\r\n  { name: \"IT Services\", icon: Code, slug: \"it-services\", description: \"IT services and support\", isPopular: true },\r\n  { name: \"Software Development\", icon: Laptop, slug: \"software\", description: \"Software development companies\" },\r\n  { name: \"Web Development\", icon: Code, slug: \"web-development\", description: \"Web design and development services\" },\r\n  { name: \"Digital Marketing\", icon: Megaphone, slug: \"digital-marketing\", description: \"Digital marketing agencies and services\" },\r\n  { name: \"App Development\", icon: Smartphone, slug: \"app-development\", description: \"Mobile app development services\" },\r\n  { name: \"IT Hardware\", icon: Laptop, slug: \"it-hardware\", description: \"Computer hardware sales and services\" },\r\n  { name: \"Cyber Security\", icon: ShieldCheck, slug: \"cyber-security\", description: \"Cybersecurity services and solutions\" },\r\n  { name: \"Cloud Services\", icon: Code, slug: \"cloud-services\", description: \"Cloud computing and hosting services\" },\r\n  { name: \"Data Analytics\", icon: Code, slug: \"data-analytics\", description: \"Data analysis and business intelligence\" },\r\n\r\n  // Automotive\r\n  { name: \"Auto Repair\", icon: Wrench, slug: \"auto-repair\", description: \"Car repair and service centers\", isPopular: true },\r\n  { name: \"Car Dealerships\", icon: Car, slug: \"car-dealerships\", description: \"New and used car dealerships\" },\r\n  { name: \"Auto Parts\", icon: Cog, slug: \"auto-parts\", description: \"Automotive parts and accessories\" },\r\n  { name: \"Two-Wheeler Services\", icon: Bike, slug: \"two-wheeler\", description: \"Motorcycle and scooter services\" },\r\n  { name: \"Car Wash\", icon: Car, slug: \"car-wash\", description: \"Car washing and detailing services\" },\r\n  { name: \"Tyre Shops\", icon: Car, slug: \"tyre-shops\", description: \"Tyre sales and services\" },\r\n  { name: \"Auto Electricians\", icon: Zap, slug: \"auto-electricians\", description: \"Automotive electrical repair services\" },\r\n  { name: \"Vehicle Rental\", icon: Car, slug: \"vehicle-rental\", description: \"Car and bike rental services\" },\r\n\r\n  // Real Estate & Construction\r\n  { name: \"Real Estate\", icon: Home, slug: \"real-estate\", description: \"Property and real estate services\", isPopular: true },\r\n  { name: \"Construction\", icon: Hammer, slug: \"construction\", description: \"Construction services and contractors\" },\r\n  { name: \"Interior Design\", icon: Palette, slug: \"interior-design\", description: \"Interior design and decoration services\" },\r\n  { name: \"Architecture\", icon: Building, slug: \"architecture\", description: \"Architectural services and firms\" },\r\n  { name: \"Property Management\", icon: Building, slug: \"property-management\", description: \"Property management services\" },\r\n  { name: \"Building Materials\", icon: Boxes, slug: \"building-materials\", description: \"Construction materials suppliers\" },\r\n  { name: \"Plumbing Services\", icon: Wrench, slug: \"plumbing\", description: \"Plumbing installation and repair\" },\r\n  { name: \"Electrical Services\", icon: Zap, slug: \"electrical\", description: \"Electrical installation and repair\" },\r\n  { name: \"Painting Services\", icon: Brush, slug: \"painting\", description: \"House painting and finishing services\" },\r\n  { name: \"Carpentry\", icon: Hammer, slug: \"carpentry\", description: \"Carpentry and woodworking services\" },\r\n  { name: \"Landscaping\", icon: Flower, slug: \"landscaping\", description: \"Garden and landscape design services\" },\r\n\r\n  // Travel & Hospitality\r\n  { name: \"Hotels\", icon: Bed, slug: \"hotels\", description: \"Hotels and accommodations\", isPopular: true },\r\n  { name: \"Travel Agencies\", icon: Plane, slug: \"travel-agencies\", description: \"Travel agencies and tour operators\" },\r\n  { name: \"Transportation\", icon: Bus, slug: \"transportation\", description: \"Transportation services\" },\r\n  { name: \"Tourism\", icon: Luggage, slug: \"tourism\", description: \"Tourism services and attractions\" },\r\n  { name: \"Homestays\", icon: Home, slug: \"homestays\", description: \"Homestays and guest houses\" },\r\n  { name: \"Tour Guides\", icon: Plane, slug: \"tour-guides\", description: \"Local tour guides and services\" },\r\n  { name: \"Adventure Tourism\", icon: Plane, slug: \"adventure-tourism\", description: \"Adventure sports and tourism\" },\r\n  { name: \"Resorts\", icon: Bed, slug: \"resorts\", description: \"Resorts and vacation properties\" },\r\n  { name: \"Visa Services\", icon: Stamp, slug: \"visa-services\", description: \"Visa application and processing services\" },\r\n\r\n  // Entertainment & Events\r\n  { name: \"Entertainment\", icon: Music, slug: \"entertainment\", description: \"Entertainment venues and services\", isPopular: true },\r\n  { name: \"Event Management\", icon: Sparkles, slug: \"event-management\", description: \"Event planning and management services\" },\r\n  { name: \"Wedding Services\", icon: Handshake, slug: \"wedding\", description: \"Wedding planning and related services\" },\r\n  { name: \"Photography\", icon: Camera, slug: \"photography\", description: \"Photography and videography services\" },\r\n  { name: \"Cinema Halls\", icon: Film, slug: \"cinema\", description: \"Movie theaters and cinemas\" },\r\n  { name: \"Gaming Zones\", icon: Gamepad2, slug: \"gaming\", description: \"Gaming arcades and entertainment centers\" },\r\n  { name: \"Party Venues\", icon: Music, slug: \"party-venues\", description: \"Party and event venues\" },\r\n  { name: \"DJs & Musicians\", icon: Music, slug: \"djs-musicians\", description: \"DJs and live music performers\" },\r\n  { name: \"Amusement Parks\", icon: Sparkles, slug: \"amusement-parks\", description: \"Amusement and theme parks\" },\r\n\r\n  // Freelancers & Creative Professionals\r\n  { name: \"Freelance Services\", icon: Briefcase, slug: \"freelance\", description: \"Independent professionals and freelancers\", isPopular: true },\r\n  { name: \"Graphic Design\", icon: PenTool, slug: \"graphic-design\", description: \"Graphic design services\" },\r\n  { name: \"Content Creation\", icon: Pen, slug: \"content-creation\", description: \"Content writing and creation services\" },\r\n  { name: \"Art & Crafts\", icon: Brush, slug: \"art-crafts\", description: \"Artists and craftspeople\" },\r\n  { name: \"Music & Performance\", icon: Mic, slug: \"music-performance\", description: \"Musicians and performers\" },\r\n  { name: \"Videography\", icon: Film, slug: \"videography\", description: \"Video production and editing services\" },\r\n  { name: \"Voice Over Artists\", icon: Mic, slug: \"voice-over\", description: \"Voice over and narration services\" },\r\n  { name: \"Translators\", icon: Scroll, slug: \"translators\", description: \"Language translation services\" },\r\n  { name: \"Tutors\", icon: BookOpen, slug: \"tutors\", description: \"Private tutors and educators\" },\r\n  { name: \"Consultants\", icon: Briefcase, slug: \"consultants\", description: \"Independent consultants and advisors\" },\r\n  { name: \"Astrologers\", icon: Sparkles, slug: \"astrologers\", description: \"Astrology and horoscope services\" },\r\n\r\n  // Manufacturing & Industry\r\n  { name: \"Manufacturing\", icon: Factory, slug: \"manufacturing\", description: \"Manufacturing businesses\" },\r\n  { name: \"Wholesale\", icon: Warehouse, slug: \"wholesale\", description: \"Wholesale suppliers and distributors\" },\r\n  { name: \"Textiles\", icon: Shirt, slug: \"textiles\", description: \"Textile manufacturing and supplies\" },\r\n  { name: \"Printing\", icon: Printer, slug: \"printing\", description: \"Printing services and press\" },\r\n  { name: \"Packaging\", icon: Boxes, slug: \"packaging\", description: \"Packaging materials and services\" },\r\n  { name: \"Metal Works\", icon: Hammer, slug: \"metal-works\", description: \"Metal fabrication and works\" },\r\n  { name: \"Plastic Products\", icon: Factory, slug: \"plastic-products\", description: \"Plastic manufacturing and products\" },\r\n  { name: \"Handicrafts\", icon: Brush, slug: \"handicrafts\", description: \"Handmade crafts and products\" },\r\n  { name: \"Furniture Making\", icon: Sofa, slug: \"furniture\", description: \"Furniture manufacturing and carpentry\" },\r\n\r\n  // Agriculture\r\n  { name: \"Agriculture\", icon: Tractor, slug: \"agriculture\", description: \"Farming and agricultural services\" },\r\n  { name: \"Dairy\", icon: Droplets, slug: \"dairy\", description: \"Dairy farms and products\" },\r\n  { name: \"Organic Products\", icon: Leaf, slug: \"organic\", description: \"Organic farming and products\" },\r\n  { name: \"Poultry\", icon: Leaf, slug: \"poultry\", description: \"Poultry farming and products\" },\r\n  { name: \"Fisheries\", icon: Droplets, slug: \"fisheries\", description: \"Fish farming and aquaculture\" },\r\n  { name: \"Nurseries\", icon: Leaf, slug: \"nurseries\", description: \"Plant nurseries and gardening supplies\" },\r\n  { name: \"Farm Equipment\", icon: Tractor, slug: \"farm-equipment\", description: \"Agricultural equipment and supplies\" },\r\n  { name: \"Seed Suppliers\", icon: Leaf, slug: \"seed-suppliers\", description: \"Seeds and agricultural inputs\" },\r\n  { name: \"Floriculture\", icon: Flower, slug: \"floriculture\", description: \"Flower growing and selling\" },\r\n\r\n  // Utilities & Services\r\n  { name: \"Utilities\", icon: Zap, slug: \"utilities\", description: \"Utility services\" },\r\n  { name: \"Cleaning Services\", icon: Sparkles, slug: \"cleaning\", description: \"Cleaning and maintenance services\" },\r\n  { name: \"Waste Management\", icon: Recycle, slug: \"waste-management\", description: \"Waste collection and recycling services\" },\r\n  { name: \"Courier & Logistics\", icon: Truck, slug: \"logistics\", description: \"Courier, delivery, and logistics services\" },\r\n  { name: \"Home Services\", icon: Home, slug: \"home-services\", description: \"Home repair and maintenance services\" },\r\n  { name: \"Pest Control\", icon: Bug, slug: \"pest-control\", description: \"Pest control and extermination services\" },\r\n  { name: \"Security Services\", icon: ShieldCheck, slug: \"security\", description: \"Security guards and services\" },\r\n  { name: \"Laundry Services\", icon: Sparkles, slug: \"laundry\", description: \"Laundry and dry cleaning services\" },\r\n  { name: \"Water Supply\", icon: Droplets, slug: \"water-supply\", description: \"Water delivery and supply services\" },\r\n  { name: \"Rental Services\", icon: Boxes, slug: \"rental\", description: \"Equipment and item rental services\" },\r\n\r\n  // Other Categories\r\n  { name: \"Religious Services\", icon: Landmark, slug: \"religious\", description: \"Religious institutions and services\" },\r\n  { name: \"NGOs & Charities\", icon: Handshake, slug: \"ngo\", description: \"Non-profit organizations and charities\" },\r\n  { name: \"Government Services\", icon: Building2, slug: \"government\", description: \"Government offices and services\" },\r\n  { name: \"Repair Services\", icon: Wrench, slug: \"repair\", description: \"General repair and maintenance services\" },\r\n  { name: \"Tailoring\", icon: Scissors, slug: \"tailoring\", description: \"Tailoring and alteration services\" },\r\n  { name: \"Printing & Copying\", icon: Printer, slug: \"printing-copying\", description: \"Printing, copying, and document services\" },\r\n  { name: \"Astrology\", icon: Sparkles, slug: \"astrology\", description: \"Astrology and spiritual services\" },\r\n  { name: \"Funeral Services\", icon: Landmark, slug: \"funeral\", description: \"Funeral homes and memorial services\" },\r\n  { name: \"Daycare\", icon: Baby, slug: \"daycare\", description: \"Childcare and daycare services\" },\r\n  { name: \"Pet Services\", icon: HeartPulse, slug: \"pet-services\", description: \"Pet grooming, boarding, and care\" },\r\n  { name: \"Other Services\", icon: Briefcase, slug: \"other\", description: \"Other business services not listed elsewhere\" },\r\n];\r\n\r\n/**\r\n * Get a subset of categories\r\n * @param count Number of categories to return\r\n * @returns Array of categories limited to the specified count\r\n */\r\nexport function getCategories(count?: number): BusinessCategory[] {\r\n  if (count && count > 0 && count < BUSINESS_CATEGORIES.length) {\r\n    return BUSINESS_CATEGORIES.slice(0, count);\r\n  }\r\n  return BUSINESS_CATEGORIES;\r\n}\r\n\r\n/**\r\n * Get popular categories\r\n * @param count Maximum number of popular categories to return\r\n * @returns Array of popular categories\r\n */\r\nexport function getPopularCategories(count?: number): BusinessCategory[] {\r\n  const popularCategories = BUSINESS_CATEGORIES.filter(category => category.isPopular);\r\n  if (count && count > 0 && count < popularCategories.length) {\r\n    return popularCategories.slice(0, count);\r\n  }\r\n  return popularCategories;\r\n}\r\n\r\n/**\r\n * Get a category by slug\r\n * @param slug The category slug to find\r\n * @returns The category object or undefined if not found\r\n */\r\nexport function getCategoryBySlug(slug: string): BusinessCategory | undefined {\r\n  return BUSINESS_CATEGORIES.find(\r\n    (category) => category.slug === slug\r\n  );\r\n}\r\n\r\n/**\r\n * Get a category by name\r\n * @param name The category name to find\r\n * @returns The category object or undefined if not found\r\n */\r\nexport function getCategoryByName(name: string): BusinessCategory | undefined {\r\n  return BUSINESS_CATEGORIES.find(\r\n    (category) => category.name.toLowerCase() === name.toLowerCase()\r\n  );\r\n}\r\n\r\n/**\r\n * Category groups with their respective categories\r\n * This is manually defined to match the comments in the BUSINESS_CATEGORIES array\r\n */\r\nexport const CATEGORY_GROUPS = [\r\n  {\r\n    name: \"Food & Dining\",\r\n    categories: BUSINESS_CATEGORIES.slice(0, 8)\r\n  },\r\n  {\r\n    name: \"Retail & Shopping\",\r\n    categories: BUSINESS_CATEGORIES.slice(8, 19)\r\n  },\r\n  {\r\n    name: \"Professional Services\",\r\n    categories: BUSINESS_CATEGORIES.slice(19, 28)\r\n  },\r\n  {\r\n    name: \"Healthcare\",\r\n    categories: BUSINESS_CATEGORIES.slice(28, 38)\r\n  },\r\n  {\r\n    name: \"Beauty & Wellness\",\r\n    categories: BUSINESS_CATEGORIES.slice(38, 46)\r\n  },\r\n  {\r\n    name: \"Education & Training\",\r\n    categories: BUSINESS_CATEGORIES.slice(46, 57)\r\n  },\r\n  {\r\n    name: \"Technology\",\r\n    categories: BUSINESS_CATEGORIES.slice(57, 66)\r\n  },\r\n  {\r\n    name: \"Automotive\",\r\n    categories: BUSINESS_CATEGORIES.slice(66, 75)\r\n  },\r\n  {\r\n    name: \"Real Estate & Construction\",\r\n    categories: BUSINESS_CATEGORIES.slice(75, 86)\r\n  },\r\n  {\r\n    name: \"Travel & Hospitality\",\r\n    categories: BUSINESS_CATEGORIES.slice(86, 95)\r\n  },\r\n  {\r\n    name: \"Entertainment & Events\",\r\n    categories: BUSINESS_CATEGORIES.slice(95, 104)\r\n  },\r\n  {\r\n    name: \"Freelancers & Creative Professionals\",\r\n    categories: BUSINESS_CATEGORIES.slice(104, 115)\r\n  },\r\n  {\r\n    name: \"Manufacturing & Industry\",\r\n    categories: BUSINESS_CATEGORIES.slice(115, 124)\r\n  },\r\n  {\r\n    name: \"Agriculture\",\r\n    categories: BUSINESS_CATEGORIES.slice(124, 133)\r\n  },\r\n  {\r\n    name: \"Utilities & Services\",\r\n    categories: BUSINESS_CATEGORIES.slice(133, 143)\r\n  },\r\n  {\r\n    name: \"Other Categories\",\r\n    categories: BUSINESS_CATEGORIES.slice(143)\r\n  }\r\n];\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAsFO,MAAM,sBAA0C;IACrD,gBAAgB;IAChB;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAe,aAAa;QAAyC,WAAW;IAAK;IAClI;QAAE,MAAM;QAAoB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAkB,aAAa;QAA8C,WAAW;IAAK;IAC7I;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAAyB;IACnG;QAAE,MAAM;QAAY,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAA+B;IAClG;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAA8C;IACnH;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAe,aAAa;IAAiC;IAC1G;QAAE,MAAM;QAAiB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAiB,aAAa;IAAgC;IAC7G;QAAE,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAmB,aAAa;IAAqC;IAEtH,oBAAoB;IACpB;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAU,aAAa;QAA4B,WAAW;IAAK;IAC/G;QAAE,MAAM;QAA0B,MAAM,sNAAA,CAAA,eAAY;QAAE,MAAM;QAAW,aAAa;QAAmC,WAAW;IAAK;IACvI;QAAE,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAW,aAAa;QAAkC,WAAW;IAAK;IAC3H;QAAE,MAAM;QAAe,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAe,aAAa;IAAgC;IAC3G;QAAE,MAAM;QAAc,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAc,aAAa;IAAmC;IACtG;QAAE,MAAM;QAAW,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAW,aAAa;IAA+B;IAC3F;QAAE,MAAM;QAAc,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAc,aAAa;IAA4B;IACnG;QAAE,MAAM;QAAY,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAY,aAAa;IAA2B;IAChG;QAAE,MAAM;QAAc,MAAM,oNAAA,CAAA,cAAW;QAAE,MAAM;QAAc,aAAa;IAA0B;IACpG;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAW,aAAa;IAAsC;IACtG;QAAE,MAAM;QAAgB,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAgB,aAAa;IAA0C;IAEvH,wBAAwB;IACxB;QAAE,MAAM;QAAkB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAS,aAAa;QAAiC,WAAW;IAAK;IACpH;QAAE,MAAM;QAAsB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;QAAmC,WAAW;IAAK;IACjI;QAAE,MAAM;QAAc,MAAM,kOAAA,CAAA,mBAAgB;QAAE,MAAM;QAAc,aAAa;IAA8B;IAC7G;QAAE,MAAM;QAAc,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAc,aAAa;IAAqC;IAC7G;QAAE,MAAM;QAAa,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAa,aAAa;IAAgC;IACnG;QAAE,MAAM;QAAe,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAe,aAAa;IAA2C;IACjH;QAAE,MAAM;QAAmB,MAAM,kOAAA,CAAA,mBAAgB;QAAE,MAAM;QAAmB,aAAa;IAAuC;IAChI;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAU,aAAa;IAA4C;IACjH;QAAE,MAAM;QAAwB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAmD;IAEnI,aAAa;IACb;QAAE,MAAM;QAAmB,MAAM,gNAAA,CAAA,cAAW;QAAE,MAAM;QAAW,aAAa;QAA+B,WAAW;IAAK;IAC3H;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAA8B;IAClG;QAAE,MAAM;QAAY,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAY,aAAa;IAAkC;IACjG;QAAE,MAAM;QAAiB,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAiB,aAAa;IAAwC;IACvH;QAAE,MAAM;QAAwB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAwB,aAAa;IAAmD;IAC1I;QAAE,MAAM;QAAsB,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAc,aAAa;IAAyC;IAC1H;QAAE,MAAM;QAAiB,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAiB,aAAa;IAA4C;IAC3H;QAAE,MAAM;QAAc,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAc,aAAa;IAAwC;IACjH;QAAE,MAAM;QAAc,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAc,aAAa;IAA0C;IACnH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAAmC;IAEzG,oBAAoB;IACpB;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;QAAkC,WAAW;IAAK;IACzH;QAAE,MAAM;QAAW,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;QAA4B,WAAW;IAAK;IAC7G;QAAE,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAQ,aAAa;IAAsC;IAC3G;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAgC;IACrG;QAAE,MAAM;QAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAAkC;IACvG;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAuC;IAClH;QAAE,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAgC;IACzG;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAoC;IAEzG,uBAAuB;IACvB;QAAE,MAAM;QAAW,MAAM,wNAAA,CAAA,gBAAa;QAAE,MAAM;QAAW,aAAa;QAAwC,WAAW;IAAK;IAC9H;QAAE,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAgC;IAC3G;QAAE,MAAM;QAAuB,MAAM,kNAAA,CAAA,eAAY;QAAE,MAAM;QAAc,aAAa;IAAgC;IACpH;QAAE,MAAM;QAAoB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAoB,aAAa;IAAgC;IACjH;QAAE,MAAM;QAAoB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAyC;IACpH;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAA6B;IACvG;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAA6B;IACvG;QAAE,MAAM;QAAe,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAe,aAAa;IAA0B;IAClG;QAAE,MAAM;QAAmB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAqB,aAAa;IAAiC;IAC/G;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAAiC;IACtG;QAAE,MAAM;QAAmB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAwC;IAEjH,aAAa;IACb;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;QAA2B,WAAW;IAAK;IAChH;QAAE,MAAM;QAAwB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAY,aAAa;IAAiC;IAC9G;QAAE,MAAM;QAAmB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAmB,aAAa;IAAsC;IACnH;QAAE,MAAM;QAAqB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAqB,aAAa;IAA0C;IAChI;QAAE,MAAM;QAAmB,MAAM,8MAAA,CAAA,aAAU;QAAE,MAAM;QAAmB,aAAa;IAAkC;IACrH;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAuC;IAC9G;QAAE,MAAM;QAAkB,MAAM,oNAAA,CAAA,cAAW;QAAE,MAAM;QAAkB,aAAa;IAAuC;IACzH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAkB,aAAa;IAAuC;IAClH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAkB,aAAa;IAA0C;IAErH,aAAa;IACb;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;QAAkC,WAAW;IAAK;IACzH;QAAE,MAAM;QAAmB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAmB,aAAa;IAA+B;IAC3G;QAAE,MAAM;QAAc,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAAmC;IACrG;QAAE,MAAM;QAAwB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAAkC;IAChH;QAAE,MAAM;QAAY,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAY,aAAa;IAAqC;IACnG;QAAE,MAAM;QAAc,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAA0B;IAC5F;QAAE,MAAM;QAAqB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAqB,aAAa;IAAwC;IACxH;QAAE,MAAM;QAAkB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAkB,aAAa;IAA+B;IAEzG,6BAA6B;IAC7B;QAAE,MAAM;QAAe,MAAM,mMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;QAAqC,WAAW;IAAK;IAC1H;QAAE,MAAM;QAAgB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAgB,aAAa;IAAwC;IACjH;QAAE,MAAM;QAAmB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAmB,aAAa;IAA0C;IAC1H;QAAE,MAAM;QAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAgB,aAAa;IAAmC;IAC9G;QAAE,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAuB,aAAa;IAA+B;IACxH;QAAE,MAAM;QAAsB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAsB,aAAa;IAAmC;IACvH;QAAE,MAAM;QAAqB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAY,aAAa;IAAmC;IAC7G;QAAE,MAAM;QAAuB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAAqC;IAChH;QAAE,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAY,aAAa;IAAwC;IACjH;QAAE,MAAM;QAAa,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAa,aAAa;IAAqC;IACxG;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAuC;IAE9G,uBAAuB;IACvB;QAAE,MAAM;QAAU,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAU,aAAa;QAA6B,WAAW;IAAK;IACvG;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAmB,aAAa;IAAqC;IACnH;QAAE,MAAM;QAAkB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAkB,aAAa;IAA0B;IACpG;QAAE,MAAM;QAAW,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAW,aAAa;IAAmC;IACnG;QAAE,MAAM;QAAa,MAAM,mMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAA6B;IAC9F;QAAE,MAAM;QAAe,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAe,aAAa;IAAiC;IACvG;QAAE,MAAM;QAAqB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAqB,aAAa;IAA+B;IACjH;QAAE,MAAM;QAAW,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAW,aAAa;IAAkC;IAC9F;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAA2C;IAErH,yBAAyB;IACzB;QAAE,MAAM;QAAiB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;QAAqC,WAAW;IAAK;IAC/H;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAoB,aAAa;IAAyC;IAC5H;QAAE,MAAM;QAAoB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAW,aAAa;IAAwC;IACnH;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAuC;IAC9G;QAAE,MAAM;QAAgB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAU,aAAa;IAA6B;IAC9F;QAAE,MAAM;QAAgB,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAA2C;IAChH;QAAE,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAgB,aAAa;IAAyB;IACjG;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAiB,aAAa;IAAgC;IAC5G;QAAE,MAAM;QAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAmB,aAAa;IAA4B;IAE7G,uCAAuC;IACvC;QAAE,MAAM;QAAsB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAa,aAAa;QAA6C,WAAW;IAAK;IAC5I;QAAE,MAAM;QAAkB,MAAM,4MAAA,CAAA,UAAO;QAAE,MAAM;QAAkB,aAAa;IAA0B;IACxG;QAAE,MAAM;QAAoB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAoB,aAAa;IAAwC;IACtH;QAAE,MAAM;QAAgB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAc,aAAa;IAA2B;IACjG;QAAE,MAAM;QAAuB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAqB,aAAa;IAA2B;IAC7G;QAAE,MAAM;QAAe,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAe,aAAa;IAAwC;IAC7G;QAAE,MAAM;QAAsB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAc,aAAa;IAAoC;IAC9G;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAAgC;IACvG;QAAE,MAAM;QAAU,MAAM,8MAAA,CAAA,WAAQ;QAAE,MAAM;QAAU,aAAa;IAA+B;IAC9F;QAAE,MAAM;QAAe,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAe,aAAa;IAAuC;IACjH;QAAE,MAAM;QAAe,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAe,aAAa;IAAmC;IAE5G,2BAA2B;IAC3B;QAAE,MAAM;QAAiB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAiB,aAAa;IAA2B;IACvG;QAAE,MAAM;QAAa,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAa,aAAa;IAAuC;IAC7G;QAAE,MAAM;QAAY,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAY,aAAa;IAAqC;IACrG;QAAE,MAAM;QAAY,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAY,aAAa;IAA8B;IAChG;QAAE,MAAM;QAAa,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAa,aAAa;IAAmC;IACrG;QAAE,MAAM;QAAe,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAe,aAAa;IAA8B;IACrG;QAAE,MAAM;QAAoB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAoB,aAAa;IAAqC;IACvH;QAAE,MAAM;QAAe,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAe,aAAa;IAA+B;IACrG;QAAE,MAAM;QAAoB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAAwC;IAEhH,cAAc;IACd;QAAE,MAAM;QAAe,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAe,aAAa;IAAoC;IAC5G;QAAE,MAAM;QAAS,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAS,aAAa;IAA2B;IACxF;QAAE,MAAM;QAAoB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAW,aAAa;IAA+B;IACrG;QAAE,MAAM;QAAW,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAW,aAAa;IAA+B;IAC5F;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAA+B;IACpG;QAAE,MAAM;QAAa,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAa,aAAa;IAAyC;IAC1G;QAAE,MAAM;QAAkB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAkB,aAAa;IAAsC;IACpH;QAAE,MAAM;QAAkB,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAkB,aAAa;IAAgC;IAC3G;QAAE,MAAM;QAAgB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAgB,aAAa;IAA6B;IAEtG,uBAAuB;IACvB;QAAE,MAAM;QAAa,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAa,aAAa;IAAmB;IACnF;QAAE,MAAM;QAAqB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAY,aAAa;IAAoC;IAChH;QAAE,MAAM;QAAoB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAoB,aAAa;IAA0C;IAC5H;QAAE,MAAM;QAAuB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAa,aAAa;IAA4C;IACxH;QAAE,MAAM;QAAiB,MAAM,mMAAA,CAAA,OAAI;QAAE,MAAM;QAAiB,aAAa;IAAuC;IAChH;QAAE,MAAM;QAAgB,MAAM,gMAAA,CAAA,MAAG;QAAE,MAAM;QAAgB,aAAa;IAA0C;IAChH;QAAE,MAAM;QAAqB,MAAM,oNAAA,CAAA,cAAW;QAAE,MAAM;QAAY,aAAa;IAA+B;IAC9G;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAoC;IAC9G;QAAE,MAAM;QAAgB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAgB,aAAa;IAAqC;IAChH;QAAE,MAAM;QAAmB,MAAM,oMAAA,CAAA,QAAK;QAAE,MAAM;QAAU,aAAa;IAAqC;IAE1G,mBAAmB;IACnB;QAAE,MAAM;QAAsB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAsC;IACpH;QAAE,MAAM;QAAoB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAO,aAAa;IAAyC;IAChH;QAAE,MAAM;QAAuB,MAAM,gNAAA,CAAA,YAAS;QAAE,MAAM;QAAc,aAAa;IAAkC;IACnH;QAAE,MAAM;QAAmB,MAAM,sMAAA,CAAA,SAAM;QAAE,MAAM;QAAU,aAAa;IAA0C;IAChH;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAoC;IACzG;QAAE,MAAM;QAAsB,MAAM,wMAAA,CAAA,UAAO;QAAE,MAAM;QAAoB,aAAa;IAA2C;IAC/H;QAAE,MAAM;QAAa,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAa,aAAa;IAAmC;IACxG;QAAE,MAAM;QAAoB,MAAM,0MAAA,CAAA,WAAQ;QAAE,MAAM;QAAW,aAAa;IAAsC;IAChH;QAAE,MAAM;QAAW,MAAM,kMAAA,CAAA,OAAI;QAAE,MAAM;QAAW,aAAa;IAAiC;IAC9F;QAAE,MAAM;QAAgB,MAAM,kNAAA,CAAA,aAAU;QAAE,MAAM;QAAgB,aAAa;IAAmC;IAChH;QAAE,MAAM;QAAkB,MAAM,4MAAA,CAAA,YAAS;QAAE,MAAM;QAAS,aAAa;IAA+C;CACvH;AAOM,SAAS,cAAc,KAAc;IAC1C,IAAI,SAAS,QAAQ,KAAK,QAAQ,oBAAoB,MAAM,EAAE;QAC5D,OAAO,oBAAoB,KAAK,CAAC,GAAG;IACtC;IACA,OAAO;AACT;AAOO,SAAS,qBAAqB,KAAc;IACjD,MAAM,oBAAoB,oBAAoB,MAAM,CAAC,CAAA,WAAY,SAAS,SAAS;IACnF,IAAI,SAAS,QAAQ,KAAK,QAAQ,kBAAkB,MAAM,EAAE;QAC1D,OAAO,kBAAkB,KAAK,CAAC,GAAG;IACpC;IACA,OAAO;AACT;AAOO,SAAS,kBAAkB,IAAY;IAC5C,OAAO,oBAAoB,IAAI,CAC7B,CAAC,WAAa,SAAS,IAAI,KAAK;AAEpC;AAOO,SAAS,kBAAkB,IAAY;IAC5C,OAAO,oBAAoB,IAAI,CAC7B,CAAC,WAAa,SAAS,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW;AAElE;AAMO,MAAM,kBAAkB;IAC7B;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,GAAG;IAC3C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,GAAG;IAC3C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,IAAI;IAC5C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC,KAAK;IAC7C;IACA;QACE,MAAM;QACN,YAAY,oBAAoB,KAAK,CAAC;IACxC;CACD", "debugId": null}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/CategoryFilter.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport { Check, ChevronsUpDown } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { getCategories } from \"@/lib/config/categories\";\r\n\r\ninterface CategoryFilterProps {\r\n  value?: string | null;\r\n  onValueChange: (_value: string | null) => void;\r\n  placeholder?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function CategoryFilter({\r\n  value,\r\n  onValueChange,\r\n  placeholder = \"Select category...\",\r\n  className,\r\n}: CategoryFilterProps) {\r\n  const [open, setOpen] = useState(false);\r\n\r\n  // Get all categories from the shared config\r\n  const categories = getCategories();\r\n\r\n  // Find the selected category\r\n  const selectedCategory = categories.find(\r\n    (category) => category.name === value\r\n  );\r\n\r\n  const handleSelect = (categoryName: string) => {\r\n    if (categoryName === value) {\r\n      onValueChange(null); // Deselect if already selected\r\n    } else {\r\n      onValueChange(categoryName);\r\n    }\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className={cn(\r\n            \"w-full md:w-[200px] h-12 min-h-[48px] justify-between bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 text-base font-normal\",\r\n            !value && \"text-neutral-500 dark:text-neutral-400\",\r\n            className\r\n          )}\r\n        >\r\n          <div className=\"flex items-center gap-2 flex-1 min-w-0\">\r\n            {selectedCategory ? (\r\n              <>\r\n                <selectedCategory.icon className=\"h-4 w-4 text-[var(--brand-gold)] flex-shrink-0\" />\r\n                <span className=\"truncate\">{selectedCategory.name}</span>\r\n              </>\r\n            ) : (\r\n              <span className=\"truncate\">{placeholder}</span>\r\n            )}\r\n          </div>\r\n          <div className=\"flex items-center gap-1 flex-shrink-0\">\r\n            <ChevronsUpDown className=\"h-4 w-4 text-neutral-400\" />\r\n          </div>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[300px] p-0\" align=\"start\">\r\n        <Command>\r\n          <CommandInput placeholder=\"Search categories...\" />\r\n          <CommandList>\r\n            <CommandEmpty>No category found.</CommandEmpty>\r\n            <CommandGroup>\r\n              {categories.map((category) => (\r\n                <CommandItem\r\n                  key={category.name}\r\n                  value={category.name}\r\n                  onSelect={() => handleSelect(category.name)}\r\n                  className=\"flex items-center gap-2 cursor-pointer\"\r\n                >\r\n                  <category.icon className=\"h-4 w-4 text-[var(--brand-gold)]\" />\r\n                  <span className=\"flex-1\">{category.name}</span>\r\n                  <Check\r\n                    className={cn(\r\n                      \"h-4 w-4\",\r\n                      value === category.name ? \"opacity-100\" : \"opacity-0\"\r\n                    )}\r\n                  />\r\n                </CommandItem>\r\n              ))}\r\n            </CommandGroup>\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AAQA;AAKA;AAnBA;;;;;;;;;AA4Be,SAAS,eAAe,EACrC,KAAK,EACL,aAAa,EACb,cAAc,oBAAoB,EAClC,SAAS,EACW;IACpB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,4CAA4C;IAC5C,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD;IAE/B,6BAA6B;IAC7B,MAAM,mBAAmB,WAAW,IAAI,CACtC,CAAC,WAAa,SAAS,IAAI,KAAK;IAGlC,MAAM,eAAe,CAAC;QACpB,IAAI,iBAAiB,OAAO;YAC1B,cAAc,OAAO,+BAA+B;QACtD,OAAO;YACL,cAAc;QAChB;QACA,QAAQ;IACV;IAEA,qBACE,8OAAC,4HAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8KACA,CAAC,SAAS,0CACV;;sCAGF,8OAAC;4BAAI,WAAU;sCACZ,iCACC;;kDACE,8OAAC,iBAAiB,IAAI;wCAAC,WAAU;;;;;;kDACjC,8OAAC;wCAAK,WAAU;kDAAY,iBAAiB,IAAI;;;;;;;6DAGnD,8OAAC;gCAAK,WAAU;0CAAY;;;;;;;;;;;sCAGhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAIhC,8OAAC,4HAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAgB,OAAM;0BAC9C,cAAA,8OAAC,4HAAA,CAAA,UAAO;;sCACN,8OAAC,4HAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,8OAAC,4HAAA,CAAA,cAAW;;8CACV,8OAAC,4HAAA,CAAA,eAAY;8CAAC;;;;;;8CACd,8OAAC,4HAAA,CAAA,eAAY;8CACV,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,4HAAA,CAAA,cAAW;4CAEV,OAAO,SAAS,IAAI;4CACpB,UAAU,IAAM,aAAa,SAAS,IAAI;4CAC1C,WAAU;;8DAEV,8OAAC,SAAS,IAAI;oDAAC,WAAU;;;;;;8DACzB,8OAAC;oDAAK,WAAU;8DAAU,SAAS,IAAI;;;;;;8DACvC,8OAAC,oMAAA,CAAA,QAAK;oDACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,WACA,UAAU,SAAS,IAAI,GAAG,gBAAgB;;;;;;;2CAVzC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBpC", "debugId": null}}, {"offset": {"line": 3280, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ImprovedSearchSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useCallback } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { useDiscoverContext } from \"../context/DiscoverContext\";\r\nimport { useForm, Controller } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport {\r\n  combinedSearchSchema,\r\n  CombinedSearchFormData,\r\n} from \"@/lib/schemas/locationSchemas\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { MapPin, Loader2, Building2, Search, X } from \"lucide-react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Command,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\";\r\nimport { getPincodeDetails } from \"@/lib/actions/location\";\r\nimport {\r\n  getCitySuggestionsClient,\r\n  getPincodeDetailsClient,\r\n} from \"@/lib/client/locationUtils\";\r\nimport CitySearchSkeleton from \"./CitySearchSkeleton\";\r\nimport CategoryFilter from \"./CategoryFilter\";\r\n\r\ninterface ImprovedSearchSectionProps {\r\n  initialValues: {\r\n    pincode?: string | null;\r\n    city?: string | null;\r\n    locality?: string | null;\r\n  };\r\n}\r\n\r\nexport default function ImprovedSearchSection({\r\n  initialValues,\r\n}: ImprovedSearchSectionProps) {\r\n  const { performSearch, isSearching, selectedCategory, handleCategoryChange } =\r\n    useDiscoverContext();\r\n  const [isPincodeLoading, setIsPincodeLoading] = useState(false);\r\n  const [isLoadingCities, setIsLoadingCities] = useState(false);\r\n  const [searchType, setSearchType] = useState<\"pincode\" | \"city\">(\r\n    initialValues.city ? \"city\" : \"pincode\"\r\n  );\r\n  const [localities, setLocalities] = useState<string[]>([]);\r\n  const [citySuggestions, setCitySuggestions] = useState<string[]>([]);\r\n  const [showCitySuggestions, setShowCitySuggestions] = useState(false);\r\n\r\n  // Form for combined search - initialize with only the relevant fields based on search type\r\n  const form = useForm<CombinedSearchFormData>({\r\n    resolver: zodResolver(combinedSearchSchema),\r\n    defaultValues: {\r\n      businessName: \"\", // Keep this for schema compatibility\r\n      pincode: searchType === \"pincode\" ? initialValues.pincode || \"\" : \"\",\r\n      city: searchType === \"city\" ? initialValues.city || \"\" : \"\",\r\n      locality:\r\n        searchType === \"pincode\" ? initialValues.locality || \"_any\" : \"_any\",\r\n    },\r\n    mode: \"onChange\",\r\n  });\r\n\r\n  // Handle form submission\r\n  const onSubmit = (data: CombinedSearchFormData) => {\r\n    console.log(\"Form submitted with data:\", data, \"Search type:\", searchType);\r\n\r\n    // Set loading state to show skeleton UI\r\n    setIsLoadingCities(false); // Hide city suggestions skeleton\r\n    setShowCitySuggestions(false); // Hide city suggestions\r\n\r\n    // We don't need to set isSearching here as it's handled by the performSearch function\r\n    // in the context, which will set isSearching to true\r\n\r\n    // Create a new data object with only the relevant fields based on search type\r\n    const searchData: CombinedSearchFormData = {\r\n      businessName: data.businessName,\r\n      category: selectedCategory, // Preserve the currently selected category\r\n    };\r\n\r\n    if (searchType === \"pincode\") {\r\n      // For pincode search, include pincode and locality\r\n      searchData.pincode = data.pincode;\r\n      searchData.locality = data.locality;\r\n      searchData.city = null; // Set city to null instead of empty string\r\n      console.log(\"Searching by pincode:\", searchData.pincode);\r\n    } else {\r\n      // For city search, include only city\r\n      searchData.city = data.city;\r\n      searchData.pincode = null; // Set pincode to null instead of empty string\r\n      searchData.locality = null; // Set locality to null instead of empty string\r\n      console.log(\"Searching by city:\", searchData.city);\r\n    }\r\n\r\n    console.log(\"Final search data:\", searchData);\r\n\r\n    // Add a small delay to ensure the form submission is processed\r\n    setTimeout(() => {\r\n      performSearch(searchData);\r\n    }, 100);\r\n  };\r\n\r\n  // Toggle search type between pincode and city\r\n  const toggleSearchType = () => {\r\n    // First determine the new search type\r\n    const newSearchType = searchType === \"pincode\" ? \"city\" : \"pincode\";\r\n    setSearchType(newSearchType);\r\n\r\n    // Reset form with only the relevant fields for the new search type\r\n    form.reset({\r\n      businessName: \"\",\r\n      // If switching to pincode, keep pincode value, otherwise clear it\r\n      pincode: newSearchType === \"pincode\" ? form.getValues(\"pincode\") : \"\",\r\n      // If switching to city, keep city value, otherwise clear it\r\n      city: newSearchType === \"city\" ? form.getValues(\"city\") : \"\",\r\n      // Only set locality for pincode search\r\n      locality: newSearchType === \"pincode\" ? \"_any\" : \"\",\r\n    });\r\n\r\n    // Clear UI state\r\n    setLocalities([]);\r\n    setCitySuggestions([]);\r\n    setShowCitySuggestions(false);\r\n\r\n    console.log(\r\n      \"Toggled search type to:\",\r\n      newSearchType,\r\n      \"Form values:\",\r\n      form.getValues()\r\n    );\r\n  };\r\n\r\n  // Function to fetch localities for a pincode - wrapped in useCallback to prevent unnecessary re-renders\r\n  const fetchLocalitiesForPincode = useCallback(\r\n    async (pincode: string) => {\r\n      if (!pincode || pincode.length !== 6) return;\r\n\r\n      console.log(\"Fetching localities for pincode:\", pincode);\r\n      setIsPincodeLoading(true);\r\n\r\n      try {\r\n        // Try client-side function first for better performance\r\n        const result = await getPincodeDetailsClient(pincode);\r\n        if (result.localities) {\r\n          // Extract unique localities\r\n          const uniqueLocalities = result.localities\r\n            .map((locality) => locality.replace(\" B.O\", \"\").trim())\r\n            .filter(Boolean);\r\n\r\n          console.log(\"Found localities:\", uniqueLocalities);\r\n          setLocalities(uniqueLocalities);\r\n\r\n          // If there's only one locality, auto-select it\r\n          if (uniqueLocalities.length === 1) {\r\n            form.setValue(\"locality\", uniqueLocalities[0]);\r\n          } else if (\r\n            initialValues.locality &&\r\n            uniqueLocalities.includes(initialValues.locality)\r\n          ) {\r\n            // If we have an initial locality value and it's in the list, select it\r\n            form.setValue(\"locality\", initialValues.locality);\r\n          }\r\n        } else {\r\n          // Fall back to server action if client-side fails\r\n          const serverResult = await getPincodeDetails(pincode);\r\n          if (serverResult.data?.localities) {\r\n            // Extract unique localities\r\n            const uniqueLocalities = serverResult.data.localities\r\n              .map((locality) => locality.replace(\" B.O\", \"\").trim())\r\n              .filter(Boolean);\r\n\r\n            console.log(\"Found localities (server):\", uniqueLocalities);\r\n            setLocalities(uniqueLocalities);\r\n\r\n            // If there's only one locality, auto-select it\r\n            if (uniqueLocalities.length === 1) {\r\n              form.setValue(\"locality\", uniqueLocalities[0]);\r\n            } else if (\r\n              initialValues.locality &&\r\n              uniqueLocalities.includes(initialValues.locality)\r\n            ) {\r\n              // If we have an initial locality value and it's in the list, select it\r\n              form.setValue(\"locality\", initialValues.locality);\r\n            }\r\n          } else {\r\n            setLocalities([]);\r\n            form.setValue(\"locality\", \"_any\");\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching localities:\", error);\r\n        setLocalities([]);\r\n      } finally {\r\n        setIsPincodeLoading(false);\r\n      }\r\n    },\r\n    [form, initialValues.locality, setIsPincodeLoading, setLocalities]\r\n  );\r\n\r\n  // Watch for pincode changes\r\n  const pincodeValue = form.watch(\"pincode\");\r\n\r\n  // Fetch localities when pincode changes\r\n  useEffect(() => {\r\n    if (pincodeValue && pincodeValue.length === 6 && searchType === \"pincode\") {\r\n      fetchLocalitiesForPincode(pincodeValue);\r\n    } else {\r\n      setLocalities([]);\r\n    }\r\n  }, [searchType, pincodeValue, fetchLocalitiesForPincode]);\r\n\r\n  // Fetch localities when component mounts with a pincode parameter\r\n  useEffect(() => {\r\n    // Only run on initial mount\r\n    if (\r\n      initialValues.pincode &&\r\n      initialValues.pincode.length === 6 &&\r\n      searchType === \"pincode\"\r\n    ) {\r\n      console.log(\"Initial load with pincode:\", initialValues.pincode);\r\n      fetchLocalitiesForPincode(initialValues.pincode);\r\n    }\r\n  }, [initialValues.pincode, searchType, fetchLocalitiesForPincode]);\r\n\r\n  // Watch for city input changes\r\n  const cityValue = form.watch(\"city\");\r\n\r\n  // Fetch city suggestions when city input changes\r\n  useEffect(() => {\r\n    console.log(\"City input changed:\", cityValue, \"Search type:\", searchType);\r\n\r\n    if (cityValue && cityValue.length >= 2 && searchType === \"city\") {\r\n      // Set loading state immediately to show skeleton\r\n      setIsLoadingCities(true);\r\n      console.log(\"Loading cities...\");\r\n\r\n      const fetchCitySuggestions = async () => {\r\n        try {\r\n          // Use client-side function for better performance - direct Supabase query\r\n          console.log(\"Fetching city suggestions for:\", cityValue);\r\n          const result = await getCitySuggestionsClient(cityValue);\r\n          console.log(\"City suggestions result:\", result);\r\n\r\n          if (result.cities) {\r\n            console.log(\"Setting city suggestions:\", result.cities);\r\n            setCitySuggestions(result.cities);\r\n            setShowCitySuggestions(true);\r\n          } else {\r\n            console.log(\"No city suggestions found\");\r\n            setCitySuggestions([]);\r\n            setShowCitySuggestions(false);\r\n          }\r\n        } catch (error) {\r\n          console.error(\"Error fetching city suggestions:\", error);\r\n          setCitySuggestions([]);\r\n          setShowCitySuggestions(false);\r\n        } finally {\r\n          setIsLoadingCities(false);\r\n        }\r\n      };\r\n\r\n      // Add a small delay to prevent too many requests while typing\r\n      const timer = setTimeout(() => {\r\n        fetchCitySuggestions();\r\n      }, 300);\r\n\r\n      return () => clearTimeout(timer);\r\n    } else {\r\n      setCitySuggestions([]);\r\n      setShowCitySuggestions(false);\r\n      setIsLoadingCities(false);\r\n    }\r\n  }, [searchType, cityValue]);\r\n\r\n  // Handle city suggestion selection\r\n  const handleCitySelect = (city: string) => {\r\n    form.setValue(\"city\", city);\r\n    setShowCitySuggestions(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full py-6 mt-20\">\r\n      <div className=\"container mx-auto px-4\">\r\n        {/* Heading */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.1 }}\r\n          className=\"text-center mb-6\"\r\n        >\r\n          <h1 className=\"text-2xl md:text-3xl font-bold text-neutral-800 dark:text-neutral-100 mb-2\">\r\n            Discover Businesses and Products/Services Across India\r\n          </h1>\r\n          <p className=\"text-sm md:text-base text-neutral-600 dark:text-neutral-400\">\r\n            Search through our extensive database of local businesses and\r\n            products/services\r\n          </p>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5, delay: 0.2 }}\r\n          className=\"max-w-5xl mx-auto\"\r\n        >\r\n          <form\r\n            onSubmit={(e) => {\r\n              e.preventDefault();\r\n              console.log(\"Form submitted directly\");\r\n\r\n              // Get form data\r\n              const formData = form.getValues();\r\n\r\n              // Create a clean search data object based on search type\r\n              const searchData: CombinedSearchFormData = {\r\n                businessName: formData.businessName,\r\n                category: selectedCategory,\r\n              };\r\n\r\n              if (searchType === \"pincode\") {\r\n                searchData.pincode = formData.pincode;\r\n                searchData.locality = formData.locality;\r\n                searchData.city = null; // Set city to null for pincode search\r\n              } else {\r\n                searchData.city = formData.city;\r\n                searchData.pincode = null; // Set pincode to null for city search\r\n                searchData.locality = null; // Set locality to null for city search\r\n              }\r\n\r\n              onSubmit(searchData);\r\n            }}\r\n          >\r\n            <div className=\"flex flex-col md:flex-row gap-3 items-center w-full\">\r\n              {/* Search type selector */}\r\n              <div className=\"w-full md:w-auto\">\r\n                <Select\r\n                  value={searchType}\r\n                  onValueChange={(value) => {\r\n                    setSearchType(value as \"pincode\" | \"city\");\r\n                    toggleSearchType();\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"w-full md:w-[140px] h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal\">\r\n                    <SelectValue\r\n                      placeholder=\"Search by\"\r\n                      className=\"text-base leading-normal\"\r\n                    />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    <SelectItem value=\"pincode\">\r\n                      <div className=\"flex items-center\">\r\n                        <MapPin className=\"mr-2 h-4 w-4 text-[var(--brand-gold)]\" />\r\n                        <span>Pincode</span>\r\n                      </div>\r\n                    </SelectItem>\r\n                    <SelectItem value=\"city\">\r\n                      <div className=\"flex items-center\">\r\n                        <Building2 className=\"mr-2 h-4 w-4 text-[var(--brand-gold)]\" />\r\n                        <span>City</span>\r\n                      </div>\r\n                    </SelectItem>\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              {/* Category Filter */}\r\n              <div className=\"w-full md:w-auto flex items-center gap-2\">\r\n                <CategoryFilter\r\n                  value={selectedCategory}\r\n                  onValueChange={handleCategoryChange}\r\n                  placeholder=\"All categories\"\r\n                />\r\n                {selectedCategory && (\r\n                  <Button\r\n                    type=\"button\"\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    onClick={() => handleCategoryChange(null)}\r\n                    className=\"h-12 px-3 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200\"\r\n                    title=\"Clear category filter\"\r\n                  >\r\n                    <X className=\"h-4 w-4\" />\r\n                  </Button>\r\n                )}\r\n              </div>\r\n\r\n              {/* Dynamic search inputs based on search type */}\r\n              <div className=\"flex-1 w-full flex flex-col md:flex-row gap-3 items-center\">\r\n                {searchType === \"pincode\" ? (\r\n                  <>\r\n                    {/* Pincode input */}\r\n                    <div className=\"relative w-full flex-1\">\r\n                      <Controller\r\n                        name=\"pincode\"\r\n                        control={form.control}\r\n                        render={({ field }) => (\r\n                          <div className=\"relative\">\r\n                            <MapPin className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400\" />\r\n                            <Input\r\n                              {...field}\r\n                              placeholder=\"Enter 6-digit pincode\"\r\n                              className=\"pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base\"\r\n                              maxLength={6}\r\n                              value={field.value || \"\"}\r\n                              type=\"tel\"\r\n                              inputMode=\"numeric\"\r\n                              onKeyDown={(e) => {\r\n                                // Allow only numbers, backspace, delete, tab, arrow keys, and enter\r\n                                if (\r\n                                  !/^\\d$/.test(e.key) && // Allow digits\r\n                                  e.key !== \"Backspace\" &&\r\n                                  e.key !== \"Delete\" &&\r\n                                  e.key !== \"Tab\" &&\r\n                                  e.key !== \"Enter\" &&\r\n                                  !e.key.includes(\"Arrow\")\r\n                                ) {\r\n                                  e.preventDefault();\r\n                                }\r\n                              }}\r\n                            />\r\n                            {isPincodeLoading && (\r\n                              <Loader2 className=\"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400\" />\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                      />\r\n                    </div>\r\n\r\n                    {/* Locality select */}\r\n                    <div className=\"w-full md:w-[200px]\">\r\n                      <Controller\r\n                        name=\"locality\"\r\n                        control={form.control}\r\n                        render={({ field }) => (\r\n                          <Select\r\n                            value={field.value || \"_any\"}\r\n                            onValueChange={field.onChange}\r\n                            disabled={\r\n                              localities.length === 0 && !isPincodeLoading\r\n                            }\r\n                          >\r\n                            <SelectTrigger className=\"w-full h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md px-4 flex items-center text-base leading-normal\">\r\n                              <SelectValue\r\n                                placeholder=\"Select locality\"\r\n                                className=\"text-base leading-normal\"\r\n                              />\r\n                            </SelectTrigger>\r\n                            <SelectContent>\r\n                              <SelectItem value=\"_any\">Any Locality</SelectItem>\r\n                              {localities.map((locality) => (\r\n                                <SelectItem key={locality} value={locality}>\r\n                                  {locality}\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                        )}\r\n                      />\r\n                    </div>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    {/* City input with suggestions */}\r\n                    <div className=\"relative w-full flex-1\">\r\n                      <Controller\r\n                        name=\"city\"\r\n                        control={form.control}\r\n                        render={({ field }) => (\r\n                          <div className=\"relative\">\r\n                            <Building2 className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-neutral-400\" />\r\n                            <Input\r\n                              {...field}\r\n                              placeholder=\"Enter city name\"\r\n                              className=\"pl-12 h-12 min-h-[48px] bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 rounded-md text-base\"\r\n                              autoComplete=\"off\"\r\n                              value={field.value || \"\"}\r\n                              onChange={(e) => {\r\n                                field.onChange(e);\r\n                                console.log(\r\n                                  \"City input changed directly:\",\r\n                                  e.target.value\r\n                                );\r\n                              }}\r\n                            />\r\n                            {isLoadingCities && (\r\n                              <Loader2 className=\"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-neutral-400\" />\r\n                            )}\r\n                          </div>\r\n                        )}\r\n                      />\r\n\r\n                      {/* City suggestions */}\r\n                      <AnimatePresence>\r\n                        {isLoadingCities && form.watch(\"city\") ? (\r\n                          <CitySearchSkeleton />\r\n                        ) : (\r\n                          showCitySuggestions &&\r\n                          citySuggestions.length > 0 && (\r\n                            <motion.div\r\n                              initial={{ opacity: 0, y: -10 }}\r\n                              animate={{ opacity: 1, y: 0 }}\r\n                              exit={{ opacity: 0, y: -10 }}\r\n                              className=\"absolute z-10 mt-1 w-full bg-white dark:bg-neutral-900 rounded-md border border-neutral-200 dark:border-neutral-800 shadow-lg\"\r\n                            >\r\n                              <Command>\r\n                                <CommandList>\r\n                                  <CommandGroup>\r\n                                    {citySuggestions.map((city) => (\r\n                                      <CommandItem\r\n                                        key={city}\r\n                                        onSelect={() => handleCitySelect(city)}\r\n                                        className=\"cursor-pointer\"\r\n                                      >\r\n                                        <Building2 className=\"mr-2 h-4 w-4 text-neutral-400\" />\r\n                                        <span>{city}</span>\r\n                                      </CommandItem>\r\n                                    ))}\r\n                                  </CommandGroup>\r\n                                </CommandList>\r\n                              </Command>\r\n                            </motion.div>\r\n                          )\r\n                        )}\r\n                      </AnimatePresence>\r\n                    </div>\r\n                  </>\r\n                )}\r\n\r\n                {/* Search button */}\r\n                <Button\r\n                  type=\"submit\"\r\n                  disabled={\r\n                    isSearching ||\r\n                    (searchType === \"pincode\"\r\n                      ? !form.getValues(\"pincode\")\r\n                      : !form.getValues(\"city\"))\r\n                  }\r\n                  className=\"h-12 min-h-[48px] bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-[var(--brand-gold-foreground)] w-full md:w-auto px-6 border border-[var(--brand-gold)] rounded-md flex items-center justify-center font-medium text-base\"\r\n                  onClick={() => {\r\n                    console.log(\r\n                      \"Search button clicked, isSearching:\",\r\n                      isSearching\r\n                    );\r\n                    // This is just for debugging, the actual submission is handled by the form\r\n                  }}\r\n                >\r\n                  {isSearching ? (\r\n                    <motion.div\r\n                      animate={{ rotate: 360 }}\r\n                      transition={{\r\n                        duration: 1,\r\n                        repeat: Infinity,\r\n                        ease: \"linear\",\r\n                      }}\r\n                      className=\"mr-2\"\r\n                    >\r\n                      <Loader2 className=\"h-4 w-4\" />\r\n                    </motion.div>\r\n                  ) : (\r\n                    <Search className=\"h-4 w-4 mr-2\" />\r\n                  )}\r\n                  Search\r\n                </Button>\r\n              </div>\r\n            </div>\r\n          </form>\r\n        </motion.div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAOA;AAMA;AACA;AAIA;AACA;AAjCA;;;;;;;;;;;;;;;;;AA2Ce,SAAS,sBAAsB,EAC5C,aAAa,EACc;IAC3B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,oBAAoB,EAAE,GAC1E,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzC,cAAc,IAAI,GAAG,SAAS;IAEhC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,2FAA2F;IAC3F,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAA0B;QAC3C,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,iIAAA,CAAA,uBAAoB;QAC1C,eAAe;YACb,cAAc;YACd,SAAS,eAAe,YAAY,cAAc,OAAO,IAAI,KAAK;YAClE,MAAM,eAAe,SAAS,cAAc,IAAI,IAAI,KAAK;YACzD,UACE,eAAe,YAAY,cAAc,QAAQ,IAAI,SAAS;QAClE;QACA,MAAM;IACR;IAEA,yBAAyB;IACzB,MAAM,WAAW,CAAC;QAChB,QAAQ,GAAG,CAAC,6BAA6B,MAAM,gBAAgB;QAE/D,wCAAwC;QACxC,mBAAmB,QAAQ,iCAAiC;QAC5D,uBAAuB,QAAQ,wBAAwB;QAEvD,sFAAsF;QACtF,qDAAqD;QAErD,8EAA8E;QAC9E,MAAM,aAAqC;YACzC,cAAc,KAAK,YAAY;YAC/B,UAAU;QACZ;QAEA,IAAI,eAAe,WAAW;YAC5B,mDAAmD;YACnD,WAAW,OAAO,GAAG,KAAK,OAAO;YACjC,WAAW,QAAQ,GAAG,KAAK,QAAQ;YACnC,WAAW,IAAI,GAAG,MAAM,2CAA2C;YACnE,QAAQ,GAAG,CAAC,yBAAyB,WAAW,OAAO;QACzD,OAAO;YACL,qCAAqC;YACrC,WAAW,IAAI,GAAG,KAAK,IAAI;YAC3B,WAAW,OAAO,GAAG,MAAM,8CAA8C;YACzE,WAAW,QAAQ,GAAG,MAAM,+CAA+C;YAC3E,QAAQ,GAAG,CAAC,sBAAsB,WAAW,IAAI;QACnD;QAEA,QAAQ,GAAG,CAAC,sBAAsB;QAElC,+DAA+D;QAC/D,WAAW;YACT,cAAc;QAChB,GAAG;IACL;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB;QACvB,sCAAsC;QACtC,MAAM,gBAAgB,eAAe,YAAY,SAAS;QAC1D,cAAc;QAEd,mEAAmE;QACnE,KAAK,KAAK,CAAC;YACT,cAAc;YACd,kEAAkE;YAClE,SAAS,kBAAkB,YAAY,KAAK,SAAS,CAAC,aAAa;YACnE,4DAA4D;YAC5D,MAAM,kBAAkB,SAAS,KAAK,SAAS,CAAC,UAAU;YAC1D,uCAAuC;YACvC,UAAU,kBAAkB,YAAY,SAAS;QACnD;QAEA,iBAAiB;QACjB,cAAc,EAAE;QAChB,mBAAmB,EAAE;QACrB,uBAAuB;QAEvB,QAAQ,GAAG,CACT,2BACA,eACA,gBACA,KAAK,SAAS;IAElB;IAEA,wGAAwG;IACxG,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC1C,OAAO;QACL,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QAEtC,QAAQ,GAAG,CAAC,oCAAoC;QAChD,oBAAoB;QAEpB,IAAI;YACF,wDAAwD;YACxD,MAAM,SAAS,MAAM,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE;YAC7C,IAAI,OAAO,UAAU,EAAE;gBACrB,4BAA4B;gBAC5B,MAAM,mBAAmB,OAAO,UAAU,CACvC,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO,CAAC,QAAQ,IAAI,IAAI,IACnD,MAAM,CAAC;gBAEV,QAAQ,GAAG,CAAC,qBAAqB;gBACjC,cAAc;gBAEd,+CAA+C;gBAC/C,IAAI,iBAAiB,MAAM,KAAK,GAAG;oBACjC,KAAK,QAAQ,CAAC,YAAY,gBAAgB,CAAC,EAAE;gBAC/C,OAAO,IACL,cAAc,QAAQ,IACtB,iBAAiB,QAAQ,CAAC,cAAc,QAAQ,GAChD;oBACA,uEAAuE;oBACvE,KAAK,QAAQ,CAAC,YAAY,cAAc,QAAQ;gBAClD;YACF,OAAO;gBACL,kDAAkD;gBAClD,MAAM,eAAe,MAAM,CAAA,GAAA,sJAAA,CAAA,oBAAiB,AAAD,EAAE;gBAC7C,IAAI,aAAa,IAAI,EAAE,YAAY;oBACjC,4BAA4B;oBAC5B,MAAM,mBAAmB,aAAa,IAAI,CAAC,UAAU,CAClD,GAAG,CAAC,CAAC,WAAa,SAAS,OAAO,CAAC,QAAQ,IAAI,IAAI,IACnD,MAAM,CAAC;oBAEV,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,cAAc;oBAEd,+CAA+C;oBAC/C,IAAI,iBAAiB,MAAM,KAAK,GAAG;wBACjC,KAAK,QAAQ,CAAC,YAAY,gBAAgB,CAAC,EAAE;oBAC/C,OAAO,IACL,cAAc,QAAQ,IACtB,iBAAiB,QAAQ,CAAC,cAAc,QAAQ,GAChD;wBACA,uEAAuE;wBACvE,KAAK,QAAQ,CAAC,YAAY,cAAc,QAAQ;oBAClD;gBACF,OAAO;oBACL,cAAc,EAAE;oBAChB,KAAK,QAAQ,CAAC,YAAY;gBAC5B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,cAAc,EAAE;QAClB,SAAU;YACR,oBAAoB;QACtB;IACF,GACA;QAAC;QAAM,cAAc,QAAQ;QAAE;QAAqB;KAAc;IAGpE,4BAA4B;IAC5B,MAAM,eAAe,KAAK,KAAK,CAAC;IAEhC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,aAAa,MAAM,KAAK,KAAK,eAAe,WAAW;YACzE,0BAA0B;QAC5B,OAAO;YACL,cAAc,EAAE;QAClB;IACF,GAAG;QAAC;QAAY;QAAc;KAA0B;IAExD,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4BAA4B;QAC5B,IACE,cAAc,OAAO,IACrB,cAAc,OAAO,CAAC,MAAM,KAAK,KACjC,eAAe,WACf;YACA,QAAQ,GAAG,CAAC,8BAA8B,cAAc,OAAO;YAC/D,0BAA0B,cAAc,OAAO;QACjD;IACF,GAAG;QAAC,cAAc,OAAO;QAAE;QAAY;KAA0B;IAEjE,+BAA+B;IAC/B,MAAM,YAAY,KAAK,KAAK,CAAC;IAE7B,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,uBAAuB,WAAW,gBAAgB;QAE9D,IAAI,aAAa,UAAU,MAAM,IAAI,KAAK,eAAe,QAAQ;YAC/D,iDAAiD;YACjD,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YAEZ,MAAM,uBAAuB;gBAC3B,IAAI;oBACF,0EAA0E;oBAC1E,QAAQ,GAAG,CAAC,kCAAkC;oBAC9C,MAAM,SAAS,MAAM,CAAA,GAAA,8HAAA,CAAA,2BAAwB,AAAD,EAAE;oBAC9C,QAAQ,GAAG,CAAC,4BAA4B;oBAExC,IAAI,OAAO,MAAM,EAAE;wBACjB,QAAQ,GAAG,CAAC,6BAA6B,OAAO,MAAM;wBACtD,mBAAmB,OAAO,MAAM;wBAChC,uBAAuB;oBACzB,OAAO;wBACL,QAAQ,GAAG,CAAC;wBACZ,mBAAmB,EAAE;wBACrB,uBAAuB;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,oCAAoC;oBAClD,mBAAmB,EAAE;oBACrB,uBAAuB;gBACzB,SAAU;oBACR,mBAAmB;gBACrB;YACF;YAEA,8DAA8D;YAC9D,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO;YACL,mBAAmB,EAAE;YACrB,uBAAuB;YACvB,mBAAmB;QACrB;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,mCAAmC;IACnC,MAAM,mBAAmB,CAAC;QACxB,KAAK,QAAQ,CAAC,QAAQ;QACtB,uBAAuB;IACzB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA6E;;;;;;sCAG3F,8OAAC;4BAAE,WAAU;sCAA8D;;;;;;;;;;;;8BAM7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBACC,UAAU,CAAC;4BACT,EAAE,cAAc;4BAChB,QAAQ,GAAG,CAAC;4BAEZ,gBAAgB;4BAChB,MAAM,WAAW,KAAK,SAAS;4BAE/B,yDAAyD;4BACzD,MAAM,aAAqC;gCACzC,cAAc,SAAS,YAAY;gCACnC,UAAU;4BACZ;4BAEA,IAAI,eAAe,WAAW;gCAC5B,WAAW,OAAO,GAAG,SAAS,OAAO;gCACrC,WAAW,QAAQ,GAAG,SAAS,QAAQ;gCACvC,WAAW,IAAI,GAAG,MAAM,sCAAsC;4BAChE,OAAO;gCACL,WAAW,IAAI,GAAG,SAAS,IAAI;gCAC/B,WAAW,OAAO,GAAG,MAAM,sCAAsC;gCACjE,WAAW,QAAQ,GAAG,MAAM,uCAAuC;4BACrE;4BAEA,SAAS;wBACX;kCAEA,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe,CAAC;4CACd,cAAc;4CACd;wCACF;;0DAEA,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;oDACV,aAAY;oDACZ,WAAU;;;;;;;;;;;0DAGd,8OAAC,2HAAA,CAAA,gBAAa;;kEACZ,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAGV,8OAAC,2HAAA,CAAA,aAAU;wDAAC,OAAM;kEAChB,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,gNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,8OAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAc;4CACb,OAAO;4CACP,eAAe;4CACf,aAAY;;;;;;wCAEb,kCACC,8OAAC,2HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,qBAAqB;4CACpC,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAMnB,8OAAC;oCAAI,WAAU;;wCACZ,eAAe,0BACd;;8DAEE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;wDACT,MAAK;wDACL,SAAS,KAAK,OAAO;wDACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,8OAAC,0HAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,aAAY;wEACZ,WAAU;wEACV,WAAW;wEACX,OAAO,MAAM,KAAK,IAAI;wEACtB,MAAK;wEACL,WAAU;wEACV,WAAW,CAAC;4EACV,oEAAoE;4EACpE,IACE,CAAC,OAAO,IAAI,CAAC,EAAE,GAAG,KAAK,eAAe;4EACtC,EAAE,GAAG,KAAK,eACV,EAAE,GAAG,KAAK,YACV,EAAE,GAAG,KAAK,SACV,EAAE,GAAG,KAAK,WACV,CAAC,EAAE,GAAG,CAAC,QAAQ,CAAC,UAChB;gFACA,EAAE,cAAc;4EAClB;wEACF;;;;;;oEAED,kCACC,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;8DAQ7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8JAAA,CAAA,aAAU;wDACT,MAAK;wDACL,SAAS,KAAK,OAAO;wDACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,2HAAA,CAAA,SAAM;gEACL,OAAO,MAAM,KAAK,IAAI;gEACtB,eAAe,MAAM,QAAQ;gEAC7B,UACE,WAAW,MAAM,KAAK,KAAK,CAAC;;kFAG9B,8OAAC,2HAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;4EACV,aAAY;4EACZ,WAAU;;;;;;;;;;;kFAGd,8OAAC,2HAAA,CAAA,gBAAa;;0FACZ,8OAAC,2HAAA,CAAA,aAAU;gFAAC,OAAM;0FAAO;;;;;;4EACxB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,2HAAA,CAAA,aAAU;oFAAgB,OAAO;8FAC/B;mFADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;yEAW/B;sDAEE,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8JAAA,CAAA,aAAU;wDACT,MAAK;wDACL,SAAS,KAAK,OAAO;wDACrB,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,gNAAA,CAAA,YAAS;wEAAC,WAAU;;;;;;kFACrB,8OAAC,0HAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,aAAY;wEACZ,WAAU;wEACV,cAAa;wEACb,OAAO,MAAM,KAAK,IAAI;wEACtB,UAAU,CAAC;4EACT,MAAM,QAAQ,CAAC;4EACf,QAAQ,GAAG,CACT,gCACA,EAAE,MAAM,CAAC,KAAK;wEAElB;;;;;;oEAED,iCACC,8OAAC,iNAAA,CAAA,UAAO;wEAAC,WAAU;;;;;;;;;;;;;;;;;kEAO3B,8OAAC,yLAAA,CAAA,kBAAe;kEACb,mBAAmB,KAAK,KAAK,CAAC,wBAC7B,8OAAC,gKAAA,CAAA,UAAkB;;;;mEAEnB,uBACA,gBAAgB,MAAM,GAAG,mBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,SAAS;gEAAG,GAAG,CAAC;4DAAG;4DAC9B,SAAS;gEAAE,SAAS;gEAAG,GAAG;4DAAE;4DAC5B,MAAM;gEAAE,SAAS;gEAAG,GAAG,CAAC;4DAAG;4DAC3B,WAAU;sEAEV,cAAA,8OAAC,4HAAA,CAAA,UAAO;0EACN,cAAA,8OAAC,4HAAA,CAAA,cAAW;8EACV,cAAA,8OAAC,4HAAA,CAAA,eAAY;kFACV,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4HAAA,CAAA,cAAW;gFAEV,UAAU,IAAM,iBAAiB;gFACjC,WAAU;;kGAEV,8OAAC,gNAAA,CAAA,YAAS;wFAAC,WAAU;;;;;;kGACrB,8OAAC;kGAAM;;;;;;;+EALF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAoB7B,8OAAC,2HAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UACE,eACA,CAAC,eAAe,YACZ,CAAC,KAAK,SAAS,CAAC,aAChB,CAAC,KAAK,SAAS,CAAC,OAAO;4CAE7B,WAAU;4CACV,SAAS;gDACP,QAAQ,GAAG,CACT,uCACA;4CAEF,2EAA2E;4CAC7E;;gDAEC,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,QAAQ;oDAAI;oDACvB,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,MAAM;oDACR;oDACA,WAAU;8DAEV,cAAA,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;yEAGrB,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}, {"offset": {"line": 4084, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport useEmblaCarousel, {\r\n  type UseEmblaCarouselType,\r\n} from \"embla-carousel-react\"\r\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\n\r\ntype CarouselApi = UseEmblaCarouselType[1]\r\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\r\ntype CarouselOptions = UseCarouselParameters[0]\r\ntype CarouselPlugin = UseCarouselParameters[1]\r\n\r\ntype CarouselProps = {\r\n  opts?: CarouselOptions\r\n  plugins?: CarouselPlugin\r\n  orientation?: \"horizontal\" | \"vertical\"\r\n  setApi?: (_api: CarouselApi) => void\r\n}\r\n\r\ntype CarouselContextProps = {\r\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\r\n  api: ReturnType<typeof useEmblaCarousel>[1]\r\n  scrollPrev: () => void\r\n  scrollNext: () => void\r\n  canScrollPrev: boolean\r\n  canScrollNext: boolean\r\n} & CarouselProps\r\n\r\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\r\n\r\nfunction useCarousel() {\r\n  const context = React.useContext(CarouselContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nfunction Carousel({\r\n  orientation = \"horizontal\",\r\n  opts,\r\n  setApi,\r\n  plugins,\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & CarouselProps) {\r\n  const [carouselRef, api] = useEmblaCarousel(\r\n    {\r\n      ...opts,\r\n      axis: orientation === \"horizontal\" ? \"x\" : \"y\",\r\n    },\r\n    plugins\r\n  )\r\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\r\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\r\n\r\n  const onSelect = React.useCallback((api: CarouselApi) => {\r\n    if (!api) return\r\n    setCanScrollPrev(api.canScrollPrev())\r\n    setCanScrollNext(api.canScrollNext())\r\n  }, [])\r\n\r\n  const scrollPrev = React.useCallback(() => {\r\n    api?.scrollPrev()\r\n  }, [api])\r\n\r\n  const scrollNext = React.useCallback(() => {\r\n    api?.scrollNext()\r\n  }, [api])\r\n\r\n  const handleKeyDown = React.useCallback(\r\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\r\n      if (event.key === \"ArrowLeft\") {\r\n        event.preventDefault()\r\n        scrollPrev()\r\n      } else if (event.key === \"ArrowRight\") {\r\n        event.preventDefault()\r\n        scrollNext()\r\n      }\r\n    },\r\n    [scrollPrev, scrollNext]\r\n  )\r\n\r\n  React.useEffect(() => {\r\n    if (!api || !setApi) return\r\n    setApi(api)\r\n  }, [api, setApi])\r\n\r\n  React.useEffect(() => {\r\n    if (!api) return\r\n    onSelect(api)\r\n    api.on(\"reInit\", onSelect)\r\n    api.on(\"select\", onSelect)\r\n\r\n    return () => {\r\n      api?.off(\"select\", onSelect)\r\n    }\r\n  }, [api, onSelect])\r\n\r\n  return (\r\n    <CarouselContext.Provider\r\n      value={{\r\n        carouselRef,\r\n        api: api,\r\n        opts,\r\n        orientation:\r\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\r\n        scrollPrev,\r\n        scrollNext,\r\n        canScrollPrev,\r\n        canScrollNext,\r\n      }}\r\n    >\r\n      <div\r\n        onKeyDownCapture={handleKeyDown}\r\n        className={cn(\"relative\", className)}\r\n        role=\"region\"\r\n        aria-roledescription=\"carousel\"\r\n        data-slot=\"carousel\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    </CarouselContext.Provider>\r\n  )\r\n}\r\n\r\nfunction CarouselContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const { carouselRef, orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      ref={carouselRef}\r\n      className=\"overflow-hidden\"\r\n      data-slot=\"carousel-content\"\r\n    >\r\n      <div\r\n        className={cn(\r\n          \"flex\",\r\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CarouselItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const { orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      role=\"group\"\r\n      aria-roledescription=\"slide\"\r\n      data-slot=\"carousel-item\"\r\n      className={cn(\r\n        \"min-w-0 shrink-0 grow-0 basis-full\",\r\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CarouselPrevious({\r\n  className,\r\n  variant = \"outline\",\r\n  size = \"icon\",\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      data-slot=\"carousel-previous\"\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute size-8 rounded-full\",\r\n        orientation === \"horizontal\"\r\n          ? \"top-1/2 -left-12 -translate-y-1/2\"\r\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollPrev}\r\n      onClick={scrollPrev}\r\n      {...props}\r\n    >\r\n      <ArrowLeft />\r\n      <span className=\"sr-only\">Previous slide</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nfunction CarouselNext({\r\n  className,\r\n  variant = \"outline\",\r\n  size = \"icon\",\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      data-slot=\"carousel-next\"\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute size-8 rounded-full\",\r\n        orientation === \"horizontal\"\r\n          ? \"top-1/2 -right-12 -translate-y-1/2\"\r\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollNext}\r\n      onClick={scrollNext}\r\n      {...props}\r\n    >\r\n      <ArrowRight />\r\n      <span className=\"sr-only\">Next slide</span>\r\n    </Button>\r\n  )\r\n}\r\n\r\nexport {\r\n  type CarouselApi,\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselPrevious,\r\n  CarouselNext,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;AATA;;;;;;;AAgCA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA+B;AAEzE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAClC,IAAI,CAAC,KAAK;QACV,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACpC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,OAAO,CAAC,QAAQ;QACrB,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,KAAK;QACV,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,8OAAC;YACC,kBAAkB;YAClB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACrB,aAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,aAAU;kBAEV,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,8OAAC;QACC,MAAK;QACL,wBAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;;;;;0BACV,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,2HAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,kNAAA,CAAA,aAAU;;;;;0BACX,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 4299, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/CategoryCarousel.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useRef } from \"react\";\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { LucideIcon } from \"lucide-react\";\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselNext,\r\n  CarouselPrevious,\r\n} from \"@/components/ui/carousel\";\r\nimport { getCategories } from \"@/lib/config/categories\";\r\nimport { useDiscoverContext } from \"../context/DiscoverContext\";\r\n\r\ninterface CategoryCarouselProps {\r\n  maxCategories?: number;\r\n  className?: string;\r\n}\r\n\r\nexport default function CategoryCarousel({\r\n  maxCategories = 15,\r\n}: CategoryCarouselProps) {\r\n  const { handleCategoryChange, selectedCategory } = useDiscoverContext();\r\n  const carouselRef = useRef<HTMLDivElement>(null);\r\n  const isInView = useInView(carouselRef, { once: false, amount: 0.2 });\r\n\r\n  // Get all categories from the shared config\r\n  const categories = getCategories(maxCategories);\r\n\r\n  const handleCategoryClick = (category: string) => {\r\n    // Toggle category selection - if already selected, clear it\r\n    if (selectedCategory === category) {\r\n      handleCategoryChange(null);\r\n    } else {\r\n      handleCategoryChange(category);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      ref={carouselRef}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={isInView ? { opacity: 1, y: 0 } : {}}\r\n      transition={{ duration: 0.5, ease: \"easeOut\" }}\r\n      className=\"w-full relative py-6 bg-neutral-50/50 dark:bg-neutral-900/20 border-y border-neutral-200/50 dark:border-neutral-800/50\"\r\n    >\r\n      <div className=\"container mx-auto px-4 mb-4\">\r\n        <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-2\">\r\n          <div className=\"mb-2 md:mb-0\">\r\n            <motion.h2\r\n              className=\"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-1\"\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\r\n              transition={{ duration: 0.5 }}\r\n            >\r\n              Popular Categories\r\n            </motion.h2>\r\n            <motion.p\r\n              className=\"text-sm text-neutral-600 dark:text-neutral-400\"\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={isInView ? { opacity: 1, y: 0 } : {}}\r\n              transition={{ duration: 0.5, delay: 0.1 }}\r\n            >\r\n              Explore businesses and products/services by category\r\n            </motion.p>\r\n          </div>\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={isInView ? { opacity: 1 } : {}}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n            className=\"flex items-center gap-2\"\r\n          >\r\n            <div className=\"hidden md:flex items-center gap-1.5\">\r\n              <div className=\"w-8 h-1 rounded-full bg-neutral-300 dark:bg-neutral-700\"></div>\r\n              <div className=\"w-3 h-1 rounded-full bg-neutral-200 dark:bg-neutral-800\"></div>\r\n              <div className=\"w-2 h-1 rounded-full bg-neutral-200 dark:bg-neutral-800\"></div>\r\n            </div>\r\n            <p className=\"text-sm text-neutral-500 dark:text-neutral-400 flex items-center gap-1.5\">\r\n              <span className=\"hidden md:inline\">Drag to scroll</span>\r\n              <span className=\"md:hidden\">Swipe to explore</span>\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"16\"\r\n                height=\"16\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                className=\"animate-pulse\"\r\n              >\r\n                <path d=\"M5 12h14\"></path>\r\n                <path d=\"m12 5 7 7-7 7\"></path>\r\n              </svg>\r\n            </p>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n\r\n      <Carousel\r\n        opts={{\r\n          align: \"start\",\r\n          loop: true,\r\n          dragFree: true,\r\n        }}\r\n        className=\"w-full\"\r\n      >\r\n        <CarouselContent className=\"-ml-2 md:-ml-4\">\r\n          {categories.map((category, index) => (\r\n            <CarouselItem\r\n              key={index}\r\n              className=\"pl-2 md:pl-4 basis-1/3 sm:basis-1/4 md:basis-1/5 lg:basis-1/6 py-2\"\r\n            >\r\n              <CategoryCard\r\n                name={category.name}\r\n                icon={category.icon}\r\n                index={index}\r\n                isSelected={selectedCategory === category.name}\r\n                onClick={() => handleCategoryClick(category.name)}\r\n              />\r\n            </CarouselItem>\r\n          ))}\r\n        </CarouselContent>\r\n\r\n        <div className=\"hidden sm:block\">\r\n          <CarouselPrevious className=\"left-2 bg-white/90 dark:bg-neutral-900/90 border-neutral-200 dark:border-neutral-800 shadow-md hover:bg-white dark:hover:bg-neutral-800\" />\r\n          <CarouselNext className=\"right-2 bg-white/90 dark:bg-neutral-900/90 border-neutral-200 dark:border-neutral-800 shadow-md hover:bg-white dark:hover:bg-neutral-800\" />\r\n        </div>\r\n      </Carousel>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\ninterface CategoryCardProps {\r\n  name: string;\r\n  icon: LucideIcon;\r\n  index: number;\r\n  isSelected: boolean;\r\n  onClick: () => void;\r\n}\r\n\r\nfunction CategoryCard({\r\n  name,\r\n  icon: Icon,\r\n  index,\r\n  isSelected,\r\n  onClick,\r\n}: CategoryCardProps) {\r\n  return (\r\n    <motion.div\r\n      className=\"group cursor-pointer p-1.5\"\r\n      whileHover={{ scale: 1.02 }}\r\n      whileTap={{ scale: 0.97 }}\r\n      initial={{ opacity: 0, y: 10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, delay: index * 0.05 }}\r\n      onClick={onClick}\r\n    >\r\n      <div\r\n        className={`flex flex-col items-center gap-2 p-3 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md h-full ${\r\n          isSelected\r\n            ? \"bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 border-[var(--brand-gold)] border-2\"\r\n            : \"bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800 hover:border-[var(--brand-gold)]/30 dark:hover:border-[var(--brand-gold)]/30\"\r\n        }`}\r\n      >\r\n        <div className=\"relative\">\r\n          {/* Background glow effect */}\r\n          <div className=\"absolute inset-0 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full blur-md opacity-30 group-hover:opacity-80 transition-opacity duration-300 scale-150\"></div>\r\n\r\n          {/* Icon container */}\r\n          <div className=\"relative z-10 p-2.5 bg-[var(--brand-gold)]/10 dark:bg-[var(--brand-gold)]/20 rounded-full\">\r\n            <Icon className=\"h-5 w-5 text-[var(--brand-gold)]\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category name */}\r\n        <span className=\"text-xs font-medium text-neutral-700 dark:text-neutral-300 text-center\">\r\n          {name}\r\n        </span>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAOA;AACA;AAbA;;;;;;;AAoBe,SAAS,iBAAiB,EACvC,gBAAgB,EAAE,EACI;IACtB,MAAM,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD;IACpE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa;QAAE,MAAM;QAAO,QAAQ;IAAI;IAEnE,4CAA4C;IAC5C,MAAM,aAAa,CAAA,GAAA,2HAAA,CAAA,gBAAa,AAAD,EAAE;IAEjC,MAAM,sBAAsB,CAAC;QAC3B,4DAA4D;QAC5D,IAAI,qBAAqB,UAAU;YACjC,qBAAqB;QACvB,OAAO;YACL,qBAAqB;QACvB;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI,CAAC;QAC5C,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC5C,YAAY;wCAAE,UAAU;oCAAI;8CAC7B;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS,WAAW;wCAAE,SAAS;wCAAG,GAAG;oCAAE,IAAI,CAAC;oCAC5C,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;;;;;;;sCAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS,WAAW;gCAAE,SAAS;4BAAE,IAAI,CAAC;4BACtC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAE,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAmB;;;;;;sDACnC,8OAAC;4CAAK,WAAU;sDAAY;;;;;;sDAC5B,8OAAC;4CACC,OAAM;4CACN,OAAM;4CACN,QAAO;4CACP,SAAQ;4CACR,MAAK;4CACL,QAAO;4CACP,aAAY;4CACZ,eAAc;4CACd,gBAAe;4CACf,WAAU;;8DAEV,8OAAC;oDAAK,GAAE;;;;;;8DACR,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,8OAAC,6HAAA,CAAA,WAAQ;gBACP,MAAM;oBACJ,OAAO;oBACP,MAAM;oBACN,UAAU;gBACZ;gBACA,WAAU;;kCAEV,8OAAC,6HAAA,CAAA,kBAAe;wBAAC,WAAU;kCACxB,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,6HAAA,CAAA,eAAY;gCAEX,WAAU;0CAEV,cAAA,8OAAC;oCACC,MAAM,SAAS,IAAI;oCACnB,MAAM,SAAS,IAAI;oCACnB,OAAO;oCACP,YAAY,qBAAqB,SAAS,IAAI;oCAC9C,SAAS,IAAM,oBAAoB,SAAS,IAAI;;;;;;+BAR7C;;;;;;;;;;kCAcX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;0CAC5B,8OAAC,6HAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC;AAUA,SAAS,aAAa,EACpB,IAAI,EACJ,MAAM,IAAI,EACV,KAAK,EACL,UAAU,EACV,OAAO,EACW;IAClB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;QACjD,SAAS;kBAET,cAAA,8OAAC;YACC,WAAW,CAAC,6GAA6G,EACvH,aACI,iGACA,+JACJ;;8BAEF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;;;;;;;;;;;;;;;;;8BAKpB,8OAAC;oBAAK,WAAU;8BACb;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 4669, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-card text-card-foreground\",\r\n        destructive:\r\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4734, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ErrorSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\r\nimport { AlertCircle } from \"lucide-react\";\r\nimport { useDiscoverContext } from \"../context/DiscoverContext\";\r\n\r\nexport default function ErrorSection() {\r\n  const { searchError, isSearching } = useDiscoverContext();\r\n\r\n  return (\r\n    <AnimatePresence mode=\"wait\">\r\n      {!isSearching && searchError && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          exit={{ opacity: 0, y: -20 }}\r\n          transition={{ duration: 0.5 }}\r\n          key=\"error-state\"\r\n          className=\"my-12\"\r\n        >\r\n          <Alert\r\n            variant=\"destructive\"\r\n            className=\"max-w-lg mx-auto border-red-200 dark:border-red-900/50 backdrop-blur-sm overflow-hidden\"\r\n          >\r\n            <div className=\"absolute inset-0 bg-red-50/50 dark:bg-red-950/20 pointer-events-none\"></div>\r\n            <div className=\"relative z-10 flex items-start\">\r\n              <motion.div\r\n                className=\"mr-2\"\r\n                initial={{ scale: 1 }}\r\n                animate={{ scale: 1.1 }}\r\n                transition={{\r\n                  duration: 0.5,\r\n                  repeat: Infinity,\r\n                  repeatType: \"reverse\"\r\n                }}\r\n              >\r\n                <AlertCircle className=\"h-6 w-6\" />\r\n              </motion.div>\r\n              <div>\r\n                <AlertTitle className=\"text-lg font-semibold mb-1\">\r\n                  Error Occurred\r\n                </AlertTitle>\r\n                <AlertDescription className=\"text-red-700 dark:text-red-300\">\r\n                  {searchError}\r\n                </AlertDescription>\r\n              </div>\r\n            </div>\r\n          </Alert>\r\n        </motion.div>\r\n      )}\r\n    </AnimatePresence>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD;IAEtD,qBACE,8OAAC,yLAAA,CAAA,kBAAe;QAAC,MAAK;kBACnB,CAAC,eAAe,6BACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,MAAM;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC3B,YAAY;gBAAE,UAAU;YAAI;YAE5B,WAAU;sBAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;gBACJ,SAAQ;gBACR,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAI;gCACtB,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,YAAY;gCACd;0CAEA,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,8OAAC;;kDACC,8OAAC,0HAAA,CAAA,aAAU;wCAAC,WAAU;kDAA6B;;;;;;kDAGnD,8OAAC,0HAAA,CAAA,mBAAgB;wCAAC,WAAU;kDACzB;;;;;;;;;;;;;;;;;;;;;;;;WA1BL;;;;;;;;;;AAmCd", "debugId": null}}, {"offset": {"line": 4862, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ViewToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Store, Package, RefreshCw } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface ViewToggleProps {\r\n  viewType: \"cards\" | \"products\";\r\n  onViewChange: (_view: \"cards\" | \"products\") => void;\r\n  disabled?: boolean;\r\n  hasFilters?: boolean;\r\n}\r\n\r\nexport default function ViewToggle({\r\n  viewType,\r\n  onViewChange,\r\n  disabled = false,\r\n  hasFilters = false,\r\n}: ViewToggleProps) {\r\n  // Function to reset all filters and show all businesses/products\r\n  const handleResetFilters = () => {\r\n    // Use window.location.href to force a full page reload\r\n    window.location.href = \"/discover\";\r\n  };\r\n  return (\r\n    <div className=\"flex flex-col items-center gap-4\">\r\n      <motion.div\r\n        className=\"relative bg-white/90 dark:bg-neutral-800/90 p-1 sm:p-1.5 rounded-xl border border-neutral-200/70 dark:border-neutral-700/70 flex gap-1 backdrop-blur-md overflow-hidden shadow-sm\"\r\n        initial={{ y: 20, opacity: 0 }}\r\n        animate={{ y: 0, opacity: 1 }}\r\n        transition={{ duration: 0.5 }}\r\n        whileHover={{ boxShadow: \"0 8px 30px rgba(0, 0, 0, 0.06)\" }}\r\n      >\r\n        {/* Decorative elements */}\r\n        <div className=\"absolute -top-6 -right-6 w-12 h-12 bg-[var(--brand-gold)]/10 blur-xl rounded-full\"></div>\r\n        <div className=\"absolute -bottom-6 -left-6 w-12 h-12 bg-purple-500/10 blur-xl rounded-full\"></div>\r\n        <motion.div\r\n          whileHover={!disabled ? { scale: 1.05 } : undefined}\r\n          whileTap={!disabled ? { scale: 0.95 } : undefined}\r\n          className=\"relative z-10\"\r\n        >\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => onViewChange(\"products\")}\r\n            disabled={disabled}\r\n            className={`\r\n              relative px-3 sm:px-4 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\r\n              ${\r\n                viewType === \"products\"\r\n                  ? \"text-black dark:text-white font-medium\"\r\n                  : \"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300\"\r\n              }\r\n              ${disabled ? \"opacity-50 cursor-not-allowed\" : \"cursor-pointer\"}\r\n            `}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <div className=\"relative\">\r\n                <Package className=\"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4\" />\r\n              </div>\r\n              <span className=\"text-xs sm:text-xs md:text-sm font-medium\">Products</span>\r\n            </div>\r\n\r\n            {/* Active indicator */}\r\n            {viewType === \"products\" && (\r\n              <motion.div\r\n                layoutId=\"activeViewIndicator\"\r\n                className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10 shadow-inner\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n            )}\r\n          </Button>\r\n        </motion.div>\r\n\r\n        <motion.div\r\n          whileHover={!disabled ? { scale: 1.05 } : undefined}\r\n          whileTap={!disabled ? { scale: 0.95 } : undefined}\r\n          className=\"relative z-10\"\r\n        >\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => onViewChange(\"cards\")}\r\n            disabled={disabled}\r\n            className={`\r\n              relative px-3 sm:px-4 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\r\n              ${\r\n                viewType === \"cards\"\r\n                  ? \"text-black dark:text-white font-medium\"\r\n                  : \"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300\"\r\n              }\r\n              ${disabled ? \"opacity-50 cursor-not-allowed\" : \"cursor-pointer\"}\r\n            `}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <div className=\"relative\">\r\n                <Store className=\"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4\" />\r\n              </div>\r\n              <span className=\"text-xs sm:text-xs md:text-sm font-medium\">\r\n                Digital Cards\r\n              </span>\r\n            </div>\r\n\r\n            {/* Active indicator */}\r\n            {viewType === \"cards\" && (\r\n              <motion.div\r\n                layoutId=\"activeViewIndicator\"\r\n                className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-xl -z-10\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n            )}\r\n          </Button>\r\n        </motion.div>\r\n      </motion.div>\r\n\r\n      {/* Reset Filters Button - Only show when filters are applied */}\r\n      {hasFilters && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.3, delay: 0.2 }}\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n        >\r\n          <Button\r\n            onClick={handleResetFilters}\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            className=\"flex items-center gap-1 sm:gap-2 bg-white dark:bg-neutral-800 border-[var(--brand-gold)]/30 dark:border-[var(--brand-gold)]/30 hover:bg-[var(--brand-gold)]/10 dark:hover:bg-[var(--brand-gold)]/20 text-neutral-700 dark:text-neutral-300 text-xs sm:text-sm shadow-sm transition-all duration-300\"\r\n          >\r\n            <RefreshCw className=\"h-3 w-3 sm:h-3.5 sm:w-3.5 text-[var(--brand-gold)]\" />\r\n            <span>Reset Filters</span>\r\n          </Button>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAJA;;;;;AAae,SAAS,WAAW,EACjC,QAAQ,EACR,YAAY,EACZ,WAAW,KAAK,EAChB,aAAa,KAAK,EACF;IAChB,iEAAiE;IACjE,MAAM,qBAAqB;QACzB,uDAAuD;QACvD,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,YAAY;oBAAE,WAAW;gBAAiC;;kCAG1D,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY,CAAC,WAAW;4BAAE,OAAO;wBAAK,IAAI;wBAC1C,UAAU,CAAC,WAAW;4BAAE,OAAO;wBAAK,IAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAW,CAAC;;cAEV,EACE,aAAa,aACT,2CACA,4FACL;cACD,EAAE,WAAW,kCAAkC,iBAAiB;YAClE,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;gCAI7D,aAAa,4BACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;kCAMpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY,CAAC,WAAW;4BAAE,OAAO;wBAAK,IAAI;wBAC1C,UAAU,CAAC,WAAW;4BAAE,OAAO;wBAAK,IAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,aAAa;4BAC5B,UAAU;4BACV,WAAW,CAAC;;cAEV,EACE,aAAa,UACT,2CACA,4FACL;cACD,EAAE,WAAW,kCAAkC,iBAAiB;YAClE,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;gCAM7D,aAAa,yBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;;;;;;;YAQrC,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBACL,SAAS;oBACT,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,8OAAC;sCAAK;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 5139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/AnimatedBusinessCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport Image from \"next/image\";\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { Heart, MapPin, Star, UserPlus } from \"lucide-react\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\nimport { useState, useRef } from \"react\";\r\n\r\ninterface AnimatedBusinessCardProps {\r\n  business: BusinessCardData;\r\n  index: number;\r\n}\r\n\r\nexport default function AnimatedBusinessCard({ business, index }: AnimatedBusinessCardProps) {\r\n  const [isHovered, setIsHovered] = useState(false);\r\n  const cardRef = useRef(null);\r\n  const isInView = useInView(cardRef, { once: false, amount: 0.2 });\r\n\r\n  // Format address components for better display\r\n  const getAddressComponents = () => {\r\n    // First line: address_line\r\n    const addressLine = business.address_line || \"\";\r\n\r\n    // Second line: locality, city\r\n    const localityCity = [business.locality, business.city]\r\n      .filter(Boolean)\r\n      .join(\", \");\r\n\r\n    // Third line: state, pincode\r\n    const statePin = [\r\n      business.state,\r\n      business.pincode ? `PIN: ${business.pincode}` : null\r\n    ]\r\n      .filter(Boolean)\r\n      .join(\", \");\r\n\r\n    return {\r\n      addressLine,\r\n      localityCity,\r\n      statePin,\r\n      hasAddress: !!(addressLine || localityCity || statePin)\r\n    };\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      ref={cardRef}\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={isInView ? { opacity: 1, y: 0 } : {}}\r\n      transition={{ duration: 0.4, delay: index * 0.05 }}\r\n      className=\"h-full\"\r\n      onHoverStart={() => setIsHovered(true)}\r\n      onHoverEnd={() => setIsHovered(false)}\r\n    >\r\n      <Link href={`/${business.business_slug}`} className=\"block h-full\">\r\n        <motion.div\r\n          className=\"relative h-full overflow-hidden rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm transition-all duration-300\"\r\n          animate={{\r\n            boxShadow: isHovered\r\n              ? \"0 10px 25px -5px rgba(var(--brand-gold-rgb), 0.2), 0 8px 10px -6px rgba(var(--brand-gold-rgb), 0.1)\"\r\n              : \"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)\",\r\n            borderColor: isHovered\r\n              ? \"rgba(var(--brand-gold-rgb), 0.3)\"\r\n              : \"\"\r\n          }}\r\n        >\r\n          {/* Card content */}\r\n          <div className=\"p-3 sm:p-4 flex flex-col h-full\">\r\n            {/* Header with logo and name - centered design */}\r\n            <div className=\"flex flex-col items-center text-center mb-4\">\r\n              <div className=\"relative flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 rounded-full overflow-hidden border-2 border-[var(--brand-gold)]/30 mb-2\">\r\n                {business.logo_url && business.logo_url.trim() !== \"\" ? (\r\n                  <Image\r\n                    src={business.logo_url}\r\n                    alt={business.business_name || \"Business logo\"}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                    sizes=\"(max-width: 640px) 48px, 64px\"\r\n                  />\r\n                ) : (\r\n                  <div className=\"w-full h-full flex items-center justify-center bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] text-xl font-semibold\">\r\n                    {business.business_name?.charAt(0) || \"B\"}\r\n                  </div>\r\n                )}\r\n\r\n                {/* Animated ring on hover */}\r\n                {isHovered && (\r\n                  <motion.div\r\n                    className=\"absolute inset-0 rounded-full border-2 border-[var(--brand-gold)]\"\r\n                    initial={{ opacity: 0, scale: 0.8 }}\r\n                    animate={{ opacity: 1, scale: 1 }}\r\n                    transition={{ duration: 0.3 }}\r\n                  />\r\n                )}\r\n              </div>\r\n\r\n              <div className=\"min-w-0 w-full px-2\">\r\n                <motion.h3\r\n                  className=\"font-semibold text-neutral-900 dark:text-white line-clamp-1 text-sm sm:text-base md:text-lg relative inline-block mx-auto\"\r\n                  animate={{ color: isHovered ? \"var(--brand-gold)\" : \"\" }}\r\n                  transition={{ duration: 0.2 }}\r\n                >\r\n                  {business.business_name}\r\n\r\n                  {/* Animated underline on hover */}\r\n                  {isHovered && (\r\n                    <motion.div\r\n                      className=\"absolute -bottom-1 left-0 h-0.5 bg-[var(--brand-gold)]\"\r\n                      initial={{ width: 0 }}\r\n                      animate={{ width: \"100%\" }}\r\n                      transition={{ duration: 0.3 }}\r\n                    />\r\n                  )}\r\n                </motion.h3>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Business details */}\r\n            <div className=\"space-y-2 mb-3 flex-grow\">\r\n              {/* Location - improved design */}\r\n              <div className=\"flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800\">\r\n                <MapPin className=\"h-4 w-4 text-neutral-500 dark:text-neutral-400 flex-shrink-0 mt-0.5\" />\r\n                <div className=\"flex-1 min-w-0\">\r\n                  {(() => {\r\n                    const { addressLine, localityCity, statePin, hasAddress } = getAddressComponents();\r\n                    return hasAddress ? (\r\n                      <>\r\n                        {addressLine && (\r\n                          <p className=\"text-xs font-medium text-neutral-700 dark:text-neutral-300 line-clamp-1\">\r\n                            {addressLine}\r\n                          </p>\r\n                        )}\r\n                        {localityCity && (\r\n                          <p className=\"text-xs text-neutral-600 dark:text-neutral-400 line-clamp-1 mt-0.5\">\r\n                            {localityCity}\r\n                          </p>\r\n                        )}\r\n                        {statePin && (\r\n                          <p className=\"text-xs text-neutral-500 dark:text-neutral-500 line-clamp-1 mt-0.5\">\r\n                            {statePin}\r\n                          </p>\r\n                        )}\r\n                      </>\r\n                    ) : (\r\n                      <p className=\"text-xs text-neutral-500 dark:text-neutral-500 italic\">\r\n                        Location not specified\r\n                      </p>\r\n                    );\r\n                  })()}\r\n                </div>\r\n              </div>\r\n\r\n\r\n\r\n              {/* Category badge - if available */}\r\n              {/* Removed business_category as it's not in the BusinessCardData type */}\r\n            </div>\r\n\r\n            {/* Stats row */}\r\n            <div className=\"flex items-center justify-between text-xs border-t border-neutral-100 dark:border-neutral-800 pt-2 mt-auto\">\r\n              <div className=\"flex items-center gap-1 text-rose-500 dark:text-rose-400\">\r\n                <Heart className=\"h-3.5 w-3.5\" />\r\n                <span>{formatIndianNumberShort(business.total_likes || 0)}</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center gap-1 text-blue-500 dark:text-blue-400\">\r\n                <UserPlus className=\"h-3.5 w-3.5\" />\r\n                <span>{formatIndianNumberShort(business.total_subscriptions || 0)}</span>\r\n              </div>\r\n\r\n              <div className=\"flex items-center gap-1 text-amber-500 dark:text-amber-400\">\r\n                <Star className=\"h-3.5 w-3.5\" />\r\n                <span>{(business.average_rating || 0).toFixed(1)}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Hover effect overlay */}\r\n          <motion.div\r\n            className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/5 to-blue-500/5 dark:from-[var(--brand-gold)]/10 dark:to-blue-500/10 pointer-events-none\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: isHovered ? 1 : 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          />\r\n        </motion.div>\r\n      </Link>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AARA;;;;;;;;AAee,SAAS,qBAAqB,EAAE,QAAQ,EAAE,KAAK,EAA6B;IACzF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAAE,MAAM;QAAO,QAAQ;IAAI;IAE/D,+CAA+C;IAC/C,MAAM,uBAAuB;QAC3B,2BAA2B;QAC3B,MAAM,cAAc,SAAS,YAAY,IAAI;QAE7C,8BAA8B;QAC9B,MAAM,eAAe;YAAC,SAAS,QAAQ;YAAE,SAAS,IAAI;SAAC,CACpD,MAAM,CAAC,SACP,IAAI,CAAC;QAER,6BAA6B;QAC7B,MAAM,WAAW;YACf,SAAS,KAAK;YACd,SAAS,OAAO,GAAG,CAAC,KAAK,EAAE,SAAS,OAAO,EAAE,GAAG;SACjD,CACE,MAAM,CAAC,SACP,IAAI,CAAC;QAER,OAAO;YACL;YACA;YACA;YACA,YAAY,CAAC,CAAC,CAAC,eAAe,gBAAgB,QAAQ;QACxD;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS,WAAW;YAAE,SAAS;YAAG,GAAG;QAAE,IAAI,CAAC;QAC5C,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;QACjD,WAAU;QACV,cAAc,IAAM,aAAa;QACjC,YAAY,IAAM,aAAa;kBAE/B,cAAA,8OAAC,4JAAA,CAAA,UAAI;YAAC,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;YAAE,WAAU;sBAClD,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,WAAW,YACP,wGACA;oBACJ,aAAa,YACT,qCACA;gBACN;;kCAGA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,IAAI,OAAO,mBACjD,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,SAAS,QAAQ;gDACtB,KAAK,SAAS,aAAa,IAAI;gDAC/B,IAAI;gDACJ,WAAU;gDACV,OAAM;;;;;qEAGR,8OAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,EAAE,OAAO,MAAM;;;;;;4CAKzC,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;kDAKlC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,SAAS;gDAAE,OAAO,YAAY,sBAAsB;4CAAG;4CACvD,YAAY;gDAAE,UAAU;4CAAI;;gDAE3B,SAAS,aAAa;gDAGtB,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAO;oDACzB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;;;;;;;;;;;;0CAQtC,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAI,WAAU;sDACZ,CAAC;gDACA,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;gDAC5D,OAAO,2BACL;;wDACG,6BACC,8OAAC;4DAAE,WAAU;sEACV;;;;;;wDAGJ,8BACC,8OAAC;4DAAE,WAAU;sEACV;;;;;;wDAGJ,0BACC,8OAAC;4DAAE,WAAU;sEACV;;;;;;;iFAKP,8OAAC;oDAAE,WAAU;8DAAwD;;;;;;4CAIzE,CAAC;;;;;;;;;;;;;;;;;0CAWP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,WAAW,IAAI;;;;;;;;;;;;kDAGzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAM,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,mBAAmB,IAAI;;;;;;;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAM,CAAC,SAAS,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAMpD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS,YAAY,IAAI;wBAAE;wBACtC,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 5503, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/AnimatedBusinessGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport AnimatedBusinessCard from \"./AnimatedBusinessCard\";\r\n\r\ninterface AnimatedBusinessGridProps {\r\n  businesses: BusinessCardData[];\r\n  isAuthenticated?: boolean;\r\n}\r\n\r\nexport default function AnimatedBusinessGrid({\r\n  businesses,\r\n}: AnimatedBusinessGridProps) {\r\n  const gridRef = useRef<HTMLDivElement>(null);\r\n\r\n  return (\r\n    <motion.div\r\n      ref={gridRef}\r\n      className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4\"\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      {businesses.map((business, index) => (\r\n        <AnimatedBusinessCard\r\n          key={business.id}\r\n          business={business}\r\n          index={index}\r\n        />\r\n      ))}\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAYe,SAAS,qBAAqB,EAC3C,UAAU,EACgB;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEvC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;kBAE3B,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,kKAAA,CAAA,UAAoB;gBAEnB,UAAU;gBACV,OAAO;eAFF,SAAS,EAAE;;;;;;;;;;AAO1B", "debugId": null}}, {"offset": {"line": 5549, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/AnimatedBusinessGridSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport default function AnimatedBusinessGridSkeleton() {\r\n  return (\r\n    <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4\">\r\n      {Array.from({ length: 6 }).map((_, index) => (\r\n        <SkeletonCard key={index} index={index} />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SkeletonCard({ index }: { index: number }) {\r\n  return (\r\n    <motion.div\r\n      className=\"rounded-xl border border-neutral-200 dark:border-neutral-800 bg-white dark:bg-neutral-900 shadow-sm p-2 sm:p-3 md:p-4 h-full flex flex-col w-full\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.3, delay: index * 0.05 }}\r\n    >\r\n      {/* Header with logo and name - centered */}\r\n      <div className=\"flex flex-col items-center text-center mb-4\">\r\n        <Skeleton className=\"h-12 w-12 sm:h-16 sm:w-16 rounded-full flex-shrink-0 mb-2\" />\r\n        <div className=\"space-y-2 w-full px-2\">\r\n          <Skeleton className=\"h-5 w-3/4 mx-auto\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Business details */}\r\n      <div className=\"space-y-2 mb-3 flex-grow\">\r\n        {/* Location with multiple lines */}\r\n        <div className=\"flex gap-1.5 sm:gap-2 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg p-2 sm:p-2.5 border border-neutral-100 dark:border-neutral-800\">\r\n          <Skeleton className=\"h-4 w-4 rounded-full flex-shrink-0 mt-0.5\" />\r\n          <div className=\"flex-1 space-y-1.5\">\r\n            <Skeleton className=\"h-3 w-full\" />\r\n            <Skeleton className=\"h-3 w-4/5\" />\r\n            <Skeleton className=\"h-3 w-3/5\" />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category badge */}\r\n        <div className=\"flex items-center justify-center mt-2\">\r\n          <Skeleton className=\"h-6 w-24 rounded-full\" />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats row */}\r\n      <div className=\"flex items-center justify-between pt-2 border-t border-neutral-100 dark:border-neutral-800 mt-auto\">\r\n        <Skeleton className=\"h-4 w-12\" />\r\n        <Skeleton className=\"h-4 w-12\" />\r\n        <Skeleton className=\"h-4 w-12\" />\r\n      </div>\r\n\r\n\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;gBAAyB,OAAO;eAAd;;;;;;;;;;AAI3B;AAEA,SAAS,aAAa,EAAE,KAAK,EAAqB;IAChD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;;0BAGjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC,6HAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAKxB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 5735, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ModernBusinessFilterGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Search, X, Filter, ArrowUpDown } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport AnimatedBusinessGrid from \"./AnimatedBusinessGrid\";\r\nimport AnimatedBusinessGridSkeleton from \"./AnimatedBusinessGridSkeleton\";\r\n\r\ninterface ModernBusinessFilterGridProps {\r\n  businesses: BusinessCardData[];\r\n  isAuthenticated: boolean;\r\n  onSortChange: (_sortBy: BusinessSortBy) => void;\r\n  currentSortBy: BusinessSortBy;\r\n  isLoading: boolean;\r\n  onSearch: (_searchTerm: string) => void;\r\n  initialSearchTerm?: string | null;\r\n}\r\n\r\nexport default function ModernBusinessFilterGrid({\r\n  businesses,\r\n  isAuthenticated,\r\n  onSortChange,\r\n  currentSortBy,\r\n  isLoading,\r\n  onSearch,\r\n  initialSearchTerm,\r\n}: ModernBusinessFilterGridProps) {\r\n  const [searchQuery, setSearchQuery] = useState(initialSearchTerm || \"\");\r\n  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(\r\n    initialSearchTerm || \"\"\r\n  );\r\n  const [isFilterExpanded, setIsFilterExpanded] = useState(false);\r\n  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const isFirstRender = useRef(true);\r\n  const prevSearchQueryRef = useRef(searchQuery);\r\n  const wasInitiallyPopulated = useRef(!!initialSearchTerm);\r\n\r\n  // Update prevSearchQueryRef when component mounts or searchQuery changes\r\n  useEffect(() => {\r\n    // Skip the first update if initialSearchTerm was provided\r\n    if (isFirstRender.current && initialSearchTerm) {\r\n      isFirstRender.current = false;\r\n      wasInitiallyPopulated.current = true;\r\n      return;\r\n    }\r\n\r\n    prevSearchQueryRef.current = searchQuery;\r\n  }, [searchQuery, initialSearchTerm]);\r\n\r\n  // Prevent infinite loops by tracking if we've already processed the initial search term\r\n  useEffect(() => {\r\n    // This effect runs only once on mount\r\n    if (initialSearchTerm) {\r\n      // Mark that we've already processed the initial search term\r\n      wasInitiallyPopulated.current = true;\r\n      isFirstRender.current = false;\r\n    }\r\n  }, [initialSearchTerm]); // Include initialSearchTerm as a dependency\r\n\r\n  // Update isFirstRender when data is loaded\r\n  useEffect(() => {\r\n    // If we have businesses or isLoading is false, we're no longer on first render\r\n    if (businesses.length > 0 || !isLoading) {\r\n      isFirstRender.current = false;\r\n    }\r\n  }, [businesses, isLoading]);\r\n\r\n  // Handle input change with manual debounce\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const query = e.target.value;\r\n    setSearchQuery(query);\r\n\r\n    // Clear any existing timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n      searchTimeoutRef.current = null;\r\n    }\r\n\r\n    // Check if the user has manually cleared the search\r\n    const wasManuallyCleared =\r\n      prevSearchQueryRef.current.length > 0 && query === \"\";\r\n\r\n    // If the search was manually cleared, trigger search immediately\r\n    if (wasManuallyCleared) {\r\n      setDebouncedSearchQuery(\"\");\r\n      // Add a small visual delay before triggering the search\r\n      setTimeout(() => {\r\n        onSearch(\"\");\r\n      }, 100);\r\n      return;\r\n    }\r\n\r\n    // Only set a new timeout if the query is empty or at least 3 characters\r\n    if (query === \"\" || query.length >= 3) {\r\n      searchTimeoutRef.current = setTimeout(() => {\r\n        setDebouncedSearchQuery(query);\r\n        // Skip search on initial render if initialSearchTerm was provided\r\n        if (!isFirstRender.current) {\r\n          // Call the server-side search function\r\n          onSearch(query);\r\n        } else {\r\n          isFirstRender.current = false;\r\n        }\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  // Handle clear search when clicking the X button\r\n  const handleClearSearch = () => {\r\n    // Clear any existing timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n      searchTimeoutRef.current = null;\r\n    }\r\n\r\n    setSearchQuery(\"\");\r\n    setDebouncedSearchQuery(\"\");\r\n\r\n    // Add a small visual delay before triggering the search\r\n    setTimeout(() => {\r\n      onSearch(\"\");\r\n    }, 100);\r\n  };\r\n\r\n  const toggleFilterExpanded = () => {\r\n    setIsFilterExpanded(!isFilterExpanded);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Search and Filter Controls */}\r\n      <motion.div\r\n        className=\"sticky top-[80px] z-30 container mx-auto px-4\"\r\n        initial={{ opacity: 0, y: -10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.3 }}\r\n      >\r\n        <div className=\"relative overflow-hidden bg-white/80 dark:bg-black/80 p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300 backdrop-blur-md\">\r\n          {/* Decorative elements */}\r\n          <div className=\"absolute inset-0 overflow-hidden pointer-events-none opacity-70\">\r\n            <div className=\"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-2xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10\"></div>\r\n            <div className=\"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-2xl dark:from-purple-500/10 dark:to-blue-500/10\"></div>\r\n          </div>\r\n\r\n          <div className=\"relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between\">\r\n            {/* Search Input */}\r\n            <div className=\"relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md\">\r\n              <form\r\n                onSubmit={(e) => {\r\n                  e.preventDefault();\r\n                  // Only trigger search if query is empty or at least 3 characters\r\n                  if (searchQuery === \"\" || searchQuery.length >= 3) {\r\n                    // Clear any existing timeout\r\n                    if (searchTimeoutRef.current) {\r\n                      clearTimeout(searchTimeoutRef.current);\r\n                      searchTimeoutRef.current = null;\r\n                    }\r\n                    setDebouncedSearchQuery(searchQuery);\r\n                    onSearch(searchQuery);\r\n                  }\r\n                }}\r\n              >\r\n                <div className=\"relative group\">\r\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200\" />\r\n                  <Input\r\n                    placeholder=\"Search businesses...\"\r\n                    value={searchQuery}\r\n                    onChange={handleSearchChange}\r\n                    className=\"pl-10 h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\"\r\n                  />\r\n                  {searchQuery && (\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={handleClearSearch}\r\n                      className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200\"\r\n                    >\r\n                      <X className=\"h-4 w-4\" />\r\n                    </button>\r\n                  )}\r\n                  {/* Hidden submit button for form submission */}\r\n                  <button type=\"submit\" className=\"hidden\">\r\n                    Search\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-2 sm:flex sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto\">\r\n              {/* Sort Dropdown */}\r\n              <Select\r\n                value={currentSortBy}\r\n                onValueChange={(value) => onSortChange(value as BusinessSortBy)}\r\n                disabled={isLoading}\r\n              >\r\n                <SelectTrigger className=\"w-full sm:w-[180px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\">\r\n                  <div className=\"flex items-center overflow-hidden\">\r\n                    <ArrowUpDown className=\"flex-shrink-0 mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]\" />\r\n                    <SelectValue\r\n                      placeholder=\"Sort by\"\r\n                      className=\"text-xs sm:text-sm truncate\"\r\n                    />\r\n                  </div>\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectGroup>\r\n                    <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5\">\r\n                      Date\r\n                    </SelectLabel>\r\n                    <SelectItem value=\"created_desc\" className=\"relative pl-8\">\r\n                      Newest First\r\n                    </SelectItem>\r\n                    <SelectItem value=\"created_asc\" className=\"relative pl-8\">\r\n                      Oldest First\r\n                    </SelectItem>\r\n                  </SelectGroup>\r\n                  <SelectGroup>\r\n                    <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1\">\r\n                      Name\r\n                    </SelectLabel>\r\n                    <SelectItem value=\"name_asc\" className=\"relative pl-8\">\r\n                      Name: A to Z\r\n                    </SelectItem>\r\n                    <SelectItem value=\"name_desc\" className=\"relative pl-8\">\r\n                      Name: Z to A\r\n                    </SelectItem>\r\n                  </SelectGroup>\r\n                  <SelectGroup>\r\n                    <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1\">\r\n                      Popularity\r\n                    </SelectLabel>\r\n                    <SelectItem value=\"likes_desc\" className=\"relative pl-8\">\r\n                      Most Liked\r\n                    </SelectItem>\r\n                    <SelectItem value=\"subscriptions_desc\" className=\"relative pl-8\">\r\n                      Most Subscribed\r\n                    </SelectItem>\r\n                    <SelectItem value=\"rating_desc\" className=\"relative pl-8\">\r\n                      Highest Rated\r\n                    </SelectItem>\r\n                  </SelectGroup>\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              {/* Filter Button */}\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={toggleFilterExpanded}\r\n                className=\"w-full sm:w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm overflow-hidden\"\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  <Filter className=\"flex-shrink-0 mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]\" />\r\n                  <span className=\"text-xs sm:text-sm truncate\">Filters</span>\r\n                </div>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Expanded Filter Options */}\r\n          <AnimatePresence>\r\n            {isFilterExpanded && (\r\n              <motion.div\r\n                className=\"mt-4 pt-4 border-t border-neutral-200/50 dark:border-neutral-800/50\"\r\n                initial={{ opacity: 0, height: 0 }}\r\n                animate={{ opacity: 1, height: \"auto\" }}\r\n                exit={{ opacity: 0, height: 0 }}\r\n                transition={{ duration: 0.2 }}\r\n              >\r\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4\">\r\n                  {/* Additional filter options could go here */}\r\n                  <div className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n                    Additional filters coming soon...\r\n                  </div>\r\n                </div>\r\n              </motion.div>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Results Count */}\r\n      <motion.div\r\n        className=\"container mx-auto px-4 flex items-center justify-between\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ duration: 0.3, delay: 0.2 }}\r\n      >\r\n        <div className=\"text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate\">\r\n          {debouncedSearchQuery && (\r\n            <span>\r\n              Showing results for{\" \"}\r\n              <span className=\"font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate\">\r\n                &quot;{debouncedSearchQuery}&quot;\r\n              </span>\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {debouncedSearchQuery && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={handleClearSearch}\r\n            className=\"text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1\"\r\n          >\r\n            <X className=\"h-3.5 w-3.5 mr-1\" />\r\n            Clear Search\r\n          </Button>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Business Grid */}\r\n      <div className=\"container mx-auto px-4\">\r\n        {isLoading || (!isLoading && businesses.length === 0 && isFirstRender.current) ? (\r\n          <AnimatedBusinessGridSkeleton />\r\n        ) : businesses.length > 0 ? (\r\n          <AnimatedBusinessGrid\r\n            businesses={businesses}\r\n            isAuthenticated={isAuthenticated}\r\n          />\r\n        ) : (\r\n          <motion.div\r\n            className=\"text-center py-12 px-4 bg-white dark:bg-neutral-900 rounded-xl border border-neutral-200 dark:border-neutral-800 shadow-sm\"\r\n            initial={{ opacity: 0, y: 20 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.4 }}\r\n          >\r\n            <div className=\"max-w-md mx-auto\">\r\n              <h3 className=\"text-xl font-semibold text-neutral-900 dark:text-white mb-2\">\r\n                No Businesses Found\r\n              </h3>\r\n              <p className=\"text-neutral-500 dark:text-neutral-400 mb-4\">\r\n                We couldn&apos;t find any businesses\r\n                {debouncedSearchQuery\r\n                  ? ` with \"${debouncedSearchQuery}\" in their name`\r\n                  : \" in this location\"}\r\n                . Try adjusting your search criteria.\r\n              </p>\r\n              {debouncedSearchQuery && (\r\n                <Button\r\n                  variant=\"outline\"\r\n                  className=\"mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10\"\r\n                  onClick={handleClearSearch}\r\n                >\r\n                  <X className=\"h-4 w-4 mr-2\" />\r\n                  Clear Search\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAWA;AACA;AAnBA;;;;;;;;;;AA+Be,SAAS,yBAAyB,EAC/C,UAAU,EACV,eAAe,EACf,YAAY,EACZ,aAAa,EACb,SAAS,EACT,QAAQ,EACR,iBAAiB,EACa;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB;IACpE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7D,qBAAqB;IAEvB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,CAAC,CAAC;IAEvC,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,cAAc,OAAO,IAAI,mBAAmB;YAC9C,cAAc,OAAO,GAAG;YACxB,sBAAsB,OAAO,GAAG;YAChC;QACF;QAEA,mBAAmB,OAAO,GAAG;IAC/B,GAAG;QAAC;QAAa;KAAkB;IAEnC,wFAAwF;IACxF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,IAAI,mBAAmB;YACrB,4DAA4D;YAC5D,sBAAsB,OAAO,GAAG;YAChC,cAAc,OAAO,GAAG;QAC1B;IACF,GAAG;QAAC;KAAkB,GAAG,4CAA4C;IAErE,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+EAA+E;QAC/E,IAAI,WAAW,MAAM,GAAG,KAAK,CAAC,WAAW;YACvC,cAAc,OAAO,GAAG;QAC1B;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QAEf,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,oDAAoD;QACpD,MAAM,qBACJ,mBAAmB,OAAO,CAAC,MAAM,GAAG,KAAK,UAAU;QAErD,iEAAiE;QACjE,IAAI,oBAAoB;YACtB,wBAAwB;YACxB,wDAAwD;YACxD,WAAW;gBACT,SAAS;YACX,GAAG;YACH;QACF;QAEA,wEAAwE;QACxE,IAAI,UAAU,MAAM,MAAM,MAAM,IAAI,GAAG;YACrC,iBAAiB,OAAO,GAAG,WAAW;gBACpC,wBAAwB;gBACxB,kEAAkE;gBAClE,IAAI,CAAC,cAAc,OAAO,EAAE;oBAC1B,uCAAuC;oBACvC,SAAS;gBACX,OAAO;oBACL,cAAc,OAAO,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAEA,iDAAiD;IACjD,MAAM,oBAAoB;QACxB,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,eAAe;QACf,wBAAwB;QAExB,wDAAwD;QACxD,WAAW;YACT,SAAS;QACX,GAAG;IACL;IAEA,MAAM,uBAAuB;QAC3B,oBAAoB,CAAC;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAGjB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,UAAU,CAAC;4CACT,EAAE,cAAc;4CAChB,iEAAiE;4CACjE,IAAI,gBAAgB,MAAM,YAAY,MAAM,IAAI,GAAG;gDACjD,6BAA6B;gDAC7B,IAAI,iBAAiB,OAAO,EAAE;oDAC5B,aAAa,iBAAiB,OAAO;oDACrC,iBAAiB,OAAO,GAAG;gDAC7B;gDACA,wBAAwB;gDACxB,SAAS;4CACX;wCACF;kDAEA,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC,0HAAA,CAAA,QAAK;oDACJ,aAAY;oDACZ,OAAO;oDACP,UAAU;oDACV,WAAU;;;;;;gDAEX,6BACC,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;8DAIjB,8OAAC;oDAAO,MAAK;oDAAS,WAAU;8DAAS;;;;;;;;;;;;;;;;;;;;;;8CAO/C,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,eAAe,CAAC,QAAU,aAAa;4CACvC,UAAU;;8DAEV,8OAAC,2HAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,wNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC,2HAAA,CAAA,cAAW;gEACV,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;8DAIhB,8OAAC,2HAAA,CAAA,gBAAa;;sEACZ,8OAAC,2HAAA,CAAA,cAAW;;8EACV,8OAAC,2HAAA,CAAA,cAAW;oEAAC,WAAU;8EAA2E;;;;;;8EAGlG,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAe,WAAU;8EAAgB;;;;;;8EAG3D,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAc,WAAU;8EAAgB;;;;;;;;;;;;sEAI5D,8OAAC,2HAAA,CAAA,cAAW;;8EACV,8OAAC,2HAAA,CAAA,cAAW;oEAAC,WAAU;8EAAgF;;;;;;8EAGvG,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAW,WAAU;8EAAgB;;;;;;8EAGvD,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAY,WAAU;8EAAgB;;;;;;;;;;;;sEAI1D,8OAAC,2HAAA,CAAA,cAAW;;8EACV,8OAAC,2HAAA,CAAA,cAAW;oEAAC,WAAU;8EAAgF;;;;;;8EAGvG,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAa,WAAU;8EAAgB;;;;;;8EAGzD,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAqB,WAAU;8EAAgB;;;;;;8EAGjE,8OAAC,2HAAA,CAAA,aAAU;oEAAC,OAAM;oEAAc,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;sDAQhE,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOtD,8OAAC,yLAAA,CAAA,kBAAe;sCACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCACjC,SAAS;oCAAE,SAAS;oCAAG,QAAQ;gCAAO;gCACtC,MAAM;oCAAE,SAAS;oCAAG,QAAQ;gCAAE;gCAC9B,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCAAI,WAAU;kDAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,8OAAC;wBAAI,WAAU;kCACZ,sCACC,8OAAC;;gCAAK;gCACgB;8CACpB,8OAAC;oCAAK,WAAU;;wCAAuG;wCAC9G;wCAAqB;;;;;;;;;;;;;;;;;;oBAMnC,sCACC,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAqB;;;;;;;;;;;;;0BAOxC,8OAAC;gBAAI,WAAU;0BACZ,aAAc,CAAC,aAAa,WAAW,MAAM,KAAK,KAAK,cAAc,OAAO,iBAC3E,8OAAC,0KAAA,CAAA,UAA4B;;;;2BAC3B,WAAW,MAAM,GAAG,kBACtB,8OAAC,kKAAA,CAAA,UAAoB;oBACnB,YAAY;oBACZ,iBAAiB;;;;;yCAGnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAG5E,8OAAC;gCAAE,WAAU;;oCAA8C;oCAExD,uBACG,CAAC,OAAO,EAAE,qBAAqB,eAAe,CAAC,GAC/C;oCAAoB;;;;;;;4BAGzB,sCACC,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;kDAET,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhD", "debugId": null}}, {"offset": {"line": 6404, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ModernBusinessResults.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef, useEffect } from \"react\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport ModernBusinessFilterGrid from \"./ModernBusinessFilterGrid\";\r\n\r\ninterface ModernBusinessResultsProps {\r\n  businesses: BusinessCardData[];\r\n  isAuthenticated: boolean;\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  isLoadingMore: boolean;\r\n  onLoadMore: () => void;\r\n  onSortChange: (_sortBy: BusinessSortBy) => void;\r\n  onSearch: (_searchTerm: string) => void;\r\n  currentSortBy: BusinessSortBy;\r\n  isLoading: boolean;\r\n  initialSearchTerm?: string | null;\r\n}\r\n\r\nexport default function ModernBusinessResults({\r\n  businesses,\r\n  isAuthenticated,\r\n  hasMore,\r\n  isLoadingMore,\r\n  onLoadMore,\r\n  onSortChange,\r\n  onSearch,\r\n  currentSortBy,\r\n  isLoading,\r\n  initialSearchTerm,\r\n}: ModernBusinessResultsProps) {\r\n  const observerTarget = useRef<HTMLDivElement>(null);\r\n\r\n  // Set up intersection observer for infinite scroll\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      (entries) => {\r\n        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {\r\n          onLoadMore();\r\n        }\r\n      },\r\n      { threshold: 0.1, rootMargin: \"100px\" }\r\n    );\r\n\r\n    const currentTarget = observerTarget.current;\r\n    if (currentTarget) {\r\n      observer.observe(currentTarget);\r\n    }\r\n\r\n    return () => {\r\n      if (currentTarget) {\r\n        observer.unobserve(currentTarget);\r\n      }\r\n    };\r\n  }, [hasMore, isLoadingMore, onLoadMore]);\r\n\r\n  // We'll always show the filter grid, regardless of loading state or empty results\r\n  return (\r\n    <div>\r\n      <ModernBusinessFilterGrid\r\n        businesses={businesses}\r\n        isAuthenticated={isAuthenticated}\r\n        onSortChange={onSortChange}\r\n        onSearch={onSearch}\r\n        currentSortBy={currentSortBy}\r\n        isLoading={isLoading}\r\n        initialSearchTerm={initialSearchTerm}\r\n      />\r\n\r\n      {/* Loading More Indicator - Always render the observer target */}\r\n      {!isLoading && (\r\n        <div\r\n          ref={observerTarget}\r\n          className=\"flex justify-center items-center py-8\"\r\n        >\r\n          {isLoadingMore ? (\r\n            <motion.div\r\n              className=\"flex flex-col items-center\"\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <Loader2 className=\"h-6 w-6 animate-spin text-[var(--brand-gold)]\" />\r\n              <span className=\"mt-2 text-sm text-neutral-600 dark:text-neutral-400\">\r\n                Loading more businesses...\r\n              </span>\r\n            </motion.div>\r\n          ) : hasMore ? (\r\n            <span className=\"text-xs text-neutral-500 dark:text-neutral-500\">\r\n              Scroll to load more\r\n            </span>\r\n          ) : businesses.length > 0 ? (\r\n            <span className=\"text-xs text-neutral-500 dark:text-neutral-500\">\r\n              You&apos;ve reached the end\r\n            </span>\r\n          ) : null}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AAPA;;;;;;AAuBe,SAAS,sBAAsB,EAC5C,UAAU,EACV,eAAe,EACf,OAAO,EACP,aAAa,EACb,UAAU,EACV,YAAY,EACZ,QAAQ,EACR,aAAa,EACb,SAAS,EACT,iBAAiB,EACU;IAC3B,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,IAAI,WAAW,CAAC,eAAe;gBAC1D;YACF;QACF,GACA;YAAE,WAAW;YAAK,YAAY;QAAQ;QAGxC,MAAM,gBAAgB,eAAe,OAAO;QAC5C,IAAI,eAAe;YACjB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,eAAe;gBACjB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAS;QAAe;KAAW;IAEvC,kFAAkF;IAClF,qBACE,8OAAC;;0BACC,8OAAC,sKAAA,CAAA,UAAwB;gBACvB,YAAY;gBACZ,iBAAiB;gBACjB,cAAc;gBACd,UAAU;gBACV,eAAe;gBACf,WAAW;gBACX,mBAAmB;;;;;;YAIpB,CAAC,2BACA,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAET,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAK,WAAU;sCAAsD;;;;;;;;;;;2BAItE,wBACF,8OAAC;oBAAK,WAAU;8BAAiD;;;;;2BAG/D,WAAW,MAAM,GAAG,kBACtB,8OAAC;oBAAK,WAAU;8BAAiD;;;;;2BAG/D;;;;;;;;;;;;AAKd", "debugId": null}}, {"offset": {"line": 6528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ProductListItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\n\r\ninterface ProductListItemProps {\r\n  product: ProductServiceData | NearbyProduct;\r\n  isLink?: boolean;\r\n}\r\n\r\n// Helper to format currency\r\nconst formatCurrency = (amount: number | null | undefined) => {\r\n  if (amount === null || amount === undefined) return null;\r\n  return amount.toLocaleString(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency: \"INR\",\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  });\r\n};\r\n\r\n// Animation variants\r\nconst cardContainerVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  show: { opacity: 1, y: 0 },\r\n};\r\n\r\nconst discountBadgeVariants = {\r\n  initial: { scale: 0.9, opacity: 0 },\r\n  animate: {\r\n    scale: 1,\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.5,\r\n      type: \"spring\",\r\n      stiffness: 400,\r\n      damping: 10,\r\n    },\r\n  },\r\n  hover: {\r\n    scale: 1.05,\r\n    rotate: -2,\r\n    transition: { type: \"spring\", stiffness: 500 },\r\n  },\r\n};\r\n\r\nexport default function ProductListItem({\r\n  product,\r\n  isLink = true,\r\n}: ProductListItemProps) {\r\n  // If isLink is true, we'll wrap the content in a link\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const formattedBasePrice = formatCurrency(product.base_price);\r\n  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price\r\n\r\n  // Determine final price and if there's a discount shown\r\n  let finalPrice = formattedBasePrice;\r\n  let priceToShowStrikethrough: string | null = null;\r\n  let discountPercentage = 0;\r\n\r\n  const hasDiscountedPrice =\r\n    typeof product.discounted_price === \"number\" &&\r\n    product.discounted_price > 0;\r\n  const hasBasePrice =\r\n    typeof product.base_price === \"number\" && product.base_price > 0;\r\n\r\n  if (\r\n    hasDiscountedPrice &&\r\n    hasBasePrice &&\r\n    product.discounted_price! < product.base_price!\r\n  ) {\r\n    // Scenario 1: Discounted price is valid and less than base price\r\n    finalPrice = formattedDiscountedPrice;\r\n    priceToShowStrikethrough = formattedBasePrice; // Strike through base price\r\n    discountPercentage = Math.round(\r\n      ((product.base_price! - product.discounted_price!) /\r\n        product.base_price!) *\r\n        100\r\n    );\r\n  } else {\r\n    // Scenario 2: No discount applicable, show base price\r\n    finalPrice = formattedBasePrice;\r\n    priceToShowStrikethrough = null;\r\n    discountPercentage = 0;\r\n  }\r\n\r\n  // Ensure finalPrice has a fallback if both prices are null/undefined\r\n  if (!finalPrice) {\r\n    finalPrice = \"Price unavailable\";\r\n  }\r\n\r\n  const showDiscountBadge = discountPercentage > 0;\r\n\r\n  // State for lazy loading images\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n\r\n  // Check if product is out of stock\r\n  const isOutOfStock = !product.is_available;\r\n\r\n  // Ensure we're not using business_id as a key\r\n  // Use the product's own ID for any keys needed\r\n  const content = (\r\n    <motion.div\r\n      variants={cardContainerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"show\"\r\n      className=\"w-full overflow-hidden\"\r\n    >\r\n      <div className=\"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg\">\r\n        <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n          {/* Image container */}\r\n          <div className=\"relative w-full overflow-hidden rounded-t-xl\">\r\n            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}\r\n            {/* Determine the image URL to use */}\r\n            {(() => {\r\n              // Get the image URL to display\r\n              let imageUrl = product.image_url;\r\n\r\n              // If product has images array and it's not empty, use the featured image\r\n              if (product.images && Array.isArray(product.images) && product.images.length > 0) {\r\n                const featuredIndex = typeof product.featured_image_index === 'number'\r\n                  ? Math.min(product.featured_image_index, product.images.length - 1)\r\n                  : 0;\r\n                imageUrl = product.images[featuredIndex];\r\n              }\r\n\r\n              if (imageUrl && !imageError) {\r\n                return (\r\n                  <div className=\"overflow-hidden\">\r\n                    {!isImageLoaded && (\r\n                      <Skeleton className=\"absolute inset-0 rounded-t-xl\" />\r\n                    )}\r\n                    <motion.div className=\"w-full\">\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={product.name ?? \"Product image\"}\r\n                        width={500}\r\n                        height={750}\r\n                        className={`w-full aspect-square object-cover ${\r\n                          isOutOfStock\r\n                            ? \"filter grayscale opacity-70 transition-all duration-500\"\r\n                            : \"\"\r\n                        } ${\r\n                          isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                        } max-w-full`}\r\n                        loading=\"lazy\"\r\n                        onError={() => setImageError(true)}\r\n                        onLoad={() => setIsImageLoaded(true)}\r\n                        quality={80}\r\n                        blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=\"\r\n                        placeholder=\"blur\"\r\n                        style={{ objectFit: \"cover\" }}\r\n                      />\r\n                    </motion.div>\r\n                  </div>\r\n                );\r\n              } else {\r\n                return (\r\n                  <div className=\"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl\">\r\n                    <svg\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={1}\r\n                        d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                );\r\n              }\r\n            })()}\r\n\r\n            {/* Out of Stock Overlay */}\r\n            {isOutOfStock && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40\">\r\n                <div className=\"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground\">\r\n                  <span className=\"font-medium tracking-wide uppercase text-xs sm:text-sm\">\r\n                    Out of Stock\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Discount Badge Overlay */}\r\n            {showDiscountBadge && (\r\n              <AnimatePresence>\r\n                <motion.div\r\n                  key={`discount-badge-${product.id}`}\r\n                  variants={discountBadgeVariants}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  whileHover=\"hover\"\r\n                  className={cn(\r\n                    \"absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg\",\r\n                    \"bg-destructive\",\r\n                    \"text-destructive-foreground border border-destructive-foreground/20\",\r\n                    \"transform-gpu\"\r\n                  )}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center\">\r\n                    <span className=\"text-[7px] sm:text-[9px] md:text-[10px] font-medium\">\r\n                      SAVE\r\n                    </span>\r\n                    <span className=\"text-[9px] sm:text-xs md:text-sm leading-none\">\r\n                      {discountPercentage}%\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n              </AnimatePresence>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1\">\r\n            {/* Title */}\r\n            <p className=\"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden\">\r\n              {product.name ?? \"Unnamed Product\"}\r\n            </p>\r\n\r\n            {/* Description (optional) */}\r\n            {product.description && (\r\n              <p className=\"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate\">\r\n                {product.description}\r\n              </p>\r\n            )}\r\n\r\n            {/* Price and Badge Container */}\r\n            <div className=\"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1\">\r\n              {/* Price Group */}\r\n              <div className=\"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full\">\r\n                {finalPrice && (\r\n                  <p className=\"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full\">\r\n                    {finalPrice}\r\n                  </p>\r\n                )}\r\n                {priceToShowStrikethrough && (\r\n                  <p className=\"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500\">\r\n                    {priceToShowStrikethrough}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Product Type Badge removed as per instructions */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  // If isLink is true, wrap the content in a link to the product detail page\r\n  if (isLink && \"business_slug\" in product && product.business_slug) {\r\n    return (\r\n      <Link\r\n        href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n        className=\"block h-full\"\r\n      >\r\n        {content}\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  // Otherwise, just return the content\r\n  return content;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;AARA;;;;;;;;AAgBA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;IACpD,OAAO,OAAO,cAAc,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEA,qBAAqB;AACrB,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAE;AAC3B;AAEA,MAAM,wBAAwB;IAC5B,SAAS;QAAE,OAAO;QAAK,SAAS;IAAE;IAClC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,CAAC;QACT,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;IAC/C;AACF;AAEe,SAAS,gBAAgB,EACtC,OAAO,EACP,SAAS,IAAI,EACQ;IACrB,sDAAsD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,eAAe,QAAQ,UAAU;IAC5D,MAAM,2BAA2B,eAAe,QAAQ,gBAAgB,GAAG,0BAA0B;IAErG,wDAAwD;IACxD,IAAI,aAAa;IACjB,IAAI,2BAA0C;IAC9C,IAAI,qBAAqB;IAEzB,MAAM,qBACJ,OAAO,QAAQ,gBAAgB,KAAK,YACpC,QAAQ,gBAAgB,GAAG;IAC7B,MAAM,eACJ,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;IAEjE,IACE,sBACA,gBACA,QAAQ,gBAAgB,GAAI,QAAQ,UAAU,EAC9C;QACA,iEAAiE;QACjE,aAAa;QACb,2BAA2B,oBAAoB,4BAA4B;QAC3E,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,QAAQ,UAAU,GAAI,QAAQ,gBAAgB,AAAC,IAC/C,QAAQ,UAAU,GAClB;IAEN,OAAO;QACL,sDAAsD;QACtD,aAAa;QACb,2BAA2B;QAC3B,qBAAqB;IACvB;IAEA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,aAAa;IACf;IAEA,MAAM,oBAAoB,qBAAqB;IAE/C,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,MAAM,eAAe,CAAC,QAAQ,YAAY;IAE1C,8CAA8C;IAC9C,+CAA+C;IAC/C,MAAM,wBACJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;4BAGZ,CAAC;gCACA,+BAA+B;gCAC/B,IAAI,WAAW,QAAQ,SAAS;gCAEhC,yEAAyE;gCACzE,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oCAChF,MAAM,gBAAgB,OAAO,QAAQ,oBAAoB,KAAK,WAC1D,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,KAC/D;oCACJ,WAAW,QAAQ,MAAM,CAAC,cAAc;gCAC1C;gCAEA,IAAI,YAAY,CAAC,YAAY;oCAC3B,qBACE,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;0DACpB,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,QAAQ,IAAI,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAW,CAAC,kCAAkC,EAC5C,eACI,4DACA,GACL,CAAC,EACA,gBAAgB,gBAAgB,YACjC,WAAW,CAAC;oDACb,SAAQ;oDACR,SAAS,IAAM,cAAc;oDAC7B,QAAQ,IAAM,iBAAiB;oDAC/B,SAAS;oDACT,aAAY;oDACZ,aAAY;oDACZ,OAAO;wDAAE,WAAW;oDAAQ;;;;;;;;;;;;;;;;;gCAKtC,OAAO;oCACL,qBACE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;gCAKZ;4BACF,CAAC;4BAGA,8BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;;;;;4BAQ9E,mCACC,8OAAC,yLAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qIACA,kBACA,uEACA;8CAGF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsD;;;;;;0DAGtE,8OAAC;gDAAK,WAAU;;oDACb;oDAAmB;;;;;;;;;;;;;mCAjBnB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;kCA0B3C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI,IAAI;;;;;;4BAIlB,QAAQ,WAAW,kBAClB,8OAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,4BACC,8OAAC;4CAAE,WAAU;sDACV;;;;;;wCAGJ,0CACC,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAanB,2EAA2E;IAC3E,IAAI,UAAU,mBAAmB,WAAW,QAAQ,aAAa,EAAE;QACjE,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;YACvE,WAAU;sBAET;;;;;;IAGP;IAEA,qCAAqC;IACrC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 6888, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ProductGridSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport default function ProductGridSkeleton() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Product Grid Skeleton */}\r\n      <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-3 md:gap-4\">\r\n        {Array.from({ length: 6 }).map((_, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl overflow-hidden bg-white dark:bg-neutral-900 transition-all duration-300 w-full\"\r\n          >\r\n            <Skeleton className=\"h-48 w-full\" />\r\n            <div className=\"p-2 sm:p-3 md:p-4 space-y-2 sm:space-y-3\">\r\n              <Skeleton className=\"h-4 sm:h-5 w-3/4\" />\r\n              <Skeleton className=\"h-3 sm:h-4 w-1/2\" />\r\n              <div className=\"flex justify-between items-center pt-1 sm:pt-2\">\r\n                <Skeleton className=\"h-5 sm:h-6 w-16 sm:w-20\" />\r\n                <Skeleton className=\"h-6 sm:h-8 w-6 sm:w-8 rounded-full\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;mBATnB;;;;;;;;;;;;;;;AAiBjB", "debugId": null}}, {"offset": {"line": 6982, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ProductGrid.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\";\r\nimport { NearbyProduct } from \"../actions/types\";\r\nimport ProductListItem from \"@/app/components/ProductListItem\";\r\nimport ProductGridSkeleton from \"./ProductGridSkeleton\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Filter, Search, SortAsc, X } from \"lucide-react\";\r\nimport { ProductSortOption, ProductFilterOption } from \"../context/types\";\r\n\r\ninterface ProductGridProps {\r\n  products: NearbyProduct[];\r\n  onSortChange: (_sortBy: ProductSortOption) => void;\r\n  onFilterChange: (_filterBy: ProductFilterOption) => void;\r\n  currentSortBy: ProductSortOption;\r\n  currentFilterBy: ProductFilterOption;\r\n  isLoading: boolean;\r\n  onSearch: (_searchTerm: string) => void;\r\n  initialSearchTerm?: string | null;\r\n}\r\n\r\nexport default function ProductGrid({\r\n  products,\r\n  onSortChange,\r\n  onFilterChange,\r\n  currentSortBy,\r\n  currentFilterBy,\r\n  isLoading,\r\n  onSearch,\r\n  initialSearchTerm,\r\n}: ProductGridProps) {\r\n  const [searchQuery, setSearchQuery] = useState(initialSearchTerm || \"\");\r\n  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(\r\n    initialSearchTerm || \"\"\r\n  );\r\n  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const isFirstRender = useRef(true);\r\n  const prevSearchQueryRef = useRef(searchQuery);\r\n  const wasInitiallyPopulated = useRef(!!initialSearchTerm);\r\n\r\n  // Update prevSearchQueryRef when component mounts or searchQuery changes\r\n  useEffect(() => {\r\n    // Skip the first update if initialSearchTerm was provided\r\n    if (isFirstRender.current && initialSearchTerm) {\r\n      isFirstRender.current = false;\r\n      wasInitiallyPopulated.current = true;\r\n      return;\r\n    }\r\n\r\n    prevSearchQueryRef.current = searchQuery;\r\n  }, [searchQuery, initialSearchTerm]);\r\n\r\n  // Prevent infinite loops by tracking if we've already processed the initial search term\r\n  useEffect(() => {\r\n    // This effect runs only once on mount\r\n    if (initialSearchTerm) {\r\n      // Mark that we've already processed the initial search term\r\n      wasInitiallyPopulated.current = true;\r\n      isFirstRender.current = false;\r\n    }\r\n  }, [initialSearchTerm]); // Include initialSearchTerm as a dependency\r\n\r\n  // Update isFirstRender when data is loaded\r\n  useEffect(() => {\r\n    // If we have products or isLoading is false, we're no longer on first render\r\n    if (products.length > 0 || !isLoading) {\r\n      isFirstRender.current = false;\r\n    }\r\n  }, [products, isLoading]);\r\n\r\n  // Handle input change with manual debounce\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const query = e.target.value;\r\n    setSearchQuery(query);\r\n\r\n    // Clear any existing timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n      searchTimeoutRef.current = null;\r\n    }\r\n\r\n    // Check if the user has manually cleared the search\r\n    const wasManuallyCleared =\r\n      prevSearchQueryRef.current.length > 0 && query === \"\";\r\n\r\n    // If the search was manually cleared, trigger search immediately\r\n    if (wasManuallyCleared) {\r\n      setDebouncedSearchQuery(\"\");\r\n      // Add a small visual delay before triggering the search\r\n      setTimeout(() => {\r\n        onSearch(\"\");\r\n      }, 100);\r\n      return;\r\n    }\r\n\r\n    // Only set a new timeout if the query is empty or at least 3 characters\r\n    if (query === \"\" || query.length >= 3) {\r\n      searchTimeoutRef.current = setTimeout(() => {\r\n        setDebouncedSearchQuery(query);\r\n        // Skip search on initial render if initialSearchTerm was provided\r\n        if (!isFirstRender.current) {\r\n          // Call the server-side search function\r\n          onSearch(query);\r\n        } else {\r\n          isFirstRender.current = false;\r\n        }\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  // Handle clear search when clicking the X button\r\n  const handleClearSearch = () => {\r\n    // Clear any existing timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n      searchTimeoutRef.current = null;\r\n    }\r\n\r\n    setSearchQuery(\"\");\r\n    setDebouncedSearchQuery(\"\");\r\n\r\n    // Add a small visual delay before triggering the search\r\n    setTimeout(() => {\r\n      onSearch(\"\");\r\n    }, 100);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6 container mx-auto px-4\">\r\n      {/* Search and Filter Controls */}\r\n      <motion.div\r\n        className=\"relative overflow-hidden bg-transparent p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.4 }}\r\n        whileHover={{ borderColor: \"rgba(var(--brand-gold-rgb), 0.3)\" }}\r\n      >\r\n        {/* Decorative elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none opacity-70\">\r\n          <div className=\"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-2xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10\"></div>\r\n          <div className=\"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-2xl dark:from-purple-500/10 dark:to-blue-500/10\"></div>\r\n        </div>\r\n\r\n        <div className=\"relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between\">\r\n          {/* Search Input */}\r\n          <div className=\"relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md\">\r\n            <form\r\n              onSubmit={(e) => {\r\n                e.preventDefault();\r\n                // Only trigger search if query is empty or at least 3 characters\r\n                if (searchQuery === \"\" || searchQuery.length >= 3) {\r\n                  // Clear any existing timeout\r\n                  if (searchTimeoutRef.current) {\r\n                    clearTimeout(searchTimeoutRef.current);\r\n                    searchTimeoutRef.current = null;\r\n                  }\r\n                  setDebouncedSearchQuery(searchQuery);\r\n                  onSearch(searchQuery);\r\n                }\r\n              }}\r\n            >\r\n              <div className=\"relative group\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200\" />\r\n                <Input\r\n                  placeholder=\"Search products...\"\r\n                  value={searchQuery}\r\n                  onChange={handleSearchChange}\r\n                  className=\"pl-10 h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\"\r\n                />\r\n                {searchQuery && (\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleClearSearch}\r\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200\"\r\n                  >\r\n                    <X className=\"h-4 w-4\" />\r\n                  </button>\r\n                )}\r\n                {/* Hidden submit button for form submission */}\r\n                <button type=\"submit\" className=\"hidden\">\r\n                  Search\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          <div className=\"flex flex-row gap-2 sm:gap-3 w-full sm:w-auto\">\r\n            {/* Filter Dropdown */}\r\n            <Select\r\n              value={currentFilterBy}\r\n              onValueChange={(value) =>\r\n                onFilterChange(value as ProductFilterOption)\r\n              }\r\n              disabled={isLoading}\r\n            >\r\n              <SelectTrigger className=\"w-full sm:min-w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\">\r\n                <Filter className=\"mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]\" />\r\n                <SelectValue\r\n                  placeholder=\"Filter\"\r\n                  className=\"text-xs sm:text-sm\"\r\n                />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5\">\r\n                    Product Type\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"all\" className=\"relative pl-8\">\r\n                    All Products\r\n                  </SelectItem>\r\n                  <SelectItem value=\"physical\" className=\"relative pl-8\">\r\n                    Physical Items\r\n                  </SelectItem>\r\n                  <SelectItem value=\"service\" className=\"relative pl-8\">\r\n                    Services\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n              </SelectContent>\r\n            </Select>\r\n\r\n            {/* Sort Dropdown */}\r\n            <Select\r\n              value={currentSortBy}\r\n              onValueChange={(value) =>\r\n                onSortChange(value as ProductSortOption)\r\n              }\r\n              disabled={isLoading}\r\n            >\r\n              <SelectTrigger className=\"w-full sm:min-w-[160px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\">\r\n                <SortAsc className=\"mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]\" />\r\n                <SelectValue\r\n                  placeholder=\"Sort by\"\r\n                  className=\"text-xs sm:text-sm\"\r\n                />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5\">\r\n                    Date\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"newest\" className=\"relative pl-8\">\r\n                    Newest First\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1\">\r\n                    Price\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"price_low\" className=\"relative pl-8\">\r\n                    Price: Low to High\r\n                  </SelectItem>\r\n                  <SelectItem value=\"price_high\" className=\"relative pl-8\">\r\n                    Price: High to Low\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1\">\r\n                    Name\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"name_asc\" className=\"relative pl-8\">\r\n                    Name: A to Z\r\n                  </SelectItem>\r\n                  <SelectItem value=\"name_desc\" className=\"relative pl-8\">\r\n                    Name: Z to A\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Results Count */}\r\n      <motion.div\r\n        className=\"flex items-center justify-between px-2\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ duration: 0.3, delay: 0.2 }}\r\n      >\r\n        <div className=\"text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate\">\r\n          {debouncedSearchQuery && (\r\n            <span>\r\n              Showing results for{\" \"}\r\n              <span className=\"font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate\">\r\n                &quot;{debouncedSearchQuery}&quot;\r\n              </span>\r\n            </span>\r\n          )}\r\n          {currentFilterBy !== \"all\" && (\r\n            <span>\r\n              {\" \"}\r\n              •{\" \"}\r\n              <span className=\"font-medium text-neutral-700 dark:text-neutral-300\">\r\n                {currentFilterBy === \"physical\" ? \"Physical Items\" : \"Services\"}\r\n              </span>\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {debouncedSearchQuery && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={handleClearSearch}\r\n            className=\"text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1\"\r\n          >\r\n            <X className=\"h-3.5 w-3.5 mr-1\" />\r\n            Clear Search\r\n          </Button>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Product Grid */}\r\n      {isLoading || (!isLoading && products.length === 0 && isFirstRender.current) ? (\r\n        <ProductGridSkeleton />\r\n      ) : products.length > 0 ? (\r\n        <div\r\n          className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4\"\r\n        >\r\n          {products.map((product, index) => {\r\n            // Use the product's ID as the key\r\n            // Each product has a unique ID in the database\r\n            const uniqueKey = `product-${product.id}`;\r\n\r\n            return (\r\n              <motion.div\r\n                key={uniqueKey}\r\n                initial={{ opacity: 0, y: 20 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{ duration: 0.3, delay: index * 0.05 }}\r\n                className=\"group\"\r\n              >\r\n                {product.business_slug ? (\r\n                  <Link\r\n                    href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n                    className=\"block h-full\"\r\n                  >\r\n                    <div className=\"h-full\">\r\n                      <ProductListItem product={product} isLink={false} />\r\n                    </div>\r\n                  </Link>\r\n                ) : (\r\n                  <div className=\"relative h-full\">\r\n                    <ProductListItem product={product} isLink={false} />\r\n                    <div className=\"absolute bottom-0 left-0 right-0 bg-red-500/80 text-white text-xs py-1 px-2 text-center rounded-b-md\">\r\n                      Unable to link to business\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </motion.div>\r\n            );\r\n          })}\r\n        </div>\r\n      ) : (\r\n        <motion.div\r\n          className=\"text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n        >\r\n          <div className=\"max-w-md mx-auto\">\r\n            <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2\">\r\n              No Products Found\r\n            </h3>\r\n            <p className=\"text-neutral-500 dark:text-neutral-400 mb-4\">\r\n              We couldn&apos;t find any products\r\n              {debouncedSearchQuery\r\n                ? ` with \"${debouncedSearchQuery}\" in the name`\r\n                : \"\"}\r\n              {currentFilterBy !== \"all\" &&\r\n                ` in the ${\r\n                  currentFilterBy === \"physical\" ? \"Physical Items\" : \"Services\"\r\n                } category`}\r\n              . Try adjusting your search criteria or browse all products.\r\n            </p>\r\n            {debouncedSearchQuery && (\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10\"\r\n                onClick={handleClearSearch}\r\n              >\r\n                <X className=\"h-4 w-4 mr-2\" />\r\n                Clear Filters\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AASA;AAAA;AAAA;AAAA;AAnBA;;;;;;;;;;;AAiCe,SAAS,YAAY,EAClC,QAAQ,EACR,YAAY,EACZ,cAAc,EACd,aAAa,EACb,eAAe,EACf,SAAS,EACT,QAAQ,EACR,iBAAiB,EACA;IACjB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,qBAAqB;IACpE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7D,qBAAqB;IAEvB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC7B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE,CAAC,CAAC;IAEvC,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0DAA0D;QAC1D,IAAI,cAAc,OAAO,IAAI,mBAAmB;YAC9C,cAAc,OAAO,GAAG;YACxB,sBAAsB,OAAO,GAAG;YAChC;QACF;QAEA,mBAAmB,OAAO,GAAG;IAC/B,GAAG;QAAC;QAAa;KAAkB;IAEnC,wFAAwF;IACxF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sCAAsC;QACtC,IAAI,mBAAmB;YACrB,4DAA4D;YAC5D,sBAAsB,OAAO,GAAG;YAChC,cAAc,OAAO,GAAG;QAC1B;IACF,GAAG;QAAC;KAAkB,GAAG,4CAA4C;IAErE,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6EAA6E;QAC7E,IAAI,SAAS,MAAM,GAAG,KAAK,CAAC,WAAW;YACrC,cAAc,OAAO,GAAG;QAC1B;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QAEf,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,oDAAoD;QACpD,MAAM,qBACJ,mBAAmB,OAAO,CAAC,MAAM,GAAG,KAAK,UAAU;QAErD,iEAAiE;QACjE,IAAI,oBAAoB;YACtB,wBAAwB;YACxB,wDAAwD;YACxD,WAAW;gBACT,SAAS;YACX,GAAG;YACH;QACF;QAEA,wEAAwE;QACxE,IAAI,UAAU,MAAM,MAAM,MAAM,IAAI,GAAG;YACrC,iBAAiB,OAAO,GAAG,WAAW;gBACpC,wBAAwB;gBACxB,kEAAkE;gBAClE,IAAI,CAAC,cAAc,OAAO,EAAE;oBAC1B,uCAAuC;oBACvC,SAAS;gBACX,OAAO;oBACL,cAAc,OAAO,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAEA,iDAAiD;IACjD,MAAM,oBAAoB;QACxB,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,eAAe;QACf,wBAAwB;QAExB,wDAAwD;QACxD,WAAW;YACT,SAAS;QACX,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,YAAY;oBAAE,aAAa;gBAAmC;;kCAG9D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,UAAU,CAAC;wCACT,EAAE,cAAc;wCAChB,iEAAiE;wCACjE,IAAI,gBAAgB,MAAM,YAAY,MAAM,IAAI,GAAG;4CACjD,6BAA6B;4CAC7B,IAAI,iBAAiB,OAAO,EAAE;gDAC5B,aAAa,iBAAiB,OAAO;gDACrC,iBAAiB,OAAO,GAAG;4CAC7B;4CACA,wBAAwB;4CACxB,SAAS;wCACX;oCACF;8CAEA,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU;gDACV,WAAU;;;;;;4CAEX,6BACC,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;0DAIjB,8OAAC;gDAAO,MAAK;gDAAS,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe,CAAC,QACd,eAAe;wCAEjB,UAAU;;0DAEV,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,2HAAA,CAAA,cAAW;wDACV,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,8OAAC,2HAAA,CAAA,gBAAa;0DACZ,cAAA,8OAAC,2HAAA,CAAA,cAAW;;sEACV,8OAAC,2HAAA,CAAA,cAAW;4DAAC,WAAU;sEAA2E;;;;;;sEAGlG,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;4DAAM,WAAU;sEAAgB;;;;;;sEAGlD,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;4DAAW,WAAU;sEAAgB;;;;;;sEAGvD,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;4DAAU,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;kDAQ5D,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe,CAAC,QACd,aAAa;wCAEf,UAAU;;0DAEV,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,8NAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,8OAAC,2HAAA,CAAA,cAAW;wDACV,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAGd,8OAAC,2HAAA,CAAA,gBAAa;;kEACZ,8OAAC,2HAAA,CAAA,cAAW;;0EACV,8OAAC,2HAAA,CAAA,cAAW;gEAAC,WAAU;0EAA2E;;;;;;0EAGlG,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAS,WAAU;0EAAgB;;;;;;;;;;;;kEAIvD,8OAAC,2HAAA,CAAA,cAAW;;0EACV,8OAAC,2HAAA,CAAA,cAAW;gEAAC,WAAU;0EAAgF;;;;;;0EAGvG,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAY,WAAU;0EAAgB;;;;;;0EAGxD,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAa,WAAU;0EAAgB;;;;;;;;;;;;kEAI3D,8OAAC,2HAAA,CAAA,cAAW;;0EACV,8OAAC,2HAAA,CAAA,cAAW;gEAAC,WAAU;0EAAgF;;;;;;0EAGvG,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAW,WAAU;0EAAgB;;;;;;0EAGvD,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAY,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWpE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,8OAAC;wBAAI,WAAU;;4BACZ,sCACC,8OAAC;;oCAAK;oCACgB;kDACpB,8OAAC;wCAAK,WAAU;;4CAAuG;4CAC9G;4CAAqB;;;;;;;;;;;;;4BAIjC,oBAAoB,uBACnB,8OAAC;;oCACE;oCAAI;oCACH;kDACF,8OAAC;wCAAK,WAAU;kDACb,oBAAoB,aAAa,mBAAmB;;;;;;;;;;;;;;;;;;oBAM5D,sCACC,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAqB;;;;;;;;;;;;;YAOvC,aAAc,CAAC,aAAa,SAAS,MAAM,KAAK,KAAK,cAAc,OAAO,iBACzE,8OAAC,iKAAA,CAAA,UAAmB;;;;uBAClB,SAAS,MAAM,GAAG,kBACpB,8OAAC;gBACC,WAAU;0BAET,SAAS,GAAG,CAAC,CAAC,SAAS;oBACtB,kCAAkC;oBAClC,+CAA+C;oBAC/C,MAAM,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;oBAEzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAK;wBACjD,WAAU;kCAET,QAAQ,aAAa,iBACpB,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;4BACvE,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,qIAAA,CAAA,UAAe;oCAAC,SAAS;oCAAS,QAAQ;;;;;;;;;;;;;;;iDAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,UAAe;oCAAC,SAAS;oCAAS,QAAQ;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CAAuG;;;;;;;;;;;;uBAlBrH;;;;;gBAyBX;;;;;qCAGF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;;gCAA8C;gCAExD,uBACG,CAAC,OAAO,EAAE,qBAAqB,aAAa,CAAC,GAC7C;gCACH,oBAAoB,SACnB,CAAC,QAAQ,EACP,oBAAoB,aAAa,mBAAmB,WACrD,SAAS,CAAC;gCAAC;;;;;;;wBAGf,sCACC,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;;8CAET,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 7712, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ProductResults.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRef, useEffect } from \"react\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { NearbyProduct } from \"../actions/types\";\r\nimport ProductGrid from \"./ProductGrid\";\r\nimport { ProductSortOption, ProductFilterOption } from \"../context/types\";\r\n\r\ninterface ProductResultsProps {\r\n  products: NearbyProduct[];\r\n  totalCount: number;\r\n  hasMore: boolean;\r\n  isLoadingMore: boolean;\r\n  onLoadMore: () => void;\r\n  onSortChange: (_sortBy: ProductSortOption) => void;\r\n  onFilterChange: (_filterBy: ProductFilterOption) => void;\r\n  onSearch: (_searchTerm: string) => void;\r\n  currentSortBy: ProductSortOption;\r\n  currentFilterBy: ProductFilterOption;\r\n  isLoading: boolean;\r\n  initialSearchTerm?: string | null;\r\n}\r\n\r\nexport default function ProductResults({\r\n  products,\r\n  hasMore,\r\n  isLoadingMore,\r\n  onLoadMore,\r\n  onSortChange,\r\n  onFilterChange,\r\n  onSearch,\r\n  currentSortBy,\r\n  currentFilterBy,\r\n  isLoading,\r\n  initialSearchTerm,\r\n}: ProductResultsProps) {\r\n  const observerTarget = useRef<HTMLDivElement>(null);\r\n\r\n  // Set up intersection observer for infinite scroll\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      (entries) => {\r\n        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {\r\n          onLoadMore();\r\n        }\r\n      },\r\n      { threshold: 0.1, rootMargin: \"100px\" }\r\n    );\r\n\r\n    const currentTarget = observerTarget.current;\r\n    if (currentTarget) {\r\n      observer.observe(currentTarget);\r\n    }\r\n\r\n    return () => {\r\n      if (currentTarget) {\r\n        observer.unobserve(currentTarget);\r\n      }\r\n    };\r\n  }, [hasMore, isLoadingMore, onLoadMore]);\r\n\r\n  // We'll always show the ProductGrid with filters, regardless of loading state or empty results\r\n  return (\r\n    <div>\r\n      <ProductGrid\r\n        products={products}\r\n        onSortChange={onSortChange}\r\n        onFilterChange={onFilterChange}\r\n        onSearch={onSearch}\r\n        currentSortBy={currentSortBy}\r\n        currentFilterBy={currentFilterBy}\r\n        isLoading={isLoading}\r\n        initialSearchTerm={initialSearchTerm}\r\n      />\r\n\r\n      {/* Loading More Indicator - Only render when not loading */}\r\n      {!isLoading && (\r\n        /* Loading More Indicator - Only render when we have products and not loading */\r\n        <div\r\n          ref={observerTarget}\r\n          className=\"flex justify-center items-center py-8\"\r\n        >\r\n          {isLoadingMore ? (\r\n            <motion.div\r\n              className=\"flex flex-col items-center\"\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.3 }}\r\n            >\r\n              <Loader2 className=\"h-6 w-6 animate-spin text-[var(--brand-gold)]\" />\r\n              <span className=\"mt-2 text-sm text-neutral-600 dark:text-neutral-400\">\r\n                Loading more products...\r\n              </span>\r\n              <span className=\"text-xs text-neutral-500 dark:text-neutral-500 mt-1\">\r\n                Loading more items...\r\n              </span>\r\n            </motion.div>\r\n          ) : hasMore ? (\r\n            <div className=\"h-10 w-full bg-transparent\" />\r\n          ) : (\r\n            <motion.div\r\n              className=\"text-sm text-neutral-500 dark:text-neutral-400 py-4\"\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.3, delay: 0.2 }}\r\n            >\r\n              {products.length > 0 ? \"You've reached the end of the list\" : \"\"}\r\n            </motion.div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAwBe,SAAS,eAAe,EACrC,QAAQ,EACR,OAAO,EACP,aAAa,EACb,UAAU,EACV,YAAY,EACZ,cAAc,EACd,QAAQ,EACR,aAAa,EACb,eAAe,EACf,SAAS,EACT,iBAAiB,EACG;IACpB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,IAAI,WAAW,CAAC,eAAe;gBAC1D;YACF;QACF,GACA;YAAE,WAAW;YAAK,YAAY;QAAQ;QAGxC,MAAM,gBAAgB,eAAe,OAAO;QAC5C,IAAI,eAAe;YACjB,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,eAAe;gBACjB,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAS;QAAe;KAAW;IAEvC,+FAA+F;IAC/F,qBACE,8OAAC;;0BACC,8OAAC,yJAAA,CAAA,UAAW;gBACV,UAAU;gBACV,cAAc;gBACd,gBAAgB;gBAChB,UAAU;gBACV,eAAe;gBACf,iBAAiB;gBACjB,WAAW;gBACX,mBAAmB;;;;;;YAIpB,CAAC,aACA,8EAA8E,iBAC9E,8OAAC;gBACC,KAAK;gBACL,WAAU;0BAET,8BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAK,WAAU;sCAAsD;;;;;;sCAGtE,8OAAC;4BAAK,WAAU;sCAAsD;;;;;;;;;;;2BAItE,wBACF,8OAAC;oBAAI,WAAU;;;;;yCAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC,SAAS,MAAM,GAAG,IAAI,uCAAuC;;;;;;;;;;;;;;;;;AAO5E", "debugId": null}}, {"offset": {"line": 7854, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/LocationIndicator.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { MapPin, Globe, Building2 } from \"lucide-react\";\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM } from \"../constants/urlParamConstants\";\r\nimport { useDiscoverContext } from \"../context/DiscoverContext\";\r\n\r\nexport default function LocationIndicator() {\r\n  const searchParams = useSearchParams();\r\n  const { searchResult, viewType } = useDiscoverContext();\r\n\r\n  const pincode = searchParams.get(PINCODE_PARAM);\r\n  const city = searchParams.get(CITY_PARAM);\r\n  const locality = searchParams.get(LOCALITY_PARAM);\r\n\r\n  // Determine the location text and icon based on search parameters\r\n  let locationText = \"All over India\";\r\n  let LocationIcon = Globe;\r\n  let detailText = \"Showing businesses and products from across the country\";\r\n  let highlightText = \"\";\r\n\r\n  if (pincode) {\r\n    locationText = `Pincode: ${pincode}`;\r\n    LocationIcon = MapPin;\r\n\r\n    // Add locality if available\r\n    if (locality && locality !== \"_any\") {\r\n      highlightText = locality;\r\n      locationText += `, ${locality}`;\r\n    }\r\n\r\n    // Add city and state if available from search result\r\n    if (searchResult?.location?.city) {\r\n      detailText = `${searchResult.location.city}, ${searchResult.location.state}`;\r\n      if (!highlightText) {\r\n        highlightText = searchResult.location.city;\r\n      }\r\n    } else {\r\n      detailText = \"Showing nearby businesses and products\";\r\n    }\r\n  } else if (city) {\r\n    locationText = `${city}`;\r\n    LocationIcon = Building2;\r\n    highlightText = city;\r\n    detailText = \"Showing businesses and products in this city\";\r\n  }\r\n\r\n  // Customize detail text based on view type\r\n  if (viewType === \"cards\") {\r\n    detailText = detailText.replace(\"businesses and products\", \"businesses\");\r\n  } else if (viewType === \"products\") {\r\n    detailText = detailText.replace(\"businesses and products\", \"products/services\");\r\n  }\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"container mx-auto px-4 mb-6\"\r\n      initial={{ opacity: 0, y: -10 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      <div className=\"bg-white/80 dark:bg-neutral-900/80 backdrop-blur-md rounded-xl border border-neutral-200/50 dark:border-neutral-800/50 p-4 flex items-center justify-center shadow-sm relative overflow-hidden\">\r\n        {/* Decorative elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none opacity-50\">\r\n          <div className=\"absolute -top-12 -right-12 w-24 h-24 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/5 to-amber-500/5 blur-xl dark:from-[var(--brand-gold)]/10 dark:to-amber-500/10\"></div>\r\n          <div className=\"absolute -bottom-12 -left-12 w-24 h-24 rounded-full bg-gradient-to-tr from-purple-500/5 to-blue-500/5 blur-xl dark:from-purple-500/10 dark:to-blue-500/10\"></div>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col items-center text-center relative z-10\">\r\n          <div className=\"flex items-center gap-2 mb-1\">\r\n            <div className=\"relative\">\r\n              <div className=\"absolute inset-0 bg-[var(--brand-gold)]/20 rounded-full blur-sm\"></div>\r\n              <LocationIcon className=\"h-5 w-5 text-[var(--brand-gold)] relative z-10\" />\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-200\">\r\n              {locationText}\r\n            </h3>\r\n          </div>\r\n          <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\r\n            {highlightText ?\r\n              detailText.split(highlightText).map((part, i, arr) =>\r\n                i === arr.length - 1 ? (\r\n                  <span key={i}>{part}</span>\r\n                ) : (\r\n                  <span key={i}>\r\n                    {part}\r\n                    <span className=\"font-medium text-[var(--brand-gold)]\">{highlightText}</span>\r\n                  </span>\r\n                )\r\n              )\r\n              : detailText\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD;IAEpD,MAAM,UAAU,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa;IAC9C,MAAM,OAAO,aAAa,GAAG,CAAC,6JAAA,CAAA,aAAU;IACxC,MAAM,WAAW,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc;IAEhD,kEAAkE;IAClE,IAAI,eAAe;IACnB,IAAI,eAAe,oMAAA,CAAA,QAAK;IACxB,IAAI,aAAa;IACjB,IAAI,gBAAgB;IAEpB,IAAI,SAAS;QACX,eAAe,CAAC,SAAS,EAAE,SAAS;QACpC,eAAe,0MAAA,CAAA,SAAM;QAErB,4BAA4B;QAC5B,IAAI,YAAY,aAAa,QAAQ;YACnC,gBAAgB;YAChB,gBAAgB,CAAC,EAAE,EAAE,UAAU;QACjC;QAEA,qDAAqD;QACrD,IAAI,cAAc,UAAU,MAAM;YAChC,aAAa,GAAG,aAAa,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,QAAQ,CAAC,KAAK,EAAE;YAC5E,IAAI,CAAC,eAAe;gBAClB,gBAAgB,aAAa,QAAQ,CAAC,IAAI;YAC5C;QACF,OAAO;YACL,aAAa;QACf;IACF,OAAO,IAAI,MAAM;QACf,eAAe,GAAG,MAAM;QACxB,eAAe,gNAAA,CAAA,YAAS;QACxB,gBAAgB;QAChB,aAAa;IACf;IAEA,2CAA2C;IAC3C,IAAI,aAAa,SAAS;QACxB,aAAa,WAAW,OAAO,CAAC,2BAA2B;IAC7D,OAAO,IAAI,aAAa,YAAY;QAClC,aAAa,WAAW,OAAO,CAAC,2BAA2B;IAC7D;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAa,WAAU;;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CACX;;;;;;;;;;;;sCAGL,8OAAC;4BAAE,WAAU;sCACV,gBACC,WAAW,KAAK,CAAC,eAAe,GAAG,CAAC,CAAC,MAAM,GAAG,MAC5C,MAAM,IAAI,MAAM,GAAG,kBACjB,8OAAC;8CAAc;mCAAJ;;;;yDAEX,8OAAC;;wCACE;sDACD,8OAAC;4CAAK,WAAU;sDAAwC;;;;;;;mCAF/C;;;;4CAMb;;;;;;;;;;;;;;;;;;;;;;;AAOhB", "debugId": null}}, {"offset": {"line": 8048, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/components/ModernResultsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { motion } from \"framer-motion\";\r\nimport ViewToggle from \"./ViewToggle\";\r\nimport ModernBusinessResults from \"./ModernBusinessResults\";\r\nimport ProductResults from \"./ProductResults\";\r\nimport LocationIndicator from \"./LocationIndicator\";\r\nimport { useDiscoverContext } from \"../context/DiscoverContext\";\r\nimport {\r\n  BUSINESS_NAME_PARAM,\r\n  PRODUCT_NAME_PARAM,\r\n  PINCODE_PARAM,\r\n  LOCALITY_PARAM,\r\n  CITY_PARAM,\r\n} from \"../constants/urlParamConstants\";\r\n\r\nexport default function ModernResultsSection() {\r\n  const {\r\n    viewType,\r\n    sortBy,\r\n    isSearching,\r\n    isLoadingMore,\r\n    businesses,\r\n    products,\r\n    hasMore,\r\n    totalCount,\r\n    isAuthenticated,\r\n    productFilterBy,\r\n    productSortBy,\r\n    handleViewChange,\r\n    handleBusinessSortChange,\r\n    handleBusinessSearch,\r\n    handleProductSearch,\r\n    handleProductSortChange,\r\n    handleProductFilterChange,\r\n    loadMore,\r\n  } = useDiscoverContext();\r\n\r\n  const searchParams = useSearchParams();\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full pb-16\"\r\n      initial={{ opacity: 0 }}\r\n      animate={{ opacity: 1 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      {/* View Toggle - centered and compact */}\r\n      <div className=\"container mx-auto px-4 mb-6\">\r\n        <div className=\"flex flex-col items-center\">\r\n          <motion.h2\r\n            className=\"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 text-center\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n          >\r\n            Browse Products/Services & Digital Cards\r\n          </motion.h2>\r\n\r\n          <ViewToggle\r\n            viewType={viewType}\r\n            onViewChange={handleViewChange}\r\n            hasFilters={\r\n              !!(\r\n                searchParams.get(BUSINESS_NAME_PARAM) ||\r\n                searchParams.get(PINCODE_PARAM) ||\r\n                searchParams.get(LOCALITY_PARAM) ||\r\n                searchParams.get(CITY_PARAM)\r\n              )\r\n            }\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Location Indicator */}\r\n      <LocationIndicator />\r\n\r\n      {/* Results Content */}\r\n      <div className=\"w-full\">\r\n        {viewType === \"cards\" && (\r\n          <ModernBusinessResults\r\n            businesses={businesses}\r\n            isAuthenticated={isAuthenticated}\r\n            totalCount={totalCount}\r\n            hasMore={hasMore}\r\n            isLoadingMore={isLoadingMore}\r\n            onLoadMore={loadMore}\r\n            onSortChange={handleBusinessSortChange}\r\n            onSearch={handleBusinessSearch}\r\n            currentSortBy={sortBy}\r\n            isLoading={isSearching}\r\n            initialSearchTerm={searchParams.get(BUSINESS_NAME_PARAM)}\r\n          />\r\n        )}\r\n\r\n        {viewType === \"products\" && (\r\n          <ProductResults\r\n            products={products}\r\n            totalCount={totalCount}\r\n            hasMore={hasMore}\r\n            isLoadingMore={isLoadingMore}\r\n            onLoadMore={loadMore}\r\n            onSortChange={handleProductSortChange}\r\n            onFilterChange={handleProductFilterChange}\r\n            onSearch={handleProductSearch}\r\n            currentSortBy={productSortBy}\r\n            currentFilterBy={productFilterBy}\r\n            isLoading={isSearching}\r\n            initialSearchTerm={searchParams.get(PRODUCT_NAME_PARAM)}\r\n          />\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,EACJ,QAAQ,EACR,MAAM,EACN,WAAW,EACX,aAAa,EACb,UAAU,EACV,QAAQ,EACR,OAAO,EACP,UAAU,EACV,eAAe,EACf,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,wBAAwB,EACxB,oBAAoB,EACpB,mBAAmB,EACnB,uBAAuB,EACvB,yBAAyB,EACzB,QAAQ,EACT,GAAG,CAAA,GAAA,0JAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAC7B;;;;;;sCAID,8OAAC,wJAAA,CAAA,UAAU;4BACT,UAAU;4BACV,cAAc;4BACd,YACE,CAAC,CAAC,CACA,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB,KACpC,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAC9B,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAC/B,aAAa,GAAG,CAAC,6JAAA,CAAA,aAAU,CAC7B;;;;;;;;;;;;;;;;;0BAOR,8OAAC,+JAAA,CAAA,UAAiB;;;;;0BAGlB,8OAAC;gBAAI,WAAU;;oBACZ,aAAa,yBACZ,8OAAC,mKAAA,CAAA,UAAqB;wBACpB,YAAY;wBACZ,iBAAiB;wBACjB,YAAY;wBACZ,SAAS;wBACT,eAAe;wBACf,YAAY;wBACZ,cAAc;wBACd,UAAU;wBACV,eAAe;wBACf,WAAW;wBACX,mBAAmB,aAAa,GAAG,CAAC,6JAAA,CAAA,sBAAmB;;;;;;oBAI1D,aAAa,4BACZ,8OAAC,4JAAA,CAAA,UAAc;wBACb,UAAU;wBACV,YAAY;wBACZ,SAAS;wBACT,eAAe;wBACf,YAAY;wBACZ,cAAc;wBACd,gBAAgB;wBAChB,UAAU;wBACV,eAAe;wBACf,iBAAiB;wBACjB,WAAW;wBACX,mBAAmB,aAAa,GAAG,CAAC,6JAAA,CAAA,qBAAkB;;;;;;;;;;;;;;;;;;AAMlE", "debugId": null}}, {"offset": {"line": 8191, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/ModernDiscoverClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearchParams } from \"next/navigation\";\r\nimport { DiscoverProvider } from \"./context/DiscoverContext\";\r\nimport { PINCODE_PARAM, CITY_PARAM, LOCALITY_PARAM } from \"./constants/urlParamConstants\";\r\n\r\n// Import components\r\nimport ImprovedSearchSection from \"./components/ImprovedSearchSection\";\r\nimport CategoryCarousel from \"./components/CategoryCarousel\";\r\nimport ErrorSection from \"./components/ErrorSection\";\r\nimport ModernResultsSection from \"./components/ModernResultsSection\";\r\n\r\nexport default function ModernDiscoverClient() {\r\n  const searchParams = useSearchParams();\r\n\r\n  const initialPincode = searchParams.get(PINCODE_PARAM) || \"\";\r\n  const initialCity = searchParams.get(CITY_PARAM) || \"\";\r\n  const initialLocality = searchParams.get(LOCALITY_PARAM) || \"\";\r\n\r\n  return (\r\n    <DiscoverProvider>\r\n      <div className=\"relative min-h-screen overflow-hidden bg-white dark:bg-black\">\r\n        {/* Improved search section - full width, one line for desktop/tablet */}\r\n        <ImprovedSearchSection\r\n          initialValues={{\r\n            pincode: initialPincode,\r\n            city: initialCity,\r\n            locality: initialLocality,\r\n          }}\r\n        />\r\n\r\n        {/* Category carousel */}\r\n        <CategoryCarousel />\r\n\r\n        {/* Error Display */}\r\n        <div className=\"container mx-auto px-4 my-4\">\r\n          <ErrorSection />\r\n        </div>\r\n\r\n        {/* Results Section */}\r\n        <ModernResultsSection />\r\n      </div>\r\n    </DiscoverProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AAVA;;;;;;;;;AAYe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,MAAM,iBAAiB,aAAa,GAAG,CAAC,6JAAA,CAAA,gBAAa,KAAK;IAC1D,MAAM,cAAc,aAAa,GAAG,CAAC,6JAAA,CAAA,aAAU,KAAK;IACpD,MAAM,kBAAkB,aAAa,GAAG,CAAC,6JAAA,CAAA,iBAAc,KAAK;IAE5D,qBACE,8OAAC,0JAAA,CAAA,mBAAgB;kBACf,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,mKAAA,CAAA,UAAqB;oBACpB,eAAe;wBACb,SAAS;wBACT,MAAM;wBACN,UAAU;oBACZ;;;;;;8BAIF,8OAAC,8JAAA,CAAA,UAAgB;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0JAAA,CAAA,UAAY;;;;;;;;;;8BAIf,8OAAC,kKAAA,CAAA,UAAoB;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 8272, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/ModernResultsSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport AnimatedBusinessGridSkeleton from \"./components/AnimatedBusinessGridSkeleton\";\r\n\r\nexport default function ModernResultsSkeleton() {\r\n  return (\r\n    <div className=\"min-h-screen bg-white dark:bg-black\">\r\n      {/* Improved Search Section Skeleton */}\r\n      <div className=\"w-full py-6 mt-6\">\r\n        <div className=\"container mx-auto px-4\">\r\n          <div className=\"max-w-5xl mx-auto\">\r\n            <div className=\"flex flex-col md:flex-row gap-3 items-start\">\r\n              <Skeleton className=\"h-12 w-full md:w-[140px]\" />\r\n              <div className=\"flex-1 w-full flex flex-col md:flex-row gap-3\">\r\n                <Skeleton className=\"h-12 flex-1\" />\r\n                <Skeleton className=\"h-12 w-full md:w-[200px]\" />\r\n                <Skeleton className=\"h-12 w-full md:w-[120px]\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Category Carousel Skeleton */}\r\n      <div className=\"mb-6 container mx-auto px-4\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <Skeleton className=\"h-5 w-36\" />\r\n          <Skeleton className=\"h-4 w-24\" />\r\n        </div>\r\n        <div className=\"flex gap-2 overflow-hidden\">\r\n          {Array.from({ length: 5 }).map((_, i) => (\r\n            <Skeleton key={i} className=\"h-20 w-20 flex-shrink-0 rounded-xl\" />\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* View Toggle Skeleton */}\r\n      <div className=\"flex justify-center mb-6\">\r\n        <Skeleton className=\"h-10 w-64 rounded-xl\" />\r\n      </div>\r\n\r\n      {/* Filter Bar Skeleton */}\r\n      <div className=\"container mx-auto px-4 mb-4\">\r\n        <Skeleton className=\"h-12 w-full rounded-lg\" />\r\n      </div>\r\n\r\n      {/* Results Grid Skeleton */}\r\n      <div className=\"container mx-auto px-4\">\r\n        <AnimatedBusinessGridSkeleton />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC,6HAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAEtB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,6HAAA,CAAA,WAAQ;gCAAS,WAAU;+BAAb;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,6HAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0KAAA,CAAA,UAA4B;;;;;;;;;;;;;;;;AAIrC", "debugId": null}}]}