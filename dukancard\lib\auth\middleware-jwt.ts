import { NextRequest } from 'next/server';
import { verify } from 'jsonwebtoken';

export interface JWTPayload {
  user_id: string;
  roles: string[];
  iat: number;
  exp: number;
}

/**
 * Extract and validate JW<PERSON> token from Authorization header in middleware
 * Returns the decoded payload if valid, null if invalid or missing
 */
export function validateJWTInMiddleware(request: NextRequest): JWTPayload | null {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    if (!token) {
      return null;
    }

    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not defined in environment variables');
      return null;
    }

    // Verify and decode the JWT
    const decoded = verify(token, jwtSecret) as JWTPayload;
    
    // Additional validation
    if (!decoded.user_id || !decoded.exp) {
      return null;
    }

    // Check if token is expired (should not happen as verify() checks this, but extra safety)
    const now = Math.floor(Date.now() / 1000);
    if (decoded.exp < now) {
      return null;
    }

    return decoded;
  } catch (_error) {
    // Token is invalid, expired, or malformed
    return null;
  }
}

/**
 * Extract JWT token from cookies (for web client session persistence)
 * This would be used if we stored JWTs in HttpOnly cookies
 */
export function getJWTFromCookies(request: NextRequest): string | null {
  try {
    const accessTokenCookie = request.cookies.get('accessToken');
    return accessTokenCookie?.value || null;
  } catch (_error) {
    return null;
  }
}

/**
 * Check if user has specific role based on JWT payload
 */
export function hasRole(payload: JWTPayload | null, role: string): boolean {
  if (!payload || !payload.roles) {
    return false;
  }
  return payload.roles.includes(role);
}

/**
 * Check if user is admin based on JWT payload
 */
export function isAdmin(payload: JWTPayload | null): boolean {
  return hasRole(payload, 'admin');
}