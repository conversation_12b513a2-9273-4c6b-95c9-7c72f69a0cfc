"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useBusinessProfileStore } from "@/lib/stores/businessProfileStore";
import { useAuthStore } from "@/lib/stores/authStore";
import BusinessDashboardClient from "../components/BusinessDashboardClient";
import { Skeleton } from "@/components/ui/skeleton";

export default function BusinessOverviewClient() {
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();
  const { 
    currentUserProfile, 
    isLoading, 
    error, 
    getCurrentUserProfile,
    clearError 
  } = useBusinessProfileStore();

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/login?message=Authentication required");
      return;
    }

    // Fetch current user's business profile with metrics
    getCurrentUserProfile({ includeMetrics: true });
  }, [isAuthenticated, getCurrentUserProfile, router]);

  useEffect(() => {
    if (error) {
      console.error("Error fetching business profile:", error);
      // Optionally redirect to login if it's an auth error
      if (error.includes("Authentication") || error.includes("Unauthorized")) {
        router.push("/login?message=Profile fetch error");
      }
    }
  }, [error, router]);

  if (!isAuthenticated) {
    return null; // Will redirect
  }

  if (isLoading) {
    return <Skeleton className="h-[600px] w-full" />;
  }

  if (error && !error.includes("Authentication")) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">Error Loading Profile</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button 
            onClick={() => {
              clearError();
              getCurrentUserProfile({ includeMetrics: true });
            }}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!currentUserProfile) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600 mb-2">No Business Profile Found</h2>
          <p className="text-gray-500 mb-4">You need to create a business profile first.</p>
          <button 
            onClick={() => router.push("/onboarding")}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
          >
            Create Profile
          </button>
        </div>
      </div>
    );
  }

  // Transform the profile data to match the expected format for BusinessDashboardClient
  const initialProfile = {
    business_name: currentUserProfile.business_name || "",
    business_slug: currentUserProfile.business_slug || "",
    total_likes: currentUserProfile.total_likes || 0,
    total_subscriptions: currentUserProfile.total_subscriptions || 0,
    average_rating: currentUserProfile.average_rating || 0,
    logo_url: currentUserProfile.logo_url,
    title: currentUserProfile.title,
    status: currentUserProfile.status || "offline" // Default to offline if status is not set
  };

  return (
    <BusinessDashboardClient
      initialProfile={initialProfile}
      userId={user?.id || currentUserProfile.id}
    />
  );
}
