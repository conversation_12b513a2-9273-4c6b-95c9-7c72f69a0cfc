[{"C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts": "1", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx": "2", "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx": "3", "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx": "4", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts": "5", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts": "6", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts": "7", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts": "8", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx": "9", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx": "10", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx": "11", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx": "12", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx": "13", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx": "14", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx": "15", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx": "16", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx": "17", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx": "18", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx": "19", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx": "20", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts": "21", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx": "22", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx": "23", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx": "24", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx": "25", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx": "26", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx": "27", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx": "28", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx": "29", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx": "30", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts": "31", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx": "32", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx": "33", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts": "34", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts": "35", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts": "36", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx": "37", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx": "38", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx": "39", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts": "40", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts": "41", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts": "42", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts": "43", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx": "44", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts": "45", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts": "46", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts": "47", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts": "48", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts": "49", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts": "50", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts": "51", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts": "52", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx": "53", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx": "54", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx": "55", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx": "56", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx": "57", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx": "58", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx": "59", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts": "60", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx": "61", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx": "62", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx": "63", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx": "64", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx": "65", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx": "66", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx": "67", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx": "68", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx": "69", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx": "70", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx": "71", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts": "72", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts": "73", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts": "74", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx": "75", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts": "76", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts": "77", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts": "78", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts": "79", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx": "80", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts": "81", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx": "82", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx": "83", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx": "84", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx": "85", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx": "86", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx": "87", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx": "88", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx": "89", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts": "90", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts": "91", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts": "92", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts": "93", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts": "94", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts": "95", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts": "96", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts": "97", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts": "98", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts": "99", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts": "100", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts": "101", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts": "102", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts": "103", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts": "104", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx": "105", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx": "106", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx": "107", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts": "108", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts": "109", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx": "110", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts": "111", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx": "112", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx": "113", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx": "114", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx": "115", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx": "116", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx": "117", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx": "118", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx": "119", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx": "120", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx": "121", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx": "122", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx": "123", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx": "124", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx": "125", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx": "126", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx": "127", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx": "128", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx": "129", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx": "130", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx": "131", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx": "132", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx": "133", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx": "134", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx": "135", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts": "136", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts": "137", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx": "138", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx": "139", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx": "140", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx": "141", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts": "142", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx": "143", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx": "144", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx": "145", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx": "146", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx": "147", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx": "148", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx": "149", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts": "150", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts": "151", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx": "152", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx": "153", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx": "154", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx": "155", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx": "156", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx": "157", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx": "158", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts": "159", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx": "160", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx": "161", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx": "162", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx": "163", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx": "164", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts": "165", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts": "166", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx": "167", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx": "168", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx": "169", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx": "170", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts": "171", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx": "172", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx": "173", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx": "174", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx": "175", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts": "176", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx": "177", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx": "178", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts": "179", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx": "180", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx": "181", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx": "182", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx": "183", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx": "184", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts": "185", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx": "186", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx": "187", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx": "188", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx": "189", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx": "190", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx": "191", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx": "192", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts": "193", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx": "194", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts": "195", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx": "196", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx": "197", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx": "198", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx": "199", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx": "200", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx": "201", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx": "202", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx": "203", "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx": "204", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx": "205", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx": "206", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx": "207", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx": "208", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx": "209", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx": "210", "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx": "211", "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx": "212", "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts": "213", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx": "214", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx": "215", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx": "216", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx": "217", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx": "218", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx": "219", "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx": "220", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx": "221", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx": "222", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts": "223", "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx": "224", "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx": "225", "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx": "226", "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts": "227", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx": "228", "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx": "229", "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx": "230", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx": "231", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx": "232", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx": "233", "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx": "234", "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx": "235", "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx": "236", "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx": "237", "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx": "238", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx": "239", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts": "240", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx": "241", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx": "242", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx": "243", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx": "244", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx": "245", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx": "246", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx": "247", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx": "248", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts": "249", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx": "250", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx": "251", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx": "252", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx": "253", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx": "254", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx": "255", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx": "256", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx": "257", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx": "258", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx": "259", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx": "260", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx": "261", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx": "262", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx": "263", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx": "264", "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx": "265", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts": "266", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx": "267", "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx": "268", "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx": "269", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx": "270", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx": "271", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx": "272", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx": "273", "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx": "274", "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx": "275", "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx": "276", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx": "277", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx": "278", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx": "279", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx": "280", "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx": "281", "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx": "282", "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx": "283", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx": "284", "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx": "285", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts": "286", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts": "287", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts": "288", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts": "289", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts": "290", "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts": "291", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx": "292", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx": "293", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx": "294", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx": "295", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx": "296", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx": "297", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx": "298", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx": "299", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx": "300", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx": "301", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx": "302", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx": "303", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx": "304", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx": "305", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx": "306", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx": "307", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx": "308", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx": "309", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx": "310", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx": "311", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx": "312", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx": "313", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx": "314", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx": "315", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx": "316", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx": "317", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx": "318", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx": "319", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx": "320", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx": "321", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx": "322", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx": "323", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx": "324", "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx": "325", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts": "326", "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts": "327", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts": "328", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts": "329", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx": "330", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts": "331", "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts": "332", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx": "333", "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx": "334", "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx": "335", "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts": "336", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx": "337", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx": "338", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx": "339", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx": "340", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx": "341", "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx": "342", "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx": "343", "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx": "344", "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts": "345", "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx": "346", "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx": "347", "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts": "348", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx": "349", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx": "350", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx": "351", "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx": "352", "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx": "353", "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx": "354", "C:\\web-app\\dukancard\\app\\(main)\\page.tsx": "355", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx": "356", "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx": "357", "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx": "358", "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx": "359", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx": "360", "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx": "361", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx": "362", "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx": "363", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx": "364", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx": "365", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx": "366", "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx": "367", "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx": "368", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx": "369", "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx": "370", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx": "371", "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx": "372", "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx": "373", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx": "374", "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx": "375", "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx": "376", "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx": "377", "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx": "378", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts": "379", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx": "380", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx": "381", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx": "382", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx": "383", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx": "384", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx": "385", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts": "386", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts": "387", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts": "388", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts": "389", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts": "390", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts": "391", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx": "392", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx": "393", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx": "394", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts": "395", "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts": "396", "C:\\web-app\\dukancard\\app\\auth\\actions.ts": "397", "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts": "398", "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx": "399", "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx": "400", "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx": "401", "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx": "402", "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx": "403", "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx": "404", "C:\\web-app\\dukancard\\app\\components\\Footer.tsx": "405", "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx": "406", "C:\\web-app\\dukancard\\app\\components\\Header.tsx": "407", "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx": "408", "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx": "409", "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx": "410", "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx": "411", "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx": "412", "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx": "413", "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx": "414", "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx": "415", "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx": "416", "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx": "417", "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx": "418", "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx": "419", "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx": "420", "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx": "421", "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx": "422", "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx": "423", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx": "424", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx": "425", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx": "426", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx": "427", "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx": "428", "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx": "429", "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx": "430", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts": "431", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx": "432", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx": "433", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx": "434", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx": "435", "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx": "436", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx": "437", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx": "438", "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx": "439", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts": "440", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx": "441", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx": "442", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx": "443", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx": "444", "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx": "445", "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx": "446", "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx": "447", "C:\\web-app\\dukancard\\app\\layout.tsx": "448", "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts": "449", "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts": "450", "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts": "451", "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts": "452", "C:\\web-app\\dukancard\\app\\locality\\actions.ts": "453", "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx": "454", "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx": "455", "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx": "456", "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx": "457", "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx": "458", "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts": "459", "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts": "460", "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts": "461", "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx": "462", "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts": "463", "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts": "464", "C:\\web-app\\dukancard\\app\\locality\\layout.tsx": "465", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx": "466", "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx": "467", "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx": "468", "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx": "469", "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx": "470", "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx": "471", "C:\\web-app\\dukancard\\app\\products\\sitemap.ts": "472", "C:\\web-app\\dukancard\\app\\robots.ts": "473", "C:\\web-app\\dukancard\\app\\sitemap.ts": "474", "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts": "475", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts": "476", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx": "477", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx": "478", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx": "479", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx": "480", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx": "481", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx": "482", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx": "483", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx": "484", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx": "485", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx": "486", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx": "487", "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx": "488", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx": "489", "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx": "490", "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx": "491", "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx": "492", "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx": "493", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts": "494", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx": "495", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx": "496", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx": "497", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx": "498", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx": "499", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx": "500", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx": "501", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx": "502", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx": "503", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts": "504", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx": "505", "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx": "506", "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx": "507", "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx": "508", "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx": "509", "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx": "510", "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx": "511", "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx": "512", "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx": "513", "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx": "514", "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx": "515", "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx": "516", "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx": "517", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx": "518", "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx": "519", "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx": "520", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx": "521", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx": "522", "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx": "523", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts": "524", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts": "525", "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts": "526", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx": "527", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx": "528", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx": "529", "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx": "530", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx": "531", "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx": "532", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx": "533", "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx": "534", "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx": "535", "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx": "536", "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx": "537", "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx": "538", "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx": "539", "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx": "540", "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx": "541", "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx": "542", "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx": "543", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx": "544", "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx": "545", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx": "546", "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx": "547", "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx": "548", "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx": "549", "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx": "550", "C:\\web-app\\dukancard\\components\\ui\\alert.tsx": "551", "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx": "552", "C:\\web-app\\dukancard\\components\\ui\\badge.tsx": "553", "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx": "554", "C:\\web-app\\dukancard\\components\\ui\\button.tsx": "555", "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx": "556", "C:\\web-app\\dukancard\\components\\ui\\card.tsx": "557", "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx": "558", "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx": "559", "C:\\web-app\\dukancard\\components\\ui\\chart.tsx": "560", "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx": "561", "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx": "562", "C:\\web-app\\dukancard\\components\\ui\\command.tsx": "563", "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx": "564", "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx": "565", "C:\\web-app\\dukancard\\components\\ui\\form.tsx": "566", "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx": "567", "C:\\web-app\\dukancard\\components\\ui\\input.tsx": "568", "C:\\web-app\\dukancard\\components\\ui\\label.tsx": "569", "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx": "570", "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx": "571", "C:\\web-app\\dukancard\\components\\ui\\popover.tsx": "572", "C:\\web-app\\dukancard\\components\\ui\\progress.tsx": "573", "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx": "574", "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx": "575", "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx": "576", "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx": "577", "C:\\web-app\\dukancard\\components\\ui\\select.tsx": "578", "C:\\web-app\\dukancard\\components\\ui\\separator.tsx": "579", "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx": "580", "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx": "581", "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx": "582", "C:\\web-app\\dukancard\\components\\ui\\slider.tsx": "583", "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx": "584", "C:\\web-app\\dukancard\\components\\ui\\switch.tsx": "585", "C:\\web-app\\dukancard\\components\\ui\\table.tsx": "586", "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx": "587", "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx": "588", "C:\\web-app\\dukancard\\components\\ui\\toast.tsx": "589", "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx": "590", "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts": "591", "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx": "592", "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts": "593", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts": "594", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts": "595", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts": "596", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts": "597", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts": "598", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts": "599", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts": "600", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts": "601", "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts": "602", "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts": "603", "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts": "604", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts": "605", "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts": "606", "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts": "607", "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts": "608", "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts": "609", "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts": "610", "C:\\web-app\\dukancard\\lib\\actions\\location.ts": "611", "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts": "612", "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts": "613", "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts": "614", "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts": "615", "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts": "616", "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts": "617", "C:\\web-app\\dukancard\\lib\\actions\\posts.ts": "618", "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts": "619", "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts": "620", "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts": "621", "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts": "622", "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts": "623", "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts": "624", "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts": "625", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts": "626", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts": "627", "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts": "628", "C:\\web-app\\dukancard\\lib\\api\\response.ts": "629", "C:\\web-app\\dukancard\\lib\\cardDownloader.ts": "630", "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts": "631", "C:\\web-app\\dukancard\\lib\\config\\categories.ts": "632", "C:\\web-app\\dukancard\\lib\\config\\states.ts": "633", "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts": "634", "C:\\web-app\\dukancard\\lib\\csrf.ts": "635", "C:\\web-app\\dukancard\\lib\\errorHandling.ts": "636", "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts": "637", "C:\\web-app\\dukancard\\lib\\rateLimiter.ts": "638", "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts": "639", "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts": "640", "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts": "641", "C:\\web-app\\dukancard\\lib\\services\\socialService.ts": "642", "C:\\web-app\\dukancard\\lib\\site-config.ts": "643", "C:\\web-app\\dukancard\\lib\\siteContent.ts": "644", "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts": "645", "C:\\web-app\\dukancard\\lib\\types\\activities.ts": "646", "C:\\web-app\\dukancard\\lib\\types\\api.ts": "647", "C:\\web-app\\dukancard\\lib\\types\\blog.ts": "648", "C:\\web-app\\dukancard\\lib\\types\\posts.ts": "649", "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts": "650", "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts": "651", "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts": "652", "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts": "653", "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts": "654", "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts": "655", "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts": "656", "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts": "657", "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts": "658", "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts": "659", "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts": "660", "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts": "661", "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts": "662", "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts": "663", "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts": "664", "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts": "665", "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts": "666", "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts": "667", "C:\\web-app\\dukancard\\lib\\utils\\seo.ts": "668", "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts": "669", "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts": "670", "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts": "671", "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts": "672", "C:\\web-app\\dukancard\\lib\\utils.ts": "673", "C:\\web-app\\dukancard\\components\\ui\\CommentDisplay.tsx": "674", "C:\\web-app\\dukancard\\components\\ui\\CommentInput.tsx": "675", "C:\\web-app\\dukancard\\components\\ui\\CommentSection.tsx": "676", "C:\\web-app\\dukancard\\components\\ui\\LikeButton.tsx": "677", "C:\\web-app\\dukancard\\lib\\actions\\comments\\postComments.ts": "678", "C:\\web-app\\dukancard\\lib\\actions\\likes\\commentLikes.ts": "679", "C:\\web-app\\dukancard\\lib\\actions\\likes\\postLikes.ts": "680", "C:\\web-app\\dukancard\\lib\\stores\\likeCommentStore.ts": "681", "C:\\web-app\\dukancard\\lib\\utils\\formatNumber.ts": "682", "C:\\web-app\\dukancard\\components\\post\\PostLikesList.tsx": "683", "C:\\web-app\\dukancard\\lib\\services\\pagination\\paginationService.ts": "684", "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\WelcomeStep.tsx": "685", "C:\\web-app\\dukancard\\app\\api\\auth\\login\\route.ts": "686", "C:\\web-app\\dukancard\\app\\api\\auth\\logout\\route.ts": "687", "C:\\web-app\\dukancard\\app\\api\\auth\\refresh\\route.ts": "688", "C:\\web-app\\dukancard\\app\\api\\devices\\register\\route.ts": "689", "C:\\web-app\\dukancard\\lib\\auth\\jwt.ts": "690", "C:\\web-app\\dukancard\\lib\\auth\\utils.ts": "691", "C:\\web-app\\dukancard\\lib\\middleware\\hmac.ts": "692", "C:\\web-app\\dukancard\\lib\\middleware\\rateLimiter.ts": "693", "C:\\web-app\\dukancard\\lib\\middleware\\timestamp.ts": "694", "C:\\web-app\\dukancard\\lib\\security\\hashing.ts": "695", "C:\\web-app\\dukancard\\lib\\security\\hmac.ts": "696", "C:\\web-app\\dukancard\\lib\\middleware\\bruteForceProtection.ts": "697", "C:\\web-app\\dukancard\\lib\\actions\\auth.ts": "698", "C:\\web-app\\dukancard\\lib\\stores\\authStore.ts": "699", "C:\\web-app\\dukancard\\lib\\utils\\authenticatedFetch.ts": "700", "C:\\web-app\\dukancard\\app\\api\\auth\\send-otp\\route.ts": "701", "C:\\web-app\\dukancard\\app\\api\\auth\\verify-otp\\route.ts": "702", "C:\\web-app\\dukancard\\lib\\auth\\middleware-jwt.ts": "703", "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\BusinessOverviewClient.tsx": "704", "C:\\web-app\\dukancard\\app\\api\\business\\access\\route.ts": "705", "C:\\web-app\\dukancard\\app\\api\\business\\me\\route.ts": "706", "C:\\web-app\\dukancard\\app\\api\\business\\profile\\exists\\route.ts": "707", "C:\\web-app\\dukancard\\app\\api\\business\\route.ts": "708", "C:\\web-app\\dukancard\\app\\api\\business\\search\\route.ts": "709", "C:\\web-app\\dukancard\\app\\api\\business\\sitemap\\route.ts": "710", "C:\\web-app\\dukancard\\app\\api\\business\\slug\\[slug]\\route.ts": "711", "C:\\web-app\\dukancard\\app\\api\\business\\[id]\\route.ts": "712", "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\exists\\route.ts": "713", "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\route.ts": "714", "C:\\web-app\\dukancard\\app\\api\\likes\\route.ts": "715", "C:\\web-app\\dukancard\\app\\api\\location\\city\\[city]\\route.ts": "716", "C:\\web-app\\dukancard\\app\\api\\location\\pincode\\[pincode]\\route.ts": "717", "C:\\web-app\\dukancard\\app\\api\\posts\\route.ts": "718", "C:\\web-app\\dukancard\\app\\api\\products\\route.ts": "719", "C:\\web-app\\dukancard\\app\\api\\products\\[id]\\route.ts": "720", "C:\\web-app\\dukancard\\app\\api\\storage\\upload\\route.ts": "721", "C:\\web-app\\dukancard\\lib\\stores\\businessProfileStore.ts": "722", "C:\\web-app\\dukancard\\lib\\stores\\postsStore.ts": "723", "C:\\web-app\\dukancard\\lib\\stores\\productsStore.ts": "724", "C:\\web-app\\dukancard\\lib\\stores\\storageStore.ts": "725", "C:\\web-app\\dukancard\\lib\\middleware\\jwt.ts": "726", "C:\\web-app\\dukancard\\app\\api\\comments\\route.ts": "727", "C:\\web-app\\dukancard\\lib\\stores\\customerProfileStore.ts": "728", "C:\\web-app\\dukancard\\app\\api\\comments\\[id]\\route.ts": "729", "C:\\web-app\\dukancard\\lib\\utils\\internalApiClient.ts": "730", "C:\\web-app\\dukancard\\app\\api\\subscriptions\\route.ts": "731", "C:\\web-app\\dukancard\\app\\api\\reviews\\route.ts": "732"}, {"size": 3965, "mtime": 1754649239254, "results": "733", "hashOfConfig": "734"}, {"size": 11792, "mtime": 1754319312875, "results": "735", "hashOfConfig": "734"}, {"size": 4230, "mtime": 1753520831364, "results": "736", "hashOfConfig": "734"}, {"size": 2270, "mtime": 1753437208359, "results": "737", "hashOfConfig": "734"}, {"size": 7333, "mtime": 1753436763325, "results": "738", "hashOfConfig": "734"}, {"size": 273, "mtime": 1752078894640, "results": "739", "hashOfConfig": "734"}, {"size": 4153, "mtime": 1754779581110, "results": "740", "hashOfConfig": "734"}, {"size": 5032, "mtime": 1754779478004, "results": "741", "hashOfConfig": "734"}, {"size": 20428, "mtime": 1754686812839, "results": "742", "hashOfConfig": "734"}, {"size": 20203, "mtime": 1754686932336, "results": "743", "hashOfConfig": "734"}, {"size": 2220, "mtime": 1752078894653, "results": "744", "hashOfConfig": "734"}, {"size": 10914, "mtime": 1752078894653, "results": "745", "hashOfConfig": "734"}, {"size": 1630, "mtime": 1752078894653, "results": "746", "hashOfConfig": "734"}, {"size": 721, "mtime": 1752078894653, "results": "747", "hashOfConfig": "734"}, {"size": 7408, "mtime": 1754687066766, "results": "748", "hashOfConfig": "734"}, {"size": 21052, "mtime": 1752078894664, "results": "749", "hashOfConfig": "734"}, {"size": 4489, "mtime": 1752078894665, "results": "750", "hashOfConfig": "734"}, {"size": 13138, "mtime": 1752078894667, "results": "751", "hashOfConfig": "734"}, {"size": 2250, "mtime": 1754686772279, "results": "752", "hashOfConfig": "734"}, {"size": 14802, "mtime": 1752080815930, "results": "753", "hashOfConfig": "734"}, {"size": 1088, "mtime": 1752078894667, "results": "754", "hashOfConfig": "734"}, {"size": 2708, "mtime": 1752078894667, "results": "755", "hashOfConfig": "734"}, {"size": 9768, "mtime": 1752078894667, "results": "756", "hashOfConfig": "734"}, {"size": 23953, "mtime": 1752078894667, "results": "757", "hashOfConfig": "734"}, {"size": 3361, "mtime": 1752078894667, "results": "758", "hashOfConfig": "734"}, {"size": 3637, "mtime": 1752078894678, "results": "759", "hashOfConfig": "734"}, {"size": 6688, "mtime": 1752078894680, "results": "760", "hashOfConfig": "734"}, {"size": 1987, "mtime": 1754686868244, "results": "761", "hashOfConfig": "734"}, {"size": 3868, "mtime": 1752078894681, "results": "762", "hashOfConfig": "734"}, {"size": 4167, "mtime": 1752078894682, "results": "763", "hashOfConfig": "734"}, {"size": 3004, "mtime": 1752078894683, "results": "764", "hashOfConfig": "734"}, {"size": 6921, "mtime": 1752078894684, "results": "765", "hashOfConfig": "734"}, {"size": 8477, "mtime": 1754649966466, "results": "766", "hashOfConfig": "734"}, {"size": 9562, "mtime": 1752078894708, "results": "767", "hashOfConfig": "734"}, {"size": 4802, "mtime": 1752078894708, "results": "768", "hashOfConfig": "734"}, {"size": 1794, "mtime": 1752078894708, "results": "769", "hashOfConfig": "734"}, {"size": 5533, "mtime": 1752078894708, "results": "770", "hashOfConfig": "734"}, {"size": 2140, "mtime": 1752078894708, "results": "771", "hashOfConfig": "734"}, {"size": 5309, "mtime": 1752078894708, "results": "772", "hashOfConfig": "734"}, {"size": 3245, "mtime": 1752078894717, "results": "773", "hashOfConfig": "734"}, {"size": 6811, "mtime": 1754689429292, "results": "774", "hashOfConfig": "734"}, {"size": 1012, "mtime": 1754689054078, "results": "775", "hashOfConfig": "734"}, {"size": 7583, "mtime": 1753436763340, "results": "776", "hashOfConfig": "734"}, {"size": 1240, "mtime": 1754689071053, "results": "777", "hashOfConfig": "734"}, {"size": 1543, "mtime": 1754781364571, "results": "778", "hashOfConfig": "734"}, {"size": 6426, "mtime": 1754687845822, "results": "779", "hashOfConfig": "734"}, {"size": 2435, "mtime": 1752078894723, "results": "780", "hashOfConfig": "734"}, {"size": 1001, "mtime": 1752078894723, "results": "781", "hashOfConfig": "734"}, {"size": 559, "mtime": 1753031839333, "results": "782", "hashOfConfig": "734"}, {"size": 2573, "mtime": 1752078894723, "results": "783", "hashOfConfig": "734"}, {"size": 404, "mtime": 1752078894723, "results": "784", "hashOfConfig": "734"}, {"size": 1825, "mtime": 1752078894723, "results": "785", "hashOfConfig": "734"}, {"size": 4687, "mtime": 1752520901592, "results": "786", "hashOfConfig": "734"}, {"size": 4992, "mtime": 1754779008814, "results": "787", "hashOfConfig": "734"}, {"size": 2514, "mtime": 1754687380447, "results": "788", "hashOfConfig": "734"}, {"size": 2722, "mtime": 1752521491570, "results": "789", "hashOfConfig": "734"}, {"size": 5665, "mtime": 1754779092008, "results": "790", "hashOfConfig": "734"}, {"size": 4935, "mtime": 1754687616421, "results": "791", "hashOfConfig": "734"}, {"size": 8561, "mtime": 1752078894735, "results": "792", "hashOfConfig": "734"}, {"size": 10073, "mtime": 1754683186101, "results": "793", "hashOfConfig": "734"}, {"size": 1993, "mtime": 1752078894735, "results": "794", "hashOfConfig": "734"}, {"size": 3314, "mtime": 1752522195446, "results": "795", "hashOfConfig": "734"}, {"size": 6883, "mtime": 1753436763340, "results": "796", "hashOfConfig": "734"}, {"size": 2180, "mtime": 1752521782818, "results": "797", "hashOfConfig": "734"}, {"size": 1799, "mtime": 1752078894735, "results": "798", "hashOfConfig": "734"}, {"size": 2794, "mtime": 1752078894751, "results": "799", "hashOfConfig": "734"}, {"size": 3181, "mtime": 1753436763340, "results": "800", "hashOfConfig": "734"}, {"size": 2096, "mtime": 1753436763340, "results": "801", "hashOfConfig": "734"}, {"size": 2833, "mtime": 1752078894751, "results": "802", "hashOfConfig": "734"}, {"size": 6615, "mtime": 1752522674608, "results": "803", "hashOfConfig": "734"}, {"size": 8681, "mtime": 1753436763340, "results": "804", "hashOfConfig": "734"}, {"size": 2305, "mtime": 1752078894751, "results": "805", "hashOfConfig": "734"}, {"size": 2046, "mtime": 1753436763340, "results": "806", "hashOfConfig": "734"}, {"size": 3165, "mtime": 1753436763340, "results": "807", "hashOfConfig": "734"}, {"size": 1673, "mtime": 1754683844676, "results": "808", "hashOfConfig": "734"}, {"size": 767, "mtime": 1753436763340, "results": "809", "hashOfConfig": "734"}, {"size": 555, "mtime": 1753436763340, "results": "810", "hashOfConfig": "734"}, {"size": 1142, "mtime": 1752078894766, "results": "811", "hashOfConfig": "734"}, {"size": 593, "mtime": 1754683088529, "results": "812", "hashOfConfig": "734"}, {"size": 2957, "mtime": 1754687323847, "results": "813", "hashOfConfig": "734"}, {"size": 1949, "mtime": 1753520944546, "results": "814", "hashOfConfig": "734"}, {"size": 10378, "mtime": 1753531001335, "results": "815", "hashOfConfig": "734"}, {"size": 4483, "mtime": 1752925036472, "results": "816", "hashOfConfig": "734"}, {"size": 3675, "mtime": 1752524849734, "results": "817", "hashOfConfig": "734"}, {"size": 4030, "mtime": 1752078894766, "results": "818", "hashOfConfig": "734"}, {"size": 1698, "mtime": 1752078894766, "results": "819", "hashOfConfig": "734"}, {"size": 3845, "mtime": 1753589702934, "results": "820", "hashOfConfig": "734"}, {"size": 521, "mtime": 1754778918947, "results": "821", "hashOfConfig": "734"}, {"size": 2331, "mtime": 1754787386256, "results": "822", "hashOfConfig": "734"}, {"size": 7929, "mtime": 1753436763340, "results": "823", "hashOfConfig": "734"}, {"size": 13026, "mtime": 1754051312710, "results": "824", "hashOfConfig": "734"}, {"size": 8366, "mtime": 1753436763340, "results": "825", "hashOfConfig": "734"}, {"size": 5098, "mtime": 1753436763340, "results": "826", "hashOfConfig": "734"}, {"size": 15095, "mtime": 1753436763356, "results": "827", "hashOfConfig": "734"}, {"size": 5751, "mtime": 1754392096865, "results": "828", "hashOfConfig": "734"}, {"size": 11161, "mtime": 1753975945921, "results": "829", "hashOfConfig": "734"}, {"size": 7625, "mtime": 1753976075953, "results": "830", "hashOfConfig": "734"}, {"size": 9106, "mtime": 1753521094750, "results": "831", "hashOfConfig": "734"}, {"size": 774, "mtime": 1752078894839, "results": "832", "hashOfConfig": "734"}, {"size": 5527, "mtime": 1753104677045, "results": "833", "hashOfConfig": "734"}, {"size": 585, "mtime": 1752170193569, "results": "834", "hashOfConfig": "734"}, {"size": 8393, "mtime": 1753976109969, "results": "835", "hashOfConfig": "734"}, {"size": 12397, "mtime": 1753436763363, "results": "836", "hashOfConfig": "734"}, {"size": 99, "mtime": 1752078894830, "results": "837", "hashOfConfig": "734"}, {"size": 10565, "mtime": 1754683399868, "results": "838", "hashOfConfig": "734"}, {"size": 2050, "mtime": 1754683328621, "results": "839", "hashOfConfig": "734"}, {"size": 16600, "mtime": 1753977090407, "results": "840", "hashOfConfig": "734"}, {"size": 6920, "mtime": 1752078894861, "results": "841", "hashOfConfig": "734"}, {"size": 8800, "mtime": 1752078894863, "results": "842", "hashOfConfig": "734"}, {"size": 7997, "mtime": 1752078894845, "results": "843", "hashOfConfig": "734"}, {"size": 743, "mtime": 1752083877657, "results": "844", "hashOfConfig": "734"}, {"size": 2089, "mtime": 1752078894863, "results": "845", "hashOfConfig": "734"}, {"size": 2029, "mtime": 1752170340365, "results": "846", "hashOfConfig": "734"}, {"size": 5665, "mtime": 1752087097784, "results": "847", "hashOfConfig": "734"}, {"size": 9160, "mtime": 1752086184726, "results": "848", "hashOfConfig": "734"}, {"size": 2212, "mtime": 1752084237937, "results": "849", "hashOfConfig": "734"}, {"size": 2762, "mtime": 1752165254320, "results": "850", "hashOfConfig": "734"}, {"size": 4770, "mtime": 1752085989931, "results": "851", "hashOfConfig": "734"}, {"size": 7051, "mtime": 1752165128789, "results": "852", "hashOfConfig": "734"}, {"size": 5000, "mtime": 1754687866954, "results": "853", "hashOfConfig": "734"}, {"size": 18791, "mtime": 1753106058738, "results": "854", "hashOfConfig": "734"}, {"size": 4142, "mtime": 1754687886146, "results": "855", "hashOfConfig": "734"}, {"size": 5264, "mtime": 1752078894845, "results": "856", "hashOfConfig": "734"}, {"size": 16573, "mtime": 1752078894852, "results": "857", "hashOfConfig": "734"}, {"size": 1270, "mtime": 1754683708553, "results": "858", "hashOfConfig": "734"}, {"size": 30029, "mtime": 1754683464926, "results": "859", "hashOfConfig": "734"}, {"size": 17692, "mtime": 1752078894855, "results": "860", "hashOfConfig": "734"}, {"size": 31473, "mtime": 1753976419791, "results": "861", "hashOfConfig": "734"}, {"size": 25712, "mtime": 1753977056767, "results": "862", "hashOfConfig": "734"}, {"size": 10885, "mtime": 1752078894855, "results": "863", "hashOfConfig": "734"}, {"size": 7797, "mtime": 1754687705844, "results": "864", "hashOfConfig": "734"}, {"size": 12996, "mtime": 1754683526681, "results": "865", "hashOfConfig": "734"}, {"size": 3126, "mtime": 1754687510171, "results": "866", "hashOfConfig": "734"}, {"size": 1137, "mtime": 1754687536107, "results": "867", "hashOfConfig": "734"}, {"size": 3020, "mtime": 1754683758576, "results": "868", "hashOfConfig": "734"}, {"size": 2542, "mtime": 1754687739927, "results": "869", "hashOfConfig": "734"}, {"size": 1190, "mtime": 1753794776157, "results": "870", "hashOfConfig": "734"}, {"size": 10361, "mtime": 1753513706129, "results": "871", "hashOfConfig": "734"}, {"size": 12000, "mtime": 1753616611181, "results": "872", "hashOfConfig": "734"}, {"size": 5373, "mtime": 1752525844741, "results": "873", "hashOfConfig": "734"}, {"size": 2006, "mtime": 1753026919824, "results": "874", "hashOfConfig": "734"}, {"size": 22096, "mtime": 1754679106337, "results": "875", "hashOfConfig": "734"}, {"size": 23396, "mtime": 1752691514782, "results": "876", "hashOfConfig": "734"}, {"size": 1822, "mtime": 1752526641904, "results": "877", "hashOfConfig": "734"}, {"size": 15608, "mtime": 1752763145906, "results": "878", "hashOfConfig": "734"}, {"size": 4060, "mtime": 1752762369862, "results": "879", "hashOfConfig": "734"}, {"size": 3182, "mtime": 1752526585020, "results": "880", "hashOfConfig": "734"}, {"size": 4559, "mtime": 1752763551631, "results": "881", "hashOfConfig": "734"}, {"size": 2104, "mtime": 1753251129502, "results": "882", "hashOfConfig": "734"}, {"size": 547, "mtime": 1752761809080, "results": "883", "hashOfConfig": "734"}, {"size": 3243, "mtime": 1754684394964, "results": "884", "hashOfConfig": "734"}, {"size": 11679, "mtime": 1754684394965, "results": "885", "hashOfConfig": "734"}, {"size": 4330, "mtime": 1754684394967, "results": "886", "hashOfConfig": "734"}, {"size": 2300, "mtime": 1752078894894, "results": "887", "hashOfConfig": "734"}, {"size": 1807, "mtime": 1752078894894, "results": "888", "hashOfConfig": "734"}, {"size": 2559, "mtime": 1752078894894, "results": "889", "hashOfConfig": "734"}, {"size": 2308, "mtime": 1752652107359, "results": "890", "hashOfConfig": "734"}, {"size": 2136, "mtime": 1752078894894, "results": "891", "hashOfConfig": "734"}, {"size": 591, "mtime": 1753445796006, "results": "892", "hashOfConfig": "734"}, {"size": 6530, "mtime": 1752078894894, "results": "893", "hashOfConfig": "734"}, {"size": 1372, "mtime": 1752925036472, "results": "894", "hashOfConfig": "734"}, {"size": 3511, "mtime": 1752078894894, "results": "895", "hashOfConfig": "734"}, {"size": 2499, "mtime": 1753251129502, "results": "896", "hashOfConfig": "734"}, {"size": 2705, "mtime": 1754787393562, "results": "897", "hashOfConfig": "734"}, {"size": 18248, "mtime": 1753797801088, "results": "898", "hashOfConfig": "734"}, {"size": 6372, "mtime": 1752078894909, "results": "899", "hashOfConfig": "734"}, {"size": 12390, "mtime": 1753515317601, "results": "900", "hashOfConfig": "734"}, {"size": 1376, "mtime": 1752688533290, "results": "901", "hashOfConfig": "734"}, {"size": 5647, "mtime": 1752688601357, "results": "902", "hashOfConfig": "734"}, {"size": 7356, "mtime": 1752078894910, "results": "903", "hashOfConfig": "734"}, {"size": 4583, "mtime": 1752078894910, "results": "904", "hashOfConfig": "734"}, {"size": 6598, "mtime": 1752078894910, "results": "905", "hashOfConfig": "734"}, {"size": 5978, "mtime": 1752078894910, "results": "906", "hashOfConfig": "734"}, {"size": 13585, "mtime": 1752678735198, "results": "907", "hashOfConfig": "734"}, {"size": 6909, "mtime": 1752078894910, "results": "908", "hashOfConfig": "734"}, {"size": 7677, "mtime": 1752078894910, "results": "909", "hashOfConfig": "734"}, {"size": 2322, "mtime": 1752078894910, "results": "910", "hashOfConfig": "734"}, {"size": 7209, "mtime": 1752678129103, "results": "911", "hashOfConfig": "734"}, {"size": 558, "mtime": 1753513910116, "results": "912", "hashOfConfig": "734"}, {"size": 10540, "mtime": 1753514869215, "results": "913", "hashOfConfig": "734"}, {"size": 733, "mtime": 1752078894910, "results": "914", "hashOfConfig": "734"}, {"size": 1203, "mtime": 1752078894910, "results": "915", "hashOfConfig": "734"}, {"size": 1827, "mtime": 1752078894910, "results": "916", "hashOfConfig": "734"}, {"size": 2950, "mtime": 1753514279872, "results": "917", "hashOfConfig": "734"}, {"size": 18116, "mtime": 1753794776165, "results": "918", "hashOfConfig": "734"}, {"size": 15615, "mtime": 1752763206921, "results": "919", "hashOfConfig": "734"}, {"size": 4083, "mtime": 1752762551074, "results": "920", "hashOfConfig": "734"}, {"size": 2929, "mtime": 1752527064479, "results": "921", "hashOfConfig": "734"}, {"size": 4305, "mtime": 1752763625974, "results": "922", "hashOfConfig": "734"}, {"size": 5476, "mtime": 1752763083916, "results": "923", "hashOfConfig": "734"}, {"size": 23276, "mtime": 1752527499610, "results": "924", "hashOfConfig": "734"}, {"size": 1602, "mtime": 1753251129502, "results": "925", "hashOfConfig": "734"}, {"size": 1380, "mtime": 1752762960440, "results": "926", "hashOfConfig": "734"}, {"size": 3464, "mtime": 1752696729504, "results": "927", "hashOfConfig": "734"}, {"size": 684, "mtime": 1754684394970, "results": "928", "hashOfConfig": "734"}, {"size": 2007, "mtime": 1754684394970, "results": "929", "hashOfConfig": "734"}, {"size": 3579, "mtime": 1754684394971, "results": "930", "hashOfConfig": "734"}, {"size": 1181, "mtime": 1754684394972, "results": "931", "hashOfConfig": "734"}, {"size": 2665, "mtime": 1754684394972, "results": "932", "hashOfConfig": "734"}, {"size": 6679, "mtime": 1754684394973, "results": "933", "hashOfConfig": "734"}, {"size": 3744, "mtime": 1754684394974, "results": "934", "hashOfConfig": "734"}, {"size": 6038, "mtime": 1754684394968, "results": "935", "hashOfConfig": "734"}, {"size": 1270, "mtime": 1754684394969, "results": "936", "hashOfConfig": "734"}, {"size": 2007, "mtime": 1752078894925, "results": "937", "hashOfConfig": "734"}, {"size": 5704, "mtime": 1752078894941, "results": "938", "hashOfConfig": "734"}, {"size": 4918, "mtime": 1754684984809, "results": "939", "hashOfConfig": "734"}, {"size": 4615, "mtime": 1752078894941, "results": "940", "hashOfConfig": "734"}, {"size": 5211, "mtime": 1752078894941, "results": "941", "hashOfConfig": "734"}, {"size": 5725, "mtime": 1752078894941, "results": "942", "hashOfConfig": "734"}, {"size": 4799, "mtime": 1752078894941, "results": "943", "hashOfConfig": "734"}, {"size": 7774, "mtime": 1752078894941, "results": "944", "hashOfConfig": "734"}, {"size": 2147, "mtime": 1752078894941, "results": "945", "hashOfConfig": "734"}, {"size": 5445, "mtime": 1754781877844, "results": "946", "hashOfConfig": "734"}, {"size": 737, "mtime": 1752078894941, "results": "947", "hashOfConfig": "734"}, {"size": 5712, "mtime": 1752078894941, "results": "948", "hashOfConfig": "734"}, {"size": 8862, "mtime": 1752078894941, "results": "949", "hashOfConfig": "734"}, {"size": 3996, "mtime": 1752078894941, "results": "950", "hashOfConfig": "734"}, {"size": 6753, "mtime": 1752078894941, "results": "951", "hashOfConfig": "734"}, {"size": 6562, "mtime": 1752078894941, "results": "952", "hashOfConfig": "734"}, {"size": 1779, "mtime": 1752078894941, "results": "953", "hashOfConfig": "734"}, {"size": 9940, "mtime": 1753436763363, "results": "954", "hashOfConfig": "734"}, {"size": 161, "mtime": 1752078894941, "results": "955", "hashOfConfig": "734"}, {"size": 189, "mtime": 1752078894956, "results": "956", "hashOfConfig": "734"}, {"size": 565, "mtime": 1752078894956, "results": "957", "hashOfConfig": "734"}, {"size": 8610, "mtime": 1752078894956, "results": "958", "hashOfConfig": "734"}, {"size": 1870, "mtime": 1752078894956, "results": "959", "hashOfConfig": "734"}, {"size": 2516, "mtime": 1752078894956, "results": "960", "hashOfConfig": "734"}, {"size": 14853, "mtime": 1752080815937, "results": "961", "hashOfConfig": "734"}, {"size": 4728, "mtime": 1753978897165, "results": "962", "hashOfConfig": "734"}, {"size": 2890, "mtime": 1752078894956, "results": "963", "hashOfConfig": "734"}, {"size": 1924, "mtime": 1752078894956, "results": "964", "hashOfConfig": "734"}, {"size": 2239, "mtime": 1752078894956, "results": "965", "hashOfConfig": "734"}, {"size": 1646, "mtime": 1752078894956, "results": "966", "hashOfConfig": "734"}, {"size": 5334, "mtime": 1752078894972, "results": "967", "hashOfConfig": "734"}, {"size": 1022, "mtime": 1754687848231, "results": "968", "hashOfConfig": "734"}, {"size": 3451, "mtime": 1752078894956, "results": "969", "hashOfConfig": "734"}, {"size": 5179, "mtime": 1752078894956, "results": "970", "hashOfConfig": "734"}, {"size": 4986, "mtime": 1752078894956, "results": "971", "hashOfConfig": "734"}, {"size": 4326, "mtime": 1752078894972, "results": "972", "hashOfConfig": "734"}, {"size": 411, "mtime": 1752078894988, "results": "973", "hashOfConfig": "734"}, {"size": 4822, "mtime": 1752078894975, "results": "974", "hashOfConfig": "734"}, {"size": 4706, "mtime": 1752078894975, "results": "975", "hashOfConfig": "734"}, {"size": 4957, "mtime": 1752078894976, "results": "976", "hashOfConfig": "734"}, {"size": 3810, "mtime": 1754689119726, "results": "977", "hashOfConfig": "734"}, {"size": 18438, "mtime": 1752078894978, "results": "978", "hashOfConfig": "734"}, {"size": 1266, "mtime": 1752078894978, "results": "979", "hashOfConfig": "734"}, {"size": 8177, "mtime": 1752078894979, "results": "980", "hashOfConfig": "734"}, {"size": 1581, "mtime": 1752078894980, "results": "981", "hashOfConfig": "734"}, {"size": 1353, "mtime": 1754688208018, "results": "982", "hashOfConfig": "734"}, {"size": 1799, "mtime": 1752078894980, "results": "983", "hashOfConfig": "734"}, {"size": 6966, "mtime": 1752078894981, "results": "984", "hashOfConfig": "734"}, {"size": 295, "mtime": 1752078894981, "results": "985", "hashOfConfig": "734"}, {"size": 3916, "mtime": 1752078894982, "results": "986", "hashOfConfig": "734"}, {"size": 5877, "mtime": 1752078894983, "results": "987", "hashOfConfig": "734"}, {"size": 18346, "mtime": 1752078894983, "results": "988", "hashOfConfig": "734"}, {"size": 897, "mtime": 1752078894983, "results": "989", "hashOfConfig": "734"}, {"size": 3936, "mtime": 1752078894983, "results": "990", "hashOfConfig": "734"}, {"size": 23617, "mtime": 1752078894983, "results": "991", "hashOfConfig": "734"}, {"size": 3305, "mtime": 1752078894983, "results": "992", "hashOfConfig": "734"}, {"size": 3489, "mtime": 1752078894983, "results": "993", "hashOfConfig": "734"}, {"size": 1739, "mtime": 1752078894988, "results": "994", "hashOfConfig": "734"}, {"size": 6529, "mtime": 1752078894988, "results": "995", "hashOfConfig": "734"}, {"size": 3222, "mtime": 1752078894988, "results": "996", "hashOfConfig": "734"}, {"size": 5762, "mtime": 1752078894988, "results": "997", "hashOfConfig": "734"}, {"size": 3984, "mtime": 1752078894988, "results": "998", "hashOfConfig": "734"}, {"size": 134, "mtime": 1752078894988, "results": "999", "hashOfConfig": "734"}, {"size": 848, "mtime": 1752078894988, "results": "1000", "hashOfConfig": "734"}, {"size": 2993, "mtime": 1752078894988, "results": "1001", "hashOfConfig": "734"}, {"size": 5827, "mtime": 1752078894956, "results": "1002", "hashOfConfig": "734"}, {"size": 4054, "mtime": 1752078894988, "results": "1003", "hashOfConfig": "734"}, {"size": 5287, "mtime": 1752078894988, "results": "1004", "hashOfConfig": "734"}, {"size": 3121, "mtime": 1752078894988, "results": "1005", "hashOfConfig": "734"}, {"size": 3264, "mtime": 1752078894988, "results": "1006", "hashOfConfig": "734"}, {"size": 2619, "mtime": 1752078894988, "results": "1007", "hashOfConfig": "734"}, {"size": 3681, "mtime": 1752078894956, "results": "1008", "hashOfConfig": "734"}, {"size": 5286, "mtime": 1752080815937, "results": "1009", "hashOfConfig": "734"}, {"size": 4433, "mtime": 1752078895003, "results": "1010", "hashOfConfig": "734"}, {"size": 5822, "mtime": 1752078894988, "results": "1011", "hashOfConfig": "734"}, {"size": 5776, "mtime": 1752078894988, "results": "1012", "hashOfConfig": "734"}, {"size": 13788, "mtime": 1752078894988, "results": "1013", "hashOfConfig": "734"}, {"size": 9337, "mtime": 1752078895003, "results": "1014", "hashOfConfig": "734"}, {"size": 3219, "mtime": 1752078894988, "results": "1015", "hashOfConfig": "734"}, {"size": 3306, "mtime": 1752078895003, "results": "1016", "hashOfConfig": "734"}, {"size": 14293, "mtime": 1752078895003, "results": "1017", "hashOfConfig": "734"}, {"size": 827, "mtime": 1752078895003, "results": "1018", "hashOfConfig": "734"}, {"size": 5306, "mtime": 1754688273590, "results": "1019", "hashOfConfig": "734"}, {"size": 6730, "mtime": 1752080815952, "results": "1020", "hashOfConfig": "734"}, {"size": 20200, "mtime": 1754688352894, "results": "1021", "hashOfConfig": "734"}, {"size": 10373, "mtime": 1754782777333, "results": "1022", "hashOfConfig": "734"}, {"size": 2735, "mtime": 1752078895003, "results": "1023", "hashOfConfig": "734"}, {"size": 925, "mtime": 1752078895003, "results": "1024", "hashOfConfig": "734"}, {"size": 1213, "mtime": 1752078895003, "results": "1025", "hashOfConfig": "734"}, {"size": 8134, "mtime": 1752078895003, "results": "1026", "hashOfConfig": "734"}, {"size": 957, "mtime": 1752078895003, "results": "1027", "hashOfConfig": "734"}, {"size": 2264, "mtime": 1752078895003, "results": "1028", "hashOfConfig": "734"}, {"size": 1677, "mtime": 1752078895003, "results": "1029", "hashOfConfig": "734"}, {"size": 1034, "mtime": 1752078895003, "results": "1030", "hashOfConfig": "734"}, {"size": 5544, "mtime": 1752078895003, "results": "1031", "hashOfConfig": "734"}, {"size": 2483, "mtime": 1752078895019, "results": "1032", "hashOfConfig": "734"}, {"size": 1092, "mtime": 1752078895019, "results": "1033", "hashOfConfig": "734"}, {"size": 4532, "mtime": 1752078895019, "results": "1034", "hashOfConfig": "734"}, {"size": 6920, "mtime": 1752080815952, "results": "1035", "hashOfConfig": "734"}, {"size": 3574, "mtime": 1752080815952, "results": "1036", "hashOfConfig": "734"}, {"size": 794, "mtime": 1752078895019, "results": "1037", "hashOfConfig": "734"}, {"size": 1902, "mtime": 1752078895019, "results": "1038", "hashOfConfig": "734"}, {"size": 1420, "mtime": 1752078895023, "results": "1039", "hashOfConfig": "734"}, {"size": 24194, "mtime": 1752080815952, "results": "1040", "hashOfConfig": "734"}, {"size": 555, "mtime": 1752078895023, "results": "1041", "hashOfConfig": "734"}, {"size": 4100, "mtime": 1752078895023, "results": "1042", "hashOfConfig": "734"}, {"size": 15578, "mtime": 1752078895023, "results": "1043", "hashOfConfig": "734"}, {"size": 3228, "mtime": 1752078895023, "results": "1044", "hashOfConfig": "734"}, {"size": 3514, "mtime": 1752078895023, "results": "1045", "hashOfConfig": "734"}, {"size": 23175, "mtime": 1752078895023, "results": "1046", "hashOfConfig": "734"}, {"size": 2060, "mtime": 1752078895023, "results": "1047", "hashOfConfig": "734"}, {"size": 16492, "mtime": 1752078895023, "results": "1048", "hashOfConfig": "734"}, {"size": 1149, "mtime": 1752078895023, "results": "1049", "hashOfConfig": "734"}, {"size": 3631, "mtime": 1752078895023, "results": "1050", "hashOfConfig": "734"}, {"size": 1859, "mtime": 1752078895023, "results": "1051", "hashOfConfig": "734"}, {"size": 4207, "mtime": 1752078895023, "results": "1052", "hashOfConfig": "734"}, {"size": 5060, "mtime": 1752078895023, "results": "1053", "hashOfConfig": "734"}, {"size": 3993, "mtime": 1752078895023, "results": "1054", "hashOfConfig": "734"}, {"size": 3872, "mtime": 1752078895023, "results": "1055", "hashOfConfig": "734"}, {"size": 1420, "mtime": 1752078895023, "results": "1056", "hashOfConfig": "734"}, {"size": 4730, "mtime": 1752078895035, "results": "1057", "hashOfConfig": "734"}, {"size": 5956, "mtime": 1752078895035, "results": "1058", "hashOfConfig": "734"}, {"size": 244, "mtime": 1752078895035, "results": "1059", "hashOfConfig": "734"}, {"size": 671, "mtime": 1752080815952, "results": "1060", "hashOfConfig": "734"}, {"size": 9220, "mtime": 1752078895035, "results": "1061", "hashOfConfig": "734"}, {"size": 9703, "mtime": 1752080815952, "results": "1062", "hashOfConfig": "734"}, {"size": 10536, "mtime": 1752080815952, "results": "1063", "hashOfConfig": "734"}, {"size": 12559, "mtime": 1752078895035, "results": "1064", "hashOfConfig": "734"}, {"size": 2402, "mtime": 1752080815952, "results": "1065", "hashOfConfig": "734"}, {"size": 1517, "mtime": 1752078895003, "results": "1066", "hashOfConfig": "734"}, {"size": 1951, "mtime": 1752078895003, "results": "1067", "hashOfConfig": "734"}, {"size": 4017, "mtime": 1752078895035, "results": "1068", "hashOfConfig": "734"}, {"size": 3456, "mtime": 1752078895035, "results": "1069", "hashOfConfig": "734"}, {"size": 4833, "mtime": 1752078895035, "results": "1070", "hashOfConfig": "734"}, {"size": 3938, "mtime": 1752078895035, "results": "1071", "hashOfConfig": "734"}, {"size": 5522, "mtime": 1752078895035, "results": "1072", "hashOfConfig": "734"}, {"size": 5183, "mtime": 1752078895035, "results": "1073", "hashOfConfig": "734"}, {"size": 7170, "mtime": 1752078895035, "results": "1074", "hashOfConfig": "734"}, {"size": 8695, "mtime": 1752078895051, "results": "1075", "hashOfConfig": "734"}, {"size": 1462, "mtime": 1754682285579, "results": "1076", "hashOfConfig": "734"}, {"size": 2053, "mtime": 1752078895051, "results": "1077", "hashOfConfig": "734"}, {"size": 1135, "mtime": 1752078895051, "results": "1078", "hashOfConfig": "734"}, {"size": 7593, "mtime": 1754688394557, "results": "1079", "hashOfConfig": "734"}, {"size": 1655, "mtime": 1752078895051, "results": "1080", "hashOfConfig": "734"}, {"size": 6285, "mtime": 1754776494017, "results": "1081", "hashOfConfig": "734"}, {"size": 1388, "mtime": 1754774546147, "results": "1082", "hashOfConfig": "734"}, {"size": 8153, "mtime": 1752498088570, "results": "1083", "hashOfConfig": "734"}, {"size": 4771, "mtime": 1752485239443, "results": "1084", "hashOfConfig": "734"}, {"size": 5040, "mtime": 1753436763374, "results": "1085", "hashOfConfig": "734"}, {"size": 13600, "mtime": 1754787610493, "results": "1086", "hashOfConfig": "734"}, {"size": 1389, "mtime": 1752596854741, "results": "1087", "hashOfConfig": "734"}, {"size": 3608, "mtime": 1754054811704, "results": "1088", "hashOfConfig": "734"}, {"size": 14232, "mtime": 1752078895066, "results": "1089", "hashOfConfig": "734"}, {"size": 1626, "mtime": 1752078895066, "results": "1090", "hashOfConfig": "734"}, {"size": 14197, "mtime": 1752078895066, "results": "1091", "hashOfConfig": "734"}, {"size": 820, "mtime": 1752078895066, "results": "1092", "hashOfConfig": "734"}, {"size": 15316, "mtime": 1752078895066, "results": "1093", "hashOfConfig": "734"}, {"size": 1887, "mtime": 1752078895066, "results": "1094", "hashOfConfig": "734"}, {"size": 12380, "mtime": 1752078895066, "results": "1095", "hashOfConfig": "734"}, {"size": 1946, "mtime": 1752078895066, "results": "1096", "hashOfConfig": "734"}, {"size": 3999, "mtime": 1752078895066, "results": "1097", "hashOfConfig": "734"}, {"size": 6385, "mtime": 1752078895066, "results": "1098", "hashOfConfig": "734"}, {"size": 9551, "mtime": 1752078895066, "results": "1099", "hashOfConfig": "734"}, {"size": 13651, "mtime": 1752078895066, "results": "1100", "hashOfConfig": "734"}, {"size": 1826, "mtime": 1752078895066, "results": "1101", "hashOfConfig": "734"}, {"size": 1920, "mtime": 1752078895082, "results": "1102", "hashOfConfig": "734"}, {"size": 13936, "mtime": 1752078895082, "results": "1103", "hashOfConfig": "734"}, {"size": 1862, "mtime": 1752078895082, "results": "1104", "hashOfConfig": "734"}, {"size": 13015, "mtime": 1752078895082, "results": "1105", "hashOfConfig": "734"}, {"size": 13703, "mtime": 1752991606206, "results": "1106", "hashOfConfig": "734"}, {"size": 1899, "mtime": 1752078895086, "results": "1107", "hashOfConfig": "734"}, {"size": 11444, "mtime": 1752078895086, "results": "1108", "hashOfConfig": "734"}, {"size": 14967, "mtime": 1754685036558, "results": "1109", "hashOfConfig": "734"}, {"size": 852, "mtime": 1752078895086, "results": "1110", "hashOfConfig": "734"}, {"size": 1676, "mtime": 1753436763374, "results": "1111", "hashOfConfig": "734"}, {"size": 13805, "mtime": 1754781936396, "results": "1112", "hashOfConfig": "734"}, {"size": 759, "mtime": 1752588678090, "results": "1113", "hashOfConfig": "734"}, {"size": 4539, "mtime": 1754679915142, "results": "1114", "hashOfConfig": "734"}, {"size": 2734, "mtime": 1754682896407, "results": "1115", "hashOfConfig": "734"}, {"size": 9997, "mtime": 1752588678105, "results": "1116", "hashOfConfig": "734"}, {"size": 3634, "mtime": 1752078895086, "results": "1117", "hashOfConfig": "734"}, {"size": 10759, "mtime": 1752078895086, "results": "1118", "hashOfConfig": "734"}, {"size": 916, "mtime": 1754679309005, "results": "1119", "hashOfConfig": "734"}, {"size": 2307, "mtime": 1754679646492, "results": "1120", "hashOfConfig": "734"}, {"size": 7300, "mtime": 1754688132602, "results": "1121", "hashOfConfig": "734"}, {"size": 4953, "mtime": 1752945992633, "results": "1122", "hashOfConfig": "734"}, {"size": 2913, "mtime": 1752078895099, "results": "1123", "hashOfConfig": "734"}, {"size": 1680, "mtime": 1752213788048, "results": "1124", "hashOfConfig": "734"}, {"size": 586, "mtime": 1752078895100, "results": "1125", "hashOfConfig": "734"}, {"size": 6465, "mtime": 1754679811177, "results": "1126", "hashOfConfig": "734"}, {"size": 1230, "mtime": 1752078895100, "results": "1127", "hashOfConfig": "734"}, {"size": 3294, "mtime": 1754679775783, "results": "1128", "hashOfConfig": "734"}, {"size": 1363, "mtime": 1753794776165, "results": "1129", "hashOfConfig": "734"}, {"size": 1215, "mtime": 1753436763421, "results": "1130", "hashOfConfig": "734"}, {"size": 2508, "mtime": 1752078895195, "results": "1131", "hashOfConfig": "734"}, {"size": 852, "mtime": 1752078895240, "results": "1132", "hashOfConfig": "734"}, {"size": 4790, "mtime": 1752078895240, "results": "1133", "hashOfConfig": "734"}, {"size": 9134, "mtime": 1754779839125, "results": "1134", "hashOfConfig": "734"}, {"size": 696, "mtime": 1752078895240, "results": "1135", "hashOfConfig": "734"}, {"size": 3247, "mtime": 1752078895240, "results": "1136", "hashOfConfig": "734"}, {"size": 16794, "mtime": 1754649919219, "results": "1137", "hashOfConfig": "734"}, {"size": 6011, "mtime": 1754684965840, "results": "1138", "hashOfConfig": "734"}, {"size": 605, "mtime": 1752078895240, "results": "1139", "hashOfConfig": "734"}, {"size": 9700, "mtime": 1752686119805, "results": "1140", "hashOfConfig": "734"}, {"size": 706, "mtime": 1752078895240, "results": "1141", "hashOfConfig": "734"}, {"size": 1299, "mtime": 1752078895256, "results": "1142", "hashOfConfig": "734"}, {"size": 918, "mtime": 1752078895256, "results": "1143", "hashOfConfig": "734"}, {"size": 1155, "mtime": 1752078895256, "results": "1144", "hashOfConfig": "734"}, {"size": 1050, "mtime": 1752078895256, "results": "1145", "hashOfConfig": "734"}, {"size": 573, "mtime": 1752078895256, "results": "1146", "hashOfConfig": "734"}, {"size": 1580, "mtime": 1752078895256, "results": "1147", "hashOfConfig": "734"}, {"size": 766, "mtime": 1752078895256, "results": "1148", "hashOfConfig": "734"}, {"size": 3070, "mtime": 1752078895240, "results": "1149", "hashOfConfig": "734"}, {"size": 1187, "mtime": 1752078895240, "results": "1150", "hashOfConfig": "734"}, {"size": 560, "mtime": 1752078895240, "results": "1151", "hashOfConfig": "734"}, {"size": 10211, "mtime": 1754681172346, "results": "1152", "hashOfConfig": "734"}, {"size": 387, "mtime": 1752078895240, "results": "1153", "hashOfConfig": "734"}, {"size": 10905, "mtime": 1752078895240, "results": "1154", "hashOfConfig": "734"}, {"size": 1430, "mtime": 1754687983587, "results": "1155", "hashOfConfig": "734"}, {"size": 928, "mtime": 1752078895256, "results": "1156", "hashOfConfig": "734"}, {"size": 11881, "mtime": 1752078895256, "results": "1157", "hashOfConfig": "734"}, {"size": 5025, "mtime": 1754649942004, "results": "1158", "hashOfConfig": "734"}, {"size": 12508, "mtime": 1754649979094, "results": "1159", "hashOfConfig": "734"}, {"size": 8293, "mtime": 1752078895256, "results": "1160", "hashOfConfig": "734"}, {"size": 8100, "mtime": 1752078895256, "results": "1161", "hashOfConfig": "734"}, {"size": 468, "mtime": 1752078895240, "results": "1162", "hashOfConfig": "734"}, {"size": 8404, "mtime": 1752078895256, "results": "1163", "hashOfConfig": "734"}, {"size": 420, "mtime": 1752078895271, "results": "1164", "hashOfConfig": "734"}, {"size": 8986, "mtime": 1753531001344, "results": "1165", "hashOfConfig": "734"}, {"size": 2536, "mtime": 1752078895256, "results": "1166", "hashOfConfig": "734"}, {"size": 3620, "mtime": 1753514198416, "results": "1167", "hashOfConfig": "734"}, {"size": 2764, "mtime": 1752078895256, "results": "1168", "hashOfConfig": "734"}, {"size": 2336, "mtime": 1752078895256, "results": "1169", "hashOfConfig": "734"}, {"size": 15343, "mtime": 1752078895271, "results": "1170", "hashOfConfig": "734"}, {"size": 2399, "mtime": 1752078895271, "results": "1171", "hashOfConfig": "734"}, {"size": 2458, "mtime": 1753515135770, "results": "1172", "hashOfConfig": "734"}, {"size": 532, "mtime": 1754678641865, "results": "1173", "hashOfConfig": "734"}, {"size": 7698, "mtime": 1754678641851, "results": "1174", "hashOfConfig": "734"}, {"size": 2870, "mtime": 1754678641861, "results": "1175", "hashOfConfig": "734"}, {"size": 3578, "mtime": 1754678641862, "results": "1176", "hashOfConfig": "734"}, {"size": 3654, "mtime": 1754678641863, "results": "1177", "hashOfConfig": "734"}, {"size": 2711, "mtime": 1754678641864, "results": "1178", "hashOfConfig": "734"}, {"size": 5073, "mtime": 1752078895240, "results": "1179", "hashOfConfig": "734"}, {"size": 468, "mtime": 1752078895293, "results": "1180", "hashOfConfig": "734"}, {"size": 2875, "mtime": 1752078895293, "results": "1181", "hashOfConfig": "734"}, {"size": 7631, "mtime": 1754787417179, "results": "1182", "hashOfConfig": "734"}, {"size": 3476, "mtime": 1752078895293, "results": "1183", "hashOfConfig": "734"}, {"size": 2968, "mtime": 1752078895293, "results": "1184", "hashOfConfig": "734"}, {"size": 7352, "mtime": 1754392096900, "results": "1185", "hashOfConfig": "734"}, {"size": 543, "mtime": 1752078895293, "results": "1186", "hashOfConfig": "734"}, {"size": 881, "mtime": 1752078895303, "results": "1187", "hashOfConfig": "734"}, {"size": 563, "mtime": 1752078895303, "results": "1188", "hashOfConfig": "734"}, {"size": 5611, "mtime": 1752078895303, "results": "1189", "hashOfConfig": "734"}, {"size": 2417, "mtime": 1752078895303, "results": "1190", "hashOfConfig": "734"}, {"size": 2958, "mtime": 1752078895303, "results": "1191", "hashOfConfig": "734"}, {"size": 140, "mtime": 1752078895303, "results": "1192", "hashOfConfig": "734"}, {"size": 5043, "mtime": 1752078895303, "results": "1193", "hashOfConfig": "734"}, {"size": 3818, "mtime": 1752078895303, "results": "1194", "hashOfConfig": "734"}, {"size": 6246, "mtime": 1752078895303, "results": "1195", "hashOfConfig": "734"}, {"size": 6801, "mtime": 1752078895303, "results": "1196", "hashOfConfig": "734"}, {"size": 2232, "mtime": 1752078895303, "results": "1197", "hashOfConfig": "734"}, {"size": 1235, "mtime": 1752078895303, "results": "1198", "hashOfConfig": "734"}, {"size": 2119, "mtime": 1752078895293, "results": "1199", "hashOfConfig": "734"}, {"size": 3747, "mtime": 1752078895293, "results": "1200", "hashOfConfig": "734"}, {"size": 2394, "mtime": 1752078895303, "results": "1201", "hashOfConfig": "734"}, {"size": 431, "mtime": 1752078895303, "results": "1202", "hashOfConfig": "734"}, {"size": 1606, "mtime": 1752078895303, "results": "1203", "hashOfConfig": "734"}, {"size": 3178, "mtime": 1753436763424, "results": "1204", "hashOfConfig": "734"}, {"size": 2689, "mtime": 1752078895303, "results": "1205", "hashOfConfig": "734"}, {"size": 1735, "mtime": 1752080815962, "results": "1206", "hashOfConfig": "734"}, {"size": 1639, "mtime": 1752080815962, "results": "1207", "hashOfConfig": "734"}, {"size": 4133, "mtime": 1754689148380, "results": "1208", "hashOfConfig": "734"}, {"size": 2860, "mtime": 1752078895114, "results": "1209", "hashOfConfig": "734"}, {"size": 8058, "mtime": 1752078895100, "results": "1210", "hashOfConfig": "734"}, {"size": 19772, "mtime": 1752080815952, "results": "1211", "hashOfConfig": "734"}, {"size": 6916, "mtime": 1752078895100, "results": "1212", "hashOfConfig": "734"}, {"size": 5528, "mtime": 1754689166011, "results": "1213", "hashOfConfig": "734"}, {"size": 2806, "mtime": 1752078895100, "results": "1214", "hashOfConfig": "734"}, {"size": 8383, "mtime": 1753436763388, "results": "1215", "hashOfConfig": "734"}, {"size": 14166, "mtime": 1754688410700, "results": "1216", "hashOfConfig": "734"}, {"size": 6718, "mtime": 1752078895111, "results": "1217", "hashOfConfig": "734"}, {"size": 5950, "mtime": 1753436763388, "results": "1218", "hashOfConfig": "734"}, {"size": 3064, "mtime": 1752078895114, "results": "1219", "hashOfConfig": "734"}, {"size": 1050, "mtime": 1752078895114, "results": "1220", "hashOfConfig": "734"}, {"size": 22169, "mtime": 1752078895114, "results": "1221", "hashOfConfig": "734"}, {"size": 9131, "mtime": 1753436763388, "results": "1222", "hashOfConfig": "734"}, {"size": 4097, "mtime": 1754689319994, "results": "1223", "hashOfConfig": "734"}, {"size": 1237, "mtime": 1752078895114, "results": "1224", "hashOfConfig": "734"}, {"size": 456, "mtime": 1752078895114, "results": "1225", "hashOfConfig": "734"}, {"size": 14885, "mtime": 1754788954276, "results": "1226", "hashOfConfig": "734"}, {"size": 16038, "mtime": 1754779682302, "results": "1227", "hashOfConfig": "734"}, {"size": 1555, "mtime": 1752078895123, "results": "1228", "hashOfConfig": "734"}, {"size": 12447, "mtime": 1752078895123, "results": "1229", "hashOfConfig": "734"}, {"size": 3075, "mtime": 1752078895123, "results": "1230", "hashOfConfig": "734"}, {"size": 2658, "mtime": 1752078895123, "results": "1231", "hashOfConfig": "734"}, {"size": 4697, "mtime": 1752078895123, "results": "1232", "hashOfConfig": "734"}, {"size": 22440, "mtime": 1753105300361, "results": "1233", "hashOfConfig": "734"}, {"size": 3730, "mtime": 1752078895123, "results": "1234", "hashOfConfig": "734"}, {"size": 12541, "mtime": 1753977611947, "results": "1235", "hashOfConfig": "734"}, {"size": 3044, "mtime": 1752078895129, "results": "1236", "hashOfConfig": "734"}, {"size": 5097, "mtime": 1752078895129, "results": "1237", "hashOfConfig": "734"}, {"size": 9601, "mtime": 1754689376978, "results": "1238", "hashOfConfig": "734"}, {"size": 2253, "mtime": 1752078895123, "results": "1239", "hashOfConfig": "734"}, {"size": 3263, "mtime": 1753436763388, "results": "1240", "hashOfConfig": "734"}, {"size": 5326, "mtime": 1752080815962, "results": "1241", "hashOfConfig": "734"}, {"size": 5995, "mtime": 1752078895316, "results": "1242", "hashOfConfig": "734"}, {"size": 3946, "mtime": 1752078895319, "results": "1243", "hashOfConfig": "734"}, {"size": 8264, "mtime": 1752080815962, "results": "1244", "hashOfConfig": "734"}, {"size": 3007, "mtime": 1752078895320, "results": "1245", "hashOfConfig": "734"}, {"size": 4189, "mtime": 1752078895321, "results": "1246", "hashOfConfig": "734"}, {"size": 9778, "mtime": 1752078895321, "results": "1247", "hashOfConfig": "734"}, {"size": 10169, "mtime": 1752080815962, "results": "1248", "hashOfConfig": "734"}, {"size": 10217, "mtime": 1752080815971, "results": "1249", "hashOfConfig": "734"}, {"size": 6291, "mtime": 1752078895323, "results": "1250", "hashOfConfig": "734"}, {"size": 7264, "mtime": 1752078895323, "results": "1251", "hashOfConfig": "734"}, {"size": 7194, "mtime": 1752078895323, "results": "1252", "hashOfConfig": "734"}, {"size": 3629, "mtime": 1752682891985, "results": "1253", "hashOfConfig": "734"}, {"size": 8662, "mtime": 1752078895323, "results": "1254", "hashOfConfig": "734"}, {"size": 4435, "mtime": 1753101910685, "results": "1255", "hashOfConfig": "734"}, {"size": 19439, "mtime": 1752078895335, "results": "1256", "hashOfConfig": "734"}, {"size": 7315, "mtime": 1752078895335, "results": "1257", "hashOfConfig": "734"}, {"size": 8073, "mtime": 1752078895335, "results": "1258", "hashOfConfig": "734"}, {"size": 2529, "mtime": 1753794776171, "results": "1259", "hashOfConfig": "734"}, {"size": 8788, "mtime": 1752080815971, "results": "1260", "hashOfConfig": "734"}, {"size": 459, "mtime": 1752683108071, "results": "1261", "hashOfConfig": "734"}, {"size": 919, "mtime": 1752078895323, "results": "1262", "hashOfConfig": "734"}, {"size": 26895, "mtime": 1754649551289, "results": "1263", "hashOfConfig": "734"}, {"size": 6952, "mtime": 1754667999946, "results": "1264", "hashOfConfig": "734"}, {"size": 3447, "mtime": 1752078895323, "results": "1265", "hashOfConfig": "734"}, {"size": 41540, "mtime": 1752925036472, "results": "1266", "hashOfConfig": "734"}, {"size": 18331, "mtime": 1752925036472, "results": "1267", "hashOfConfig": "734"}, {"size": 2729, "mtime": 1754649572073, "results": "1268", "hashOfConfig": "734"}, {"size": 935, "mtime": 1752078895335, "results": "1269", "hashOfConfig": "734"}, {"size": 5900, "mtime": 1754787189956, "results": "1270", "hashOfConfig": "734"}, {"size": 1935, "mtime": 1752078895335, "results": "1271", "hashOfConfig": "734"}, {"size": 8518, "mtime": 1754787731339, "results": "1272", "hashOfConfig": "734"}, {"size": 7855, "mtime": 1754392096910, "results": "1273", "hashOfConfig": "734"}, {"size": 8965, "mtime": 1754392096910, "results": "1274", "hashOfConfig": "734"}, {"size": 9258, "mtime": 1754687423156, "results": "1275", "hashOfConfig": "734"}, {"size": 7671, "mtime": 1754678664660, "results": "1276", "hashOfConfig": "734"}, {"size": 5281, "mtime": 1752078895335, "results": "1277", "hashOfConfig": "734"}, {"size": 5670, "mtime": 1753436763427, "results": "1278", "hashOfConfig": "734"}, {"size": 4400, "mtime": 1752078895335, "results": "1279", "hashOfConfig": "734"}, {"size": 5504, "mtime": 1753436763427, "results": "1280", "hashOfConfig": "734"}, {"size": 638, "mtime": 1752078895335, "results": "1281", "hashOfConfig": "734"}, {"size": 2119, "mtime": 1752078895335, "results": "1282", "hashOfConfig": "734"}, {"size": 4021, "mtime": 1752078895335, "results": "1283", "hashOfConfig": "734"}, {"size": 1680, "mtime": 1752078895335, "results": "1284", "hashOfConfig": "734"}, {"size": 1150, "mtime": 1752078895335, "results": "1285", "hashOfConfig": "734"}, {"size": 1677, "mtime": 1752078895335, "results": "1286", "hashOfConfig": "734"}, {"size": 2466, "mtime": 1752078895335, "results": "1287", "hashOfConfig": "734"}, {"size": 2199, "mtime": 1754076729971, "results": "1288", "hashOfConfig": "734"}, {"size": 2995, "mtime": 1752078895335, "results": "1289", "hashOfConfig": "734"}, {"size": 2081, "mtime": 1752078895350, "results": "1290", "hashOfConfig": "734"}, {"size": 5798, "mtime": 1752078895350, "results": "1291", "hashOfConfig": "734"}, {"size": 2814, "mtime": 1752078895350, "results": "1292", "hashOfConfig": "734"}, {"size": 10137, "mtime": 1752078895350, "results": "1293", "hashOfConfig": "734"}, {"size": 1258, "mtime": 1752078895350, "results": "1294", "hashOfConfig": "734"}, {"size": 833, "mtime": 1752078895350, "results": "1295", "hashOfConfig": "734"}, {"size": 4833, "mtime": 1752078895350, "results": "1296", "hashOfConfig": "734"}, {"size": 4119, "mtime": 1752078895350, "results": "1297", "hashOfConfig": "734"}, {"size": 8541, "mtime": 1752078895350, "results": "1298", "hashOfConfig": "734"}, {"size": 3926, "mtime": 1752078895350, "results": "1299", "hashOfConfig": "734"}, {"size": 2331, "mtime": 1752078895350, "results": "1300", "hashOfConfig": "734"}, {"size": 988, "mtime": 1752078895350, "results": "1301", "hashOfConfig": "734"}, {"size": 635, "mtime": 1752078895350, "results": "1302", "hashOfConfig": "734"}, {"size": 6792, "mtime": 1752078895350, "results": "1303", "hashOfConfig": "734"}, {"size": 3090, "mtime": 1752078895350, "results": "1304", "hashOfConfig": "734"}, {"size": 1683, "mtime": 1752078895350, "results": "1305", "hashOfConfig": "734"}, {"size": 771, "mtime": 1752078895350, "results": "1306", "hashOfConfig": "734"}, {"size": 1511, "mtime": 1752078895350, "results": "1307", "hashOfConfig": "734"}, {"size": 8203, "mtime": 1752078895350, "results": "1308", "hashOfConfig": "734"}, {"size": 1703, "mtime": 1752078895350, "results": "1309", "hashOfConfig": "734"}, {"size": 2479, "mtime": 1752078895350, "results": "1310", "hashOfConfig": "734"}, {"size": 6438, "mtime": 1752078895350, "results": "1311", "hashOfConfig": "734"}, {"size": 732, "mtime": 1752078895350, "results": "1312", "hashOfConfig": "734"}, {"size": 4244, "mtime": 1752078895350, "results": "1313", "hashOfConfig": "734"}, {"size": 22737, "mtime": 1752078895366, "results": "1314", "hashOfConfig": "734"}, {"size": 289, "mtime": 1752078895366, "results": "1315", "hashOfConfig": "734"}, {"size": 2064, "mtime": 1752078895366, "results": "1316", "hashOfConfig": "734"}, {"size": 589, "mtime": 1752078895366, "results": "1317", "hashOfConfig": "734"}, {"size": 1208, "mtime": 1752078895366, "results": "1318", "hashOfConfig": "734"}, {"size": 2564, "mtime": 1752078895366, "results": "1319", "hashOfConfig": "734"}, {"size": 2035, "mtime": 1752078895366, "results": "1320", "hashOfConfig": "734"}, {"size": 777, "mtime": 1752078895366, "results": "1321", "hashOfConfig": "734"}, {"size": 3457, "mtime": 1752078895366, "results": "1322", "hashOfConfig": "734"}, {"size": 1952, "mtime": 1752078895366, "results": "1323", "hashOfConfig": "734"}, {"size": 145, "mtime": 1752078895366, "results": "1324", "hashOfConfig": "734"}, {"size": 831, "mtime": 1752078895366, "results": "1325", "hashOfConfig": "734"}, {"size": 3614, "mtime": 1753105671578, "results": "1326", "hashOfConfig": "734"}, {"size": 2546, "mtime": 1754780733076, "results": "1327", "hashOfConfig": "734"}, {"size": 6170, "mtime": 1754787762485, "results": "1328", "hashOfConfig": "734"}, {"size": 668, "mtime": 1752078895397, "results": "1329", "hashOfConfig": "734"}, {"size": 3705, "mtime": 1754787780072, "results": "1330", "hashOfConfig": "734"}, {"size": 2667, "mtime": 1754779771884, "results": "1331", "hashOfConfig": "734"}, {"size": 3593, "mtime": 1754787808579, "results": "1332", "hashOfConfig": "734"}, {"size": 1716, "mtime": 1754782056461, "results": "1333", "hashOfConfig": "734"}, {"size": 761, "mtime": 1754688103699, "results": "1334", "hashOfConfig": "734"}, {"size": 2364, "mtime": 1752078895397, "results": "1335", "hashOfConfig": "734"}, {"size": 10163, "mtime": 1754787827490, "results": "1336", "hashOfConfig": "734"}, {"size": 178, "mtime": 1752078895397, "results": "1337", "hashOfConfig": "734"}, {"size": 8054, "mtime": 1752925036485, "results": "1338", "hashOfConfig": "734"}, {"size": 198, "mtime": 1752078895397, "results": "1339", "hashOfConfig": "734"}, {"size": 6353, "mtime": 1752078895413, "results": "1340", "hashOfConfig": "734"}, {"size": 10585, "mtime": 1754686492459, "results": "1341", "hashOfConfig": "734"}, {"size": 14528, "mtime": 1754788946397, "results": "1342", "hashOfConfig": "734"}, {"size": 1712, "mtime": 1753101910685, "results": "1343", "hashOfConfig": "734"}, {"size": 6536, "mtime": 1753069810361, "results": "1344", "hashOfConfig": "734"}, {"size": 14131, "mtime": 1754333690839, "results": "1345", "hashOfConfig": "734"}, {"size": 3402, "mtime": 1754787876129, "results": "1346", "hashOfConfig": "734"}, {"size": 651, "mtime": 1752078895413, "results": "1347", "hashOfConfig": "734"}, {"size": 878, "mtime": 1752078895413, "results": "1348", "hashOfConfig": "734"}, {"size": 11302, "mtime": 1754787898849, "results": "1349", "hashOfConfig": "734"}, {"size": 1318, "mtime": 1752078895413, "results": "1350", "hashOfConfig": "734"}, {"size": 152, "mtime": 1752078895413, "results": "1351", "hashOfConfig": "734"}, {"size": 1407, "mtime": 1753070813299, "results": "1352", "hashOfConfig": "734"}, {"size": 3482, "mtime": 1753106562909, "results": "1353", "hashOfConfig": "734"}, {"size": 2311, "mtime": 1753436763427, "results": "1354", "hashOfConfig": "734"}, {"size": 4745, "mtime": 1754787916543, "results": "1355", "hashOfConfig": "734"}, {"size": 10105, "mtime": 1754788666523, "results": "1356", "hashOfConfig": "734"}, {"size": 2324, "mtime": 1753794776175, "results": "1357", "hashOfConfig": "734"}, {"size": 3387, "mtime": 1753101910697, "results": "1358", "hashOfConfig": "734"}, {"size": 3822, "mtime": 1753030756833, "results": "1359", "hashOfConfig": "734"}, {"size": 3630, "mtime": 1753030782254, "results": "1360", "hashOfConfig": "734"}, {"size": 6461, "mtime": 1754334168034, "results": "1361", "hashOfConfig": "734"}, {"size": 1264, "mtime": 1752078895429, "results": "1362", "hashOfConfig": "734"}, {"size": 6440, "mtime": 1752078895429, "results": "1363", "hashOfConfig": "734"}, {"size": 7233, "mtime": 1752078895429, "results": "1364", "hashOfConfig": "734"}, {"size": 22742, "mtime": 1752078895429, "results": "1365", "hashOfConfig": "734"}, {"size": 2503, "mtime": 1752078895429, "results": "1366", "hashOfConfig": "734"}, {"size": 17737, "mtime": 1752078895444, "results": "1367", "hashOfConfig": "734"}, {"size": 1604, "mtime": 1752078895444, "results": "1368", "hashOfConfig": "734"}, {"size": 4395, "mtime": 1752078895444, "results": "1369", "hashOfConfig": "734"}, {"size": 25034, "mtime": 1752078895444, "results": "1370", "hashOfConfig": "734"}, {"size": 2857, "mtime": 1752078895444, "results": "1371", "hashOfConfig": "734"}, {"size": 1425, "mtime": 1752692226155, "results": "1372", "hashOfConfig": "734"}, {"size": 3060, "mtime": 1752080816015, "results": "1373", "hashOfConfig": "734"}, {"size": 4548, "mtime": 1754684670660, "results": "1374", "hashOfConfig": "734"}, {"size": 31908, "mtime": 1754683902285, "results": "1375", "hashOfConfig": "734"}, {"size": 1344, "mtime": 1752078895508, "results": "1376", "hashOfConfig": "734"}, {"size": 2864, "mtime": 1754684860825, "results": "1377", "hashOfConfig": "734"}, {"size": 2147, "mtime": 1754683839940, "results": "1378", "hashOfConfig": "734"}, {"size": 504, "mtime": 1753101910738, "results": "1379", "hashOfConfig": "734"}, {"size": 416, "mtime": 1752078895520, "results": "1380", "hashOfConfig": "734"}, {"size": 1666, "mtime": 1753101910738, "results": "1381", "hashOfConfig": "734"}, {"size": 5564, "mtime": 1754647879393, "results": "1382", "hashOfConfig": "734"}, {"size": 3163, "mtime": 1752078895525, "results": "1383", "hashOfConfig": "734"}, {"size": 4249, "mtime": 1752078895525, "results": "1384", "hashOfConfig": "734"}, {"size": 6892, "mtime": 1752685339243, "results": "1385", "hashOfConfig": "734"}, {"size": 4659, "mtime": 1752078895525, "results": "1386", "hashOfConfig": "734"}, {"size": 5890, "mtime": 1754054911028, "results": "1387", "hashOfConfig": "734"}, {"size": 1337, "mtime": 1752078895525, "results": "1388", "hashOfConfig": "734"}, {"size": 3715, "mtime": 1753436763427, "results": "1389", "hashOfConfig": "734"}, {"size": 5943, "mtime": 1752078895525, "results": "1390", "hashOfConfig": "734"}, {"size": 9375, "mtime": 1753436763427, "results": "1391", "hashOfConfig": "734"}, {"size": 10403, "mtime": 1753436763427, "results": "1392", "hashOfConfig": "734"}, {"size": 4149, "mtime": 1753436763442, "results": "1393", "hashOfConfig": "734"}, {"size": 7483, "mtime": 1752078895525, "results": "1394", "hashOfConfig": "734"}, {"size": 5633, "mtime": 1753436763442, "results": "1395", "hashOfConfig": "734"}, {"size": 6872, "mtime": 1752078895540, "results": "1396", "hashOfConfig": "734"}, {"size": 5929, "mtime": 1752078895540, "results": "1397", "hashOfConfig": "734"}, {"size": 1824, "mtime": 1752078895540, "results": "1398", "hashOfConfig": "734"}, {"size": 2184, "mtime": 1752078895540, "results": "1399", "hashOfConfig": "734"}, {"size": 5432, "mtime": 1752685361427, "results": "1400", "hashOfConfig": "734"}, {"size": 7773, "mtime": 1752080816015, "results": "1401", "hashOfConfig": "734"}, {"size": 1869, "mtime": 1753436763442, "results": "1402", "hashOfConfig": "734"}, {"size": 6965, "mtime": 1752078895540, "results": "1403", "hashOfConfig": "734"}, {"size": 7497, "mtime": 1752427779267, "results": "1404", "hashOfConfig": "734"}, {"size": 10288, "mtime": 1754788059735, "results": "1405", "hashOfConfig": "734"}, {"size": 6604, "mtime": 1752078895525, "results": "1406", "hashOfConfig": "734"}, {"size": 12212, "mtime": 1754667481425, "results": "1407", "hashOfConfig": "734"}, {"size": 7340, "mtime": 1754666000084, "results": "1408", "hashOfConfig": "734"}, {"size": 10056, "mtime": 1754665923000, "results": "1409", "hashOfConfig": "734"}, {"size": 8667, "mtime": 1754649046686, "results": "1410", "hashOfConfig": "734"}, {"size": 23771, "mtime": 1754787861825, "results": "1411", "hashOfConfig": "734"}, {"size": 7622, "mtime": 1754392096920, "results": "1412", "hashOfConfig": "734"}, {"size": 11094, "mtime": 1754542539873, "results": "1413", "hashOfConfig": "734"}, {"size": 30747, "mtime": 1754787776566, "results": "1414", "hashOfConfig": "734"}, {"size": 2443, "mtime": 1754046892204, "results": "1415", "hashOfConfig": "734"}, {"size": 5530, "mtime": 1754666022221, "results": "1416", "hashOfConfig": "734"}, {"size": 15355, "mtime": 1754682915822, "results": "1417", "hashOfConfig": "734"}, {"size": 5035, "mtime": 1754679452962, "results": "1418", "hashOfConfig": "734"}, {"size": 4795, "mtime": 1754783686096, "results": "1419", "hashOfConfig": "734"}, {"size": 4315, "mtime": 1754783667352, "results": "1420", "hashOfConfig": "734"}, {"size": 6767, "mtime": 1754783855692, "results": "1421", "hashOfConfig": "734"}, {"size": 3467, "mtime": 1754749020368, "results": "1422", "hashOfConfig": "734"}, {"size": 2036, "mtime": 1754745984293, "results": "1423", "hashOfConfig": "734"}, {"size": 1627, "mtime": 1754785594930, "results": "1424", "hashOfConfig": "734"}, {"size": 4143, "mtime": 1754787872914, "results": "1425", "hashOfConfig": "734"}, {"size": 8549, "mtime": 1754788826987, "results": "1426", "hashOfConfig": "734"}, {"size": 3935, "mtime": 1754747301710, "results": "1427", "hashOfConfig": "734"}, {"size": 734, "mtime": 1754744986121, "results": "1428", "hashOfConfig": "734"}, {"size": 3442, "mtime": 1754787439029, "results": "1429", "hashOfConfig": "734"}, {"size": 7821, "mtime": 1754786064553, "results": "1430", "hashOfConfig": "734"}, {"size": 1010, "mtime": 1754775474424, "results": "1431", "hashOfConfig": "734"}, {"size": 15681, "mtime": 1754788626401, "results": "1432", "hashOfConfig": "734"}, {"size": 1672, "mtime": 1754775458107, "results": "1433", "hashOfConfig": "734"}, {"size": 2480, "mtime": 1754776106113, "results": "1434", "hashOfConfig": "734"}, {"size": 4604, "mtime": 1754783877482, "results": "1435", "hashOfConfig": "734"}, {"size": 2133, "mtime": 1754786033444, "results": "1436", "hashOfConfig": "734"}, {"size": 3398, "mtime": 1754785867464, "results": "1437", "hashOfConfig": "734"}, {"size": 5172, "mtime": 1754786331415, "results": "1438", "hashOfConfig": "734"}, {"size": 4628, "mtime": 1754787367310, "results": "1439", "hashOfConfig": "734"}, {"size": 2872, "mtime": 1754784694854, "results": "1440", "hashOfConfig": "734"}, {"size": 11946, "mtime": 1754787621289, "results": "1441", "hashOfConfig": "734"}, {"size": 6513, "mtime": 1754786747331, "results": "1442", "hashOfConfig": "734"}, {"size": 2896, "mtime": 1754787632295, "results": "1443", "hashOfConfig": "734"}, {"size": 5237, "mtime": 1754787644717, "results": "1444", "hashOfConfig": "734"}, {"size": 10552, "mtime": 1754785900267, "results": "1445", "hashOfConfig": "734"}, {"size": 2872, "mtime": 1754784710820, "results": "1446", "hashOfConfig": "734"}, {"size": 8482, "mtime": 1754784786801, "results": "1447", "hashOfConfig": "734"}, {"size": 12736, "mtime": 1754787693045, "results": "1448", "hashOfConfig": "734"}, {"size": 3388, "mtime": 1754782569304, "results": "1449", "hashOfConfig": "734"}, {"size": 2896, "mtime": 1754782548107, "results": "1450", "hashOfConfig": "734"}, {"size": 10013, "mtime": 1754787713472, "results": "1451", "hashOfConfig": "734"}, {"size": 8907, "mtime": 1754785993148, "results": "1452", "hashOfConfig": "734"}, {"size": 9087, "mtime": 1754785524728, "results": "1453", "hashOfConfig": "734"}, {"size": 8378, "mtime": 1754786003719, "results": "1454", "hashOfConfig": "734"}, {"size": 11799, "mtime": 1754788027400, "results": "1455", "hashOfConfig": "734"}, {"size": 6684, "mtime": 1754784554253, "results": "1456", "hashOfConfig": "734"}, {"size": 11049, "mtime": 1754784535134, "results": "1457", "hashOfConfig": "734"}, {"size": 6955, "mtime": 1754784610694, "results": "1458", "hashOfConfig": "734"}, {"size": 2114, "mtime": 1754784675054, "results": "1459", "hashOfConfig": "734"}, {"size": 7951, "mtime": 1754787478644, "results": "1460", "hashOfConfig": "734"}, {"size": 4847, "mtime": 1754787599533, "results": "1461", "hashOfConfig": "734"}, {"size": 10870, "mtime": 1754787524991, "results": "1462", "hashOfConfig": "734"}, {"size": 6818, "mtime": 1754787849882, "results": "1463", "hashOfConfig": "734"}, {"size": 11003, "mtime": 1754788485202, "results": "1464", "hashOfConfig": "734"}, {"size": 9980, "mtime": 1754788637371, "results": "1465", "hashOfConfig": "734"}, {"filePath": "1466", "messages": "1467", "suppressedMessages": "1468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "3j0uch", {"filePath": "1469", "messages": "1470", "suppressedMessages": "1471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1472", "messages": "1473", "suppressedMessages": "1474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1475", "messages": "1476", "suppressedMessages": "1477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1478", "messages": "1479", "suppressedMessages": "1480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1481", "messages": "1482", "suppressedMessages": "1483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1484", "messages": "1485", "suppressedMessages": "1486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1487", "messages": "1488", "suppressedMessages": "1489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1490", "messages": "1491", "suppressedMessages": "1492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1493", "messages": "1494", "suppressedMessages": "1495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1496", "messages": "1497", "suppressedMessages": "1498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1499", "messages": "1500", "suppressedMessages": "1501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1502", "messages": "1503", "suppressedMessages": "1504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1505", "messages": "1506", "suppressedMessages": "1507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1508", "messages": "1509", "suppressedMessages": "1510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1511", "messages": "1512", "suppressedMessages": "1513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1514", "messages": "1515", "suppressedMessages": "1516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1517", "messages": "1518", "suppressedMessages": "1519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1520", "messages": "1521", "suppressedMessages": "1522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1523", "messages": "1524", "suppressedMessages": "1525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1526", "messages": "1527", "suppressedMessages": "1528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1529", "messages": "1530", "suppressedMessages": "1531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1532", "messages": "1533", "suppressedMessages": "1534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1535", "messages": "1536", "suppressedMessages": "1537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1538", "messages": "1539", "suppressedMessages": "1540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1541", "messages": "1542", "suppressedMessages": "1543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1544", "messages": "1545", "suppressedMessages": "1546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1547", "messages": "1548", "suppressedMessages": "1549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1550", "messages": "1551", "suppressedMessages": "1552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1553", "messages": "1554", "suppressedMessages": "1555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1556", "messages": "1557", "suppressedMessages": "1558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1559", "messages": "1560", "suppressedMessages": "1561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1562", "messages": "1563", "suppressedMessages": "1564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1565", "messages": "1566", "suppressedMessages": "1567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1568", "messages": "1569", "suppressedMessages": "1570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1571", "messages": "1572", "suppressedMessages": "1573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1574", "messages": "1575", "suppressedMessages": "1576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1577", "messages": "1578", "suppressedMessages": "1579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1580", "messages": "1581", "suppressedMessages": "1582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1583", "messages": "1584", "suppressedMessages": "1585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1586", "messages": "1587", "suppressedMessages": "1588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1589", "messages": "1590", "suppressedMessages": "1591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1592", "messages": "1593", "suppressedMessages": "1594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1595", "messages": "1596", "suppressedMessages": "1597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1598", "messages": "1599", "suppressedMessages": "1600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1601", "messages": "1602", "suppressedMessages": "1603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1604", "messages": "1605", "suppressedMessages": "1606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1607", "messages": "1608", "suppressedMessages": "1609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1610", "messages": "1611", "suppressedMessages": "1612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1613", "messages": "1614", "suppressedMessages": "1615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1616", "messages": "1617", "suppressedMessages": "1618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1619", "messages": "1620", "suppressedMessages": "1621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1622", "messages": "1623", "suppressedMessages": "1624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1625", "messages": "1626", "suppressedMessages": "1627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1628", "messages": "1629", "suppressedMessages": "1630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1631", "messages": "1632", "suppressedMessages": "1633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1634", "messages": "1635", "suppressedMessages": "1636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1637", "messages": "1638", "suppressedMessages": "1639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1640", "messages": "1641", "suppressedMessages": "1642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1643", "messages": "1644", "suppressedMessages": "1645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1646", "messages": "1647", "suppressedMessages": "1648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1649", "messages": "1650", "suppressedMessages": "1651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1652", "messages": "1653", "suppressedMessages": "1654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1655", "messages": "1656", "suppressedMessages": "1657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1658", "messages": "1659", "suppressedMessages": "1660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1661", "messages": "1662", "suppressedMessages": "1663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1664", "messages": "1665", "suppressedMessages": "1666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1667", "messages": "1668", "suppressedMessages": "1669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1670", "messages": "1671", "suppressedMessages": "1672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1673", "messages": "1674", "suppressedMessages": "1675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1676", "messages": "1677", "suppressedMessages": "1678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1679", "messages": "1680", "suppressedMessages": "1681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1682", "messages": "1683", "suppressedMessages": "1684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1685", "messages": "1686", "suppressedMessages": "1687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1688", "messages": "1689", "suppressedMessages": "1690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1691", "messages": "1692", "suppressedMessages": "1693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1694", "messages": "1695", "suppressedMessages": "1696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1697", "messages": "1698", "suppressedMessages": "1699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1700", "messages": "1701", "suppressedMessages": "1702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1703", "messages": "1704", "suppressedMessages": "1705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1706", "messages": "1707", "suppressedMessages": "1708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1709", "messages": "1710", "suppressedMessages": "1711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1712", "messages": "1713", "suppressedMessages": "1714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1715", "messages": "1716", "suppressedMessages": "1717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1718", "messages": "1719", "suppressedMessages": "1720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1721", "messages": "1722", "suppressedMessages": "1723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1724", "messages": "1725", "suppressedMessages": "1726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1727", "messages": "1728", "suppressedMessages": "1729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1730", "messages": "1731", "suppressedMessages": "1732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1733", "messages": "1734", "suppressedMessages": "1735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1736", "messages": "1737", "suppressedMessages": "1738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1739", "messages": "1740", "suppressedMessages": "1741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1742", "messages": "1743", "suppressedMessages": "1744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1745", "messages": "1746", "suppressedMessages": "1747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1748", "messages": "1749", "suppressedMessages": "1750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1751", "messages": "1752", "suppressedMessages": "1753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1754", "messages": "1755", "suppressedMessages": "1756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1757", "messages": "1758", "suppressedMessages": "1759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1760", "messages": "1761", "suppressedMessages": "1762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1763", "messages": "1764", "suppressedMessages": "1765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1766", "messages": "1767", "suppressedMessages": "1768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1769", "messages": "1770", "suppressedMessages": "1771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1772", "messages": "1773", "suppressedMessages": "1774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1775", "messages": "1776", "suppressedMessages": "1777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1778", "messages": "1779", "suppressedMessages": "1780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1781", "messages": "1782", "suppressedMessages": "1783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1784", "messages": "1785", "suppressedMessages": "1786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1787", "messages": "1788", "suppressedMessages": "1789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1790", "messages": "1791", "suppressedMessages": "1792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1793", "messages": "1794", "suppressedMessages": "1795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1796", "messages": "1797", "suppressedMessages": "1798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1799", "messages": "1800", "suppressedMessages": "1801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1802", "messages": "1803", "suppressedMessages": "1804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1805", "messages": "1806", "suppressedMessages": "1807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1808", "messages": "1809", "suppressedMessages": "1810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1811", "messages": "1812", "suppressedMessages": "1813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1814", "messages": "1815", "suppressedMessages": "1816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1817", "messages": "1818", "suppressedMessages": "1819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1820", "messages": "1821", "suppressedMessages": "1822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1823", "messages": "1824", "suppressedMessages": "1825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1826", "messages": "1827", "suppressedMessages": "1828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1829", "messages": "1830", "suppressedMessages": "1831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1832", "messages": "1833", "suppressedMessages": "1834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1835", "messages": "1836", "suppressedMessages": "1837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1838", "messages": "1839", "suppressedMessages": "1840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1841", "messages": "1842", "suppressedMessages": "1843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1844", "messages": "1845", "suppressedMessages": "1846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1847", "messages": "1848", "suppressedMessages": "1849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1850", "messages": "1851", "suppressedMessages": "1852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1853", "messages": "1854", "suppressedMessages": "1855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1856", "messages": "1857", "suppressedMessages": "1858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1859", "messages": "1860", "suppressedMessages": "1861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1862", "messages": "1863", "suppressedMessages": "1864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1865", "messages": "1866", "suppressedMessages": "1867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1868", "messages": "1869", "suppressedMessages": "1870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1871", "messages": "1872", "suppressedMessages": "1873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1874", "messages": "1875", "suppressedMessages": "1876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1877", "messages": "1878", "suppressedMessages": "1879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1880", "messages": "1881", "suppressedMessages": "1882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1883", "messages": "1884", "suppressedMessages": "1885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1886", "messages": "1887", "suppressedMessages": "1888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1889", "messages": "1890", "suppressedMessages": "1891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1892", "messages": "1893", "suppressedMessages": "1894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1895", "messages": "1896", "suppressedMessages": "1897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1898", "messages": "1899", "suppressedMessages": "1900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1901", "messages": "1902", "suppressedMessages": "1903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1904", "messages": "1905", "suppressedMessages": "1906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1907", "messages": "1908", "suppressedMessages": "1909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1910", "messages": "1911", "suppressedMessages": "1912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1913", "messages": "1914", "suppressedMessages": "1915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1916", "messages": "1917", "suppressedMessages": "1918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1919", "messages": "1920", "suppressedMessages": "1921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1922", "messages": "1923", "suppressedMessages": "1924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1925", "messages": "1926", "suppressedMessages": "1927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1928", "messages": "1929", "suppressedMessages": "1930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1931", "messages": "1932", "suppressedMessages": "1933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1934", "messages": "1935", "suppressedMessages": "1936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1937", "messages": "1938", "suppressedMessages": "1939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1940", "messages": "1941", "suppressedMessages": "1942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1943", "messages": "1944", "suppressedMessages": "1945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1946", "messages": "1947", "suppressedMessages": "1948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1949", "messages": "1950", "suppressedMessages": "1951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1952", "messages": "1953", "suppressedMessages": "1954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1955", "messages": "1956", "suppressedMessages": "1957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1958", "messages": "1959", "suppressedMessages": "1960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1961", "messages": "1962", "suppressedMessages": "1963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1964", "messages": "1965", "suppressedMessages": "1966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1967", "messages": "1968", "suppressedMessages": "1969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1970", "messages": "1971", "suppressedMessages": "1972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1973", "messages": "1974", "suppressedMessages": "1975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1976", "messages": "1977", "suppressedMessages": "1978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1979", "messages": "1980", "suppressedMessages": "1981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1982", "messages": "1983", "suppressedMessages": "1984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1985", "messages": "1986", "suppressedMessages": "1987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1988", "messages": "1989", "suppressedMessages": "1990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1991", "messages": "1992", "suppressedMessages": "1993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1994", "messages": "1995", "suppressedMessages": "1996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1997", "messages": "1998", "suppressedMessages": "1999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2000", "messages": "2001", "suppressedMessages": "2002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2003", "messages": "2004", "suppressedMessages": "2005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2006", "messages": "2007", "suppressedMessages": "2008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2009", "messages": "2010", "suppressedMessages": "2011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2012", "messages": "2013", "suppressedMessages": "2014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2015", "messages": "2016", "suppressedMessages": "2017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2018", "messages": "2019", "suppressedMessages": "2020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2021", "messages": "2022", "suppressedMessages": "2023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2024", "messages": "2025", "suppressedMessages": "2026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2027", "messages": "2028", "suppressedMessages": "2029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2030", "messages": "2031", "suppressedMessages": "2032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2033", "messages": "2034", "suppressedMessages": "2035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2036", "messages": "2037", "suppressedMessages": "2038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2039", "messages": "2040", "suppressedMessages": "2041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2042", "messages": "2043", "suppressedMessages": "2044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2045", "messages": "2046", "suppressedMessages": "2047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2048", "messages": "2049", "suppressedMessages": "2050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2051", "messages": "2052", "suppressedMessages": "2053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2054", "messages": "2055", "suppressedMessages": "2056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2057", "messages": "2058", "suppressedMessages": "2059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2060", "messages": "2061", "suppressedMessages": "2062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2063", "messages": "2064", "suppressedMessages": "2065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2066", "messages": "2067", "suppressedMessages": "2068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2069", "messages": "2070", "suppressedMessages": "2071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2072", "messages": "2073", "suppressedMessages": "2074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2075", "messages": "2076", "suppressedMessages": "2077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2078", "messages": "2079", "suppressedMessages": "2080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2081", "messages": "2082", "suppressedMessages": "2083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2084", "messages": "2085", "suppressedMessages": "2086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2087", "messages": "2088", "suppressedMessages": "2089", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2090", "messages": "2091", "suppressedMessages": "2092", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2093", "messages": "2094", "suppressedMessages": "2095", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2096", "messages": "2097", "suppressedMessages": "2098", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2099", "messages": "2100", "suppressedMessages": "2101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2102", "messages": "2103", "suppressedMessages": "2104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2105", "messages": "2106", "suppressedMessages": "2107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2108", "messages": "2109", "suppressedMessages": "2110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2111", "messages": "2112", "suppressedMessages": "2113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2114", "messages": "2115", "suppressedMessages": "2116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2117", "messages": "2118", "suppressedMessages": "2119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2120", "messages": "2121", "suppressedMessages": "2122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2123", "messages": "2124", "suppressedMessages": "2125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2126", "messages": "2127", "suppressedMessages": "2128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2129", "messages": "2130", "suppressedMessages": "2131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2132", "messages": "2133", "suppressedMessages": "2134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2135", "messages": "2136", "suppressedMessages": "2137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2138", "messages": "2139", "suppressedMessages": "2140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2141", "messages": "2142", "suppressedMessages": "2143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2144", "messages": "2145", "suppressedMessages": "2146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2147", "messages": "2148", "suppressedMessages": "2149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2150", "messages": "2151", "suppressedMessages": "2152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2153", "messages": "2154", "suppressedMessages": "2155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2156", "messages": "2157", "suppressedMessages": "2158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2159", "messages": "2160", "suppressedMessages": "2161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2162", "messages": "2163", "suppressedMessages": "2164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2165", "messages": "2166", "suppressedMessages": "2167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2168", "messages": "2169", "suppressedMessages": "2170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2171", "messages": "2172", "suppressedMessages": "2173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2174", "messages": "2175", "suppressedMessages": "2176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2177", "messages": "2178", "suppressedMessages": "2179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2180", "messages": "2181", "suppressedMessages": "2182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2183", "messages": "2184", "suppressedMessages": "2185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2186", "messages": "2187", "suppressedMessages": "2188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2189", "messages": "2190", "suppressedMessages": "2191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2192", "messages": "2193", "suppressedMessages": "2194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2195", "messages": "2196", "suppressedMessages": "2197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2198", "messages": "2199", "suppressedMessages": "2200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2201", "messages": "2202", "suppressedMessages": "2203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2204", "messages": "2205", "suppressedMessages": "2206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2207", "messages": "2208", "suppressedMessages": "2209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2210", "messages": "2211", "suppressedMessages": "2212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2213", "messages": "2214", "suppressedMessages": "2215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2216", "messages": "2217", "suppressedMessages": "2218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2219", "messages": "2220", "suppressedMessages": "2221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2222", "messages": "2223", "suppressedMessages": "2224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2225", "messages": "2226", "suppressedMessages": "2227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2228", "messages": "2229", "suppressedMessages": "2230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2231", "messages": "2232", "suppressedMessages": "2233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2234", "messages": "2235", "suppressedMessages": "2236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2237", "messages": "2238", "suppressedMessages": "2239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2240", "messages": "2241", "suppressedMessages": "2242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2243", "messages": "2244", "suppressedMessages": "2245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2246", "messages": "2247", "suppressedMessages": "2248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2249", "messages": "2250", "suppressedMessages": "2251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2252", "messages": "2253", "suppressedMessages": "2254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2255", "messages": "2256", "suppressedMessages": "2257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2258", "messages": "2259", "suppressedMessages": "2260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2261", "messages": "2262", "suppressedMessages": "2263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2264", "messages": "2265", "suppressedMessages": "2266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2267", "messages": "2268", "suppressedMessages": "2269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2270", "messages": "2271", "suppressedMessages": "2272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2273", "messages": "2274", "suppressedMessages": "2275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2276", "messages": "2277", "suppressedMessages": "2278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2279", "messages": "2280", "suppressedMessages": "2281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2282", "messages": "2283", "suppressedMessages": "2284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2285", "messages": "2286", "suppressedMessages": "2287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2288", "messages": "2289", "suppressedMessages": "2290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2291", "messages": "2292", "suppressedMessages": "2293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2294", "messages": "2295", "suppressedMessages": "2296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2297", "messages": "2298", "suppressedMessages": "2299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2300", "messages": "2301", "suppressedMessages": "2302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2303", "messages": "2304", "suppressedMessages": "2305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2306", "messages": "2307", "suppressedMessages": "2308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2309", "messages": "2310", "suppressedMessages": "2311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2312", "messages": "2313", "suppressedMessages": "2314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2315", "messages": "2316", "suppressedMessages": "2317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2318", "messages": "2319", "suppressedMessages": "2320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2321", "messages": "2322", "suppressedMessages": "2323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2324", "messages": "2325", "suppressedMessages": "2326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2327", "messages": "2328", "suppressedMessages": "2329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2330", "messages": "2331", "suppressedMessages": "2332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2333", "messages": "2334", "suppressedMessages": "2335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2336", "messages": "2337", "suppressedMessages": "2338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2339", "messages": "2340", "suppressedMessages": "2341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2342", "messages": "2343", "suppressedMessages": "2344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2345", "messages": "2346", "suppressedMessages": "2347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2348", "messages": "2349", "suppressedMessages": "2350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2351", "messages": "2352", "suppressedMessages": "2353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2354", "messages": "2355", "suppressedMessages": "2356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2357", "messages": "2358", "suppressedMessages": "2359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2360", "messages": "2361", "suppressedMessages": "2362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2363", "messages": "2364", "suppressedMessages": "2365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2366", "messages": "2367", "suppressedMessages": "2368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2369", "messages": "2370", "suppressedMessages": "2371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2372", "messages": "2373", "suppressedMessages": "2374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2375", "messages": "2376", "suppressedMessages": "2377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2378", "messages": "2379", "suppressedMessages": "2380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2381", "messages": "2382", "suppressedMessages": "2383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2384", "messages": "2385", "suppressedMessages": "2386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2387", "messages": "2388", "suppressedMessages": "2389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2390", "messages": "2391", "suppressedMessages": "2392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2393", "messages": "2394", "suppressedMessages": "2395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2396", "messages": "2397", "suppressedMessages": "2398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2399", "messages": "2400", "suppressedMessages": "2401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2402", "messages": "2403", "suppressedMessages": "2404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2405", "messages": "2406", "suppressedMessages": "2407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2408", "messages": "2409", "suppressedMessages": "2410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2411", "messages": "2412", "suppressedMessages": "2413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2414", "messages": "2415", "suppressedMessages": "2416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2417", "messages": "2418", "suppressedMessages": "2419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2420", "messages": "2421", "suppressedMessages": "2422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2423", "messages": "2424", "suppressedMessages": "2425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2426", "messages": "2427", "suppressedMessages": "2428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2429", "messages": "2430", "suppressedMessages": "2431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2432", "messages": "2433", "suppressedMessages": "2434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2435", "messages": "2436", "suppressedMessages": "2437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2438", "messages": "2439", "suppressedMessages": "2440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2441", "messages": "2442", "suppressedMessages": "2443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2444", "messages": "2445", "suppressedMessages": "2446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2447", "messages": "2448", "suppressedMessages": "2449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2450", "messages": "2451", "suppressedMessages": "2452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2453", "messages": "2454", "suppressedMessages": "2455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2456", "messages": "2457", "suppressedMessages": "2458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2459", "messages": "2460", "suppressedMessages": "2461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2462", "messages": "2463", "suppressedMessages": "2464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2465", "messages": "2466", "suppressedMessages": "2467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2468", "messages": "2469", "suppressedMessages": "2470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2471", "messages": "2472", "suppressedMessages": "2473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2474", "messages": "2475", "suppressedMessages": "2476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2477", "messages": "2478", "suppressedMessages": "2479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2480", "messages": "2481", "suppressedMessages": "2482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2483", "messages": "2484", "suppressedMessages": "2485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2486", "messages": "2487", "suppressedMessages": "2488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2489", "messages": "2490", "suppressedMessages": "2491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2492", "messages": "2493", "suppressedMessages": "2494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2495", "messages": "2496", "suppressedMessages": "2497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2498", "messages": "2499", "suppressedMessages": "2500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2501", "messages": "2502", "suppressedMessages": "2503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2504", "messages": "2505", "suppressedMessages": "2506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2507", "messages": "2508", "suppressedMessages": "2509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2510", "messages": "2511", "suppressedMessages": "2512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2513", "messages": "2514", "suppressedMessages": "2515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2516", "messages": "2517", "suppressedMessages": "2518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2519", "messages": "2520", "suppressedMessages": "2521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2522", "messages": "2523", "suppressedMessages": "2524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2525", "messages": "2526", "suppressedMessages": "2527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2528", "messages": "2529", "suppressedMessages": "2530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2531", "messages": "2532", "suppressedMessages": "2533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2534", "messages": "2535", "suppressedMessages": "2536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2537", "messages": "2538", "suppressedMessages": "2539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2540", "messages": "2541", "suppressedMessages": "2542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2543", "messages": "2544", "suppressedMessages": "2545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2546", "messages": "2547", "suppressedMessages": "2548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2549", "messages": "2550", "suppressedMessages": "2551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2552", "messages": "2553", "suppressedMessages": "2554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2555", "messages": "2556", "suppressedMessages": "2557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2558", "messages": "2559", "suppressedMessages": "2560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2561", "messages": "2562", "suppressedMessages": "2563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2564", "messages": "2565", "suppressedMessages": "2566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2567", "messages": "2568", "suppressedMessages": "2569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2570", "messages": "2571", "suppressedMessages": "2572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2573", "messages": "2574", "suppressedMessages": "2575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2576", "messages": "2577", "suppressedMessages": "2578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2579", "messages": "2580", "suppressedMessages": "2581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2582", "messages": "2583", "suppressedMessages": "2584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2585", "messages": "2586", "suppressedMessages": "2587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2588", "messages": "2589", "suppressedMessages": "2590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2591", "messages": "2592", "suppressedMessages": "2593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2594", "messages": "2595", "suppressedMessages": "2596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2597", "messages": "2598", "suppressedMessages": "2599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2600", "messages": "2601", "suppressedMessages": "2602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2603", "messages": "2604", "suppressedMessages": "2605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2606", "messages": "2607", "suppressedMessages": "2608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2609", "messages": "2610", "suppressedMessages": "2611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2612", "messages": "2613", "suppressedMessages": "2614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2615", "messages": "2616", "suppressedMessages": "2617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2618", "messages": "2619", "suppressedMessages": "2620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2621", "messages": "2622", "suppressedMessages": "2623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2624", "messages": "2625", "suppressedMessages": "2626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2627", "messages": "2628", "suppressedMessages": "2629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2630", "messages": "2631", "suppressedMessages": "2632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2633", "messages": "2634", "suppressedMessages": "2635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2636", "messages": "2637", "suppressedMessages": "2638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2639", "messages": "2640", "suppressedMessages": "2641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2642", "messages": "2643", "suppressedMessages": "2644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2645", "messages": "2646", "suppressedMessages": "2647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2648", "messages": "2649", "suppressedMessages": "2650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2651", "messages": "2652", "suppressedMessages": "2653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2654", "messages": "2655", "suppressedMessages": "2656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2657", "messages": "2658", "suppressedMessages": "2659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2660", "messages": "2661", "suppressedMessages": "2662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2663", "messages": "2664", "suppressedMessages": "2665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2666", "messages": "2667", "suppressedMessages": "2668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2669", "messages": "2670", "suppressedMessages": "2671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2672", "messages": "2673", "suppressedMessages": "2674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2675", "messages": "2676", "suppressedMessages": "2677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2678", "messages": "2679", "suppressedMessages": "2680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2681", "messages": "2682", "suppressedMessages": "2683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2684", "messages": "2685", "suppressedMessages": "2686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2687", "messages": "2688", "suppressedMessages": "2689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2690", "messages": "2691", "suppressedMessages": "2692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2693", "messages": "2694", "suppressedMessages": "2695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2696", "messages": "2697", "suppressedMessages": "2698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2699", "messages": "2700", "suppressedMessages": "2701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2702", "messages": "2703", "suppressedMessages": "2704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2705", "messages": "2706", "suppressedMessages": "2707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2708", "messages": "2709", "suppressedMessages": "2710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2711", "messages": "2712", "suppressedMessages": "2713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2714", "messages": "2715", "suppressedMessages": "2716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2717", "messages": "2718", "suppressedMessages": "2719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2720", "messages": "2721", "suppressedMessages": "2722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2723", "messages": "2724", "suppressedMessages": "2725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2726", "messages": "2727", "suppressedMessages": "2728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2729", "messages": "2730", "suppressedMessages": "2731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2732", "messages": "2733", "suppressedMessages": "2734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2735", "messages": "2736", "suppressedMessages": "2737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2738", "messages": "2739", "suppressedMessages": "2740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2741", "messages": "2742", "suppressedMessages": "2743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2744", "messages": "2745", "suppressedMessages": "2746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2747", "messages": "2748", "suppressedMessages": "2749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2750", "messages": "2751", "suppressedMessages": "2752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2753", "messages": "2754", "suppressedMessages": "2755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2756", "messages": "2757", "suppressedMessages": "2758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2759", "messages": "2760", "suppressedMessages": "2761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2762", "messages": "2763", "suppressedMessages": "2764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2765", "messages": "2766", "suppressedMessages": "2767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2768", "messages": "2769", "suppressedMessages": "2770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2771", "messages": "2772", "suppressedMessages": "2773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2774", "messages": "2775", "suppressedMessages": "2776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2777", "messages": "2778", "suppressedMessages": "2779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2780", "messages": "2781", "suppressedMessages": "2782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2783", "messages": "2784", "suppressedMessages": "2785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2786", "messages": "2787", "suppressedMessages": "2788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2789", "messages": "2790", "suppressedMessages": "2791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2792", "messages": "2793", "suppressedMessages": "2794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2795", "messages": "2796", "suppressedMessages": "2797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2798", "messages": "2799", "suppressedMessages": "2800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2801", "messages": "2802", "suppressedMessages": "2803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2804", "messages": "2805", "suppressedMessages": "2806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2807", "messages": "2808", "suppressedMessages": "2809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2810", "messages": "2811", "suppressedMessages": "2812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2813", "messages": "2814", "suppressedMessages": "2815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2816", "messages": "2817", "suppressedMessages": "2818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2819", "messages": "2820", "suppressedMessages": "2821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2822", "messages": "2823", "suppressedMessages": "2824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2825", "messages": "2826", "suppressedMessages": "2827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2828", "messages": "2829", "suppressedMessages": "2830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2831", "messages": "2832", "suppressedMessages": "2833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2834", "messages": "2835", "suppressedMessages": "2836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2837", "messages": "2838", "suppressedMessages": "2839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2840", "messages": "2841", "suppressedMessages": "2842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2843", "messages": "2844", "suppressedMessages": "2845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2846", "messages": "2847", "suppressedMessages": "2848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2849", "messages": "2850", "suppressedMessages": "2851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2852", "messages": "2853", "suppressedMessages": "2854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2855", "messages": "2856", "suppressedMessages": "2857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2858", "messages": "2859", "suppressedMessages": "2860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2861", "messages": "2862", "suppressedMessages": "2863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2864", "messages": "2865", "suppressedMessages": "2866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2867", "messages": "2868", "suppressedMessages": "2869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2870", "messages": "2871", "suppressedMessages": "2872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2873", "messages": "2874", "suppressedMessages": "2875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2876", "messages": "2877", "suppressedMessages": "2878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2879", "messages": "2880", "suppressedMessages": "2881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2882", "messages": "2883", "suppressedMessages": "2884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2885", "messages": "2886", "suppressedMessages": "2887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2888", "messages": "2889", "suppressedMessages": "2890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2891", "messages": "2892", "suppressedMessages": "2893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2894", "messages": "2895", "suppressedMessages": "2896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2897", "messages": "2898", "suppressedMessages": "2899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2900", "messages": "2901", "suppressedMessages": "2902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2903", "messages": "2904", "suppressedMessages": "2905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2906", "messages": "2907", "suppressedMessages": "2908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2909", "messages": "2910", "suppressedMessages": "2911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2912", "messages": "2913", "suppressedMessages": "2914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2915", "messages": "2916", "suppressedMessages": "2917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2918", "messages": "2919", "suppressedMessages": "2920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2921", "messages": "2922", "suppressedMessages": "2923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2924", "messages": "2925", "suppressedMessages": "2926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2927", "messages": "2928", "suppressedMessages": "2929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2930", "messages": "2931", "suppressedMessages": "2932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2933", "messages": "2934", "suppressedMessages": "2935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2936", "messages": "2937", "suppressedMessages": "2938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2939", "messages": "2940", "suppressedMessages": "2941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2942", "messages": "2943", "suppressedMessages": "2944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2945", "messages": "2946", "suppressedMessages": "2947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2948", "messages": "2949", "suppressedMessages": "2950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2951", "messages": "2952", "suppressedMessages": "2953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2954", "messages": "2955", "suppressedMessages": "2956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2957", "messages": "2958", "suppressedMessages": "2959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2960", "messages": "2961", "suppressedMessages": "2962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2963", "messages": "2964", "suppressedMessages": "2965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2966", "messages": "2967", "suppressedMessages": "2968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2969", "messages": "2970", "suppressedMessages": "2971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2972", "messages": "2973", "suppressedMessages": "2974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2975", "messages": "2976", "suppressedMessages": "2977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2978", "messages": "2979", "suppressedMessages": "2980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2981", "messages": "2982", "suppressedMessages": "2983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2984", "messages": "2985", "suppressedMessages": "2986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2987", "messages": "2988", "suppressedMessages": "2989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2990", "messages": "2991", "suppressedMessages": "2992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2993", "messages": "2994", "suppressedMessages": "2995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2996", "messages": "2997", "suppressedMessages": "2998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2999", "messages": "3000", "suppressedMessages": "3001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3002", "messages": "3003", "suppressedMessages": "3004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3005", "messages": "3006", "suppressedMessages": "3007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3008", "messages": "3009", "suppressedMessages": "3010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3011", "messages": "3012", "suppressedMessages": "3013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3014", "messages": "3015", "suppressedMessages": "3016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3017", "messages": "3018", "suppressedMessages": "3019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3020", "messages": "3021", "suppressedMessages": "3022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3023", "messages": "3024", "suppressedMessages": "3025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3026", "messages": "3027", "suppressedMessages": "3028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3029", "messages": "3030", "suppressedMessages": "3031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3032", "messages": "3033", "suppressedMessages": "3034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3035", "messages": "3036", "suppressedMessages": "3037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3038", "messages": "3039", "suppressedMessages": "3040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3041", "messages": "3042", "suppressedMessages": "3043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3044", "messages": "3045", "suppressedMessages": "3046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3047", "messages": "3048", "suppressedMessages": "3049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3050", "messages": "3051", "suppressedMessages": "3052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3053", "messages": "3054", "suppressedMessages": "3055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3056", "messages": "3057", "suppressedMessages": "3058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3059", "messages": "3060", "suppressedMessages": "3061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3062", "messages": "3063", "suppressedMessages": "3064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3065", "messages": "3066", "suppressedMessages": "3067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3068", "messages": "3069", "suppressedMessages": "3070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3071", "messages": "3072", "suppressedMessages": "3073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3074", "messages": "3075", "suppressedMessages": "3076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3077", "messages": "3078", "suppressedMessages": "3079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3080", "messages": "3081", "suppressedMessages": "3082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3083", "messages": "3084", "suppressedMessages": "3085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3086", "messages": "3087", "suppressedMessages": "3088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3089", "messages": "3090", "suppressedMessages": "3091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3092", "messages": "3093", "suppressedMessages": "3094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3095", "messages": "3096", "suppressedMessages": "3097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3098", "messages": "3099", "suppressedMessages": "3100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3101", "messages": "3102", "suppressedMessages": "3103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3104", "messages": "3105", "suppressedMessages": "3106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3107", "messages": "3108", "suppressedMessages": "3109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3110", "messages": "3111", "suppressedMessages": "3112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3113", "messages": "3114", "suppressedMessages": "3115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3116", "messages": "3117", "suppressedMessages": "3118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3119", "messages": "3120", "suppressedMessages": "3121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3122", "messages": "3123", "suppressedMessages": "3124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3125", "messages": "3126", "suppressedMessages": "3127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3128", "messages": "3129", "suppressedMessages": "3130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3131", "messages": "3132", "suppressedMessages": "3133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3134", "messages": "3135", "suppressedMessages": "3136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3137", "messages": "3138", "suppressedMessages": "3139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3140", "messages": "3141", "suppressedMessages": "3142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3143", "messages": "3144", "suppressedMessages": "3145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3146", "messages": "3147", "suppressedMessages": "3148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3149", "messages": "3150", "suppressedMessages": "3151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3152", "messages": "3153", "suppressedMessages": "3154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3155", "messages": "3156", "suppressedMessages": "3157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3158", "messages": "3159", "suppressedMessages": "3160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3161", "messages": "3162", "suppressedMessages": "3163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3164", "messages": "3165", "suppressedMessages": "3166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3167", "messages": "3168", "suppressedMessages": "3169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3170", "messages": "3171", "suppressedMessages": "3172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3173", "messages": "3174", "suppressedMessages": "3175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3176", "messages": "3177", "suppressedMessages": "3178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3179", "messages": "3180", "suppressedMessages": "3181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3182", "messages": "3183", "suppressedMessages": "3184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3185", "messages": "3186", "suppressedMessages": "3187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3188", "messages": "3189", "suppressedMessages": "3190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3191", "messages": "3192", "suppressedMessages": "3193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3194", "messages": "3195", "suppressedMessages": "3196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3197", "messages": "3198", "suppressedMessages": "3199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3200", "messages": "3201", "suppressedMessages": "3202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3203", "messages": "3204", "suppressedMessages": "3205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3206", "messages": "3207", "suppressedMessages": "3208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3209", "messages": "3210", "suppressedMessages": "3211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3212", "messages": "3213", "suppressedMessages": "3214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3215", "messages": "3216", "suppressedMessages": "3217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3218", "messages": "3219", "suppressedMessages": "3220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3221", "messages": "3222", "suppressedMessages": "3223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3224", "messages": "3225", "suppressedMessages": "3226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3227", "messages": "3228", "suppressedMessages": "3229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3230", "messages": "3231", "suppressedMessages": "3232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3233", "messages": "3234", "suppressedMessages": "3235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3236", "messages": "3237", "suppressedMessages": "3238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3239", "messages": "3240", "suppressedMessages": "3241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3242", "messages": "3243", "suppressedMessages": "3244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3245", "messages": "3246", "suppressedMessages": "3247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3248", "messages": "3249", "suppressedMessages": "3250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3251", "messages": "3252", "suppressedMessages": "3253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3254", "messages": "3255", "suppressedMessages": "3256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3257", "messages": "3258", "suppressedMessages": "3259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3260", "messages": "3261", "suppressedMessages": "3262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3263", "messages": "3264", "suppressedMessages": "3265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3266", "messages": "3267", "suppressedMessages": "3268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3269", "messages": "3270", "suppressedMessages": "3271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3272", "messages": "3273", "suppressedMessages": "3274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3275", "messages": "3276", "suppressedMessages": "3277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3278", "messages": "3279", "suppressedMessages": "3280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3281", "messages": "3282", "suppressedMessages": "3283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3284", "messages": "3285", "suppressedMessages": "3286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3287", "messages": "3288", "suppressedMessages": "3289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3290", "messages": "3291", "suppressedMessages": "3292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3293", "messages": "3294", "suppressedMessages": "3295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3296", "messages": "3297", "suppressedMessages": "3298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3299", "messages": "3300", "suppressedMessages": "3301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3302", "messages": "3303", "suppressedMessages": "3304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3305", "messages": "3306", "suppressedMessages": "3307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3308", "messages": "3309", "suppressedMessages": "3310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3311", "messages": "3312", "suppressedMessages": "3313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3314", "messages": "3315", "suppressedMessages": "3316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3317", "messages": "3318", "suppressedMessages": "3319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3320", "messages": "3321", "suppressedMessages": "3322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3323", "messages": "3324", "suppressedMessages": "3325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3326", "messages": "3327", "suppressedMessages": "3328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3329", "messages": "3330", "suppressedMessages": "3331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3332", "messages": "3333", "suppressedMessages": "3334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3335", "messages": "3336", "suppressedMessages": "3337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3338", "messages": "3339", "suppressedMessages": "3340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3341", "messages": "3342", "suppressedMessages": "3343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3344", "messages": "3345", "suppressedMessages": "3346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3347", "messages": "3348", "suppressedMessages": "3349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3350", "messages": "3351", "suppressedMessages": "3352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3353", "messages": "3354", "suppressedMessages": "3355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3356", "messages": "3357", "suppressedMessages": "3358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3359", "messages": "3360", "suppressedMessages": "3361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3362", "messages": "3363", "suppressedMessages": "3364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3365", "messages": "3366", "suppressedMessages": "3367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3368", "messages": "3369", "suppressedMessages": "3370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3371", "messages": "3372", "suppressedMessages": "3373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3374", "messages": "3375", "suppressedMessages": "3376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3377", "messages": "3378", "suppressedMessages": "3379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3380", "messages": "3381", "suppressedMessages": "3382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3383", "messages": "3384", "suppressedMessages": "3385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3386", "messages": "3387", "suppressedMessages": "3388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3389", "messages": "3390", "suppressedMessages": "3391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3392", "messages": "3393", "suppressedMessages": "3394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3395", "messages": "3396", "suppressedMessages": "3397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3398", "messages": "3399", "suppressedMessages": "3400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3401", "messages": "3402", "suppressedMessages": "3403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3404", "messages": "3405", "suppressedMessages": "3406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3407", "messages": "3408", "suppressedMessages": "3409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3410", "messages": "3411", "suppressedMessages": "3412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3413", "messages": "3414", "suppressedMessages": "3415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3416", "messages": "3417", "suppressedMessages": "3418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3419", "messages": "3420", "suppressedMessages": "3421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3422", "messages": "3423", "suppressedMessages": "3424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3425", "messages": "3426", "suppressedMessages": "3427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3428", "messages": "3429", "suppressedMessages": "3430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3431", "messages": "3432", "suppressedMessages": "3433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3434", "messages": "3435", "suppressedMessages": "3436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3437", "messages": "3438", "suppressedMessages": "3439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3440", "messages": "3441", "suppressedMessages": "3442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3443", "messages": "3444", "suppressedMessages": "3445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3446", "messages": "3447", "suppressedMessages": "3448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3449", "messages": "3450", "suppressedMessages": "3451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3452", "messages": "3453", "suppressedMessages": "3454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3455", "messages": "3456", "suppressedMessages": "3457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3458", "messages": "3459", "suppressedMessages": "3460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3461", "messages": "3462", "suppressedMessages": "3463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3464", "messages": "3465", "suppressedMessages": "3466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3467", "messages": "3468", "suppressedMessages": "3469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3470", "messages": "3471", "suppressedMessages": "3472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3473", "messages": "3474", "suppressedMessages": "3475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3476", "messages": "3477", "suppressedMessages": "3478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3479", "messages": "3480", "suppressedMessages": "3481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3482", "messages": "3483", "suppressedMessages": "3484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3485", "messages": "3486", "suppressedMessages": "3487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3488", "messages": "3489", "suppressedMessages": "3490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3491", "messages": "3492", "suppressedMessages": "3493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3494", "messages": "3495", "suppressedMessages": "3496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3497", "messages": "3498", "suppressedMessages": "3499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3500", "messages": "3501", "suppressedMessages": "3502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3503", "messages": "3504", "suppressedMessages": "3505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3506", "messages": "3507", "suppressedMessages": "3508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3509", "messages": "3510", "suppressedMessages": "3511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3512", "messages": "3513", "suppressedMessages": "3514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3515", "messages": "3516", "suppressedMessages": "3517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3518", "messages": "3519", "suppressedMessages": "3520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3521", "messages": "3522", "suppressedMessages": "3523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3524", "messages": "3525", "suppressedMessages": "3526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3527", "messages": "3528", "suppressedMessages": "3529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3530", "messages": "3531", "suppressedMessages": "3532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3533", "messages": "3534", "suppressedMessages": "3535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3536", "messages": "3537", "suppressedMessages": "3538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3539", "messages": "3540", "suppressedMessages": "3541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3542", "messages": "3543", "suppressedMessages": "3544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "3545", "messages": "3546", "suppressedMessages": "3547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3548", "messages": "3549", "suppressedMessages": "3550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3551", "messages": "3552", "suppressedMessages": "3553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3554", "messages": "3555", "suppressedMessages": "3556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3557", "messages": "3558", "suppressedMessages": "3559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3560", "messages": "3561", "suppressedMessages": "3562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3563", "messages": "3564", "suppressedMessages": "3565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3566", "messages": "3567", "suppressedMessages": "3568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3569", "messages": "3570", "suppressedMessages": "3571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3572", "messages": "3573", "suppressedMessages": "3574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3575", "messages": "3576", "suppressedMessages": "3577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3578", "messages": "3579", "suppressedMessages": "3580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3581", "messages": "3582", "suppressedMessages": "3583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3584", "messages": "3585", "suppressedMessages": "3586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3587", "messages": "3588", "suppressedMessages": "3589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3590", "messages": "3591", "suppressedMessages": "3592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3593", "messages": "3594", "suppressedMessages": "3595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3596", "messages": "3597", "suppressedMessages": "3598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3599", "messages": "3600", "suppressedMessages": "3601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3602", "messages": "3603", "suppressedMessages": "3604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3605", "messages": "3606", "suppressedMessages": "3607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3608", "messages": "3609", "suppressedMessages": "3610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3611", "messages": "3612", "suppressedMessages": "3613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3614", "messages": "3615", "suppressedMessages": "3616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3617", "messages": "3618", "suppressedMessages": "3619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3620", "messages": "3621", "suppressedMessages": "3622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3623", "messages": "3624", "suppressedMessages": "3625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3626", "messages": "3627", "suppressedMessages": "3628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3629", "messages": "3630", "suppressedMessages": "3631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3632", "messages": "3633", "suppressedMessages": "3634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3635", "messages": "3636", "suppressedMessages": "3637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3638", "messages": "3639", "suppressedMessages": "3640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3641", "messages": "3642", "suppressedMessages": "3643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3644", "messages": "3645", "suppressedMessages": "3646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3647", "messages": "3648", "suppressedMessages": "3649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3650", "messages": "3651", "suppressedMessages": "3652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3653", "messages": "3654", "suppressedMessages": "3655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "3656", "messages": "3657", "suppressedMessages": "3658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "3659", "messages": "3660", "suppressedMessages": "3661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\ChooseRoleClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\choose-role\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(auth)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions\\themeHeaderActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\getBusinessCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\business-card\\updateBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\CardEditorClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\BusinessCardPreview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBackgroundEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardBusinessInfo.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardCornerDecorations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\AppearanceSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BasicInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessDetailsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\BusinessHoursEditor.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\CardEditFormContent.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\ContactLocationSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\formStyles.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\FormSubmitButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\LinksSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardEditForm\\StatusSlugSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardGlowEffects.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\DownloadButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\index.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardPreviewSection\\ShareButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardProfile.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CardTextures.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\CustomAdCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\EnhancedInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useLogoUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\hooks\\useSlugCheck.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\ImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\LogoDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\UnsavedChangesReminder.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\components\\utils\\cardUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\businessCardMapper.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\data\\subscriptionChecker.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\logo\\logoActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\public\\publicCardActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\slug\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\businessHoursProcessor.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\constants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\scrollToError.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\utils\\slugGenerator.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\card\\validation\\businessCardValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\AnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\BusinessStatusAlert.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\DashboardOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\EnhancedQuickActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\components\\FlipTimer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\DeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\EmptyGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\GalleryHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\Lightbox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\ReorderControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\SortableImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\StaticImageItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadBox.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\components\\UploadDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useDragAndDrop.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useGalleryState.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\hooks\\useReordering.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types\\galleryTypes.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils\\fileValidation.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\gallery\\utils.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessLikesReceivedList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\components\\BusinessMyLikesList.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\addVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\bulkVariantOperations.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\deleteVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProducts.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\getProductWithVariants.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\image-library.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\imageHandlers.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\schemas.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateProduct.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions\\updateVariant.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\AddProductClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\add\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\BulkVariantOperations.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\hooks\\useProductMultiImageUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ImageLibraryDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductEmptyState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductFilters.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductItemsPerPageSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductLoadingState.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductStats.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\product-ui\\ProductViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductImageCropDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductMultiImageUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\ProductsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\StandaloneProductForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantCombinationGenerator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\components\\VariantTypeSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\context\\ProductsContext.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\EditProductClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\edit\\[productId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\ProductsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\products\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessMyReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\components\\BusinessReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\AccountDeletionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\CardEditorLinkSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\components\\BusinessSubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerAnimatedMetricCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerDashboardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\components\\CustomerMetricsOverview.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\CustomerDashboardClientLayout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\components\\LikesPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\LikeListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\likes\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\overview\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\avatar-actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AddressForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\AvatarUpload.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\EmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\MobileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\PhoneForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfilePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\components\\ProfileRequirementDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\hooks\\useAvatarUpload.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\profile\\ProfileForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\EnhancedReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\components\\ReviewsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\reviews\\ReviewListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\EmailUpdateDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkEmailSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\LinkPhoneSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\components\\SettingsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\CustomerSettingsForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\DeleteAccountSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\schema.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\settings\\UpdateEmailForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\components\\SubscriptionsPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionCardClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\customer\\subscriptions\\SubscriptionListClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\AboutUsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\AboutHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\animations\\AboutAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\CoreValuesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MilestonesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\MissionVisionSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\components\\StorySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\about\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\actions\\getHomepageBusinessCard.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\AdvertisePageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\BenefitsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\ContactSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\FAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\components\\HubSpotForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\advertise\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\AuthCallbackWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\metadata.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\auth\\callback\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\components\\BlogListingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\components\\BlogPostClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\blog\\[blogSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\AnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedAuthCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AnimatedIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\auth\\AuthPageBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\CardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\DeviceFrame.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\FuturisticScanner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ActionButtonsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CardBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\CTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\DigitalCardFeature.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedCardShowcase.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedFeatureElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\EnhancedMetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ErrorDialog.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\exampleCardData.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FeaturesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingParticlesBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\FloatingPricingElements.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroActionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HeroSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\HomeCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\MobileFeatureCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\NewArrivalsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\PopularBusinessesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\SectionDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\StickyHeroSectionClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\landing\\TestimonialsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsContainer.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\metrics\\MetricsParticles.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\MetricsDisplay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicyNavigation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\policy\\PolicySection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\SectionBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\components\\shared\\PopularCategoriesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\animations\\ContactAnimatedBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactHeroSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactInfoSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\components\\ContactMapSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\ModernContactClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\contact\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\ModernCookiePolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\cookies\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\productActions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBadge.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedBusinessGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\AnimatedDivider.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessCardTableSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\BusinessSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryCarousel.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CategoryFilter.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\CitySearchSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\FloatingCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LoadingSpinner.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessFilterGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernBusinessResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ModernSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\NoResultsMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResults.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductResultsGrid.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ProductSortControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\RevealTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SearchResultsHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SectionTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\SortingControls.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\components\\ViewToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\constants\\urlParamConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\DiscoverContext.tsx", [], ["3662"], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernDiscoverClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\ModernResultsSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\discover\\utils\\sortMappings.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\BusinessUseCasesSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureAnimation.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureBackground.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeatureCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\FeaturesCTASection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\components\\HeroFeatureSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\ModernFeaturesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\features\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\hooks\\useIntersectionAnimation.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\LandingPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\AuthMethodToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\EmailOTPForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\MobilePasswordForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\components\\SocialLoginButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\LoginForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\login\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\ModernPrivacyPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\privacy\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\ModernRefundPolicyClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\refund\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\AccountBillingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\account-billing\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\BusinessCardSetupClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\business-card-setup\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\AnimatedTitle.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedFAQSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportFAQ.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\components\\EnhancedSupportSubPage.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\product-management\\ProductManagementClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\settings\\SettingsClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\SupportPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\support\\technical-issues\\TechnicalIssuesClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\ModernTermsOfServiceClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(main)\\terms\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\NavigationButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\StepProgress.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\AddressStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\BusinessDetailsStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\CardInformationStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\constants\\onboardingSteps.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useExistingData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useOnboardingForm.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\usePincodeDetails.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useSlugAvailability.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\hooks\\useUserData.ts", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\OnboardingClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\types\\onboarding.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\check-user-type\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\auth\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\cards\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\AdSlot.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\AdvertiseButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\BottomNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\EnhancedPublicCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingAIButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\FloatingInteractionButtons.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Footer.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\GoogleAnalytics.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\Header.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\FacebookIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\InstagramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\LinkedInIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\PinterestIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TelegramIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\TwitterIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\WhatsAppIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\icons\\YouTubeIcon.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\LoadingOverlay.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MetaPixel.tsx", [], ["3663"], "C:\\web-app\\dukancard\\app\\components\\MinimalFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MinimalHeader.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\MobileFooter.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ProductListItem.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\PublicCardView.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\AuthMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewForm.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewSignInPrompt.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsSummary.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\reviews\\ReviewsTab.tsx", [], ["3664"], "C:\\web-app\\dukancard\\app\\components\\ReviewsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\EnhancedCardActions.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikePagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\likes\\LikeSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\reviews\\ReviewSortDropdown.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\index.ts", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCard.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionList.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionPagination.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\shared\\subscriptions\\SubscriptionSearch.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ThemeToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\components\\ui\\container.tsx", [], [], "C:\\web-app\\dukancard\\app\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\businessActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\combinedActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\locationActions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\actions\\productActions.ts", [], ["3665"], "C:\\web-app\\dukancard\\app\\locality\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\BreadcrumbNav.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ErrorSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ImprovedSearchSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\LocationIndicator.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\components\\ModernResultsSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\constants\\paginationConstants.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\businessContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\commonContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\LocalityContext.tsx", [], ["3666"], "C:\\web-app\\dukancard\\app\\locality\\context\\productContextFunctions.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\context\\types.ts", [], [], "C:\\web-app\\dukancard\\app\\locality\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\LocalityPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\locality\\[localSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\error.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\not-found.tsx", [], [], "C:\\web-app\\dukancard\\app\\post\\[postId]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\products\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\robots.ts", [], [], "C:\\web-app\\dukancard\\app\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\animations.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\EnhancedMetricsCards.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\BusinessDetails\\ProfessionalBusinessTable.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessCardSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessDetails.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedBusinessGallery.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedPublicCardPageWrapper.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\EnhancedTabsToggle.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\GalleryTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\OfflineBusinessMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductGridSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\components\\ProductsTab.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\GalleryPageClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\gallery\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\layout.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\loading.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\page.tsx", ["3667", "3668"], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\actions.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\BuyNowButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ImageZoomModal.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\OfflineProductMessage.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\PhoneButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductAdSection.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductDetail.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\ProductRecommendations.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\VariantSelector.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\components\\WhatsAppButton.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\hooks\\usePinchZoom.ts", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\page.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\product\\[productSlug]\\ProductDetailClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\[cardSlug]\\PublicCardPageClient.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogContent.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogListingSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogPostSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\blog\\BlogSearch.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernBusinessFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\ModernCustomerFeedList.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\dialogs\\PostDeleteDialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostAndProductEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\editors\\InlinePostEditor.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\FilterPills.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ImageCropper.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\LocationDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\forms\\ProductSelector.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\useCustomerPostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostMediaUpload.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\hooks\\usePostOwnership.ts", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernCustomerPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedContainer.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernFeedHeader.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\ModernPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostActions.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\PostCardSkeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaBusinessPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\SocialMediaPostCreator.tsx", [], [], "C:\\web-app\\dukancard\\components\\feed\\shared\\UnifiedPostCard.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\BackNavigation.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\ConditionalPostLayout.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\PostShareButton.tsx", [], [], "C:\\web-app\\dukancard\\components\\post\\SinglePostView.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScanner.tsx", [], [], "C:\\web-app\\dukancard\\components\\qr\\QRScannerModal.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\BusinessAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\CustomerAppSidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavBusinessUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerMain.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\NavCustomerUser.tsx", [], [], "C:\\web-app\\dukancard\\components\\sidebar\\SidebarLink.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\accordion.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert-dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\alert.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\avatar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\badge.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\breadcrumb.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\calendar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\card.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\carousel.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\category-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\chart.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\checkbox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\collapsible.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\command.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dialog.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\form.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input-otp.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\input.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\label.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\multi-select-combobox.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\pagination.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\popover.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\progress.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\radio-group.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\resizable-navbar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-area.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\scroll-to-top-button.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\select.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\separator.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sheet.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sidebar.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\skeleton.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\slider.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\sonner.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\switch.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\table.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tabs.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\textarea.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\toast.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\tooltip.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\use-toast.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\visually-hidden.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\blogs.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\access.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\discovery.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\profileRetrieval.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\search.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\sitemap.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\businessProfiles\\utils.ts", [], ["3669", "3670"], "C:\\web-app\\dukancard\\lib\\actions\\categories\\locationBasedFetching.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\categories\\pincodeTypes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerPosts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\customerProfiles\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\gallery.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\interactions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location\\locationBySlug.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\location.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\crud.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\fetchSinglePost.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\index.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\types.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\unifiedFeed.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\fetchProductsByIds.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\products\\sitemapHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\redirectAfterLogin.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\reviews.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\secureCustomerProfiles.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\delete-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\productActions.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-business-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-customer-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\shared\\upload-post-media.ts", [], [], "C:\\web-app\\dukancard\\lib\\api\\response.ts", [], [], "C:\\web-app\\dukancard\\lib\\cardDownloader.ts", [], [], "C:\\web-app\\dukancard\\lib\\client\\locationUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\categories.ts", [], [], "C:\\web-app\\dukancard\\lib\\config\\states.ts", [], [], "C:\\web-app\\dukancard\\lib\\constants\\predefinedVariants.ts", [], [], "C:\\web-app\\dukancard\\lib\\csrf.ts", [], [], "C:\\web-app\\dukancard\\lib\\errorHandling.ts", [], ["3671", "3672"], "C:\\web-app\\dukancard\\lib\\qrCodeGenerator.ts", [], [], "C:\\web-app\\dukancard\\lib\\rateLimiter.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\authSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\schemas\\locationSchemas.ts", [], [], "C:\\web-app\\dukancard\\lib\\services\\realtimeService.ts", [], ["3673"], "C:\\web-app\\dukancard\\lib\\services\\socialService.ts", [], [], "C:\\web-app\\dukancard\\lib\\site-config.ts", [], [], "C:\\web-app\\dukancard\\lib\\siteContent.ts", [], [], "C:\\web-app\\dukancard\\lib\\supabase\\constants.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\activities.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\api.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\blog.ts", [], [], "C:\\web-app\\dukancard\\lib\\types\\posts.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\addressValidation.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\cameraUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\client-image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\customBranding.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\debounce.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\diversityEngine.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\feedMerger.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\hybridTimeAndPlanAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\optimizedHybridAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\planPrioritizer.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\postCreationHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\feed\\smartFeedAlgorithm.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\image-compression.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\markdown.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\pagination.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\postUrl.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\qrCodeUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\seo.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\slugUtils.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\storage-paths.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\supabaseErrorHandler.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\variantHelpers.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils.ts", [], [], "C:\\web-app\\dukancard\\components\\ui\\CommentDisplay.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\CommentInput.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\CommentSection.tsx", [], [], "C:\\web-app\\dukancard\\components\\ui\\LikeButton.tsx", [], [], "C:\\web-app\\dukancard\\lib\\actions\\comments\\postComments.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\likes\\commentLikes.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\likes\\postLikes.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\likeCommentStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\formatNumber.ts", [], [], "C:\\web-app\\dukancard\\components\\post\\PostLikesList.tsx", [], [], "C:\\web-app\\dukancard\\lib\\services\\pagination\\paginationService.ts", [], ["3674", "3675", "3676"], "C:\\web-app\\dukancard\\app\\(onboarding)\\onboarding\\components\\steps\\WelcomeStep.tsx", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\login\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\logout\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\refresh\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\devices\\register\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\auth\\jwt.ts", [], [], "C:\\web-app\\dukancard\\lib\\auth\\utils.ts", [], [], "C:\\web-app\\dukancard\\lib\\middleware\\hmac.ts", [], [], "C:\\web-app\\dukancard\\lib\\middleware\\rateLimiter.ts", ["3677", "3678", "3679"], [], "C:\\web-app\\dukancard\\lib\\middleware\\timestamp.ts", [], [], "C:\\web-app\\dukancard\\lib\\security\\hashing.ts", [], [], "C:\\web-app\\dukancard\\lib\\security\\hmac.ts", ["3680", "3681", "3682"], [], "C:\\web-app\\dukancard\\lib\\middleware\\bruteForceProtection.ts", [], [], "C:\\web-app\\dukancard\\lib\\actions\\auth.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\authStore.ts", ["3683", "3684"], [], "C:\\web-app\\dukancard\\lib\\utils\\authenticatedFetch.ts", ["3685"], [], "C:\\web-app\\dukancard\\app\\api\\auth\\send-otp\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\auth\\verify-otp\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\auth\\middleware-jwt.ts", [], [], "C:\\web-app\\dukancard\\app\\(dashboard)\\dashboard\\business\\overview\\BusinessOverviewClient.tsx", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\access\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\me\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\profile\\exists\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\search\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\sitemap\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\slug\\[slug]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\business\\[id]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\exists\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\customer\\profile\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\likes\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\location\\city\\[city]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\location\\pincode\\[pincode]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\posts\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\products\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\products\\[id]\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\storage\\upload\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\businessProfileStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\postsStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\productsStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\storageStore.ts", [], [], "C:\\web-app\\dukancard\\lib\\middleware\\jwt.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\comments\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\stores\\customerProfileStore.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\comments\\[id]\\route.ts", [], [], "C:\\web-app\\dukancard\\lib\\utils\\internalApiClient.ts", ["3686", "3687", "3688", "3689", "3690", "3691", "3692", "3693", "3694", "3695", "3696", "3697", "3698", "3699", "3700", "3701", "3702", "3703", "3704", "3705"], [], "C:\\web-app\\dukancard\\app\\api\\subscriptions\\route.ts", [], [], "C:\\web-app\\dukancard\\app\\api\\reviews\\route.ts", [], [], {"ruleId": "3706", "severity": 1, "message": "3707", "line": 282, "column": 6, "nodeType": "3708", "endLine": 282, "endColumn": 8, "suggestions": "3709", "suppressions": "3710"}, {"ruleId": "3711", "severity": 1, "message": "3712", "line": 25, "column": 9, "nodeType": "3713", "endLine": 31, "endColumn": 11, "suppressions": "3714"}, {"ruleId": "3706", "severity": 1, "message": "3715", "line": 53, "column": 6, "nodeType": "3708", "endLine": 53, "endColumn": 46, "suggestions": "3716", "suppressions": "3717"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 159, "column": 62, "nodeType": "3720", "messageId": "3721", "endLine": 159, "endColumn": 65, "suggestions": "3722", "suppressions": "3723"}, {"ruleId": "3706", "severity": 1, "message": "3724", "line": 172, "column": 6, "nodeType": "3708", "endLine": 172, "endColumn": 8, "suggestions": "3725", "suppressions": "3726"}, {"ruleId": "3727", "severity": 1, "message": "3728", "line": 15, "column": 10, "nodeType": "3729", "messageId": "3730", "endLine": 15, "endColumn": 21, "suggestions": "3731"}, {"ruleId": "3732", "severity": 1, "message": "3728", "line": 15, "column": 10, "nodeType": null, "messageId": "3730", "endLine": 15, "endColumn": 21}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 9, "column": 37, "nodeType": "3720", "messageId": "3721", "endLine": 9, "endColumn": 40, "suggestions": "3733", "suppressions": "3734"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 9, "column": 67, "nodeType": "3720", "messageId": "3721", "endLine": 9, "endColumn": 70, "suggestions": "3735", "suppressions": "3736"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 6, "column": 18, "nodeType": "3720", "messageId": "3721", "endLine": 6, "endColumn": 21, "suggestions": "3737", "suppressions": "3738"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 13, "column": 10, "nodeType": "3720", "messageId": "3721", "endLine": 13, "endColumn": 13, "suggestions": "3739", "suppressions": "3740"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 55, "column": 31, "nodeType": "3720", "messageId": "3721", "endLine": 55, "endColumn": 34, "suggestions": "3741", "suppressions": "3742"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 104, "column": 47, "nodeType": "3720", "messageId": "3721", "endLine": 104, "endColumn": 50, "suggestions": "3743", "suppressions": "3744"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 233, "column": 53, "nodeType": "3720", "messageId": "3721", "endLine": 233, "endColumn": 56, "suggestions": "3745", "suppressions": "3746"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 391, "column": 50, "nodeType": "3720", "messageId": "3721", "endLine": 391, "endColumn": 53, "suggestions": "3747", "suppressions": "3748"}, {"ruleId": "3727", "severity": 1, "message": "3749", "line": 126, "column": 16, "nodeType": "3729", "messageId": "3730", "endLine": 126, "endColumn": 32, "suggestions": "3750"}, {"ruleId": "3727", "severity": 1, "message": "3749", "line": 203, "column": 16, "nodeType": "3729", "messageId": "3730", "endLine": 203, "endColumn": 32, "suggestions": "3751"}, {"ruleId": null, "message": "3752", "line": 260, "column": 3, "severity": 1, "nodeType": null, "fix": "3753"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 96, "column": 19, "nodeType": "3720", "messageId": "3721", "endLine": 96, "endColumn": 22, "suggestions": "3754"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 99, "column": 19, "nodeType": "3720", "messageId": "3721", "endLine": 99, "endColumn": 22, "suggestions": "3755"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 102, "column": 19, "nodeType": "3720", "messageId": "3721", "endLine": 102, "endColumn": 22, "suggestions": "3756"}, {"ruleId": "3727", "severity": 1, "message": "3757", "line": 48, "column": 13, "nodeType": "3729", "messageId": "3730", "endLine": 48, "endColumn": 34, "suggestions": "3758"}, {"ruleId": "3727", "severity": 1, "message": "3759", "line": 158, "column": 26, "nodeType": "3729", "messageId": "3730", "endLine": 158, "endColumn": 42, "suggestions": "3760"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 18, "column": 48, "nodeType": "3720", "messageId": "3721", "endLine": 18, "endColumn": 51, "suggestions": "3761"}, {"ruleId": "3727", "severity": 1, "message": "3762", "line": 9, "column": 10, "nodeType": "3729", "messageId": "3730", "endLine": 9, "endColumn": 17, "suggestions": "3763"}, {"ruleId": "3732", "severity": 1, "message": "3762", "line": 9, "column": 10, "nodeType": null, "messageId": "3730", "endLine": 9, "endColumn": 17}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 16, "column": 35, "nodeType": "3720", "messageId": "3721", "endLine": 16, "endColumn": 38, "suggestions": "3764"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 36, "column": 18, "nodeType": "3720", "messageId": "3721", "endLine": 36, "endColumn": 21, "suggestions": "3765"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 108, "column": 17, "nodeType": "3720", "messageId": "3721", "endLine": 108, "endColumn": 20, "suggestions": "3766"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 115, "column": 18, "nodeType": "3720", "messageId": "3721", "endLine": 115, "endColumn": 21, "suggestions": "3767"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 115, "column": 48, "nodeType": "3720", "messageId": "3721", "endLine": 115, "endColumn": 51, "suggestions": "3768"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 126, "column": 19, "nodeType": "3720", "messageId": "3721", "endLine": 126, "endColumn": 22, "suggestions": "3769"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 126, "column": 49, "nodeType": "3720", "messageId": "3721", "endLine": 126, "endColumn": 52, "suggestions": "3770"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 137, "column": 17, "nodeType": "3720", "messageId": "3721", "endLine": 137, "endColumn": 20, "suggestions": "3771"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 137, "column": 47, "nodeType": "3720", "messageId": "3721", "endLine": 137, "endColumn": 50, "suggestions": "3772"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 148, "column": 20, "nodeType": "3720", "messageId": "3721", "endLine": 148, "endColumn": 23, "suggestions": "3773"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 188, "column": 43, "nodeType": "3720", "messageId": "3721", "endLine": 188, "endColumn": 46, "suggestions": "3774"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 199, "column": 50, "nodeType": "3720", "messageId": "3721", "endLine": 199, "endColumn": 53, "suggestions": "3775"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 211, "column": 39, "nodeType": "3720", "messageId": "3721", "endLine": 211, "endColumn": 42, "suggestions": "3776"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 222, "column": 40, "nodeType": "3720", "messageId": "3721", "endLine": 222, "endColumn": 43, "suggestions": "3777"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 224, "column": 10, "nodeType": "3720", "messageId": "3721", "endLine": 224, "endColumn": 13, "suggestions": "3778"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 234, "column": 41, "nodeType": "3720", "messageId": "3721", "endLine": 234, "endColumn": 44, "suggestions": "3779"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 236, "column": 10, "nodeType": "3720", "messageId": "3721", "endLine": 236, "endColumn": 13, "suggestions": "3780"}, {"ruleId": "3718", "severity": 1, "message": "3719", "line": 246, "column": 42, "nodeType": "3720", "messageId": "3721", "endLine": 246, "endColumn": 45, "suggestions": "3781"}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'initialBusinessName', 'initialCategory', 'initialCity', 'initialLocality', 'initialPincode', 'performSearch', 'productFilterBy', and 'productSortBy'. Either include them or remove the dependency array.", "ArrayExpression", ["3782"], ["3783"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["3784"], "React Hook useEffect has a missing dependency: 'loadReviews'. Either include it or remove the dependency array.", ["3785"], ["3786"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["3787", "3788"], ["3789"], "React Hook useEffect has missing dependencies: 'isSearching', 'performSearch', 'products.length', and 'viewType'. Either include them or remove the dependency array.", ["3790"], ["3791"], "no-unused-vars", "'internalGet' is defined but never used. Allowed unused vars must match /^_/u.", "Identifier", "unusedVar", ["3792"], "@typescript-eslint/no-unused-vars", ["3793", "3794"], ["3795"], ["3796", "3797"], ["3798"], ["3799", "3800"], ["3801"], ["3802", "3803"], ["3804"], ["3805", "3806"], ["3807"], ["3808", "3809"], ["3810"], ["3811", "3812"], ["3813"], ["3814", "3815"], ["3816"], "'req' is defined but never used. Allowed unused args must match /^_/u.", ["3817"], ["3818"], "Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-unused-vars').", {"range": "3819", "text": "3820"}, ["3821", "3822"], ["3823", "3824"], ["3825", "3826"], "'user' is defined but never used. Allowed unused args must match /^_/u.", ["3827"], "'draft' is defined but never used. Allowed unused args must match /^_/u.", ["3828"], ["3829", "3830"], "'headers' is defined but never used. Allowed unused vars must match /^_/u.", ["3831"], ["3832", "3833"], ["3834", "3835"], ["3836", "3837"], ["3838", "3839"], ["3840", "3841"], ["3842", "3843"], ["3844", "3845"], ["3846", "3847"], ["3848", "3849"], ["3850", "3851"], ["3852", "3853"], ["3854", "3855"], ["3856", "3857"], ["3858", "3859"], ["3860", "3861"], ["3862", "3863"], ["3864", "3865"], ["3866", "3867"], {"desc": "3868", "fix": "3869"}, {"kind": "3870", "justification": "3871"}, {"kind": "3870", "justification": "3871"}, {"desc": "3872", "fix": "3873"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3875", "desc": "3876"}, {"messageId": "3877", "fix": "3878", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"desc": "3880", "fix": "3881"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3882", "data": "3883", "fix": "3884", "desc": "3885"}, {"messageId": "3874", "fix": "3886", "desc": "3876"}, {"messageId": "3877", "fix": "3887", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3888", "desc": "3876"}, {"messageId": "3877", "fix": "3889", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3890", "desc": "3876"}, {"messageId": "3877", "fix": "3891", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3892", "desc": "3876"}, {"messageId": "3877", "fix": "3893", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3894", "desc": "3876"}, {"messageId": "3877", "fix": "3895", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3896", "desc": "3876"}, {"messageId": "3877", "fix": "3897", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3898", "desc": "3876"}, {"messageId": "3877", "fix": "3899", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3874", "fix": "3900", "desc": "3876"}, {"messageId": "3877", "fix": "3901", "desc": "3879"}, {"kind": "3870", "justification": "3871"}, {"messageId": "3882", "data": "3902", "fix": "3903", "desc": "3904"}, {"messageId": "3882", "data": "3905", "fix": "3906", "desc": "3904"}, [7134, 7195], " ", {"messageId": "3874", "fix": "3907", "desc": "3876"}, {"messageId": "3877", "fix": "3908", "desc": "3879"}, {"messageId": "3874", "fix": "3909", "desc": "3876"}, {"messageId": "3877", "fix": "3910", "desc": "3879"}, {"messageId": "3874", "fix": "3911", "desc": "3876"}, {"messageId": "3877", "fix": "3912", "desc": "3879"}, {"messageId": "3882", "data": "3913", "fix": "3914", "desc": "3915"}, {"messageId": "3882", "data": "3916", "fix": "3917", "desc": "3918"}, {"messageId": "3874", "fix": "3919", "desc": "3876"}, {"messageId": "3877", "fix": "3920", "desc": "3879"}, {"messageId": "3882", "data": "3921", "fix": "3922", "desc": "3923"}, {"messageId": "3874", "fix": "3924", "desc": "3876"}, {"messageId": "3877", "fix": "3925", "desc": "3879"}, {"messageId": "3874", "fix": "3926", "desc": "3876"}, {"messageId": "3877", "fix": "3927", "desc": "3879"}, {"messageId": "3874", "fix": "3928", "desc": "3876"}, {"messageId": "3877", "fix": "3929", "desc": "3879"}, {"messageId": "3874", "fix": "3930", "desc": "3876"}, {"messageId": "3877", "fix": "3931", "desc": "3879"}, {"messageId": "3874", "fix": "3932", "desc": "3876"}, {"messageId": "3877", "fix": "3933", "desc": "3879"}, {"messageId": "3874", "fix": "3934", "desc": "3876"}, {"messageId": "3877", "fix": "3935", "desc": "3879"}, {"messageId": "3874", "fix": "3936", "desc": "3876"}, {"messageId": "3877", "fix": "3937", "desc": "3879"}, {"messageId": "3874", "fix": "3938", "desc": "3876"}, {"messageId": "3877", "fix": "3939", "desc": "3879"}, {"messageId": "3874", "fix": "3940", "desc": "3876"}, {"messageId": "3877", "fix": "3941", "desc": "3879"}, {"messageId": "3874", "fix": "3942", "desc": "3876"}, {"messageId": "3877", "fix": "3943", "desc": "3879"}, {"messageId": "3874", "fix": "3944", "desc": "3876"}, {"messageId": "3877", "fix": "3945", "desc": "3879"}, {"messageId": "3874", "fix": "3946", "desc": "3876"}, {"messageId": "3877", "fix": "3947", "desc": "3879"}, {"messageId": "3874", "fix": "3948", "desc": "3876"}, {"messageId": "3877", "fix": "3949", "desc": "3879"}, {"messageId": "3874", "fix": "3950", "desc": "3876"}, {"messageId": "3877", "fix": "3951", "desc": "3879"}, {"messageId": "3874", "fix": "3952", "desc": "3876"}, {"messageId": "3877", "fix": "3953", "desc": "3879"}, {"messageId": "3874", "fix": "3954", "desc": "3876"}, {"messageId": "3877", "fix": "3955", "desc": "3879"}, {"messageId": "3874", "fix": "3956", "desc": "3876"}, {"messageId": "3877", "fix": "3957", "desc": "3879"}, {"messageId": "3874", "fix": "3958", "desc": "3876"}, {"messageId": "3877", "fix": "3959", "desc": "3879"}, "Update the dependencies array to be: [initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", {"range": "3960", "text": "3961"}, "directive", "", "Update the dependencies array to be: [currentPage, sortBy, businessProfileId, loadReviews]", {"range": "3962", "text": "3963"}, "suggestUnknown", {"range": "3964", "text": "3965"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "3966", "text": "3967"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [isSearching, performSearch, products.length, viewType]", {"range": "3968", "text": "3969"}, "removeVar", {"varName": "3970"}, {"range": "3971", "text": "3871"}, "Remove unused variable 'internalGet'.", {"range": "3972", "text": "3965"}, {"range": "3973", "text": "3967"}, {"range": "3974", "text": "3965"}, {"range": "3975", "text": "3967"}, {"range": "3976", "text": "3965"}, {"range": "3977", "text": "3967"}, {"range": "3978", "text": "3965"}, {"range": "3979", "text": "3967"}, {"range": "3980", "text": "3965"}, {"range": "3981", "text": "3967"}, {"range": "3982", "text": "3965"}, {"range": "3983", "text": "3967"}, {"range": "3984", "text": "3965"}, {"range": "3985", "text": "3967"}, {"range": "3986", "text": "3965"}, {"range": "3987", "text": "3967"}, {"varName": "3988"}, {"range": "3989", "text": "3871"}, "Remove unused variable 'req'.", {"varName": "3988"}, {"range": "3990", "text": "3871"}, {"range": "3991", "text": "3965"}, {"range": "3992", "text": "3967"}, {"range": "3993", "text": "3965"}, {"range": "3994", "text": "3967"}, {"range": "3995", "text": "3965"}, {"range": "3996", "text": "3967"}, {"varName": "3997"}, {"range": "3998", "text": "3871"}, "Remove unused variable 'user'.", {"varName": "3999"}, {"range": "4000", "text": "3871"}, "Remove unused variable 'draft'.", {"range": "4001", "text": "3965"}, {"range": "4002", "text": "3967"}, {"varName": "4003"}, {"range": "4004", "text": "3871"}, "Remove unused variable 'headers'.", {"range": "4005", "text": "3965"}, {"range": "4006", "text": "3967"}, {"range": "4007", "text": "3965"}, {"range": "4008", "text": "3967"}, {"range": "4009", "text": "3965"}, {"range": "4010", "text": "3967"}, {"range": "4011", "text": "3965"}, {"range": "4012", "text": "3967"}, {"range": "4013", "text": "3965"}, {"range": "4014", "text": "3967"}, {"range": "4015", "text": "3965"}, {"range": "4016", "text": "3967"}, {"range": "4017", "text": "3965"}, {"range": "4018", "text": "3967"}, {"range": "4019", "text": "3965"}, {"range": "4020", "text": "3967"}, {"range": "4021", "text": "3965"}, {"range": "4022", "text": "3967"}, {"range": "4023", "text": "3965"}, {"range": "4024", "text": "3967"}, {"range": "4025", "text": "3965"}, {"range": "4026", "text": "3967"}, {"range": "4027", "text": "3965"}, {"range": "4028", "text": "3967"}, {"range": "4029", "text": "3965"}, {"range": "4030", "text": "3967"}, {"range": "4031", "text": "3965"}, {"range": "4032", "text": "3967"}, {"range": "4033", "text": "3965"}, {"range": "4034", "text": "3967"}, {"range": "4035", "text": "3965"}, {"range": "4036", "text": "3967"}, {"range": "4037", "text": "3965"}, {"range": "4038", "text": "3967"}, {"range": "4039", "text": "3965"}, {"range": "4040", "text": "3967"}, [9509, 9511], "[initialBusinessName, initialCategory, initialCity, initialLocality, initialPincode, performSearch, productFilterBy, productSortBy]", [1800, 1840], "[currentPage, sortBy, businessProfileId, loadReviews]", [4444, 4447], "unknown", [4444, 4447], "never", [5544, 5546], "[isSearching, performSearch, products.length, viewType]", "internalGet", [673, 733], [394, 397], [394, 397], [424, 427], [424, 427], [170, 173], [170, 173], [362, 365], [362, 365], [1622, 1625], [1622, 1625], [2707, 2710], [2707, 2710], [6809, 6812], [6809, 6812], [12115, 12118], [12115, 12118], "req", [3321, 3337], [5420, 5436], [3053, 3056], [3053, 3056], [3179, 3182], [3179, 3182], [3305, 3308], [3305, 3308], "user", [1119, 1140], "draft", [4639, 4655], [626, 629], [626, 629], "headers", [265, 304], [513, 516], [513, 516], [939, 942], [939, 942], [2959, 2962], [2959, 2962], [3169, 3172], [3169, 3172], [3199, 3202], [3199, 3202], [3464, 3467], [3464, 3467], [3494, 3497], [3494, 3497], [3756, 3759], [3756, 3759], [3786, 3789], [3786, 3789], [4052, 4055], [4052, 4055], [5198, 5201], [5198, 5201], [5490, 5493], [5490, 5493], [5787, 5790], [5787, 5790], [6052, 6055], [6052, 6055], [6087, 6090], [6087, 6090], [6340, 6343], [6340, 6343], [6375, 6378], [6375, 6378], [6631, 6634], [6631, 6634]]