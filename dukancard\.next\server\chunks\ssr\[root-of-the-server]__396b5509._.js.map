{"version": 3, "sources": [], "sections": [{"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/utils/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\nimport { Database } from '@/types/supabase';\r\n\r\nexport async function createClient(): Promise<SupabaseClient<Database>> {\r\n  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;\r\n\r\n  if (!supabaseUrl || !supabaseAnonKey) {\r\n    throw new Error('Supabase environment variables are not set.');\r\n  }\r\n\r\n  // Check if we're in a test environment\r\n  let headersList: Headers | null = null;\r\n  let cookieStore: any = null;\r\n\r\n  try {\r\n    // Dynamically import next/headers to avoid issues in edge runtime\r\n    const { headers, cookies } = await import('next/headers');\r\n    headersList = await headers();\r\n    cookieStore = await cookies();\r\n  } catch (error) {\r\n    // If next/headers is not available (e.g., in edge runtime), continue without it\r\n    console.warn('next/headers not available in this context, using fallback');\r\n  }\r\n\r\n  const isTestEnvironment =\r\n    process.env.NODE_ENV === 'test' ||\r\n    process.env.PLAYWRIGHT_TESTING === 'true' ||\r\n    (headersList && headersList.get('x-playwright-testing') === 'true');\r\n\r\n  if (isTestEnvironment && headersList) {\r\n    // Return a mocked Supabase client for testing\r\n    return createMockSupabaseClient(headersList) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  // If cookies are not available, create a basic server client\r\n  if (!cookieStore) {\r\n    return createServerClient(\r\n      supabaseUrl,\r\n      supabaseAnonKey,\r\n      {\r\n        cookies: {\r\n          getAll() {\r\n            return [];\r\n          },\r\n          setAll() {\r\n            // No-op when cookies are not available\r\n          },\r\n        },\r\n      }\r\n    ) as unknown as SupabaseClient<Database>;\r\n  }\r\n\r\n  return createServerClient(\r\n    supabaseUrl,\r\n    supabaseAnonKey,\r\n    {\r\n      cookies: {\r\n        async getAll() {\r\n          return await cookieStore.getAll();\r\n        },\r\n        async setAll(cookiesToSet: any[]) {\r\n          try {\r\n            for (const { name, value, options } of cookiesToSet) {\r\n              await cookieStore.set(name, value, options);\r\n            }\r\n          } catch {\r\n            // The `setAll` method was called from a Server Component.\r\n            // This can be ignored if you have middleware refreshing\r\n            // user sessions.\r\n          }\r\n        },\r\n      },\r\n    }\r\n  ) as unknown as SupabaseClient<Database>;\r\n}\r\n\r\ntype MockQueryBuilder = {\r\n  select: (columns?: string) => MockQueryBuilder;\r\n  eq: (column: string, value: any) => MockQueryBuilder;\r\n  neq: (column: string, value: any) => MockQueryBuilder;\r\n  gt: (column: string, value: any) => MockQueryBuilder;\r\n  gte: (column: string, value: any) => MockQueryBuilder;\r\n  lt: (column: string, value: any) => MockQueryBuilder;\r\n  lte: (column: string, value: any) => MockQueryBuilder;\r\n  like: (column: string, pattern: string) => MockQueryBuilder;\r\n  ilike: (column: string, pattern: string) => MockQueryBuilder;\r\n  is: (column: string, value: any) => MockQueryBuilder;\r\n  in: (column: string, values: any[]) => MockQueryBuilder;\r\n  contains: (column: string, value: any) => MockQueryBuilder;\r\n  containedBy: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeGte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLt: (column: string, value: any) => MockQueryBuilder;\r\n  rangeLte: (column: string, value: any) => MockQueryBuilder;\r\n  rangeAdjacent: (column: string, value: any) => MockQueryBuilder;\r\n  overlaps: (column: string, value: any) => MockQueryBuilder;\r\n  textSearch: (column: string, query: string) => MockQueryBuilder;\r\n  match: (query: Record<string, any>) => MockQueryBuilder;\r\n  not: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  or: (filters: string) => MockQueryBuilder;\r\n  filter: (column: string, operator: string, value: any) => MockQueryBuilder;\r\n  order: (column: string, options?: { ascending?: boolean; nullsFirst?: boolean }) => MockQueryBuilder;\r\n  limit: (count: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  range: (from: number, to: number, options?: { foreignTable?: string }) => MockQueryBuilder;\r\n  abortSignal: (signal: AbortSignal) => MockQueryBuilder;\r\n  single: () => Promise<any>;\r\n  maybeSingle: () => Promise<any>;\r\n  then: (callback?: any) => Promise<any>;\r\n  data: any;\r\n  error: any;\r\n  count: number;\r\n  status: number;\r\n  statusText: string;\r\n};\r\n\r\nfunction createMockSupabaseClient(headersList: Headers) {\r\n  const testAuthState = headersList.get('x-test-auth-state');\r\n  const testUserType = headersList.get('x-test-user-type');\r\n  const testHasProfile = testUserType === 'customer' || testUserType === 'business';\r\n  const testBusinessSlug = headersList.get('x-test-business-slug');\r\n  const testPlanId = headersList.get('x-test-plan-id') || 'free';\r\n\r\n  return {\r\n    auth: {\r\n      getUser: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { user: { id: 'test-user-id', email: '<EMAIL>' } }, error: null };\r\n        }\r\n        return { data: { user: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      getSession: async () => {\r\n        if (testAuthState === 'authenticated') {\r\n          return { data: { session: { user: { id: 'test-user-id', email: '<EMAIL>' } } }, error: null };\r\n        }\r\n        return { data: { session: null }, error: { message: 'Unauthorized', name: 'AuthApiError', status: 401 } };\r\n      },\r\n      signInWithOtp: async () => ({ data: { user: null, session: null }, error: null }),\r\n      signOut: async () => ({ error: null }),\r\n    },\r\n    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId),\r\n  };\r\n}\r\n\r\nfunction createMockQueryBuilder(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n): any {\r\n  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);\r\n\r\n  const createChainableMock = (data?: any): MockQueryBuilder => ({\r\n    select: (_columns?: string) => createChainableMock(data),\r\n    eq: (_column: string, _value: any) => createChainableMock(data),\r\n    neq: (_column: string, _value: any) => createChainableMock(data),\r\n    gt: (_column: string, _value: any) => createChainableMock(data),\r\n    gte: (_column: string, _value: any) => createChainableMock(data),\r\n    lt: (_column: string, _value: any) => createChainableMock(data),\r\n    lte: (_column: string, _value: any) => createChainableMock(data),\r\n    like: (_column: string, _pattern: string) => createChainableMock(data),\r\n    ilike: (_column: string, _pattern: string) => createChainableMock(data),\r\n    is: (_column: string, _value: any) => createChainableMock(data),\r\n    in: (_column: string, _values: any[]) => createChainableMock(data),\r\n    contains: (_column: string, _value: any) => createChainableMock(data),\r\n    containedBy: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeGte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLt: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeLte: (_column: string, _value: any) => createChainableMock(data),\r\n    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),\r\n    overlaps: (_column: string, _value: any) => createChainableMock(data),\r\n    textSearch: (_column: string, _query: string) => createChainableMock(data),\r\n    match: (_query: Record<string, any>) => createChainableMock(data),\r\n    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    or: (_filters: string) => createChainableMock(data),\r\n    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),\r\n    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),\r\n    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),\r\n    abortSignal: (_signal: AbortSignal) => createChainableMock(data),\r\n    single: async () => getMockData(),\r\n    maybeSingle: async () => getMockData(),\r\n    then: async (callback?: any) => {\r\n      const result = getMockData();\r\n      return callback ? callback(result) : result;\r\n    },\r\n    data: data || [],\r\n    error: null,\r\n    count: data ? data.length : 0,\r\n    status: 200,\r\n    statusText: 'OK',\r\n  });\r\n\r\n  return {\r\n    select: (_columns?: string) => createChainableMock(),\r\n    insert: (data: any | any[]) => ({\r\n      select: (_columns?: string) => ({\r\n        single: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        maybeSingle: async () => ({\r\n          data: Array.isArray(data) ? data[0] : data,\r\n          error: null,\r\n        }),\r\n        then: async (_callback?: any) => {\r\n          const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n          return _callback ? _callback(result) : result;\r\n        },\r\n      }),\r\n      then: async (_callback?: any) => {\r\n        const result = { data: Array.isArray(data) ? data : [data], error: null };\r\n        return _callback ? _callback(result) : result;\r\n      },\r\n    }),\r\n    update: (data: any) => createChainableMock(data),\r\n    upsert: (data: any | any[]) => createChainableMock(data),\r\n    delete: () => createChainableMock(),\r\n    rpc: (_functionName: string, _params?: any) => createChainableMock(),\r\n  };\r\n}\r\n\r\n/**\r\n * Helper function to get mock table data based on test state\r\n */\r\nfunction getMockTableData(\r\n  table: string,\r\n  testUserType: string | null,\r\n  testHasProfile: boolean,\r\n  testBusinessSlug: string | null,\r\n  testPlanId: string\r\n) {\r\n  if (table === 'customer_profiles') {\r\n    const hasCustomerProfile = testHasProfile && testUserType === 'customer';\r\n    return {\r\n      data: hasCustomerProfile ? {\r\n        id: 'test-user-id',\r\n        name: 'Test Customer',\r\n        avatar_url: null,\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        address: 'Test Address',\r\n        city: 'Test City',\r\n        state: 'Test State',\r\n        pincode: '123456'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'business_profiles') {\r\n    const hasBusinessProfile = testHasProfile && testUserType === 'business';\r\n    return {\r\n      data: hasBusinessProfile ? {\r\n        id: 'test-user-id',\r\n        business_slug: testBusinessSlug || null,\r\n        trial_end_date: null,\r\n        has_active_subscription: true,\r\n        business_name: 'Test Business',\r\n        city_slug: 'test-city',\r\n        state_slug: 'test-state',\r\n        locality_slug: 'test-locality',\r\n        pincode: '123456',\r\n        business_description: 'Test business description',\r\n        business_category: 'retail',\r\n        phone: '+1234567890',\r\n        email: '<EMAIL>',\r\n        website: 'https://testbusiness.com'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'payment_subscriptions') {\r\n    return {\r\n      data: testUserType === 'business' ? {\r\n        id: 'test-subscription-id',\r\n        plan_id: testPlanId,\r\n        business_profile_id: 'test-user-id',\r\n        status: 'active',\r\n        created_at: '2024-01-01T00:00:00Z'\r\n      } : null,\r\n      error: null\r\n    };\r\n  }\r\n\r\n  if (table === 'products') {\r\n    return {\r\n      data: testUserType === 'business' ? [\r\n        {\r\n          id: 'test-product-1',\r\n          name: 'Test Product 1',\r\n          price: 100,\r\n          business_profile_id: 'test-user-id',\r\n          available: true\r\n        },\r\n        {\r\n          id: 'test-product-2',\r\n          name: 'Test Product 2',\r\n          price: 200,\r\n          business_profile_id: 'test-user-id',\r\n          available: false\r\n        }\r\n      ] : [],\r\n      error: null\r\n    };\r\n  }\r\n\r\n  // Default return for unknown tables\r\n  return { data: null, error: null };\r\n}\r\n\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAIO,eAAe;IACpB,MAAM;IACN,MAAM;IAEN,uCAAsC;;IAEtC;IAEA,uCAAuC;IACvC,IAAI,cAA8B;IAClC,IAAI,cAAmB;IAEvB,IAAI;QACF,kEAAkE;QAClE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,cAAc,MAAM;QACpB,cAAc,MAAM;IACtB,EAAE,OAAO,OAAO;QACd,gFAAgF;QAChF,QAAQ,IAAI,CAAC;IACf;IAEA,MAAM,oBACJ,oDAAyB,UACzB,QAAQ,GAAG,CAAC,kBAAkB,KAAK,UAClC,eAAe,YAAY,GAAG,CAAC,4BAA4B;IAE9D,IAAI,qBAAqB,aAAa;QACpC,8CAA8C;QAC9C,OAAO,yBAAyB;IAClC;IAEA,6DAA6D;IAC7D,IAAI,CAAC,aAAa;QAChB,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;YACE,SAAS;gBACP;oBACE,OAAO,EAAE;gBACX;gBACA;gBACE,uCAAuC;gBACzC;YACF;QACF;IAEJ;IAEA,OAAO,CAAA,GAAA,yKAAA,CAAA,qBAAkB,AAAD,EACtB,aACA,iBACA;QACE,SAAS;YACP,MAAM;gBACJ,OAAO,MAAM,YAAY,MAAM;YACjC;YACA,MAAM,QAAO,YAAmB;gBAC9B,IAAI;oBACF,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,aAAc;wBACnD,MAAM,YAAY,GAAG,CAAC,MAAM,OAAO;oBACrC;gBACF,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ;AAyCA,SAAS,yBAAyB,WAAoB;IACpD,MAAM,gBAAgB,YAAY,GAAG,CAAC;IACtC,MAAM,eAAe,YAAY,GAAG,CAAC;IACrC,MAAM,iBAAiB,iBAAiB,cAAc,iBAAiB;IACvE,MAAM,mBAAmB,YAAY,GAAG,CAAC;IACzC,MAAM,aAAa,YAAY,GAAG,CAAC,qBAAqB;IAExD,OAAO;QACL,MAAM;YACJ,SAAS;gBACP,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,MAAM;gCAAE,IAAI;gCAAgB,OAAO;4BAAmB;wBAAE;wBAAG,OAAO;oBAAK;gBAC1F;gBACA,OAAO;oBAAE,MAAM;wBAAE,MAAM;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YACvG;YACA,YAAY;gBACV,IAAI,kBAAkB,iBAAiB;oBACrC,OAAO;wBAAE,MAAM;4BAAE,SAAS;gCAAE,MAAM;oCAAE,IAAI;oCAAgB,OAAO;gCAAmB;4BAAE;wBAAE;wBAAG,OAAO;oBAAK;gBACvG;gBACA,OAAO;oBAAE,MAAM;wBAAE,SAAS;oBAAK;oBAAG,OAAO;wBAAE,SAAS;wBAAgB,MAAM;wBAAgB,QAAQ;oBAAI;gBAAE;YAC1G;YACA,eAAe,UAAY,CAAC;oBAAE,MAAM;wBAAE,MAAM;wBAAM,SAAS;oBAAK;oBAAG,OAAO;gBAAK,CAAC;YAChF,SAAS,UAAY,CAAC;oBAAE,OAAO;gBAAK,CAAC;QACvC;QACA,MAAM,CAAC,QAAkB,uBAAuB,OAAO,cAAc,gBAAgB,kBAAkB;IACzG;AACF;AAEA,SAAS,uBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,MAAM,cAAc,IAAM,iBAAiB,OAAO,cAAc,gBAAgB,kBAAkB;IAElG,MAAM,sBAAsB,CAAC,OAAiC,CAAC;YAC7D,QAAQ,CAAC,WAAsB,oBAAoB;YACnD,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,KAAK,CAAC,SAAiB,SAAgB,oBAAoB;YAC3D,MAAM,CAAC,SAAiB,WAAqB,oBAAoB;YACjE,OAAO,CAAC,SAAiB,WAAqB,oBAAoB;YAClE,IAAI,CAAC,SAAiB,SAAgB,oBAAoB;YAC1D,IAAI,CAAC,SAAiB,UAAmB,oBAAoB;YAC7D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,aAAa,CAAC,SAAiB,SAAgB,oBAAoB;YACnE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,SAAS,CAAC,SAAiB,SAAgB,oBAAoB;YAC/D,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,eAAe,CAAC,SAAiB,SAAgB,oBAAoB;YACrE,UAAU,CAAC,SAAiB,SAAgB,oBAAoB;YAChE,YAAY,CAAC,SAAiB,SAAmB,oBAAoB;YACrE,OAAO,CAAC,SAAgC,oBAAoB;YAC5D,KAAK,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YAC9E,IAAI,CAAC,WAAqB,oBAAoB;YAC9C,QAAQ,CAAC,SAAiB,WAAmB,SAAgB,oBAAoB;YACjF,OAAO,CAAC,SAAiB,WAA6D,oBAAoB;YAC1G,OAAO,CAAC,QAAgB,WAAyC,oBAAoB;YACrF,OAAO,CAAC,OAAe,KAAa,WAAyC,oBAAoB;YACjG,aAAa,CAAC,UAAyB,oBAAoB;YAC3D,QAAQ,UAAY;YACpB,aAAa,UAAY;YACzB,MAAM,OAAO;gBACX,MAAM,SAAS;gBACf,OAAO,WAAW,SAAS,UAAU;YACvC;YACA,MAAM,QAAQ,EAAE;YAChB,OAAO;YACP,OAAO,OAAO,KAAK,MAAM,GAAG;YAC5B,QAAQ;YACR,YAAY;QACd,CAAC;IAED,OAAO;QACL,QAAQ,CAAC,WAAsB;QAC/B,QAAQ,CAAC,OAAsB,CAAC;gBAC9B,QAAQ,CAAC,WAAsB,CAAC;wBAC9B,QAAQ,UAAY,CAAC;gCACnB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,aAAa,UAAY,CAAC;gCACxB,MAAM,MAAM,OAAO,CAAC,QAAQ,IAAI,CAAC,EAAE,GAAG;gCACtC,OAAO;4BACT,CAAC;wBACD,MAAM,OAAO;4BACX,MAAM,SAAS;gCAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;oCAAC;iCAAK;gCAAE,OAAO;4BAAK;4BACxE,OAAO,YAAY,UAAU,UAAU;wBACzC;oBACF,CAAC;gBACD,MAAM,OAAO;oBACX,MAAM,SAAS;wBAAE,MAAM,MAAM,OAAO,CAAC,QAAQ,OAAO;4BAAC;yBAAK;wBAAE,OAAO;oBAAK;oBACxE,OAAO,YAAY,UAAU,UAAU;gBACzC;YACF,CAAC;QACD,QAAQ,CAAC,OAAc,oBAAoB;QAC3C,QAAQ,CAAC,OAAsB,oBAAoB;QACnD,QAAQ,IAAM;QACd,KAAK,CAAC,eAAuB,UAAkB;IACjD;AACF;AAEA;;CAEC,GACD,SAAS,iBACP,KAAa,EACb,YAA2B,EAC3B,cAAuB,EACvB,gBAA+B,EAC/B,UAAkB;IAElB,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,MAAM;gBACN,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,MAAM;gBACN,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,qBAAqB;QACjC,MAAM,qBAAqB,kBAAkB,iBAAiB;QAC9D,OAAO;YACL,MAAM,qBAAqB;gBACzB,IAAI;gBACJ,eAAe,oBAAoB;gBACnC,gBAAgB;gBAChB,yBAAyB;gBACzB,eAAe;gBACf,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,SAAS;gBACT,sBAAsB;gBACtB,mBAAmB;gBACnB,OAAO;gBACP,OAAO;gBACP,SAAS;YACX,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,yBAAyB;QACrC,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC,IAAI;gBACJ,SAAS;gBACT,qBAAqB;gBACrB,QAAQ;gBACR,YAAY;YACd,IAAI;YACJ,OAAO;QACT;IACF;IAEA,IAAI,UAAU,YAAY;QACxB,OAAO;YACL,MAAM,iBAAiB,aAAa;gBAClC;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,qBAAqB;oBACrB,WAAW;gBACb;aACD,GAAG,EAAE;YACN,OAAO;QACT;IACF;IAEA,oCAAoC;IACpC,OAAO;QAAE,MAAM;QAAM,OAAO;IAAK;AACnC", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/utils.ts"], "sourcesContent": ["import { BusinessSortBy } from \"./types\";\r\n\r\n/**\r\n * Apply sorting to a Supabase query based on the provided sort option\r\n * Using 'any' type here is acceptable since we're working with Supabase query builder\r\n * which has a complex type structure that's difficult to represent precisely\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport function applySorting(query: any, sortBy: BusinessSortBy): any {\r\n  switch (sortBy) {\r\n    case \"name_asc\":\r\n      return query.order(\"business_name\", { ascending: true });\r\n    case \"name_desc\":\r\n      return query.order(\"business_name\", { ascending: false });\r\n    case \"created_asc\":\r\n      return query.order(\"created_at\", { ascending: true });\r\n    case \"created_desc\":\r\n      return query.order(\"created_at\", { ascending: false });\r\n    case \"likes_asc\":\r\n      return query.order(\"total_likes\", { ascending: true });\r\n    case \"likes_desc\":\r\n      return query.order(\"total_likes\", { ascending: false });\r\n    case \"subscriptions_asc\":\r\n      return query.order(\"total_subscriptions\", { ascending: true });\r\n    case \"subscriptions_desc\":\r\n      return query.order(\"total_subscriptions\", { ascending: false });\r\n    case \"rating_asc\":\r\n      return query.order(\"average_rating\", { ascending: true });\r\n    case \"rating_desc\":\r\n      return query.order(\"average_rating\", { ascending: false });\r\n    default:\r\n      return query.order(\"created_at\", { ascending: false });\r\n  }\r\n}\r\n\r\n/**\r\n * Get the current ISO timestamp\r\n */\r\nexport function getCurrentISOTimestamp(): string {\r\n  return new Date().toISOString();\r\n}\r\n\r\n/**\r\n * Create a subscription map from subscription data\r\n */\r\nexport function createSubscriptionMap(subscriptionsData: Array<{\r\n  business_profile_id: string;\r\n  subscription_status: string | null;\r\n  plan_id: string | null;\r\n}> | null) {\r\n  const subscriptionMap = new Map<string, {\r\n    subscription_status: string | null;\r\n    plan_id: string | null;\r\n  }>();\r\n\r\n  if (subscriptionsData) {\r\n    // Group by business_profile_id and take the most recent one\r\n    subscriptionsData.forEach(sub => {\r\n      if (!subscriptionMap.has(sub.business_profile_id)) {\r\n        subscriptionMap.set(sub.business_profile_id, {\r\n          subscription_status: sub.subscription_status,\r\n          plan_id: sub.plan_id\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  return subscriptionMap;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,aAAa,KAAU,EAAE,MAAsB;IAC7D,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,iBAAiB;gBAAE,WAAW;YAAK;QACxD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,iBAAiB;gBAAE,WAAW;YAAM;QACzD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAK;QACrD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;QACtD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAK;QACtD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,eAAe;gBAAE,WAAW;YAAM;QACvD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,uBAAuB;gBAAE,WAAW;YAAK;QAC9D,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,uBAAuB;gBAAE,WAAW;YAAM;QAC/D,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,kBAAkB;gBAAE,WAAW;YAAK;QACzD,KAAK;YACH,OAAO,MAAM,KAAK,CAAC,kBAAkB;gBAAE,WAAW;YAAM;QAC1D;YACE,OAAO,MAAM,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;IACxD;AACF;AAKO,SAAS;IACd,OAAO,IAAI,OAAO,WAAW;AAC/B;AAKO,SAAS,sBAAsB,iBAI7B;IACP,MAAM,kBAAkB,IAAI;IAK5B,IAAI,mBAAmB;QACrB,4DAA4D;QAC5D,kBAAkB,OAAO,CAAC,CAAA;YACxB,IAAI,CAAC,gBAAgB,GAAG,CAAC,IAAI,mBAAmB,GAAG;gBACjD,gBAAgB,GAAG,CAAC,IAAI,mBAAmB,EAAE;oBAC3C,qBAAqB,IAAI,mBAAmB;oBAC5C,SAAS,IAAI,OAAO;gBACtB;YACF;QACF;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/profileRetrieval.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport {\r\n  BusinessProfilePublicData,\r\n  BusinessProfileWithProducts,\r\n} from \"./types\";\r\n\r\n/**\r\n * Securely fetch a business profile by slug using the service role key\r\n * This bypasses RLS and ensures sensitive data is not exposed to the client\r\n */\r\nexport async function getSecureBusinessProfileBySlug(slug: string): Promise<{\r\n  data?: BusinessProfilePublicData;\r\n  error?: string;\r\n}> {\r\n  if (!slug) {\r\n    return { error: \"Business slug is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the business profile API endpoint for public access by slug\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to fetch business profile\",\r\n      };\r\n    }\r\n\r\n    const profileData = result.business;\r\n    if (!profileData) {\r\n      return { error: \"Profile not found.\" };\r\n    }\r\n\r\n    return { data: profileData };\r\n  } catch (e) {\r\n    console.error(\"Exception in getSecureBusinessProfileBySlug:\", e);\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Securely fetch a business profile with products by slug using the service role key\r\n */\r\nexport async function getSecureBusinessProfileWithProductsBySlug(\r\n  slug: string\r\n): Promise<{\r\n  data?: BusinessProfileWithProducts;\r\n  error?: string;\r\n}> {\r\n  if (!slug) {\r\n    return { error: \"Business slug is required.\" };\r\n  }\r\n\r\n  try {\r\n    // Use the business profile API endpoint for public access by slug\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/slug/${slug}?include_products=true`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to fetch business profile\",\r\n      };\r\n    }\r\n\r\n    const profileData = result.business;\r\n    if (!profileData) {\r\n      return { error: \"Profile not found.\" };\r\n    }\r\n\r\n    const safeData: BusinessProfileWithProducts = {\r\n      ...profileData,\r\n      products_services: profileData.products_services || [],\r\n    };\r\n\r\n    return { data: safeData };\r\n  } catch (e) {\r\n    console.error(\r\n      \"Exception in getSecureBusinessProfileWithProductsBySlug:\",\r\n      e\r\n    );\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAWO,eAAe,+BAA+B,IAAY;IAI/D,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;QAA6B;IAC/C;IAEA,IAAI;QACF,kEAAkE;QAClE,MAAM,WAAW,MAAM,MAAM,6DAAoC,mBAAmB,EAAE,MAAM,EAAE;YAC5F,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,OAAO;YAAE,MAAM;QAAY;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,2CACpB,IAAY;IAKZ,IAAI,CAAC,MAAM;QACT,OAAO;YAAE,OAAO;QAA6B;IAC/C;IAEA,IAAI;QACF,kEAAkE;QAClE,MAAM,WAAW,MAAM,MAAM,6DAAoC,mBAAmB,EAAE,KAAK,sBAAsB,CAAC,EAAE;YAClH,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,cAAc,OAAO,QAAQ;QACnC,IAAI,CAAC,aAAa;YAChB,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,MAAM,WAAwC;YAC5C,GAAG,WAAW;YACd,mBAAmB,YAAY,iBAAiB,IAAI,EAAE;QACxD;QAEA,OAAO;YAAE,MAAM;QAAS;IAC1B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CACX,4DACA;QAEF,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAvFsB;IAyCA;;AAzCA,+OAAA;AAyCA,+OAAA", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/discovery.ts"], "sourcesContent": ["\"use server\";\n\nimport { BusinessProfilePublicData, BusinessSortBy } from \"./types\";\n\n/**\n * Securely fetch business profiles for discover page using the service role key\n */\nexport async function getSecureBusinessProfilesForDiscover(\n  pincodes: string | string[],\n  locality?: string | null,\n  page: number = 1,\n  limit: number = 10,\n  sortBy: BusinessSortBy = \"created_desc\"\n): Promise<{\n  data?: BusinessProfilePublicData[];\n  count?: number;\n  error?: string;\n}> {\n  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {\n    return { error: \"At least one pincode is required.\" };\n  }\n\n  // Convert single pincode to array for consistent handling\n  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];\n\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    queryParams.set('pincodes', pincodeArray.join(','));\n    queryParams.set('page', page.toString());\n    queryParams.set('limit', limit.toString());\n    queryParams.set('status', 'online');\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n        apiSortBy = 'likes_asc';\n        break;\n      case 'likes_desc':\n        apiSortBy = 'likes_desc';\n        break;\n      case 'subscriptions_asc':\n        apiSortBy = 'subscriptions_asc';\n        break;\n      case 'subscriptions_desc':\n        apiSortBy = 'subscriptions_desc';\n        break;\n      case 'rating_asc':\n        apiSortBy = 'rating_asc';\n        break;\n      case 'rating_desc':\n        apiSortBy = 'rating_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n\n    // Use the business profile API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profiles\",\n      };\n    }\n\n    const businesses = result.businesses || [];\n    const count = result.pagination?.total || 0;\n\n    // Transform data to match expected format\n    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {\n      return {\n        ...profile,\n        // Add missing fields with default values if not present\n        total_visits: profile.total_visits || 0,\n        today_visits: profile.today_visits || 0,\n        yesterday_visits: profile.yesterday_visits || 0,\n        visits_7_days: profile.visits_7_days || 0,\n        visits_30_days: profile.visits_30_days || 0,\n        city_slug: profile.city_slug || null,\n        state_slug: profile.state_slug || null,\n        locality_slug: profile.locality_slug || null,\n        gallery: profile.gallery || null,\n        latitude: profile.latitude || null,\n        longitude: profile.longitude || null,\n      } as BusinessProfilePublicData;\n    });\n\n    return { data: safeData, count };\n  } catch (e) {\n    console.error(\"Exception in getSecureBusinessProfilesForDiscover:\", e);\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n\n/**\n * Securely fetch business profile IDs for discover page products using the service role key\n */\nexport async function getSecureBusinessProfileIdsForDiscover(\n  pincodes: string | string[],\n  locality?: string | null,\n  sortBy: BusinessSortBy = \"created_desc\"\n): Promise<{\n  data?: string[];\n  error?: string;\n}> {\n  if (!pincodes || (Array.isArray(pincodes) && pincodes.length === 0)) {\n    return { error: \"At least one pincode is required.\" };\n  }\n\n  // Convert single pincode to array for consistent handling\n  const pincodeArray = Array.isArray(pincodes) ? pincodes : [pincodes];\n\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    queryParams.set('pincodes', pincodeArray.join(','));\n    queryParams.set('status', 'online');\n    queryParams.set('ids_only', 'true');\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n        apiSortBy = 'likes_asc';\n        break;\n      case 'likes_desc':\n        apiSortBy = 'likes_desc';\n        break;\n      case 'subscriptions_asc':\n        apiSortBy = 'subscriptions_asc';\n        break;\n      case 'subscriptions_desc':\n        apiSortBy = 'subscriptions_desc';\n        break;\n      case 'rating_asc':\n        apiSortBy = 'rating_asc';\n        break;\n      case 'rating_desc':\n        apiSortBy = 'rating_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n\n    // Use the business profile API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profile IDs\",\n      };\n    }\n\n    const businessIds = result.business_ids || [];\n\n    return { data: businessIds };\n  } catch (e) {\n    console.error(\"Exception in getSecureBusinessProfileIdsForDiscover:\", e);\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;AAOO,eAAe,qCACpB,QAA2B,EAC3B,QAAwB,EACxB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAyB,cAAc;IAMvC,IAAI,CAAC,YAAa,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAI;QACnE,OAAO;YAAE,OAAO;QAAoC;IACtD;IAEA,0DAA0D;IAC1D,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW;QAAC;KAAS;IAEpE,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,YAAY,aAAa,IAAI,CAAC;QAC9C,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;QACrC,YAAY,GAAG,CAAC,SAAS,MAAM,QAAQ;QACvC,YAAY,GAAG,CAAC,UAAU;QAE1B,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC,YAAY;QAC9B;QAEA,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,aAAa,OAAO,UAAU,IAAI,EAAE;QAC1C,MAAM,QAAQ,OAAO,UAAU,EAAE,SAAS;QAE1C,0CAA0C;QAC1C,MAAM,WAAwC,WAAW,GAAG,CAAC,CAAC;YAC5D,OAAO;gBACL,GAAG,OAAO;gBACV,wDAAwD;gBACxD,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,eAAe,QAAQ,aAAa,IAAI;gBACxC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,WAAW,QAAQ,SAAS,IAAI;gBAChC,YAAY,QAAQ,UAAU,IAAI;gBAClC,eAAe,QAAQ,aAAa,IAAI;gBACxC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI;YAClC;QACF;QAEA,OAAO;YAAE,MAAM;YAAU;QAAM;IACjC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,sDAAsD;QACpE,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;AAKO,eAAe,uCACpB,QAA2B,EAC3B,QAAwB,EACxB,SAAyB,cAAc;IAKvC,IAAI,CAAC,YAAa,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAI;QACnE,OAAO;YAAE,OAAO;QAAoC;IACtD;IAEA,0DAA0D;IAC1D,MAAM,eAAe,MAAM,OAAO,CAAC,YAAY,WAAW;QAAC;KAAS;IAEpE,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,YAAY,aAAa,IAAI,CAAC;QAC9C,YAAY,GAAG,CAAC,UAAU;QAC1B,YAAY,GAAG,CAAC,YAAY;QAE5B,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC,YAAY;QAC9B;QAEA,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,cAAc,OAAO,YAAY,IAAI,EAAE;QAE7C,OAAO;YAAE,MAAM;QAAY;IAC7B,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,wDAAwD;QACtE,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAzMsB;IAmHA;;AAnHA,+OAAA;AAmHA,+OAAA", "debugId": null}}, {"offset": {"line": 772, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/search.ts"], "sourcesContent": ["\"use server\";\n\nimport { BusinessProfilePublicData, BusinessSortBy } from \"./types\";\n\n/**\n * Securely fetch all business profiles or search by name and/or location\n */\nexport async function getSecureBusinessProfiles(\n  searchTerm?: string | null,\n  pincode?: string | null,\n  locality?: string | null,\n  page: number = 1,\n  limit: number = 20,\n  sortBy: BusinessSortBy = \"created_desc\",\n  category?: string | null\n): Promise<{\n  data?: BusinessProfilePublicData[];\n  count?: number;\n  error?: string;\n}> {\n  try {\n    // Build query parameters\n    const queryParams = new URLSearchParams();\n    queryParams.set('page', page.toString());\n    queryParams.set('limit', limit.toString());\n    queryParams.set('status', 'online');\n\n    if (searchTerm) {\n      queryParams.set('search', searchTerm.trim());\n    }\n    if (pincode) {\n      queryParams.set('pincode', pincode);\n    }\n    if (locality) {\n      queryParams.set('locality', locality);\n    }\n    if (category && category.trim()) {\n      queryParams.set('category', category.trim());\n    }\n\n    // Convert sortBy to API format\n    let apiSortBy = 'created_desc';\n    switch (sortBy) {\n      case 'name_asc':\n        apiSortBy = 'name_asc';\n        break;\n      case 'name_desc':\n        apiSortBy = 'name_desc';\n        break;\n      case 'created_asc':\n        apiSortBy = 'created_asc';\n        break;\n      case 'created_desc':\n        apiSortBy = 'created_desc';\n        break;\n      case 'likes_asc':\n        apiSortBy = 'likes_asc';\n        break;\n      case 'likes_desc':\n        apiSortBy = 'likes_desc';\n        break;\n      case 'subscriptions_asc':\n        apiSortBy = 'subscriptions_asc';\n        break;\n      case 'subscriptions_desc':\n        apiSortBy = 'subscriptions_desc';\n        break;\n      case 'rating_asc':\n        apiSortBy = 'rating_asc';\n        break;\n      case 'rating_desc':\n        apiSortBy = 'rating_desc';\n        break;\n    }\n    queryParams.set('sort_by', apiSortBy);\n\n    // Use the business profile API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profiles\",\n      };\n    }\n\n    const businesses = result.businesses || [];\n    const count = result.pagination?.total || 0;\n\n    // Transform data to match expected format\n    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {\n      return {\n        ...profile,\n        // Add missing fields with default values if not present\n        total_visits: profile.total_visits || 0,\n        today_visits: profile.today_visits || 0,\n        yesterday_visits: profile.yesterday_visits || 0,\n        visits_7_days: profile.visits_7_days || 0,\n        visits_30_days: profile.visits_30_days || 0,\n        city_slug: profile.city_slug || null,\n        state_slug: profile.state_slug || null,\n        locality_slug: profile.locality_slug || null,\n        gallery: profile.gallery || null,\n        latitude: profile.latitude || null,\n        longitude: profile.longitude || null,\n      } as BusinessProfilePublicData;\n    });\n\n    return { data: safeData, count };\n  } catch (e) {\n    console.error(\"Exception in getSecureBusinessProfiles:\", e);\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAOO,eAAe,0BACpB,UAA0B,EAC1B,OAAuB,EACvB,QAAwB,EACxB,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAyB,cAAc,EACvC,QAAwB;IAMxB,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;QACrC,YAAY,GAAG,CAAC,SAAS,MAAM,QAAQ;QACvC,YAAY,GAAG,CAAC,UAAU;QAE1B,IAAI,YAAY;YACd,YAAY,GAAG,CAAC,UAAU,WAAW,IAAI;QAC3C;QACA,IAAI,SAAS;YACX,YAAY,GAAG,CAAC,WAAW;QAC7B;QACA,IAAI,UAAU;YACZ,YAAY,GAAG,CAAC,YAAY;QAC9B;QACA,IAAI,YAAY,SAAS,IAAI,IAAI;YAC/B,YAAY,GAAG,CAAC,YAAY,SAAS,IAAI;QAC3C;QAEA,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,aAAa,OAAO,UAAU,IAAI,EAAE;QAC1C,MAAM,QAAQ,OAAO,UAAU,EAAE,SAAS;QAE1C,0CAA0C;QAC1C,MAAM,WAAwC,WAAW,GAAG,CAAC,CAAC;YAC5D,OAAO;gBACL,GAAG,OAAO;gBACV,wDAAwD;gBACxD,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,eAAe,QAAQ,aAAa,IAAI;gBACxC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,WAAW,QAAQ,SAAS,IAAI;gBAChC,YAAY,QAAQ,UAAU,IAAI;gBAClC,eAAe,QAAQ,aAAa,IAAI;gBACxC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI;YAClC;QACF;QAEA,OAAO;YAAE,MAAM;YAAU;QAAM;IACjC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,2CAA2C;QACzD,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAjHsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/sitemap.ts"], "sourcesContent": ["\"use server\";\n\nimport { SitemapProfileData } from \"./types\";\n\n/**\n * Securely fetch business profiles for sitemap using the API endpoint\n */\nexport async function getSecureBusinessProfilesForSitemap(): Promise<{\n  data?: SitemapProfileData[];\n  error?: string;\n}> {\n  try {\n    // Use the business sitemap API endpoint\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/sitemap`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"API Fetch Error:\", result);\n      return {\n        error: result.error || \"Failed to fetch business profiles for sitemap\",\n      };\n    }\n\n    const profiles = result.profiles || [];\n\n    // If there are no profiles, return empty array\n    if (!profiles || profiles.length === 0) {\n      return { data: [] };\n    }\n\n    // Create a map to deduplicate by business_slug\n    const uniqueProfiles = new Map<\n      string,\n      { business_slug: string; updated_at: string }\n    >();\n\n    // Add all profiles to the map (this automatically deduplicates by business_slug)\n    profiles.forEach((profile: { business_slug: string; updated_at: string }) => {\n      if (profile.business_slug) {\n        uniqueProfiles.set(profile.business_slug, {\n          business_slug: profile.business_slug,\n          updated_at: profile.updated_at,\n        });\n      }\n    });\n\n    // Convert map values to array\n    const combinedProfiles = Array.from(uniqueProfiles.values());\n\n    // Return the deduplicated profiles\n    return { data: combinedProfiles };\n  } catch (_e) {\n    return { error: \"An unexpected error occurred.\" };\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;AAOO,eAAe;IAIpB,IAAI;QACF,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,qBAAqB,CAAC,EAAE;YACvF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,WAAW,OAAO,QAAQ,IAAI,EAAE;QAEtC,+CAA+C;QAC/C,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,MAAM,EAAE;YAAC;QACpB;QAEA,+CAA+C;QAC/C,MAAM,iBAAiB,IAAI;QAK3B,iFAAiF;QACjF,SAAS,OAAO,CAAC,CAAC;YAChB,IAAI,QAAQ,aAAa,EAAE;gBACzB,eAAe,GAAG,CAAC,QAAQ,aAAa,EAAE;oBACxC,eAAe,QAAQ,aAAa;oBACpC,YAAY,QAAQ,UAAU;gBAChC;YACF;QACF;QAEA,8BAA8B;QAC9B,MAAM,mBAAmB,MAAM,IAAI,CAAC,eAAe,MAAM;QAEzD,mCAAmC;QACnC,OAAO;YAAE,MAAM;QAAiB;IAClC,EAAE,OAAO,IAAI;QACX,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IArDsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/access.ts"], "sourcesContent": ["\"use server\";\r\n\r\n/**\r\n * Check if the current user has access to a business profile\r\n * This is used to verify ownership before allowing certain operations\r\n */\r\nexport async function checkBusinessProfileAccess(\r\n  businessProfileId: string\r\n): Promise<{\r\n  hasAccess: boolean;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the business access API endpoint\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/access`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        // Note: In a real implementation, this would need proper authentication headers\r\n        // For now, this is a placeholder - the actual implementation would need\r\n        // to pass through the user's JWT token and HMAC signature\r\n      },\r\n      body: JSON.stringify({\r\n        business_profile_id: businessProfileId,\r\n      }),\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        hasAccess: false,\r\n        error: result.error || \"Failed to check business profile access\",\r\n      };\r\n    }\r\n\r\n    return { hasAccess: result.has_access };\r\n  } catch (e) {\r\n    console.error(\"Exception in checkBusinessProfileAccess:\", e);\r\n    return { hasAccess: false, error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Get the current user's business profile ID\r\n * This is useful for operations that need to know the user's business profile\r\n */\r\nexport async function getCurrentUserBusinessProfileId(): Promise<{\r\n  profileId?: string;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Use the business access API endpoint\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/access/me`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        // Note: In a real implementation, this would need proper authentication headers\r\n        // For now, this is a placeholder - the actual implementation would need\r\n        // to pass through the user's JWT token and HMAC signature\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to get current user business profile ID\",\r\n      };\r\n    }\r\n\r\n    return { profileId: result.profile_id };\r\n  } catch (e) {\r\n    console.error(\"Exception in getCurrentUserBusinessProfileId:\", e);\r\n    return { error: \"An unexpected error occurred\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAMO,eAAe,2BACpB,iBAAyB;IAKzB,IAAI;QACF,uCAAuC;QACvC,MAAM,WAAW,MAAM,MAAM,6DAAoC,oBAAoB,CAAC,EAAE;YACtF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAIlB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,qBAAqB;YACvB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,WAAW;gBACX,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,OAAO;YAAE,WAAW,OAAO,UAAU;QAAC;IACxC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO;YAAE,WAAW;YAAO,OAAO;QAA+B;IACnE;AACF;AAMO,eAAe;IAIpB,IAAI;QACF,uCAAuC;QACvC,MAAM,WAAW,MAAM,MAAM,6DAAoC,uBAAuB,CAAC,EAAE;YACzF,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAIlB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,OAAO;YAAE,WAAW,OAAO,UAAU;QAAC;IACxC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,OAAO;YAAE,OAAO;QAA+B;IACjD;AACF;;;IAxEsB;IA0CA;;AA1CA,+OAAA;AA0CA,+OAAA", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/location.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { BusinessProfilePublicData, BusinessSortBy } from \"./types\";\r\n\r\n/**\r\n * Securely fetch business profiles by location using the service role key\r\n */\r\nexport async function getSecureBusinessProfilesByLocation(\r\n  location: {\r\n    pincode?: string;\r\n    city?: string;\r\n    state?: string;\r\n    locality?: string;\r\n  },\r\n  page: number = 1,\r\n  limit: number = 20,\r\n  sortBy: BusinessSortBy = \"created_desc\"\r\n): Promise<{\r\n  data?: BusinessProfilePublicData[];\r\n  count?: number;\r\n  error?: string;\r\n}> {\r\n  try {\r\n    // Build query parameters\r\n    const queryParams = new URLSearchParams();\r\n    queryParams.set('page', page.toString());\r\n    queryParams.set('limit', limit.toString());\r\n    queryParams.set('status', 'online');\r\n\r\n    // Add location filters if provided\r\n    if (location.pincode) {\r\n      queryParams.set('pincode', location.pincode);\r\n    }\r\n    if (location.city) {\r\n      queryParams.set('city', location.city);\r\n    }\r\n    if (location.state) {\r\n      queryParams.set('state', location.state);\r\n    }\r\n    if (location.locality) {\r\n      queryParams.set('locality', location.locality);\r\n    }\r\n\r\n    // Convert sortBy to API format\r\n    let apiSortBy = 'created_desc';\r\n    switch (sortBy) {\r\n      case 'name_asc':\r\n        apiSortBy = 'name_asc';\r\n        break;\r\n      case 'name_desc':\r\n        apiSortBy = 'name_desc';\r\n        break;\r\n      case 'created_asc':\r\n        apiSortBy = 'created_asc';\r\n        break;\r\n      case 'created_desc':\r\n        apiSortBy = 'created_desc';\r\n        break;\r\n      case 'likes_asc':\r\n        apiSortBy = 'likes_asc';\r\n        break;\r\n      case 'likes_desc':\r\n        apiSortBy = 'likes_desc';\r\n        break;\r\n      case 'subscriptions_asc':\r\n        apiSortBy = 'subscriptions_asc';\r\n        break;\r\n      case 'subscriptions_desc':\r\n        apiSortBy = 'subscriptions_desc';\r\n        break;\r\n      case 'rating_asc':\r\n        apiSortBy = 'rating_asc';\r\n        break;\r\n      case 'rating_desc':\r\n        apiSortBy = 'rating_desc';\r\n        break;\r\n    }\r\n    queryParams.set('sort_by', apiSortBy);\r\n\r\n    // Use the business profile API endpoint\r\n    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n    });\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      console.error(\"API Fetch Error:\", result);\r\n      return {\r\n        error: result.error || \"Failed to fetch business profiles by location\",\r\n      };\r\n    }\r\n\r\n    const businesses = result.businesses || [];\r\n    const count = result.pagination?.total || 0;\r\n\r\n    // Transform data to match expected format\r\n    const safeData: BusinessProfilePublicData[] = businesses.map((profile: any) => {\r\n      return {\r\n        ...profile,\r\n        // Add missing fields with default values if not present\r\n        total_visits: profile.total_visits || 0,\r\n        today_visits: profile.today_visits || 0,\r\n        yesterday_visits: profile.yesterday_visits || 0,\r\n        visits_7_days: profile.visits_7_days || 0,\r\n        visits_30_days: profile.visits_30_days || 0,\r\n        city_slug: profile.city_slug || null,\r\n        state_slug: profile.state_slug || null,\r\n        locality_slug: profile.locality_slug || null,\r\n        gallery: profile.gallery || null,\r\n        latitude: profile.latitude || null,\r\n        longitude: profile.longitude || null,\r\n      } as BusinessProfilePublicData;\r\n    });\r\n\r\n    return { data: safeData, count };\r\n  } catch (error) {\r\n    console.error(\r\n      \"Unexpected error in getSecureBusinessProfilesByLocation:\",\r\n      error\r\n    );\r\n    return { error: \"An unexpected error occurred.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAOO,eAAe,oCACpB,QAKC,EACD,OAAe,CAAC,EAChB,QAAgB,EAAE,EAClB,SAAyB,cAAc;IAMvC,IAAI;QACF,yBAAyB;QACzB,MAAM,cAAc,IAAI;QACxB,YAAY,GAAG,CAAC,QAAQ,KAAK,QAAQ;QACrC,YAAY,GAAG,CAAC,SAAS,MAAM,QAAQ;QACvC,YAAY,GAAG,CAAC,UAAU;QAE1B,mCAAmC;QACnC,IAAI,SAAS,OAAO,EAAE;YACpB,YAAY,GAAG,CAAC,WAAW,SAAS,OAAO;QAC7C;QACA,IAAI,SAAS,IAAI,EAAE;YACjB,YAAY,GAAG,CAAC,QAAQ,SAAS,IAAI;QACvC;QACA,IAAI,SAAS,KAAK,EAAE;YAClB,YAAY,GAAG,CAAC,SAAS,SAAS,KAAK;QACzC;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,YAAY,GAAG,CAAC,YAAY,SAAS,QAAQ;QAC/C;QAEA,+BAA+B;QAC/B,IAAI,YAAY;QAChB,OAAQ;YACN,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;YACF,KAAK;gBACH,YAAY;gBACZ;QACJ;QACA,YAAY,GAAG,CAAC,WAAW;QAE3B,wCAAwC;QACxC,MAAM,WAAW,MAAM,MAAM,6DAAoC,cAAc,EAAE,YAAY,QAAQ,IAAI,EAAE;YACzG,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBACL,OAAO,OAAO,KAAK,IAAI;YACzB;QACF;QAEA,MAAM,aAAa,OAAO,UAAU,IAAI,EAAE;QAC1C,MAAM,QAAQ,OAAO,UAAU,EAAE,SAAS;QAE1C,0CAA0C;QAC1C,MAAM,WAAwC,WAAW,GAAG,CAAC,CAAC;YAC5D,OAAO;gBACL,GAAG,OAAO;gBACV,wDAAwD;gBACxD,cAAc,QAAQ,YAAY,IAAI;gBACtC,cAAc,QAAQ,YAAY,IAAI;gBACtC,kBAAkB,QAAQ,gBAAgB,IAAI;gBAC9C,eAAe,QAAQ,aAAa,IAAI;gBACxC,gBAAgB,QAAQ,cAAc,IAAI;gBAC1C,WAAW,QAAQ,SAAS,IAAI;gBAChC,YAAY,QAAQ,UAAU,IAAI;gBAClC,eAAe,QAAQ,aAAa,IAAI;gBACxC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,UAAU,QAAQ,QAAQ,IAAI;gBAC9B,WAAW,QAAQ,SAAS,IAAI;YAClC;QACF;QAEA,OAAO;YAAE,MAAM;YAAU;QAAM;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,4DACA;QAEF,OAAO;YAAE,OAAO;QAAgC;IAClD;AACF;;;IAvHsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/businessProfiles/index.ts"], "sourcesContent": ["// Re-export types and utility functions\r\nexport * from './types';\r\nexport * from './utils';\r\n\r\n// Re-export server actions\r\nexport {\r\n  getSecureBusinessProfileBySlug,\r\n  getSecureBusinessProfileWithProductsBySlug\r\n} from './profileRetrieval';\r\n\r\nexport {\r\n  getSecureBusinessProfilesForDiscover,\r\n  getSecureBusinessProfileIdsForDiscover\r\n} from './discovery';\r\n\r\nexport {\r\n  getSecureBusinessProfiles\r\n} from './search';\r\n\r\nexport {\r\n  getSecureBusinessProfilesForSitemap\r\n} from './sitemap';\r\n\r\nexport {\r\n  checkBusinessProfileAccess,\r\n  getCurrentUserBusinessProfileId\r\n} from './access';\r\n\r\nexport {\r\n  getSecureBusinessProfilesByLocation\r\n} from './location';\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;AACxC;AACA;AAEA,2BAA2B;AAC3B;AAKA;AAKA;AAIA;AAIA;AAKA", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/businessActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport {\r\n  BusinessSortBy,\r\n  getSecureBusinessProfiles,\r\n} from \"@/lib/actions/businessProfiles\";\r\nimport { searchDiscoverCombined } from \"./combinedActions\";\r\n\r\n// Function to fetch more business cards for infinite scroll\r\nexport async function fetchMoreBusinessCardsCombined(params: {\r\n  businessName?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  page: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n}): Promise<{\r\n  data?: {\r\n    businesses: BusinessCardData[];\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    nextPage: number | null;\r\n  };\r\n  error?: string;\r\n}> {\r\n  // Reuse the searchDiscoverCombined function with viewType set to \"cards\"\r\n  const result = await searchDiscoverCombined({\r\n    ...params,\r\n    viewType: \"cards\",\r\n  });\r\n\r\n  if (result.error) {\r\n    return { error: result.error };\r\n  }\r\n\r\n  if (!result.data?.businesses) {\r\n    return { error: \"No business data found\" };\r\n  }\r\n\r\n  return {\r\n    data: {\r\n      businesses: result.data.businesses,\r\n      totalCount: result.data.totalCount,\r\n      hasMore: result.data.hasMore,\r\n      nextPage: result.data.nextPage,\r\n    },\r\n  };\r\n}\r\n\r\n// Function to fetch businesses by search criteria\r\nexport async function fetchBusinessesBySearch(params: {\r\n  businessName?: string | null;\r\n  pincode?: string | null;\r\n  locality?: string | null;\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n  category?: string | null;\r\n}): Promise<{\r\n  data?: {\r\n    businesses: BusinessCardData[];\r\n    location?: { city: string; state: string } | null;\r\n    isAuthenticated: boolean;\r\n    totalCount: number;\r\n    hasMore: boolean;\r\n    nextPage: number | null;\r\n  };\r\n  error?: string;\r\n}> {\r\n  const {\r\n    businessName,\r\n    pincode,\r\n    locality,\r\n    page = 1,\r\n    limit = 20,\r\n    sortBy = \"created_desc\",\r\n    category = null,\r\n  } = params;\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Check Authentication\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n\r\n  try {\r\n    // Use the secure method to fetch business profiles\r\n    const {\r\n      data: businessesData,\r\n      count,\r\n      error: businessesError,\r\n    } = await getSecureBusinessProfiles(\r\n      businessName,\r\n      pincode,\r\n      locality,\r\n      page,\r\n      limit,\r\n      sortBy,\r\n      category\r\n    );\r\n\r\n    if (businessesError) {\r\n      console.error(\"Search Businesses By Name Error:\", businessesError);\r\n      return { error: businessesError };\r\n    }\r\n\r\n    const totalCount = count || 0;\r\n    // Calculate if there are more pages\r\n    const hasMore =\r\n      totalCount > (page - 1) * limit + (businessesData?.length || 0);\r\n    const nextPage = hasMore ? page + 1 : null;\r\n\r\n    // Map raw data to BusinessCardData, handling potential nulls\r\n    const businesses: BusinessCardData[] =\r\n      businessesData?.map((data) => {\r\n        // Use the actual data from the database\r\n        return {\r\n          id: data.id,\r\n          business_name: data.business_name ?? \"\",\r\n          contact_email: \"\", // Not included in secure data\r\n          // Subscription fields removed - all users now have access to all features\r\n          created_at: data.created_at ?? undefined,\r\n          updated_at: data.updated_at ?? undefined,\r\n          logo_url: data.logo_url ?? \"\",\r\n          member_name: data.member_name ?? \"\",\r\n          title: data.title ?? \"\",\r\n          address_line: data.address_line ?? \"\",\r\n          city: data.city ?? \"\",\r\n          state: data.state ?? \"\",\r\n          pincode: data.pincode ?? \"\",\r\n          locality: data.locality ?? \"\",\r\n          phone: data.phone ?? \"\",\r\n          business_category: data.business_category ?? \"\",\r\n          instagram_url: data.instagram_url ?? \"\",\r\n          facebook_url: data.facebook_url ?? \"\",\r\n          whatsapp_number: data.whatsapp_number ?? \"\",\r\n          about_bio: data.about_bio ?? \"\",\r\n          status: data.status === \"online\" ? \"online\" : \"offline\",\r\n          business_slug: data.business_slug ?? \"\",\r\n\r\n          // Include metrics data\r\n          total_likes: data.total_likes ?? 0,\r\n          total_subscriptions: data.total_subscriptions ?? 0,\r\n          average_rating: data.average_rating ?? 0,\r\n\r\n          // Use actual data if available, otherwise use defaults\r\n          // theme_color field removed - using default styling\r\n          delivery_info: data.delivery_info ?? \"\",\r\n          business_hours: data.business_hours,\r\n\r\n          established_year: data.established_year ?? null,\r\n\r\n          // Add default values for fields required by BusinessCardData but not in our query\r\n          website_url: \"\",\r\n          linkedin_url: \"\",\r\n          twitter_url: \"\",\r\n          youtube_url: \"\",\r\n          call_number: \"\", // This field doesn't exist in the database\r\n        };\r\n      }) ?? [];\r\n\r\n    return {\r\n      data: {\r\n        businesses,\r\n        isAuthenticated,\r\n        totalCount,\r\n        hasMore,\r\n        nextPage,\r\n      },\r\n    };\r\n  } catch (e) {\r\n    console.error(\"Search Businesses Exception:\", e);\r\n    return { error: \"An unexpected error occurred during the search.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAAA;AAIA;;;;;;;AAGO,eAAe,+BAA+B,MAOpD;IASC,yEAAyE;IACzE,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,yBAAsB,AAAD,EAAE;QAC1C,GAAG,MAAM;QACT,UAAU;IACZ;IAEA,IAAI,OAAO,KAAK,EAAE;QAChB,OAAO;YAAE,OAAO,OAAO,KAAK;QAAC;IAC/B;IAEA,IAAI,CAAC,OAAO,IAAI,EAAE,YAAY;QAC5B,OAAO;YAAE,OAAO;QAAyB;IAC3C;IAEA,OAAO;QACL,MAAM;YACJ,YAAY,OAAO,IAAI,CAAC,UAAU;YAClC,YAAY,OAAO,IAAI,CAAC,UAAU;YAClC,SAAS,OAAO,IAAI,CAAC,OAAO;YAC5B,UAAU,OAAO,IAAI,CAAC,QAAQ;QAChC;IACF;AACF;AAGO,eAAe,wBAAwB,MAQ7C;IAWC,MAAM,EACJ,YAAY,EACZ,OAAO,EACP,QAAQ,EACR,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,cAAc,EACvB,WAAW,IAAI,EAChB,GAAG;IAEJ,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,MAAM,kBAAkB,CAAC,CAAC;IAE1B,IAAI;QACF,mDAAmD;QACnD,MAAM,EACJ,MAAM,cAAc,EACpB,KAAK,EACL,OAAO,eAAe,EACvB,GAAG,MAAM,CAAA,GAAA,4IAAA,CAAA,4BAAyB,AAAD,EAChC,cACA,SACA,UACA,MACA,OACA,QACA;QAGF,IAAI,iBAAiB;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,OAAO;YAAgB;QAClC;QAEA,MAAM,aAAa,SAAS;QAC5B,oCAAoC;QACpC,MAAM,UACJ,aAAa,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,gBAAgB,UAAU,CAAC;QAChE,MAAM,WAAW,UAAU,OAAO,IAAI;QAEtC,6DAA6D;QAC7D,MAAM,aACJ,gBAAgB,IAAI,CAAC;YACnB,wCAAwC;YACxC,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,eAAe,KAAK,aAAa,IAAI;gBACrC,eAAe;gBACf,0EAA0E;gBAC1E,YAAY,KAAK,UAAU,IAAI;gBAC/B,YAAY,KAAK,UAAU,IAAI;gBAC/B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,aAAa,KAAK,WAAW,IAAI;gBACjC,OAAO,KAAK,KAAK,IAAI;gBACrB,cAAc,KAAK,YAAY,IAAI;gBACnC,MAAM,KAAK,IAAI,IAAI;gBACnB,OAAO,KAAK,KAAK,IAAI;gBACrB,SAAS,KAAK,OAAO,IAAI;gBACzB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,OAAO,KAAK,KAAK,IAAI;gBACrB,mBAAmB,KAAK,iBAAiB,IAAI;gBAC7C,eAAe,KAAK,aAAa,IAAI;gBACrC,cAAc,KAAK,YAAY,IAAI;gBACnC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,WAAW,KAAK,SAAS,IAAI;gBAC7B,QAAQ,KAAK,MAAM,KAAK,WAAW,WAAW;gBAC9C,eAAe,KAAK,aAAa,IAAI;gBAErC,uBAAuB;gBACvB,aAAa,KAAK,WAAW,IAAI;gBACjC,qBAAqB,KAAK,mBAAmB,IAAI;gBACjD,gBAAgB,KAAK,cAAc,IAAI;gBAEvC,uDAAuD;gBACvD,oDAAoD;gBACpD,eAAe,KAAK,aAAa,IAAI;gBACrC,gBAAgB,KAAK,cAAc;gBAEnC,kBAAkB,KAAK,gBAAgB,IAAI;gBAE3C,kFAAkF;gBAClF,aAAa;gBACb,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,aAAa;YACf;QACF,MAAM,EAAE;QAEV,OAAO;YACL,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA;YACF;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,OAAO;QAAkD;IACpE;AACF;;;IAvKsB;IAyCA;;AAzCA,+OAAA;AAyCA,+OAAA", "debugId": null}}, {"offset": {"line": 1326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/types.ts"], "sourcesContent": ["import { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\n\r\n// Define the structure for products found nearby\r\nexport type NearbyProduct = ProductServiceData & {\r\n  business_slug: string | null;\r\n  slug?: string;\r\n};\r\n\r\n// Define the overall return structure for the action\r\nexport type DiscoverSearchResult = {\r\n  location?: { city: string; state: string } | null; // Optional for name search\r\n  businesses?: BusinessCardData[]; // Now full data, optional\r\n  products?: NearbyProduct[]; // Optional\r\n  isAuthenticated: boolean; // Add authentication status\r\n  totalCount: number; // Total count for pagination\r\n  hasMore: boolean; // Whether there are more items to load\r\n  nextPage: number | null; // Next page number or null if no more pages\r\n};\r\n\r\n// Helper function to get the column name for sorting\r\nexport function getSortingColumn(\r\n  sortBy: BusinessSortBy | string,\r\n  isProductView: boolean = false\r\n): string {\r\n  // For product view, we need to handle sorting differently\r\n  if (isProductView) {\r\n    switch (sortBy) {\r\n      case \"name_asc\":\r\n      case \"name_desc\":\r\n        return \"name\";\r\n      case \"price_asc\":\r\n      case \"price_desc\":\r\n        // We'll handle price sorting with a custom approach in the query\r\n        return \"price\";\r\n      case \"newest\":\r\n        // Handle 'newest' as a special case - sort by created_at descending\r\n        return \"created_at\";\r\n      case \"created_asc\":\r\n      case \"created_desc\":\r\n      default:\r\n        return \"created_at\";\r\n    }\r\n  } else {\r\n    // For business view\r\n    switch (sortBy) {\r\n      case \"name_asc\":\r\n      case \"name_desc\":\r\n        return \"name\";\r\n      case \"price_asc\":\r\n      case \"price_desc\":\r\n        return \"base_price\";\r\n      case \"likes_desc\":\r\n        return \"likes_count\";\r\n      case \"subscriptions_desc\":\r\n        return \"subscriptions_count\";\r\n      case \"rating_desc\":\r\n        return \"average_rating\";\r\n      case \"created_asc\":\r\n      case \"created_desc\":\r\n      default:\r\n        return \"created_at\";\r\n    }\r\n  }\r\n}\r\n\r\n// Helper function to determine sort direction\r\nexport function getSortingDirection(sortBy: BusinessSortBy | string): boolean {\r\n  switch (sortBy) {\r\n    case \"name_asc\":\r\n    case \"price_asc\":\r\n    case \"created_asc\":\r\n      return true; // ascending\r\n    case \"name_desc\":\r\n    case \"price_desc\":\r\n    case \"likes_desc\":\r\n    case \"subscriptions_desc\":\r\n    case \"rating_desc\":\r\n    case \"created_desc\":\r\n    case \"newest\": // 'newest' is equivalent to 'created_desc'\r\n    default:\r\n      return false; // descending\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAsBO,SAAS,iBACd,MAA+B,EAC/B,gBAAyB,KAAK;IAE9B,0DAA0D;IAC1D,IAAI,eAAe;QACjB,OAAQ;YACN,KAAK;YACL,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;YACL,KAAK;gBACH,iEAAiE;gBACjE,OAAO;YACT,KAAK;gBACH,oEAAoE;gBACpE,OAAO;YACT,KAAK;YACL,KAAK;YACL;gBACE,OAAO;QACX;IACF,OAAO;QACL,oBAAoB;QACpB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL;gBACE,OAAO;QACX;IACF;AACF;AAGO,SAAS,oBAAoB,MAA+B;IACjE,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,MAAM,YAAY;QAC3B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL;YACE,OAAO,OAAO,aAAa;IAC/B;AACF", "debugId": null}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/schemas/locationSchemas.ts"], "sourcesContent": ["import * as z from \"zod\";\r\n\r\n// Schema for pincode-based search\r\nexport const pincodeSchema = z.object({\r\n  pincode: z\r\n    .string()\r\n    .regex(/^\\d{6}$/, { message: \"Pincode must be exactly 6 digits.\" }),\r\n  locality: z.string().optional().nullable(), // Optional locality\r\n});\r\n\r\n// Schema for city-based search\r\nexport const citySchema = z.object({\r\n  city: z\r\n    .string()\r\n    .min(2, { message: \"City name must be at least 2 characters.\" }),\r\n  locality: z.string().optional().nullable(), // Optional locality\r\n});\r\n\r\n// Schema for business name search\r\nexport const businessNameSchema = z.object({\r\n  businessName: z.string().min(1, { message: \"Business name is required.\" }),\r\n});\r\n\r\n// Schema for combined search\r\nexport const combinedSearchSchema = z.object({\r\n  businessName: z.string().optional().nullable(),\r\n  pincode: z\r\n    .string()\r\n    .regex(/^\\d{6}$/, { message: \"Pincode must be exactly 6 digits.\" })\r\n    .optional()\r\n    .nullable(),\r\n  city: z.string().optional().nullable(),\r\n  locality: z.string().optional().nullable(),\r\n  category: z.string().optional().nullable(),\r\n});\r\n\r\n// Schema for pagination parameters\r\nexport const paginationSchema = z.object({\r\n  page: z.number().int().positive().default(1),\r\n  limit: z.number().int().positive().max(50).default(20), // Max 50 items per page\r\n});\r\n\r\n// Schema for sorting options\r\nexport const sortingSchema = z.object({\r\n  sortBy: z\r\n    .enum([\r\n      \"name_asc\",\r\n      \"name_desc\",\r\n      \"created_asc\",\r\n      \"created_desc\",\r\n      \"likes_asc\",\r\n      \"likes_desc\",\r\n      \"subscriptions_asc\",\r\n      \"subscriptions_desc\",\r\n      \"rating_asc\",\r\n      \"rating_desc\",\r\n    ])\r\n    .default(\"created_desc\"),\r\n});\r\n\r\n// Combined schema for pincode-based discovery search with pagination\r\nexport const discoverySearchSchema = pincodeSchema.extend({\r\n  viewType: z.enum([\"cards\", \"products\"]),\r\n  ...paginationSchema.shape,\r\n  ...sortingSchema.shape,\r\n});\r\n\r\n// Combined schema for business name search with pagination\r\nexport const businessNameSearchSchema = businessNameSchema.extend({\r\n  viewType: z.enum([\"cards\", \"products\"]),\r\n  ...paginationSchema.shape,\r\n  ...sortingSchema.shape,\r\n});\r\n\r\n// Combined schema for combined search with pagination\r\nexport const combinedSearchParamsSchema = combinedSearchSchema.extend({\r\n  viewType: z.enum([\"cards\", \"products\"]),\r\n  ...paginationSchema.shape,\r\n  ...sortingSchema.shape,\r\n});\r\n\r\nexport type LocationSearchFormData = z.infer<typeof pincodeSchema>;\r\nexport type CitySearchFormData = z.infer<typeof citySchema>;\r\nexport type BusinessNameSearchFormData = z.infer<typeof businessNameSchema>;\r\nexport type PaginationParams = z.infer<typeof paginationSchema>;\r\nexport type SortingParams = z.infer<typeof sortingSchema>;\r\nexport type DiscoverySearchParams = z.infer<typeof discoverySearchSchema>;\r\nexport type BusinessNameSearchParams = z.infer<typeof businessNameSearchSchema>;\r\nexport type CombinedSearchParams = z.infer<typeof combinedSearchParamsSchema>;\r\nexport type CombinedSearchFormData = z.infer<typeof combinedSearchSchema>;\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;AAGO,MAAM,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACpC,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,KAAK,CAAC,WAAW;QAAE,SAAS;IAAoC;IACnE,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACjC,MAAM,CAAA,GAAA,oIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C;IAChE,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;AAC1E;AAGO,MAAM,uBAAuB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IAC3C,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC5C,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,KAAK,CAAC,WAAW;QAAE,SAAS;IAAoC,GAChE,QAAQ,GACR,QAAQ;IACX,MAAM,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IACpC,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IACxC,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AAC1C;AAGO,MAAM,mBAAmB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACvC,MAAM,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,OAAO,CAAC;IAC1C,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,QAAQ,GAAG,GAAG,CAAC,IAAI,OAAO,CAAC;AACrD;AAGO,MAAM,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACpC,QAAQ,CAAA,GAAA,oIAAA,CAAA,OACD,AAAD,EAAE;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD,EACA,OAAO,CAAC;AACb;AAGO,MAAM,wBAAwB,cAAc,MAAM,CAAC;IACxD,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;KAAW;IACtC,GAAG,iBAAiB,KAAK;IACzB,GAAG,cAAc,KAAK;AACxB;AAGO,MAAM,2BAA2B,mBAAmB,MAAM,CAAC;IAChE,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;KAAW;IACtC,GAAG,iBAAiB,KAAK;IACzB,GAAG,cAAc,KAAK;AACxB;AAGO,MAAM,6BAA6B,qBAAqB,MAAM,CAAC;IACpE,UAAU,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAS;KAAW;IACtC,GAAG,iBAAiB,KAAK;IACzB,GAAG,cAAc,KAAK;AACxB", "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/lib/actions/location.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\nimport { SupabaseClient } from \"@supabase/supabase-js\";\nimport { Database } from \"@/types/supabase\";\n\n\n// --- Pincode Lookup Action ---\nexport async function getPincodeDetails(pincode: string): Promise<{\n  data?: {\n    city: string;\n    state: string;\n    localities: string[];\n  };\n  city?: string;\n  state?: string;\n  localities?: string[];\n  error?: string;\n}> {\n  if (!pincode || !/^\\d{6}$/.test(pincode)) {\n    return { error: \"Invalid Pincode format.\" };\n  }\n\n  const supabase = await createClient();\n  try {\n    // First get city and state from pincodes table\n    const { data: pincodeData, error: pincodeError } = await supabase\n      .from(\"pincodes\")\n      .select(\"OfficeName, DivisionName, StateName\")\n      .eq(\"Pincode\", pincode) // Updated column name to match database\n      .order(\"OfficeName\");\n\n    if (pincodeError) {\n      console.error(\"Pincode Fetch Error:\", pincodeError);\n      return { error: \"Database error fetching pincode details.\" };\n    }\n\n    if (!pincodeData || pincodeData.length === 0) {\n      return { error: \"Pincode not found.\" };\n    }\n\n    // State names are already in title case format in the database\n    const state = pincodeData[0].StateName;\n\n    // Use DivisionName as the city (already cleaned)\n    const city = pincodeData[0].DivisionName;\n\n    // Get unique localities from post office names\n    const localities = [\n      ...new Set(pincodeData.map((item: { OfficeName: string }) => item.OfficeName)),\n    ] as string[];\n\n    return {\n      data: { city, state, localities },\n      city,\n      state,\n      localities\n    };\n  } catch (e) {\n    console.error(\"Pincode Lookup Exception:\", e);\n    return { error: \"An unexpected error occurred during pincode lookup.\" };\n  }\n}\n// --- End Pincode Lookup ---\n\n// --- City Lookup Action ---\nexport async function getCityDetails(city: string): Promise<{\n  data?: {\n    pincodes: string[];\n    state: string;\n    localities: string[];\n  };\n  pincodes?: string[];\n  state?: string;\n  localities?: string[];\n  error?: string;\n}> {\n  if (!city || city.length < 2) {\n    return { error: \"City name must be at least 2 characters.\" };\n  }\n\n  const supabase = await createClient();\n  try {\n    // Get pincodes and state for the city - DivisionName is the city column\n    const { data: cityData, error: cityError } = await supabase\n      .from(\"pincodes\")\n      .select(\"Pincode, OfficeName, StateName, DivisionName\")\n      .ilike(\"DivisionName\", `%${city}%`)\n      .order(\"Pincode\");\n\n    if (cityError) {\n      console.error(\"City Fetch Error:\", cityError);\n      return { error: \"Database error fetching city details.\" };\n    }\n\n    if (!cityData || cityData.length === 0) {\n      return { error: \"City not found.\" };\n    }\n\n    // State names are already in title case format in the database\n    const state = cityData[0].StateName;\n\n    // Get unique pincodes\n    const pincodes = [...new Set(cityData.map((item: { Pincode: string }) => item.Pincode))] as string[];\n\n    // Get unique localities from post office names\n    const localities = [\n      ...new Set(cityData.map((item: { OfficeName: string }) => item.OfficeName)),\n    ] as string[];\n\n    return {\n      data: { pincodes, state, localities },\n      pincodes,\n      state,\n      localities\n    };\n  } catch (e) {\n    console.error(\"City Lookup Exception:\", e);\n    return { error: \"An unexpected error occurred during city lookup.\" };\n  }\n}\n// --- End City Lookup ---\n\n// --- City Autocomplete Action ---\n/**\n * Get city suggestions based on a search query\n *\n * This function uses the Supabase PostgreSQL function 'get_distinct_cities' to fetch unique city names.\n * The PostgreSQL function is defined as:\n *\n * ```sql\n * CREATE OR REPLACE FUNCTION get_distinct_cities(search_query TEXT, result_limit INTEGER)\n * RETURNS TABLE(city TEXT) AS $$\n * BEGIN\n *   RETURN QUERY\n *   SELECT DISTINCT \"DivisionName\" as city\n *   FROM pincodes\n *   WHERE \"DivisionName\" ILIKE search_query\n *   ORDER BY \"DivisionName\"\n *   LIMIT result_limit;\n * END;\n * $$ LANGUAGE plpgsql;\n * ```\n *\n * @param query The search query (minimum 2 characters)\n * @returns Array of up to 5 unique city suggestions\n */\nexport async function getCitySuggestions(query: string): Promise<{\n  data?: {\n    cities: string[];\n  };\n  cities?: string[];\n  error?: string;\n}> {\n  if (!query || query.length < 2) {\n    return { error: \"Query must be at least 2 characters.\" };\n  }\n\n  const supabase = (await createClient()) as SupabaseClient<Database>;\n  try {\n    // Use the PostgreSQL function to get distinct cities (up to 5)\n    const { data: cityData, error: cityError } = await supabase\n      .rpc('get_distinct_cities', {\n        search_query: `%${query}%`,\n        result_limit: 5\n      });\n\n    if (cityError) {\n      console.error(\"City Suggestions Error:\", cityError);\n\n      // Fallback to regular query if RPC fails\n      try {\n        // Use a regular query as fallback\n        const { data: fallbackData, error: fallbackError } = await supabase\n          .from(\"pincodes\")\n          .select(\"DivisionName\")\n          .ilike(\"DivisionName\", `%${query}%`)\n          .order(\"DivisionName\")\n          .limit(100);\n\n        if (fallbackError) {\n          throw fallbackError;\n        }\n\n        if (!fallbackData || fallbackData.length === 0) {\n          return { data: { cities: [] }, cities: [] };\n        }\n\n        // Get unique cities and format them\n        const cities = [...new Set(fallbackData.map((item: { DivisionName: string }) =>\n          item.DivisionName.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\n        ))] as string[];\n\n        const topCities = cities.slice(0, 5);\n\n        return {\n          data: { cities: topCities },\n          cities: topCities\n        };\n      } catch (fallbackErr) {\n        console.error(\"Fallback City Query Error:\", fallbackErr);\n        return { error: \"Database error fetching city suggestions.\" };\n      }\n    }\n\n    if (!cityData || cityData.length === 0) {\n      return { data: { cities: [] }, cities: [] };\n    }\n\n    // Format the city names to Title Case\n    const cities = cityData.map((item: { city: string }) =>\n      item.city.toLowerCase().replace(/\\b\\w/g, (char: string) => char.toUpperCase())\n    );\n\n    return {\n      data: { cities },\n      cities\n    };\n  } catch (e) {\n    console.error(\"City Suggestions Exception:\", e);\n    return { error: \"An unexpected error occurred while fetching city suggestions.\" };\n  }\n}\n// --- End City Autocomplete ---\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAMO,eAAe,kBAAkB,OAAe;IAWrD,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,CAAC,UAAU;QACxC,OAAO;YAAE,OAAO;QAA0B;IAC5C;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,+CAA+C;QAC/C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACtD,IAAI,CAAC,YACL,MAAM,CAAC,uCACP,EAAE,CAAC,WAAW,SAAS,wCAAwC;SAC/D,KAAK,CAAC;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE,OAAO;YAA2C;QAC7D;QAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;YAC5C,OAAO;gBAAE,OAAO;YAAqB;QACvC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,WAAW,CAAC,EAAE,CAAC,SAAS;QAEtC,iDAAiD;QACjD,MAAM,OAAO,WAAW,CAAC,EAAE,CAAC,YAAY;QAExC,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC,OAAiC,KAAK,UAAU;SAC7E;QAED,OAAO;YACL,MAAM;gBAAE;gBAAM;gBAAO;YAAW;YAChC;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;YAAE,OAAO;QAAsD;IACxE;AACF;AAIO,eAAe,eAAe,IAAY;IAW/C,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;QAC5B,OAAO;YAAE,OAAO;QAA2C;IAC7D;IAEA,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAClC,IAAI;QACF,wEAAwE;QACxE,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,YACL,MAAM,CAAC,gDACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EACjC,KAAK,CAAC;QAET,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;gBAAE,OAAO;YAAwC;QAC1D;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,OAAO;YAAkB;QACpC;QAEA,+DAA+D;QAC/D,MAAM,QAAQ,QAAQ,CAAC,EAAE,CAAC,SAAS;QAEnC,sBAAsB;QACtB,MAAM,WAAW;eAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,OAA8B,KAAK,OAAO;SAAG;QAExF,+CAA+C;QAC/C,MAAM,aAAa;eACd,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,OAAiC,KAAK,UAAU;SAC1E;QAED,OAAO;YACL,MAAM;gBAAE;gBAAU;gBAAO;YAAW;YACpC;YACA;YACA;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO;YAAE,OAAO;QAAmD;IACrE;AACF;AA2BO,eAAe,mBAAmB,KAAa;IAOpD,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;QAC9B,OAAO;YAAE,OAAO;QAAuC;IACzD;IAEA,MAAM,WAAY,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IACnC,IAAI;QACF,+DAA+D;QAC/D,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAChD,GAAG,CAAC,uBAAuB;YAC1B,cAAc,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,cAAc;QAChB;QAEF,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,yCAAyC;YACzC,IAAI;gBACF,kCAAkC;gBAClC,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,gBACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAClC,KAAK,CAAC,gBACN,KAAK,CAAC;gBAET,IAAI,eAAe;oBACjB,MAAM;gBACR;gBAEA,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;oBAC9C,OAAO;wBAAE,MAAM;4BAAE,QAAQ,EAAE;wBAAC;wBAAG,QAAQ,EAAE;oBAAC;gBAC5C;gBAEA,oCAAoC;gBACpC,MAAM,SAAS;uBAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC,OAC3C,KAAK,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;iBAClF;gBAEH,MAAM,YAAY,OAAO,KAAK,CAAC,GAAG;gBAElC,OAAO;oBACL,MAAM;wBAAE,QAAQ;oBAAU;oBAC1B,QAAQ;gBACV;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,OAAO;oBAAE,OAAO;gBAA4C;YAC9D;QACF;QAEA,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG;YACtC,OAAO;gBAAE,MAAM;oBAAE,QAAQ,EAAE;gBAAC;gBAAG,QAAQ,EAAE;YAAC;QAC5C;QAEA,sCAAsC;QACtC,MAAM,SAAS,SAAS,GAAG,CAAC,CAAC,OAC3B,KAAK,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,SAAS,CAAC,OAAiB,KAAK,WAAW;QAG7E,OAAO;YACL,MAAM;gBAAE;YAAO;YACf;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,OAAO;QAAgE;IAClF;AACF,EACA,gCAAgC;;;IAvNV;IA0DA;IAiFA;;AA3IA,+OAAA;AA0DA,+OAAA;AAiFA,+OAAA", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/locationActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\nimport { pincodeSchema, citySchema } from \"@/lib/schemas/locationSchemas\";\r\nimport { getPincodeDetails } from \"@/lib/actions/location\";\r\nimport {\r\n  BusinessSortBy,\r\n  getSecureBusinessProfilesForDiscover,\r\n  getSecureBusinessProfileIdsForDiscover,\r\n} from \"@/lib/actions/businessProfiles\";\r\nimport {\r\n  DiscoverSearchResult,\r\n  getSortingColumn,\r\n  getSortingDirection,\r\n} from \"./types\";\r\n\r\n// Action to find businesses or products based on location (pincode) and view type\r\nexport async function searchDiscoverData(params: {\r\n  pincode?: string;\r\n  city?: string;\r\n  locality?: string | null;\r\n  viewType: \"cards\" | \"products\";\r\n  page?: number;\r\n  limit?: number;\r\n  sortBy?: BusinessSortBy;\r\n  productType?: \"physical\" | \"service\" | null;\r\n  category?: string | null;\r\n}): Promise<{\r\n  data?: DiscoverSearchResult;\r\n  error?: string;\r\n}> {\r\n  const {\r\n    pincode,\r\n    city,\r\n    locality,\r\n    viewType,\r\n    page = 1,\r\n    limit = 20,\r\n    sortBy = \"created_desc\",\r\n    productType = null,\r\n    category = null,\r\n  } = params;\r\n\r\n  // Check if we have either pincode or city\r\n  if (!pincode && !city) {\r\n    return { error: \"Either pincode or city is required.\" };\r\n  }\r\n\r\n  // Initialize Supabase clients early\r\n  const supabase = await createClient();\r\n  \r\n\r\n  let locationCity: string;\r\n  let locationState: string;\r\n  let validPincodes: string[] = [];\r\n\r\n  // Handle pincode-based search\r\n  if (pincode) {\r\n    // 1. Validate Pincode\r\n    const validatedPincode = pincodeSchema.safeParse({ pincode });\r\n    if (!validatedPincode.success) {\r\n      return { error: \"Invalid Pincode format. Must be 6 digits.\" };\r\n    }\r\n    const validPincode = validatedPincode.data.pincode;\r\n\r\n    // 2. Get Location Details\r\n    const locationDetails = await getPincodeDetails(validPincode);\r\n    if (\r\n      locationDetails.error ||\r\n      !locationDetails.city ||\r\n      !locationDetails.state\r\n    ) {\r\n      return { error: locationDetails.error || \"Pincode not found.\" };\r\n    }\r\n\r\n    locationCity = locationDetails.city;\r\n    locationState = locationDetails.state;\r\n    validPincodes = [validPincode];\r\n  }\r\n  // Handle city-based search\r\n  else if (city) {\r\n    // 1. Validate City\r\n    const validatedCity = citySchema.safeParse({ city });\r\n    if (!validatedCity.success) {\r\n      return { error: \"Invalid city name. Must be at least 2 characters.\" };\r\n    }\r\n    const validCity = validatedCity.data.city;\r\n\r\n    // For city-based search, we'll directly filter by the city column\r\n    // No need to get pincodes or other location details\r\n    locationCity = validCity;\r\n\r\n    // Set empty pincodes array to indicate we're doing a direct city search\r\n    validPincodes = [];\r\n\r\n    // Try to get the state for display purposes only\r\n    try {\r\n      const { data } = await supabase\r\n        .from(\"pincodes\")\r\n        .select(\"StateName\")\r\n        .ilike(\"DivisionName\", `%${validCity}%`)\r\n        .limit(1);\r\n\r\n      if (data && data.length > 0) {\r\n        locationState = data[0].StateName;\r\n      } else {\r\n        locationState = \"\"; // Default empty state if not found\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error getting state for city:\", error);\r\n      locationState = \"\"; // Default empty state on error\r\n    }\r\n  } else {\r\n    return { error: \"Either pincode or city is required.\" };\r\n  }\r\n\r\n  // 3. Check Authentication\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n\r\n  try {\r\n    // Add a small delay to prevent infinite loops\r\n    await new Promise((resolve) => setTimeout(resolve, 100));\r\n\r\n    // 4. Build Base Query for Valid Businesses\r\n    // Reference code removed\r\n\r\n    // 5. Fetch Data Based on viewType\r\n    if (viewType === \"cards\") {\r\n      // Check if we're searching by city directly\r\n      if (city && validPincodes.length === 0) {\r\n        // Direct city-based search using Supabase\r\n        const offset = (page - 1) * limit;\r\n\r\n        // Define fields to select\r\n        const businessFields = `\r\n          id, business_name, logo_url, member_name, title,\r\n          address_line, city, state, pincode, locality, phone, business_category, instagram_url,\r\n          facebook_url, whatsapp_number, about_bio, status, business_slug,\r\n          delivery_info, total_likes, total_subscriptions, average_rating, business_hours,\r\n          created_at, updated_at, contact_email\r\n        `;\r\n\r\n        // Get count of businesses matching the city\r\n        let countQuery = supabase\r\n          .from(\"business_profiles\")\r\n          .select(\"id\", { count: \"exact\" })\r\n          .eq(\"city\", city) // Use exact matching for city\r\n          .eq(\"status\", \"online\");\r\n\r\n        // Add category filter if provided\r\n        if (category && category.trim()) {\r\n          countQuery = countQuery.eq(\"business_category\", category.trim());\r\n        }\r\n\r\n        const { count, error: countError } = await countQuery;\r\n\r\n        if (countError) {\r\n          console.error(\"City Business Count Error:\", countError);\r\n          return { error: \"Database error counting businesses by city.\" };\r\n        }\r\n\r\n        // Fetch businesses matching the city\r\n        let businessQuery = supabase\r\n          .from(\"business_profiles\")\r\n          .select(businessFields)\r\n          .eq(\"city\", city) // Use exact matching for city\r\n          .eq(\"status\", \"online\")\r\n          .range(offset, offset + limit - 1)\r\n          .order(getSortingColumn(sortBy), {\r\n            ascending: getSortingDirection(sortBy),\r\n          });\r\n\r\n        // Add category filter if provided\r\n        if (category && category.trim()) {\r\n          businessQuery = businessQuery.eq(\r\n            \"business_category\",\r\n            category.trim()\r\n          );\r\n        }\r\n\r\n        const { data, error } = await businessQuery;\r\n\r\n        if (error) {\r\n          console.error(\"City Business Query Error:\", error);\r\n          return { error: \"Database error fetching businesses by city.\" };\r\n        }\r\n\r\n        // Map the data to the expected format (no subscription data needed)\r\n        const businessesData = data;\r\n\r\n        return {\r\n          data: {\r\n            location: { city: locationCity, state: locationState },\r\n            businesses: businessesData.map((data: { id: string; business_name: string | null; created_at: string | null; updated_at: string | null; logo_url: string | null; member_name: string | null; [key: string]: unknown }) => ({\r\n              id: data.id,\r\n              business_name: data.business_name ?? \"\",\r\n              contact_email: \"\", // contact_email is not in BusinessProfilePublicData\r\n              created_at: data.created_at ?? undefined,\r\n              updated_at: data.updated_at ?? undefined,\r\n              logo_url: data.logo_url ?? \"\",\r\n              member_name: data.member_name ?? \"\",\r\n              title: (data.title as string) ?? \"\",\r\n              address_line: (data.address_line as string) ?? \"\",\r\n              city: (data.city as string) ?? \"\",\r\n              state: (data.state as string) ?? \"\",\r\n              pincode: (data.pincode as string) ?? \"\",\r\n              locality: (data.locality as string) ?? \"\",\r\n              phone: (data.phone as string) ?? \"\",\r\n              business_category: (data.business_category as string) ?? \"\",\r\n              instagram_url: (data.instagram_url as string) ?? \"\",\r\n              facebook_url: (data.facebook_url as string) ?? \"\",\r\n              whatsapp_number: (data.whatsapp_number as string) ?? \"\",\r\n              about_bio: (data.about_bio as string) ?? \"\",\r\n              status:\r\n                data.status === \"online\"\r\n                  ? \"online\"\r\n                  : (\"offline\" as \"online\" | \"offline\"),\r\n              business_slug: (data.business_slug as string) ?? \"\",\r\n              total_likes: (data.total_likes as number) ?? 0,\r\n              total_subscriptions: (data.total_subscriptions as number) ?? 0,\r\n              average_rating: (data.average_rating as number) ?? 0,\r\n              theme_color: (data.theme_color as string) ?? \"#D4AF37\",\r\n              delivery_info: (data.delivery_info as string) ?? \"\",\r\n              business_hours: data.business_hours,\r\n\r\n              established_year: null, // Not available in this query\r\n              website_url: \"\",\r\n              linkedin_url: \"\",\r\n              twitter_url: \"\",\r\n              youtube_url: \"\",\r\n              call_number: \"\",\r\n            })),\r\n            isAuthenticated: isAuthenticated,\r\n            totalCount: count || 0,\r\n            hasMore: (count || 0) > page * limit,\r\n            nextPage: (count || 0) > page * limit ? page + 1 : null,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Use the secure method to fetch business profiles by pincode\r\n      const {\r\n        data: businessesData,\r\n        count,\r\n        error: businessesError,\r\n      } = await getSecureBusinessProfilesForDiscover(\r\n        validPincodes,\r\n        locality,\r\n        page,\r\n        limit,\r\n        sortBy\r\n      );\r\n\r\n      if (businessesError) {\r\n        console.error(\"Search Discover (Cards) Error:\", businessesError);\r\n        return { error: businessesError };\r\n      }\r\n\r\n      const totalCount = count || 0;\r\n\r\n      // Calculate if there are more pages\r\n      const hasMore = totalCount > page * limit;\r\n      const nextPage = hasMore ? page + 1 : null;\r\n\r\n      // Map raw data to BusinessCardData, handling potential nulls\r\n      const businesses =\r\n        businessesData?.map((data) => {\r\n          // Use the actual data from the database\r\n          return {\r\n            id: data.id,\r\n            business_name: data.business_name ?? \"\",\r\n            contact_email: \"\", // contact_email is not in BusinessProfilePublicData\r\n            // Subscription fields removed - all users now have access to all features\r\n            created_at: data.created_at ?? undefined,\r\n            updated_at: data.updated_at ?? undefined,\r\n            logo_url: data.logo_url ?? \"\",\r\n            member_name: data.member_name ?? \"\",\r\n            title: data.title ?? \"\",\r\n            address_line: data.address_line ?? \"\",\r\n            city: data.city ?? \"\",\r\n            state: data.state ?? \"\",\r\n            pincode: data.pincode ?? \"\",\r\n            locality: data.locality ?? \"\",\r\n            phone: data.phone ?? \"\",\r\n            business_category: data.business_category ?? \"\",\r\n            instagram_url: data.instagram_url ?? \"\",\r\n            facebook_url: data.facebook_url ?? \"\",\r\n            whatsapp_number: data.whatsapp_number ?? \"\",\r\n            about_bio: data.about_bio ?? \"\",\r\n            status:\r\n              data.status === \"online\"\r\n                ? \"online\"\r\n                : (\"offline\" as \"online\" | \"offline\"),\r\n            business_slug: data.business_slug ?? \"\",\r\n\r\n            // Include metrics data\r\n            total_likes: data.total_likes ?? 0,\r\n            total_subscriptions: data.total_subscriptions ?? 0,\r\n            average_rating: data.average_rating ?? 0,\r\n\r\n            // Use actual data if available, otherwise use defaults\r\n            // theme_color field removed - using default styling\r\n            delivery_info: data.delivery_info ?? \"\",\r\n            business_hours: data.business_hours,\r\n\r\n            established_year: data.established_year ?? null,\r\n\r\n            // Add default values for fields required by BusinessCardData but not in our query\r\n            website_url: \"\",\r\n            linkedin_url: \"\",\r\n            twitter_url: \"\",\r\n            youtube_url: \"\",\r\n            call_number: \"\", // This field doesn't exist in the database\r\n          };\r\n        }) ?? [];\r\n\r\n      return {\r\n        data: {\r\n          location: { city: locationCity, state: locationState },\r\n          businesses: businesses,\r\n          isAuthenticated: isAuthenticated,\r\n          totalCount,\r\n          hasMore,\r\n          nextPage,\r\n        },\r\n      };\r\n    } else {\r\n      // viewType === 'products'\r\n      let validBusinessIds: string[] = [];\r\n\r\n      // Check if we're searching by city directly\r\n      if (city && validPincodes.length === 0) {\r\n        // Direct city-based search using Supabase\r\n        // Get business IDs matching the city\r\n        let businessIdsQuery = supabase\r\n          .from(\"business_profiles\")\r\n          .select(\"id\")\r\n          .eq(\"city\", city) // Use exact matching for city\r\n          .eq(\"status\", \"online\");\r\n\r\n        // Add category filter if provided\r\n        if (category && category.trim()) {\r\n          businessIdsQuery = businessIdsQuery.eq(\r\n            \"business_category\",\r\n            category.trim()\r\n          );\r\n        }\r\n\r\n        const { data, error } = await businessIdsQuery;\r\n\r\n        if (error) {\r\n          console.error(\"City Business IDs Error:\", error);\r\n          return { error: \"Database error fetching business IDs by city.\" };\r\n        }\r\n\r\n        validBusinessIds = data.map((item: { id: string }) => item.id);\r\n      } else {\r\n        // First, get IDs of valid businesses using the secure method\r\n        const { data: ids, error: validBusinessesError } =\r\n          await getSecureBusinessProfileIdsForDiscover(\r\n            validPincodes,\r\n            locality,\r\n            sortBy\r\n          );\r\n\r\n        if (validBusinessesError) {\r\n          console.error(\r\n            \"Search Discover (Product IDs) Error:\",\r\n            validBusinessesError\r\n          );\r\n          return { error: validBusinessesError };\r\n        }\r\n\r\n        validBusinessIds = ids || [];\r\n      }\r\n\r\n      // This check is now handled inside the else block above\r\n\r\n      if (!validBusinessIds || validBusinessIds.length === 0) {\r\n        // No valid businesses found, return empty results\r\n        return {\r\n          data: {\r\n            location: { city: locationCity, state: locationState },\r\n            products: [],\r\n            isAuthenticated: isAuthenticated,\r\n            totalCount: 0,\r\n            hasMore: false,\r\n            nextPage: null,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Get total count of products first\r\n      let countQuery = supabase\r\n        .from(\"products_services\")\r\n        .select(\"id\", { count: \"exact\" })\r\n        .in(\"business_id\", validBusinessIds || [])\r\n        .eq(\"is_available\", true);\r\n\r\n      // Add product type filter if provided\r\n      if (productType) {\r\n        countQuery = countQuery.eq(\"product_type\", productType);\r\n      }\r\n\r\n      const { count: totalProductCount, error: productCountError } =\r\n        await countQuery;\r\n\r\n      if (productCountError) {\r\n        console.error(\r\n          \"Search Discover (Product Count) Error:\",\r\n          productCountError\r\n        );\r\n        return { error: \"Database error counting products.\" };\r\n      }\r\n\r\n      // Calculate pagination\r\n      const from = (page - 1) * limit;\r\n      const to = from + limit - 1;\r\n      const totalCount = totalProductCount || 0;\r\n      const hasMore = totalCount > page * limit;\r\n      const nextPage = hasMore ? page + 1 : null;\r\n\r\n      // Fetch Products belonging to valid businesses with pagination\r\n\r\n      // Build the query for products\r\n      let productsQuery = supabase\r\n        .from(\"products_services\")\r\n        .select(\r\n          // Select required fields + business_slug\r\n          `\r\n          id, business_id, name, description, base_price, discounted_price, product_type,\r\n          is_available, image_url, created_at, updated_at, slug,\r\n          business_profiles!business_id(business_slug)\r\n        `\r\n        )\r\n        .in(\"business_id\", validBusinessIds || []) // Filter products by valid business IDs\r\n        .eq(\"is_available\", true);\r\n\r\n      // Add product type filter if provided\r\n      if (productType) {\r\n        productsQuery = productsQuery.eq(\"product_type\", productType);\r\n      }\r\n\r\n      // Add pagination\r\n      productsQuery = productsQuery.range(from, to);\r\n\r\n      // Apply sorting based on the sortBy parameter\r\n      const sortColumn = getSortingColumn(sortBy, true); // true indicates product view\r\n      const sortAscending = getSortingDirection(sortBy);\r\n\r\n      // Special handling for price sorting to use discounted_price when available, otherwise base_price\r\n      if (sortColumn === \"price\") {\r\n        if (sortAscending) {\r\n          productsQuery = productsQuery\r\n            .order(\"discounted_price\", { ascending: true, nullsFirst: false })\r\n            .order(\"base_price\", { ascending: true, nullsFirst: false });\r\n        } else {\r\n          productsQuery = productsQuery\r\n            .order(\"discounted_price\", { ascending: false, nullsFirst: false })\r\n            .order(\"base_price\", { ascending: false, nullsFirst: false });\r\n        }\r\n      } else {\r\n        productsQuery = productsQuery.order(sortColumn, {\r\n          ascending: sortAscending,\r\n        });\r\n      }\r\n\r\n      const { data: productsData, error: productsError } = await productsQuery;\r\n\r\n      if (productsError) {\r\n        console.error(\"Search Discover (Products) Error:\", productsError);\r\n        return { error: \"Database error fetching nearby products.\" };\r\n      }\r\n\r\n      // Define a type representing the actual data structure returned by the query\r\n      type ProductQueryResult = {\r\n        id: string;\r\n        business_id: string | null;\r\n        name: string | null;\r\n        description: string | null;\r\n        base_price: number | null;\r\n        discounted_price: number | null;\r\n        product_type: string | null;\r\n        is_available: boolean | null;\r\n        image_url: string | null;\r\n        created_at: string | null;\r\n        updated_at: string | null;\r\n        slug: string | null;\r\n        business_profiles:\r\n          | { business_slug: string | null }[]\r\n          | { business_slug: string | null }\r\n          | null;\r\n      };\r\n\r\n      // Process products to match the full ProductServiceData structure + business_slug\r\n      const products =\r\n        productsData?.map((p: ProductQueryResult) => {\r\n          // Extract business_slug from the joined business_profiles\r\n          let business_slug = null;\r\n\r\n          if (p.business_profiles) {\r\n            // Check if it's an array or an object\r\n            if (\r\n              Array.isArray(p.business_profiles) &&\r\n              p.business_profiles.length > 0\r\n            ) {\r\n              business_slug = p.business_profiles[0].business_slug;\r\n            } else if (\r\n              typeof p.business_profiles === \"object\" &&\r\n              p.business_profiles !== null\r\n            ) {\r\n              // Cast to a more specific type to handle different response formats\r\n              business_slug = (\r\n                p.business_profiles as { business_slug: string | null }\r\n              ).business_slug;\r\n            }\r\n          }\r\n\r\n          // Ensure we have a valid business_slug\r\n\r\n          const product = {\r\n            id: p.id,\r\n            business_id: p.business_id ?? undefined,\r\n            name: p.name ?? \"\",\r\n            description: p.description ?? \"\",\r\n            base_price: p.base_price ?? 0,\r\n            discounted_price: p.discounted_price ?? null,\r\n            product_type: (p.product_type as \"physical\" | \"service\") ?? \"physical\",\r\n            is_available: p.is_available ?? true,\r\n            image_url: p.image_url,\r\n            created_at: p.created_at || undefined,\r\n            updated_at: p.updated_at || undefined,\r\n            business_slug: business_slug,\r\n            featured_image_index: 0, // Default value for NearbyProduct\r\n            images: [], // Default empty array for images\r\n            slug: p.slug || undefined, // Use the fetched slug or undefined if not available\r\n            // Add default/empty values for fields not fetched but required by ProductServiceData\r\n          };\r\n          return product;\r\n        }) ?? [];\r\n\r\n      return {\r\n        data: {\r\n          location: { city: locationCity, state: locationState },\r\n          products: products,\r\n          isAuthenticated: isAuthenticated,\r\n          totalCount,\r\n          hasMore,\r\n          nextPage,\r\n        },\r\n      };\r\n    }\r\n  } catch (e) {\r\n    console.error(\"Search Discover Exception:\", e);\r\n    return { error: \"An unexpected error occurred during the search.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AAAA;AAKA;;;;;;;;;AAOO,eAAe,mBAAmB,MAUxC;IAIC,MAAM,EACJ,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,SAAS,cAAc,EACvB,cAAc,IAAI,EAClB,WAAW,IAAI,EAChB,GAAG;IAEJ,0CAA0C;IAC1C,IAAI,CAAC,WAAW,CAAC,MAAM;QACrB,OAAO;YAAE,OAAO;QAAsC;IACxD;IAEA,oCAAoC;IACpC,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAGlC,IAAI;IACJ,IAAI;IACJ,IAAI,gBAA0B,EAAE;IAEhC,8BAA8B;IAC9B,IAAI,SAAS;QACX,sBAAsB;QACtB,MAAM,mBAAmB,iIAAA,CAAA,gBAAa,CAAC,SAAS,CAAC;YAAE;QAAQ;QAC3D,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B,OAAO;gBAAE,OAAO;YAA4C;QAC9D;QACA,MAAM,eAAe,iBAAiB,IAAI,CAAC,OAAO;QAElD,0BAA0B;QAC1B,MAAM,kBAAkB,MAAM,CAAA,GAAA,0HAAA,CAAA,oBAAiB,AAAD,EAAE;QAChD,IACE,gBAAgB,KAAK,IACrB,CAAC,gBAAgB,IAAI,IACrB,CAAC,gBAAgB,KAAK,EACtB;YACA,OAAO;gBAAE,OAAO,gBAAgB,KAAK,IAAI;YAAqB;QAChE;QAEA,eAAe,gBAAgB,IAAI;QACnC,gBAAgB,gBAAgB,KAAK;QACrC,gBAAgB;YAAC;SAAa;IAChC,OAEK,IAAI,MAAM;QACb,mBAAmB;QACnB,MAAM,gBAAgB,iIAAA,CAAA,aAAU,CAAC,SAAS,CAAC;YAAE;QAAK;QAClD,IAAI,CAAC,cAAc,OAAO,EAAE;YAC1B,OAAO;gBAAE,OAAO;YAAoD;QACtE;QACA,MAAM,YAAY,cAAc,IAAI,CAAC,IAAI;QAEzC,kEAAkE;QAClE,oDAAoD;QACpD,eAAe;QAEf,wEAAwE;QACxE,gBAAgB,EAAE;QAElB,iDAAiD;QACjD,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SACpB,IAAI,CAAC,YACL,MAAM,CAAC,aACP,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,EACtC,KAAK,CAAC;YAET,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;gBAC3B,gBAAgB,IAAI,CAAC,EAAE,CAAC,SAAS;YACnC,OAAO;gBACL,gBAAgB,IAAI,mCAAmC;YACzD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,gBAAgB,IAAI,+BAA+B;QACrD;IACF,OAAO;QACL,OAAO;YAAE,OAAO;QAAsC;IACxD;IAEA,0BAA0B;IAC1B,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,MAAM,kBAAkB,CAAC,CAAC;IAE1B,IAAI;QACF,8CAA8C;QAC9C,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;QAEnD,2CAA2C;QAC3C,yBAAyB;QAEzB,kCAAkC;QAClC,IAAI,aAAa,SAAS;YACxB,4CAA4C;YAC5C,IAAI,QAAQ,cAAc,MAAM,KAAK,GAAG;gBACtC,0CAA0C;gBAC1C,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;gBAE5B,0BAA0B;gBAC1B,MAAM,iBAAiB,CAAC;;;;;;QAMxB,CAAC;gBAED,4CAA4C;gBAC5C,IAAI,aAAa,SACd,IAAI,CAAC,qBACL,MAAM,CAAC,MAAM;oBAAE,OAAO;gBAAQ,GAC9B,EAAE,CAAC,QAAQ,MAAM,8BAA8B;iBAC/C,EAAE,CAAC,UAAU;gBAEhB,kCAAkC;gBAClC,IAAI,YAAY,SAAS,IAAI,IAAI;oBAC/B,aAAa,WAAW,EAAE,CAAC,qBAAqB,SAAS,IAAI;gBAC/D;gBAEA,MAAM,EAAE,KAAK,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM;gBAE3C,IAAI,YAAY;oBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;wBAAE,OAAO;oBAA8C;gBAChE;gBAEA,qCAAqC;gBACrC,IAAI,gBAAgB,SACjB,IAAI,CAAC,qBACL,MAAM,CAAC,gBACP,EAAE,CAAC,QAAQ,MAAM,8BAA8B;iBAC/C,EAAE,CAAC,UAAU,UACb,KAAK,CAAC,QAAQ,SAAS,QAAQ,GAC/B,KAAK,CAAC,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;oBAC/B,WAAW,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE;gBACjC;gBAEF,kCAAkC;gBAClC,IAAI,YAAY,SAAS,IAAI,IAAI;oBAC/B,gBAAgB,cAAc,EAAE,CAC9B,qBACA,SAAS,IAAI;gBAEjB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;gBAE9B,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;wBAAE,OAAO;oBAA8C;gBAChE;gBAEA,oEAAoE;gBACpE,MAAM,iBAAiB;gBAEvB,OAAO;oBACL,MAAM;wBACJ,UAAU;4BAAE,MAAM;4BAAc,OAAO;wBAAc;wBACrD,YAAY,eAAe,GAAG,CAAC,CAAC,OAA0L,CAAC;gCACzN,IAAI,KAAK,EAAE;gCACX,eAAe,KAAK,aAAa,IAAI;gCACrC,eAAe;gCACf,YAAY,KAAK,UAAU,IAAI;gCAC/B,YAAY,KAAK,UAAU,IAAI;gCAC/B,UAAU,KAAK,QAAQ,IAAI;gCAC3B,aAAa,KAAK,WAAW,IAAI;gCACjC,OAAO,AAAC,KAAK,KAAK,IAAe;gCACjC,cAAc,AAAC,KAAK,YAAY,IAAe;gCAC/C,MAAM,AAAC,KAAK,IAAI,IAAe;gCAC/B,OAAO,AAAC,KAAK,KAAK,IAAe;gCACjC,SAAS,AAAC,KAAK,OAAO,IAAe;gCACrC,UAAU,AAAC,KAAK,QAAQ,IAAe;gCACvC,OAAO,AAAC,KAAK,KAAK,IAAe;gCACjC,mBAAmB,AAAC,KAAK,iBAAiB,IAAe;gCACzD,eAAe,AAAC,KAAK,aAAa,IAAe;gCACjD,cAAc,AAAC,KAAK,YAAY,IAAe;gCAC/C,iBAAiB,AAAC,KAAK,eAAe,IAAe;gCACrD,WAAW,AAAC,KAAK,SAAS,IAAe;gCACzC,QACE,KAAK,MAAM,KAAK,WACZ,WACC;gCACP,eAAe,AAAC,KAAK,aAAa,IAAe;gCACjD,aAAa,AAAC,KAAK,WAAW,IAAe;gCAC7C,qBAAqB,AAAC,KAAK,mBAAmB,IAAe;gCAC7D,gBAAgB,AAAC,KAAK,cAAc,IAAe;gCACnD,aAAa,AAAC,KAAK,WAAW,IAAe;gCAC7C,eAAe,AAAC,KAAK,aAAa,IAAe;gCACjD,gBAAgB,KAAK,cAAc;gCAEnC,kBAAkB;gCAClB,aAAa;gCACb,cAAc;gCACd,aAAa;gCACb,aAAa;gCACb,aAAa;4BACf,CAAC;wBACD,iBAAiB;wBACjB,YAAY,SAAS;wBACrB,SAAS,CAAC,SAAS,CAAC,IAAI,OAAO;wBAC/B,UAAU,CAAC,SAAS,CAAC,IAAI,OAAO,QAAQ,OAAO,IAAI;oBACrD;gBACF;YACF;YAEA,8DAA8D;YAC9D,MAAM,EACJ,MAAM,cAAc,EACpB,KAAK,EACL,OAAO,eAAe,EACvB,GAAG,MAAM,CAAA,GAAA,+IAAA,CAAA,uCAAoC,AAAD,EAC3C,eACA,UACA,MACA,OACA;YAGF,IAAI,iBAAiB;gBACnB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO;oBAAE,OAAO;gBAAgB;YAClC;YAEA,MAAM,aAAa,SAAS;YAE5B,oCAAoC;YACpC,MAAM,UAAU,aAAa,OAAO;YACpC,MAAM,WAAW,UAAU,OAAO,IAAI;YAEtC,6DAA6D;YAC7D,MAAM,aACJ,gBAAgB,IAAI,CAAC;gBACnB,wCAAwC;gBACxC,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,eAAe,KAAK,aAAa,IAAI;oBACrC,eAAe;oBACf,0EAA0E;oBAC1E,YAAY,KAAK,UAAU,IAAI;oBAC/B,YAAY,KAAK,UAAU,IAAI;oBAC/B,UAAU,KAAK,QAAQ,IAAI;oBAC3B,aAAa,KAAK,WAAW,IAAI;oBACjC,OAAO,KAAK,KAAK,IAAI;oBACrB,cAAc,KAAK,YAAY,IAAI;oBACnC,MAAM,KAAK,IAAI,IAAI;oBACnB,OAAO,KAAK,KAAK,IAAI;oBACrB,SAAS,KAAK,OAAO,IAAI;oBACzB,UAAU,KAAK,QAAQ,IAAI;oBAC3B,OAAO,KAAK,KAAK,IAAI;oBACrB,mBAAmB,KAAK,iBAAiB,IAAI;oBAC7C,eAAe,KAAK,aAAa,IAAI;oBACrC,cAAc,KAAK,YAAY,IAAI;oBACnC,iBAAiB,KAAK,eAAe,IAAI;oBACzC,WAAW,KAAK,SAAS,IAAI;oBAC7B,QACE,KAAK,MAAM,KAAK,WACZ,WACC;oBACP,eAAe,KAAK,aAAa,IAAI;oBAErC,uBAAuB;oBACvB,aAAa,KAAK,WAAW,IAAI;oBACjC,qBAAqB,KAAK,mBAAmB,IAAI;oBACjD,gBAAgB,KAAK,cAAc,IAAI;oBAEvC,uDAAuD;oBACvD,oDAAoD;oBACpD,eAAe,KAAK,aAAa,IAAI;oBACrC,gBAAgB,KAAK,cAAc;oBAEnC,kBAAkB,KAAK,gBAAgB,IAAI;oBAE3C,kFAAkF;oBAClF,aAAa;oBACb,cAAc;oBACd,aAAa;oBACb,aAAa;oBACb,aAAa;gBACf;YACF,MAAM,EAAE;YAEV,OAAO;gBACL,MAAM;oBACJ,UAAU;wBAAE,MAAM;wBAAc,OAAO;oBAAc;oBACrD,YAAY;oBACZ,iBAAiB;oBACjB;oBACA;oBACA;gBACF;YACF;QACF,OAAO;YACL,0BAA0B;YAC1B,IAAI,mBAA6B,EAAE;YAEnC,4CAA4C;YAC5C,IAAI,QAAQ,cAAc,MAAM,KAAK,GAAG;gBACtC,0CAA0C;gBAC1C,qCAAqC;gBACrC,IAAI,mBAAmB,SACpB,IAAI,CAAC,qBACL,MAAM,CAAC,MACP,EAAE,CAAC,QAAQ,MAAM,8BAA8B;iBAC/C,EAAE,CAAC,UAAU;gBAEhB,kCAAkC;gBAClC,IAAI,YAAY,SAAS,IAAI,IAAI;oBAC/B,mBAAmB,iBAAiB,EAAE,CACpC,qBACA,SAAS,IAAI;gBAEjB;gBAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;gBAE9B,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,OAAO;wBAAE,OAAO;oBAAgD;gBAClE;gBAEA,mBAAmB,KAAK,GAAG,CAAC,CAAC,OAAyB,KAAK,EAAE;YAC/D,OAAO;gBACL,6DAA6D;gBAC7D,MAAM,EAAE,MAAM,GAAG,EAAE,OAAO,oBAAoB,EAAE,GAC9C,MAAM,CAAA,GAAA,+IAAA,CAAA,yCAAsC,AAAD,EACzC,eACA,UACA;gBAGJ,IAAI,sBAAsB;oBACxB,QAAQ,KAAK,CACX,wCACA;oBAEF,OAAO;wBAAE,OAAO;oBAAqB;gBACvC;gBAEA,mBAAmB,OAAO,EAAE;YAC9B;YAEA,wDAAwD;YAExD,IAAI,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,GAAG;gBACtD,kDAAkD;gBAClD,OAAO;oBACL,MAAM;wBACJ,UAAU;4BAAE,MAAM;4BAAc,OAAO;wBAAc;wBACrD,UAAU,EAAE;wBACZ,iBAAiB;wBACjB,YAAY;wBACZ,SAAS;wBACT,UAAU;oBACZ;gBACF;YACF;YAEA,oCAAoC;YACpC,IAAI,aAAa,SACd,IAAI,CAAC,qBACL,MAAM,CAAC,MAAM;gBAAE,OAAO;YAAQ,GAC9B,EAAE,CAAC,eAAe,oBAAoB,EAAE,EACxC,EAAE,CAAC,gBAAgB;YAEtB,sCAAsC;YACtC,IAAI,aAAa;gBACf,aAAa,WAAW,EAAE,CAAC,gBAAgB;YAC7C;YAEA,MAAM,EAAE,OAAO,iBAAiB,EAAE,OAAO,iBAAiB,EAAE,GAC1D,MAAM;YAER,IAAI,mBAAmB;gBACrB,QAAQ,KAAK,CACX,0CACA;gBAEF,OAAO;oBAAE,OAAO;gBAAoC;YACtD;YAEA,uBAAuB;YACvB,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;YAC1B,MAAM,KAAK,OAAO,QAAQ;YAC1B,MAAM,aAAa,qBAAqB;YACxC,MAAM,UAAU,aAAa,OAAO;YACpC,MAAM,WAAW,UAAU,OAAO,IAAI;YAEtC,+DAA+D;YAE/D,+BAA+B;YAC/B,IAAI,gBAAgB,SACjB,IAAI,CAAC,qBACL,MAAM,CACL,yCAAyC;YACzC,CAAC;;;;QAIH,CAAC,EAEA,EAAE,CAAC,eAAe,oBAAoB,EAAE,EAAE,wCAAwC;aAClF,EAAE,CAAC,gBAAgB;YAEtB,sCAAsC;YACtC,IAAI,aAAa;gBACf,gBAAgB,cAAc,EAAE,CAAC,gBAAgB;YACnD;YAEA,iBAAiB;YACjB,gBAAgB,cAAc,KAAK,CAAC,MAAM;YAE1C,8CAA8C;YAC9C,MAAM,aAAa,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,OAAO,8BAA8B;YACjF,MAAM,gBAAgB,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE;YAE1C,kGAAkG;YAClG,IAAI,eAAe,SAAS;gBAC1B,IAAI,eAAe;oBACjB,gBAAgB,cACb,KAAK,CAAC,oBAAoB;wBAAE,WAAW;wBAAM,YAAY;oBAAM,GAC/D,KAAK,CAAC,cAAc;wBAAE,WAAW;wBAAM,YAAY;oBAAM;gBAC9D,OAAO;oBACL,gBAAgB,cACb,KAAK,CAAC,oBAAoB;wBAAE,WAAW;wBAAO,YAAY;oBAAM,GAChE,KAAK,CAAC,cAAc;wBAAE,WAAW;wBAAO,YAAY;oBAAM;gBAC/D;YACF,OAAO;gBACL,gBAAgB,cAAc,KAAK,CAAC,YAAY;oBAC9C,WAAW;gBACb;YACF;YAEA,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM;YAE3D,IAAI,eAAe;gBACjB,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,OAAO;oBAAE,OAAO;gBAA2C;YAC7D;YAsBA,kFAAkF;YAClF,MAAM,WACJ,cAAc,IAAI,CAAC;gBACjB,0DAA0D;gBAC1D,IAAI,gBAAgB;gBAEpB,IAAI,EAAE,iBAAiB,EAAE;oBACvB,sCAAsC;oBACtC,IACE,MAAM,OAAO,CAAC,EAAE,iBAAiB,KACjC,EAAE,iBAAiB,CAAC,MAAM,GAAG,GAC7B;wBACA,gBAAgB,EAAE,iBAAiB,CAAC,EAAE,CAAC,aAAa;oBACtD,OAAO,IACL,OAAO,EAAE,iBAAiB,KAAK,YAC/B,EAAE,iBAAiB,KAAK,MACxB;wBACA,oEAAoE;wBACpE,gBAAgB,AACd,EAAE,iBAAiB,CACnB,aAAa;oBACjB;gBACF;gBAEA,uCAAuC;gBAEvC,MAAM,UAAU;oBACd,IAAI,EAAE,EAAE;oBACR,aAAa,EAAE,WAAW,IAAI;oBAC9B,MAAM,EAAE,IAAI,IAAI;oBAChB,aAAa,EAAE,WAAW,IAAI;oBAC9B,YAAY,EAAE,UAAU,IAAI;oBAC5B,kBAAkB,EAAE,gBAAgB,IAAI;oBACxC,cAAc,AAAC,EAAE,YAAY,IAA+B;oBAC5D,cAAc,EAAE,YAAY,IAAI;oBAChC,WAAW,EAAE,SAAS;oBACtB,YAAY,EAAE,UAAU,IAAI;oBAC5B,YAAY,EAAE,UAAU,IAAI;oBAC5B,eAAe;oBACf,sBAAsB;oBACtB,QAAQ,EAAE;oBACV,MAAM,EAAE,IAAI,IAAI;gBAElB;gBACA,OAAO;YACT,MAAM,EAAE;YAEV,OAAO;gBACL,MAAM;oBACJ,UAAU;wBAAE,MAAM;wBAAc,OAAO;oBAAc;oBACrD,UAAU;oBACV,iBAAiB;oBACjB;oBACA;oBACA;gBACF;YACF;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;YAAE,OAAO;QAAkD;IACpE;AACF;;;IA9hBsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 2128, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/actions/combinedActions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { BusinessSortBy } from \"@/lib/actions/businessProfiles\";\r\nimport { createClient } from \"@/utils/supabase/server\";\r\n\r\nimport { DiscoverSearchResult } from \"./types\";\r\nimport { fetchBusinessesBySearch } from \"./businessActions\";\r\nimport { fetchAllProducts, fetchProductsByBusinessIds } from \"./productActions\";\r\nimport { searchDiscoverData } from \"./locationActions\";\r\n\r\n// Combined search function that handles both business and product searches\r\nexport async function searchDiscoverCombined(params: {\r\n  businessName?: string | null;\r\n  productName?: string | null;\r\n  pincode?: string | null;\r\n  city?: string | null;\r\n  locality?: string | null;\r\n  category?: string | null;\r\n  viewType: \"cards\" | \"products\";\r\n  page?: number;\r\n  limit?: number;\r\n  businessSort?: BusinessSortBy | string;\r\n  productSort?: BusinessSortBy | string;\r\n  productType?: \"physical\" | \"service\" | null;\r\n}): Promise<{\r\n  data?: DiscoverSearchResult;\r\n  error?: string;\r\n}> {\r\n  const {\r\n    businessName,\r\n    productName,\r\n    pincode,\r\n    city,\r\n    locality,\r\n    category,\r\n    viewType,\r\n    page = 1,\r\n    limit = viewType === \"products\" ? 20 : 5,\r\n    businessSort = \"created_desc\",\r\n    productSort = \"created_desc\",\r\n    productType = null,\r\n  } = params;\r\n\r\n  const supabase = await createClient();\r\n\r\n  // Check Authentication\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  const isAuthenticated = !!user;\r\n\r\n  try {\r\n    // Case 1: Search by product name (only for products view)\r\n    if (\r\n      viewType === \"products\" &&\r\n      productName &&\r\n      productName.trim().length > 0\r\n    ) {\r\n      // Fetch products by name\r\n      const productsResult = await fetchAllProducts({\r\n        page,\r\n        limit,\r\n        sortBy: productSort as BusinessSortBy,\r\n        productType,\r\n        pincode,\r\n        locality,\r\n        productName,\r\n        category,\r\n      });\r\n\r\n      if (productsResult.error) {\r\n        return { error: productsResult.error };\r\n      }\r\n\r\n      return {\r\n        data: {\r\n          products: productsResult.data?.products || [],\r\n          isAuthenticated,\r\n          totalCount: productsResult.data?.totalCount || 0,\r\n          hasMore: productsResult.data?.hasMore || false,\r\n          nextPage: productsResult.data?.nextPage || null,\r\n        },\r\n      };\r\n    }\r\n    // Case 2: Search by business name\r\n    else if (businessName && businessName.trim().length > 0) {\r\n      // First, search for businesses by name\r\n      const businessResult = await fetchBusinessesBySearch({\r\n        businessName,\r\n        pincode,\r\n        locality,\r\n        page,\r\n        limit,\r\n        sortBy: businessSort as BusinessSortBy,\r\n        category,\r\n      });\r\n\r\n      if (businessResult.error) {\r\n        return { error: businessResult.error };\r\n      }\r\n\r\n      if (viewType === \"cards\") {\r\n        // Return businesses directly\r\n        return {\r\n          data: {\r\n            businesses: businessResult.data?.businesses || [],\r\n            isAuthenticated,\r\n            totalCount: businessResult.data?.totalCount || 0,\r\n            hasMore: businessResult.data?.hasMore || false,\r\n            nextPage: businessResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      } else {\r\n        // viewType === \"products\"\r\n        // Get business IDs from the search results\r\n        const businessIds = businessResult.data?.businesses.map(\r\n          (business) => business.id\r\n        );\r\n\r\n        if (!businessIds || businessIds.length === 0) {\r\n          return {\r\n            data: {\r\n              products: [],\r\n              isAuthenticated,\r\n              totalCount: 0,\r\n              hasMore: false,\r\n              nextPage: null,\r\n            },\r\n          };\r\n        }\r\n\r\n        // Fetch products for these businesses\r\n        const productsResult = await fetchProductsByBusinessIds({\r\n          businessIds: businessIds as string[],\r\n          page,\r\n          limit,\r\n          sortBy: productSort as BusinessSortBy,\r\n          productType,\r\n        });\r\n\r\n        if (productsResult.error) {\r\n          return { error: productsResult.error };\r\n        }\r\n\r\n        return {\r\n          data: {\r\n            products: productsResult.data?.products || [],\r\n            isAuthenticated,\r\n            totalCount: productsResult.data?.totalCount || 0,\r\n            hasMore: productsResult.data?.hasMore || false,\r\n            nextPage: productsResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      }\r\n    }\r\n    // Case 3: Search by location (pincode or city)\r\n    else if (pincode || city) {\r\n      return await searchDiscoverData({\r\n        pincode: pincode || undefined,\r\n        city: city || undefined,\r\n        locality,\r\n        viewType,\r\n        page,\r\n        limit,\r\n        sortBy: (viewType === \"products\"\r\n          ? productSort\r\n          : businessSort) as BusinessSortBy,\r\n        productType: viewType === \"products\" ? productType : null,\r\n        category,\r\n      });\r\n    }\r\n    // Case 4: No search criteria - show all businesses or products\r\n    else {\r\n      if (viewType === \"cards\") {\r\n        // Fetch all businesses\r\n        const businessResult = await fetchBusinessesBySearch({\r\n          page,\r\n          limit,\r\n          sortBy: businessSort as BusinessSortBy,\r\n          category,\r\n        });\r\n\r\n        if (businessResult.error) {\r\n          return { error: businessResult.error };\r\n        }\r\n\r\n        return {\r\n          data: {\r\n            businesses: businessResult.data?.businesses || [],\r\n            isAuthenticated,\r\n            totalCount: businessResult.data?.totalCount || 0,\r\n            hasMore: businessResult.data?.hasMore || false,\r\n            nextPage: businessResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      } else {\r\n        // viewType === \"products\"\r\n        // Fetch all products\r\n\r\n        const productsResult = await fetchAllProducts({\r\n          page,\r\n          limit,\r\n          sortBy: productSort as BusinessSortBy,\r\n          productType,\r\n          pincode: pincode || undefined,\r\n          locality,\r\n          category,\r\n        });\r\n\r\n        if (productsResult.error) {\r\n          return { error: productsResult.error };\r\n        }\r\n\r\n        return {\r\n          data: {\r\n            products: productsResult.data?.products || [],\r\n            isAuthenticated,\r\n            totalCount: productsResult.data?.totalCount || 0,\r\n            hasMore: productsResult.data?.hasMore || false,\r\n            nextPage: productsResult.data?.nextPage || null,\r\n          },\r\n        };\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.error(\"Search Discover Combined Exception:\", e);\r\n    return { error: \"An unexpected error occurred during the search.\" };\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAGA;AAGA;AACA;AACA;;;;;;;;AAGO,eAAe,uBAAuB,MAa5C;IAIC,MAAM,EACJ,YAAY,EACZ,WAAW,EACX,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,QAAQ,aAAa,aAAa,KAAK,CAAC,EACxC,eAAe,cAAc,EAC7B,cAAc,cAAc,EAC5B,cAAc,IAAI,EACnB,GAAG;IAEJ,MAAM,WAAW,MAAM,CAAA,GAAA,2HAAA,CAAA,eAAY,AAAD;IAElC,uBAAuB;IACvB,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,MAAM,kBAAkB,CAAC,CAAC;IAE1B,IAAI;QACF,0DAA0D;QAC1D,IACE,aAAa,cACb,eACA,YAAY,IAAI,GAAG,MAAM,GAAG,GAC5B;YACA,yBAAyB;YACzB,MAAM,iBAAiB,MAAM,CAAA,GAAA,wJAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC5C;gBACA;gBACA,QAAQ;gBACR;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,IAAI,eAAe,KAAK,EAAE;gBACxB,OAAO;oBAAE,OAAO,eAAe,KAAK;gBAAC;YACvC;YAEA,OAAO;gBACL,MAAM;oBACJ,UAAU,eAAe,IAAI,EAAE,YAAY,EAAE;oBAC7C;oBACA,YAAY,eAAe,IAAI,EAAE,cAAc;oBAC/C,SAAS,eAAe,IAAI,EAAE,WAAW;oBACzC,UAAU,eAAe,IAAI,EAAE,YAAY;gBAC7C;YACF;QACF,OAEK,IAAI,gBAAgB,aAAa,IAAI,GAAG,MAAM,GAAG,GAAG;YACvD,uCAAuC;YACvC,MAAM,iBAAiB,MAAM,CAAA,GAAA,yJAAA,CAAA,0BAAuB,AAAD,EAAE;gBACnD;gBACA;gBACA;gBACA;gBACA;gBACA,QAAQ;gBACR;YACF;YAEA,IAAI,eAAe,KAAK,EAAE;gBACxB,OAAO;oBAAE,OAAO,eAAe,KAAK;gBAAC;YACvC;YAEA,IAAI,aAAa,SAAS;gBACxB,6BAA6B;gBAC7B,OAAO;oBACL,MAAM;wBACJ,YAAY,eAAe,IAAI,EAAE,cAAc,EAAE;wBACjD;wBACA,YAAY,eAAe,IAAI,EAAE,cAAc;wBAC/C,SAAS,eAAe,IAAI,EAAE,WAAW;wBACzC,UAAU,eAAe,IAAI,EAAE,YAAY;oBAC7C;gBACF;YACF,OAAO;gBACL,0BAA0B;gBAC1B,2CAA2C;gBAC3C,MAAM,cAAc,eAAe,IAAI,EAAE,WAAW,IAClD,CAAC,WAAa,SAAS,EAAE;gBAG3B,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;oBAC5C,OAAO;wBACL,MAAM;4BACJ,UAAU,EAAE;4BACZ;4BACA,YAAY;4BACZ,SAAS;4BACT,UAAU;wBACZ;oBACF;gBACF;gBAEA,sCAAsC;gBACtC,MAAM,iBAAiB,MAAM,CAAA,GAAA,wJAAA,CAAA,6BAA0B,AAAD,EAAE;oBACtD,aAAa;oBACb;oBACA;oBACA,QAAQ;oBACR;gBACF;gBAEA,IAAI,eAAe,KAAK,EAAE;oBACxB,OAAO;wBAAE,OAAO,eAAe,KAAK;oBAAC;gBACvC;gBAEA,OAAO;oBACL,MAAM;wBACJ,UAAU,eAAe,IAAI,EAAE,YAAY,EAAE;wBAC7C;wBACA,YAAY,eAAe,IAAI,EAAE,cAAc;wBAC/C,SAAS,eAAe,IAAI,EAAE,WAAW;wBACzC,UAAU,eAAe,IAAI,EAAE,YAAY;oBAC7C;gBACF;YACF;QACF,OAEK,IAAI,WAAW,MAAM;YACxB,OAAO,MAAM,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC9B,SAAS,WAAW;gBACpB,MAAM,QAAQ;gBACd;gBACA;gBACA;gBACA;gBACA,QAAS,aAAa,aAClB,cACA;gBACJ,aAAa,aAAa,aAAa,cAAc;gBACrD;YACF;QACF,OAEK;YACH,IAAI,aAAa,SAAS;gBACxB,uBAAuB;gBACvB,MAAM,iBAAiB,MAAM,CAAA,GAAA,yJAAA,CAAA,0BAAuB,AAAD,EAAE;oBACnD;oBACA;oBACA,QAAQ;oBACR;gBACF;gBAEA,IAAI,eAAe,KAAK,EAAE;oBACxB,OAAO;wBAAE,OAAO,eAAe,KAAK;oBAAC;gBACvC;gBAEA,OAAO;oBACL,MAAM;wBACJ,YAAY,eAAe,IAAI,EAAE,cAAc,EAAE;wBACjD;wBACA,YAAY,eAAe,IAAI,EAAE,cAAc;wBAC/C,SAAS,eAAe,IAAI,EAAE,WAAW;wBACzC,UAAU,eAAe,IAAI,EAAE,YAAY;oBAC7C;gBACF;YACF,OAAO;gBACL,0BAA0B;gBAC1B,qBAAqB;gBAErB,MAAM,iBAAiB,MAAM,CAAA,GAAA,wJAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC5C;oBACA;oBACA,QAAQ;oBACR;oBACA,SAAS,WAAW;oBACpB;oBACA;gBACF;gBAEA,IAAI,eAAe,KAAK,EAAE;oBACxB,OAAO;wBAAE,OAAO,eAAe,KAAK;oBAAC;gBACvC;gBAEA,OAAO;oBACL,MAAM;wBACJ,UAAU,eAAe,IAAI,EAAE,YAAY,EAAE;wBAC7C;wBACA,YAAY,eAAe,IAAI,EAAE,cAAc;wBAC/C,SAAS,eAAe,IAAI,EAAE,WAAW;wBACzC,UAAU,eAAe,IAAI,EAAE,YAAY;oBAC7C;gBACF;YACF;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;YAAE,OAAO;QAAkD;IACpE;AACF;;;IAzNsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/.next-internal/server/app/%28main%29/discover/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {searchDiscoverCombined as '40233faece1b387c15157ff9feaa5d282a77135da8'} from 'ACTIONS_MODULE0'\nexport {getPincodeDetails as '401d1fd9f2f483c3d17b0c79ea53a43a19793352e1'} from 'ACTIONS_MODULE1'\nexport {fetchMoreBusinessCardsCombined as '4059dac56ec2d4fa83cae240560bfebb118fd5640f'} from 'ACTIONS_MODULE2'\nexport {fetchMoreProductsCombined as '40cf076a0ff37f204cc8708187f4777e00ee8b9608'} from 'ACTIONS_MODULE3'\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/ModernDiscoverClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/discover/ModernDiscoverClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/discover/ModernDiscoverClient.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/ModernDiscoverClient.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/discover/ModernDiscoverClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/discover/ModernDiscoverClient.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 2434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2444, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/ModernResultsSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/discover/ModernResultsSkeleton.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/discover/ModernResultsSkeleton.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAiT,GAC9U,+EACA", "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/ModernResultsSkeleton.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/(main)/discover/ModernResultsSkeleton.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/(main)/discover/ModernResultsSkeleton.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "debugId": null}}, {"offset": {"line": 2472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2482, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/constants/urlParamConstants.ts"], "sourcesContent": ["/**\r\n * Constants for URL parameters in the discover page\r\n */\r\n\r\n// Business tab URL parameters\r\nexport const BUSINESS_NAME_PARAM = \"businessName\";\r\nexport const BUSINESS_SORT_PARAM = \"businessSort\";\r\nexport const BUSINESS_FILTER_PARAM = \"businessFilter\";\r\n\r\n// Product tab URL parameters\r\nexport const PRODUCT_NAME_PARAM = \"productName\";\r\nexport const PRODUCT_SORT_PARAM = \"productSort\";\r\nexport const PRODUCT_TYPE_PARAM = \"productType\";\r\n\r\n// Common URL parameters\r\nexport const PINCODE_PARAM = \"pincode\";\r\nexport const CITY_PARAM = \"city\";\r\nexport const LOCALITY_PARAM = \"locality\";\r\nexport const VIEW_TYPE_PARAM = \"view\";\r\nexport const CATEGORY_PARAM = \"category\";\r\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,8BAA8B;;;;;;;;;;;;;;AACvB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,wBAAwB;AAG9B,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAG3B,MAAM,gBAAgB;AACtB,MAAM,aAAa;AACnB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,iBAAiB", "debugId": null}}, {"offset": {"line": 2515, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28main%29/discover/page.tsx"], "sourcesContent": ["import { Suspense } from \"react\";\r\nimport ModernDiscoverClient from \"./ModernDiscoverClient\";\r\nimport { Metadata } from \"next\";\r\nimport ModernResultsSkeleton from \"./ModernResultsSkeleton\";\r\nimport {\r\n  BUSINESS_NAME_PARAM,\r\n  PINCODE_PARAM,\r\n} from \"./constants/urlParamConstants\";\r\n\r\n// SEO Metadata\r\nexport async function generateMetadata(): Promise<Metadata> {\r\n  const title = \"Discover Local Businesses & Products\";\r\n  const description =\r\n    \"Find and explore local businesses and products using Dukancard. Search by pincode, locality, or business name to discover shops, services, products, and professionals near you in India.\";\r\n  const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || \"https://dukancard.in\";\r\n  const pageUrl = `${siteUrl}/discover`;\r\n  const ogImage = `${siteUrl}/opengraph-image.png`; // Default OG image\r\n\r\n  return {\r\n    title, // Uses template: \"Discover Local Businesses - Dukancard\"\r\n    description,\r\n    keywords: [\r\n      \"discover local business\",\r\n      \"find shops near me\",\r\n      \"search business by pincode\",\r\n      \"search business by name\",\r\n      \"local business directory India\",\r\n      \"Dukancard discover\",\r\n      \"nearby services\",\r\n      \"local products\",\r\n      \"search by locality\",\r\n      \"business cards\",\r\n      \"products and services\",\r\n      \"infinite scroll\",\r\n    ],\r\n    alternates: {\r\n      canonical: \"/discover\", // Relative canonical path\r\n    },\r\n    openGraph: {\r\n      title: title,\r\n      description: description,\r\n      url: pageUrl,\r\n      siteName: \"Dukancard\",\r\n      type: \"website\",\r\n      locale: \"en_IN\",\r\n      images: [\r\n        {\r\n          url: ogImage,\r\n          width: 1200,\r\n          height: 630,\r\n          alt: \"Discover Local Businesses on Dukancard\",\r\n        },\r\n      ],\r\n    },\r\n    twitter: {\r\n      card: \"summary_large_image\",\r\n      title: title,\r\n      description: description,\r\n      images: [ogImage],\r\n    },\r\n    // Add WebPage Schema with SearchAction\r\n    other: {\r\n      \"application-ld+json\": JSON.stringify({\r\n        \"@context\": \"https://schema.org\",\r\n        \"@type\": \"WebPage\",\r\n        name: title,\r\n        description: description,\r\n        url: pageUrl,\r\n        isPartOf: {\r\n          \"@type\": \"WebSite\",\r\n          name: \"Dukancard\",\r\n          url: siteUrl,\r\n        },\r\n        // Add SearchAction for structured data\r\n        potentialAction: [\r\n          {\r\n            \"@type\": \"SearchAction\",\r\n            target: {\r\n              \"@type\": \"EntryPoint\",\r\n              urlTemplate: `${siteUrl}/discover?${PINCODE_PARAM}={pincode}`,\r\n            },\r\n            \"query-input\": \"required name=pincode\",\r\n            description: \"Search for businesses and products by pincode\",\r\n          },\r\n          {\r\n            \"@type\": \"SearchAction\",\r\n            target: {\r\n              \"@type\": \"EntryPoint\",\r\n              urlTemplate: `${siteUrl}/discover?${BUSINESS_NAME_PARAM}={businessName}`,\r\n            },\r\n            \"query-input\": \"required name=businessName\",\r\n            description: \"Search for businesses by name\",\r\n          },\r\n        ],\r\n        // Add breadcrumb for better navigation structure\r\n        breadcrumb: {\r\n          \"@type\": \"BreadcrumbList\",\r\n          itemListElement: [\r\n            {\r\n              \"@type\": \"ListItem\",\r\n              position: 1,\r\n              name: \"Home\",\r\n              item: siteUrl,\r\n            },\r\n            {\r\n              \"@type\": \"ListItem\",\r\n              position: 2,\r\n              name: \"Discover\",\r\n              item: pageUrl,\r\n            },\r\n          ],\r\n        },\r\n      }),\r\n    },\r\n  };\r\n}\r\n\r\n// Server Component - Handles initial rendering\r\nasync function DiscoverPageContent() {\r\n  return <ModernDiscoverClient />;\r\n}\r\n\r\n// Main Page Component using Suspense for streaming\r\nexport default function DiscoverPage() {\r\n  return (\r\n    <div className=\"min-h-screen bg-white dark:bg-black\">\r\n      <Suspense fallback={<ModernResultsSkeleton />}>\r\n        <DiscoverPageContent />\r\n      </Suspense>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;AACA;;;;;;AAMO,eAAe;IACpB,MAAM,QAAQ;IACd,MAAM,cACJ;IACF,MAAM,UAAU,6DAAoC;IACpD,MAAM,UAAU,GAAG,QAAQ,SAAS,CAAC;IACrC,MAAM,UAAU,GAAG,QAAQ,oBAAoB,CAAC,EAAE,mBAAmB;IAErE,OAAO;QACL;QACA;QACA,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,YAAY;YACV,WAAW;QACb;QACA,WAAW;YACT,OAAO;YACP,aAAa;YACb,KAAK;YACL,UAAU;YACV,MAAM;YACN,QAAQ;YACR,QAAQ;gBACN;oBACE,KAAK;oBACL,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;QACH;QACA,SAAS;YACP,MAAM;YACN,OAAO;YACP,aAAa;YACb,QAAQ;gBAAC;aAAQ;QACnB;QACA,uCAAuC;QACvC,OAAO;YACL,uBAAuB,KAAK,SAAS,CAAC;gBACpC,YAAY;gBACZ,SAAS;gBACT,MAAM;gBACN,aAAa;gBACb,KAAK;gBACL,UAAU;oBACR,SAAS;oBACT,MAAM;oBACN,KAAK;gBACP;gBACA,uCAAuC;gBACvC,iBAAiB;oBACf;wBACE,SAAS;wBACT,QAAQ;4BACN,SAAS;4BACT,aAAa,GAAG,QAAQ,UAAU,EAAE,6JAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;wBAC/D;wBACA,eAAe;wBACf,aAAa;oBACf;oBACA;wBACE,SAAS;wBACT,QAAQ;4BACN,SAAS;4BACT,aAAa,GAAG,QAAQ,UAAU,EAAE,6JAAA,CAAA,sBAAmB,CAAC,eAAe,CAAC;wBAC1E;wBACA,eAAe;wBACf,aAAa;oBACf;iBACD;gBACD,iDAAiD;gBACjD,YAAY;oBACV,SAAS;oBACT,iBAAiB;wBACf;4BACE,SAAS;4BACT,UAAU;4BACV,MAAM;4BACN,MAAM;wBACR;wBACA;4BACE,SAAS;4BACT,UAAU;4BACV,MAAM;4BACN,MAAM;wBACR;qBACD;gBACH;YACF;QACF;IACF;AACF;AAEA,+CAA+C;AAC/C,eAAe;IACb,qBAAO,8OAAC,oJAAA,CAAA,UAAoB;;;;;AAC9B;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;YAAC,wBAAU,8OAAC,qJAAA,CAAA,UAAqB;;;;;sBACxC,cAAA,8OAAC;;;;;;;;;;;;;;;AAIT", "debugId": null}}]}