import { createClient } from '@/utils/supabase/client';
import { TABLES, COLUMNS } from '@/lib/supabase/constants';
import type {
  CreateCommentResponse,
  EditCommentResponse,
  DeleteCommentResponse,
  PinCommentResponse,
  UnpinCommentResponse,
  PostCommentWithUser,
  CommentFilters,
} from '@/types/like-comment';



interface CommentLikeId {
  comment_id: string;
}

/**
 * Create a new comment on a post - Direct query implementation
 */
export async function createComment(
  postId: string,
  postSource: 'business' | 'customer',
  content: string,
  parentCommentId?: string
): Promise<CreateCommentResponse> {
  try {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      };
    }

    // Input validation
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!UUID_REGEX.test(postId)) {
      return {
        success: false,
        message: 'Invalid post ID',
        error: 'Post ID must be a valid UUID',
      };
    }

    if (!['business', 'customer'].includes(postSource)) {
      return {
        success: false,
        message: 'Invalid post source',
        error: 'Post source must be either business or customer',
      };
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return {
        success: false,
        message: 'Invalid content',
        error: 'Content is required and must be a string',
      };
    }

    if (content.length > 500) {
      return {
        success: false,
        message: 'Invalid content length',
        error: 'Content must be between 1 and 500 characters',
      };
    }

    // Sanitize content - remove script tags for XSS prevention
    const sanitizedContent = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '').trim();

    // Validate parent comment if provided
    if (parentCommentId) {
      if (!UUID_REGEX.test(parentCommentId)) {
        return {
          success: false,
          message: 'Invalid parent comment ID',
          error: 'Parent comment ID must be a valid UUID',
        };
      }

      // Check if parent comment exists and prevent deep nesting
      const { data: parentComment } = await supabase
        .from(TABLES.POST_COMMENTS)
        .select(`${COLUMNS.ID}, ${COLUMNS.PARENT_COMMENT_ID}`)
        .eq(COLUMNS.ID, parentCommentId)
        .maybeSingle();

      if (!parentComment) {
        return {
          success: false,
          message: 'Parent comment not found',
          error: 'Parent comment does not exist',
        };
      }

      // Prevent replies to replies (only single-level threading)
      if (parentComment.parent_comment_id) {
        return {
          success: false,
          message: 'Cannot reply to a reply',
          error: 'Only single-level threading is allowed',
        };
      }
    }

    // Verify post exists
    const postTable = postSource === 'business' ? TABLES.BUSINESS_POSTS : TABLES.CUSTOMER_POSTS;
    const { data: post, error: postError } = await supabase
      .from(postTable)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, postId)
      .maybeSingle();

    if (postError || !post) {
      return {
        success: false,
        message: 'Post not found',
        error: 'Post does not exist',
      };
    }

    // Insert comment
    const { data: insertData, error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .insert({
        [COLUMNS.USER_ID]: user.id,
        [COLUMNS.POST_ID]: postId,
        [COLUMNS.POST_SOURCE]: postSource,
        [COLUMNS.CONTENT]: sanitizedContent,
        [COLUMNS.PARENT_COMMENT_ID]: parentCommentId || null,
      })
      .select(COLUMNS.ID)
      .single();

    if (error) {
      console.error('Error creating comment:', error);
      return {
        success: false,
        message: 'Failed to create comment',
        error: 'Operation failed',
      };
    }

    return {
      success: true,
      message: 'Comment created successfully',
      comment_id: insertData.id,
    };
  } catch (error) {
    console.error('Error in createComment:', error);
    return {
      success: false,
      message: 'Failed to create comment',
      error: 'Operation failed',
    };
  }
}

/**
 * Edit an existing comment - Direct query implementation
 */
export async function editComment(
  commentId: string,
  content: string
): Promise<EditCommentResponse> {
  try {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      };
    }

    // Input validation
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!UUID_REGEX.test(commentId)) {
      return {
        success: false,
        message: 'Invalid comment ID',
        error: 'Comment ID must be a valid UUID',
      };
    }

    if (!content || typeof content !== 'string' || content.trim().length === 0) {
      return {
        success: false,
        message: 'Invalid content',
        error: 'Content is required and must be a string',
      };
    }

    if (content.length > 500) {
      return {
        success: false,
        message: 'Invalid content length',
        error: 'Content must be between 1 and 500 characters',
      };
    }

    // Sanitize content - remove script tags for XSS prevention
    const sanitizedContent = content.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '').trim();

    // Check if comment exists and get owner
    const { data: comment } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select(COLUMNS.USER_ID)
      .eq(COLUMNS.ID, commentId)
      .maybeSingle();

    if (!comment) {
      return {
        success: false,
        message: 'Comment not found',
        error: 'Comment does not exist',
      };
    }

    // Check if user owns the comment
    if (comment.user_id !== user.id) {
      return {
        success: false,
        message: 'Permission denied',
        error: 'You can only edit your own comments',
      };
    }

    // Update the comment
    const { error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .update({
        [COLUMNS.CONTENT]: sanitizedContent,
        [COLUMNS.IS_EDITED]: true,
        [COLUMNS.EDITED_AT]: new Date().toISOString(),
        [COLUMNS.UPDATED_AT]: new Date().toISOString(),
      })
      .eq(COLUMNS.ID, commentId);

    if (error) {
      console.error('Error editing comment:', error);
      return {
        success: false,
        message: 'Failed to edit comment',
        error: 'Operation failed',
      };
    }

    return {
      success: true,
      message: 'Comment updated successfully',
    };
  } catch (error) {
    console.error('Error in editComment:', error);
    return {
      success: false,
      message: 'Failed to edit comment',
      error: 'Operation failed',
    };
  }
}

/**
 * Delete a comment - Direct query implementation
 */
export async function deleteComment(commentId: string): Promise<DeleteCommentResponse> {
  try {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      };
    }

    // Check if comment exists and get owner
    const { data: comment } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select(COLUMNS.USER_ID)
      .eq(COLUMNS.ID, commentId)
      .maybeSingle();

    if (!comment) {
      return {
        success: false,
        message: 'Comment not found',
        error: 'Comment does not exist',
      };
    }

    // Check if user owns the comment (ONLY comment owner can delete)
    if (comment.user_id !== user.id) {
      return {
        success: false,
        message: 'Permission denied',
        error: 'You can only delete your own comments',
      };
    }

    // Delete the comment (cascade will handle replies and likes)
    const { error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .delete()
      .eq(COLUMNS.ID, commentId);

    if (error) {
      console.error('Error deleting comment:', error);
      return {
        success: false,
        message: 'Failed to delete comment',
        error: error.message,
      };
    }

    return {
      success: true,
      message: 'Comment deleted successfully',
    };
  } catch (error) {
    console.error('Error in deleteComment:', error);
    return {
      success: false,
      message: 'Failed to delete comment',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Pin a comment (post owner only) - Direct query implementation
 */
export async function pinComment(commentId: string): Promise<PinCommentResponse> {
  try {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      };
    }

    // Input validation
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!UUID_REGEX.test(commentId)) {
      return {
        success: false,
        message: 'Invalid comment ID',
        error: 'Comment ID must be a valid UUID',
      };
    }

    // Get comment details
    const { data: comment } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select(`${COLUMNS.POST_ID}, ${COLUMNS.POST_SOURCE}`)
      .eq(COLUMNS.ID, commentId)
      .maybeSingle();

    if (!comment) {
      return {
        success: false,
        message: 'Comment not found',
        error: 'Comment does not exist',
      };
    }

    // Check if user is the post owner
    const postTable = comment.post_source === 'business' ? TABLES.BUSINESS_POSTS : TABLES.CUSTOMER_POSTS;
    const ownerColumn = comment.post_source === 'business' ? COLUMNS.BUSINESS_ID : COLUMNS.CUSTOMER_ID;
    
    const { data: post } = await supabase
      .from(postTable)
      .select(ownerColumn)
      .eq(COLUMNS.ID, comment.post_id)
      .maybeSingle();

    if (!post) {
      return {
        success: false,
        message: 'Post not found',
        error: 'Post does not exist',
      };
    }

    if ((post as Record<string, string>)[ownerColumn] !== user.id) {
      return {
        success: false,
        message: 'Permission denied',
        error: 'Only post owners can pin comments',
      };
    }

    // Pin the comment
    const { error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .update({
        [COLUMNS.IS_PINNED]: true,
        [COLUMNS.UPDATED_AT]: new Date().toISOString(),
      })
      .eq(COLUMNS.ID, commentId);

    if (error) {
      console.error('Error pinning comment:', error);
      return {
        success: false,
        message: 'Failed to pin comment',
        error: 'Operation failed',
      };
    }

    return {
      success: true,
      message: 'Comment pinned successfully',
    };
  } catch (error) {
    console.error('Error in pinComment:', error);
    return {
      success: false,
      message: 'Failed to pin comment',
      error: 'Operation failed',
    };
  }
}

/**
 * Unpin a comment (post owner only) - Direct query implementation
 */
export async function unpinComment(commentId: string): Promise<UnpinCommentResponse> {
  try {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return {
        success: false,
        message: 'Authentication required',
        error: 'User not authenticated',
      };
    }

    // Input validation
    const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    
    if (!UUID_REGEX.test(commentId)) {
      return {
        success: false,
        message: 'Invalid comment ID',
        error: 'Comment ID must be a valid UUID',
      };
    }

    // Get comment details
    const { data: comment } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select(`${COLUMNS.POST_ID}, ${COLUMNS.POST_SOURCE}`)
      .eq(COLUMNS.ID, commentId)
      .maybeSingle();

    if (!comment) {
      return {
        success: false,
        message: 'Comment not found',
        error: 'Comment does not exist',
      };
    }

    // Check if user is the post owner
    const postTable = comment.post_source === 'business' ? TABLES.BUSINESS_POSTS : TABLES.CUSTOMER_POSTS;
    const ownerColumn = comment.post_source === 'business' ? COLUMNS.BUSINESS_ID : COLUMNS.CUSTOMER_ID;
    
    const { data: post } = await supabase
      .from(postTable)
      .select(ownerColumn)
      .eq(COLUMNS.ID, comment.post_id)
      .maybeSingle();

    if (!post) {
      return {
        success: false,
        message: 'Post not found',
        error: 'Post does not exist',
      };
    }

    if ((post as Record<string, string>)[ownerColumn] !== user.id) {
      return {
        success: false,
        message: 'Permission denied',
        error: 'Only post owners can unpin comments',
      };
    }

    // Unpin the comment
    const { error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .update({
        [COLUMNS.IS_PINNED]: false,
        [COLUMNS.UPDATED_AT]: new Date().toISOString(),
      })
      .eq(COLUMNS.ID, commentId);

    if (error) {
      console.error('Error unpinning comment:', error);
      return {
        success: false,
        message: 'Failed to unpin comment',
        error: 'Operation failed',
      };
    }

    return {
      success: true,
      message: 'Comment unpinned successfully',
    };
  } catch (error) {
    console.error('Error in unpinComment:', error);
    return {
      success: false,
      message: 'Failed to unpin comment',
      error: 'Operation failed',
    };
  }
}

/**
 * Get replies for a specific comment
 */
async function getCommentReplies(
  commentId: string,
  currentUserId?: string
): Promise<PostCommentWithUser[]> {
  try {
    const supabase = createClient();

    // Fetch replies for this comment
    const { data: replies, error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.USER_ID},
        ${COLUMNS.POST_ID},
        ${COLUMNS.POST_SOURCE},
        ${COLUMNS.PARENT_COMMENT_ID},
        ${COLUMNS.CONTENT},
        ${COLUMNS.IS_PINNED},
        ${COLUMNS.IS_EDITED},
        ${COLUMNS.EDITED_AT},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT}
      `)
      .eq(COLUMNS.PARENT_COMMENT_ID, commentId)
      .order(COLUMNS.CREATED_AT, { ascending: true });

    if (error || !replies || replies.length === 0) {
      return [];
    }

    // Get user IDs for fetching user information
    const userIds = replies.map(reply => reply.user_id);

    // Fetch business profiles
    const { data: businessProfiles } = await supabase
      .from('business_profiles')
      .select('id, business_name, logo_url')
      .in('id', userIds);

    // Fetch customer profiles
    const { data: customerProfiles } = await supabase
      .from('customer_profiles_public')
      .select('id, name, avatar_url')
      .in('id', userIds);

    // Fetch reply like counts
    const { data: likeCounts } = await supabase
      .from(TABLES.COMMENT_LIKES)
      .select('comment_id')
      .in('comment_id', replies.map(r => r.id));

    

    // Fetch current user's likes on these replies
    let userLikes: CommentLikeId[] = [];
    if (currentUserId) {
      const { data } = await supabase
        .from(TABLES.COMMENT_LIKES)
        .select('comment_id')
        .eq('user_id', currentUserId)
        .in('comment_id', replies.map(r => r.id));
      userLikes = data || [];
    }

    // Create lookup maps
    const businessProfileMap = new Map(businessProfiles?.map(bp => [bp.id, bp]) || []);
    const customerProfileMap = new Map(customerProfiles?.map(cp => [cp.id, cp]) || []);
    const likeCountMap = new Map();
    const userLikeMap = new Set(userLikes.map(ul => ul.comment_id));

    // Count likes per reply
    likeCounts?.forEach(like => {
      const count = likeCountMap.get(like.comment_id) || 0;
      likeCountMap.set(like.comment_id, count + 1);
    });

    // Enhance replies with user information
    const repliesWithUser: PostCommentWithUser[] = replies.map(reply => {
      const businessProfile = businessProfileMap.get(reply.user_id);
      const customerProfile = customerProfileMap.get(reply.user_id);

      let user_name = 'Unknown User';
      let user_avatar = undefined;
      let user_type: 'business' | 'customer' = 'customer';

      if (businessProfile) {
        user_name = businessProfile.business_name;
        user_avatar = businessProfile.logo_url || undefined;
        user_type = 'business';
      } else if (customerProfile) {
        user_name = customerProfile.name || 'Customer';
        user_avatar = customerProfile.avatar_url || undefined;
        user_type = 'customer';
      }

      return {
        ...reply,
        post_source: reply.post_source as 'business' | 'customer',
        user_name,
        user_avatar,
        user_type,
        like_count: likeCountMap.get(reply.id) || 0,
        is_liked_by_current_user: userLikeMap.has(reply.id),
        replies: [], // Replies don't have nested replies (only 2 levels deep)
      };
    });

    return repliesWithUser;
  } catch (error) {
    console.error('Error fetching comment replies:', error);
    return [];
  }
}

/**
 * Get comments for a post with user information
 */
export async function getPostComments(
  filters: CommentFilters
): Promise<{ success: boolean; data: PostCommentWithUser[]; error?: string }> {
  try {
    const supabase = createClient();
    const { post_id, post_source, sort_order = 'pinned_first', limit = 20, offset = 0 } = filters;

    // Get current user ID for like status checking
    const { data: { user } } = await supabase.auth.getUser();
    const currentUserId = user?.id;

    // Build the query
    let query = supabase
      .from(TABLES.POST_COMMENTS)
      .select(`
        ${COLUMNS.ID},
        ${COLUMNS.USER_ID},
        ${COLUMNS.POST_ID},
        ${COLUMNS.POST_SOURCE},
        ${COLUMNS.PARENT_COMMENT_ID},
        ${COLUMNS.CONTENT},
        ${COLUMNS.IS_PINNED},
        ${COLUMNS.IS_EDITED},
        ${COLUMNS.EDITED_AT},
        ${COLUMNS.CREATED_AT},
        ${COLUMNS.UPDATED_AT}
      `)
      .eq(COLUMNS.POST_ID, post_id)
      .eq(COLUMNS.POST_SOURCE, post_source)
      .is(COLUMNS.PARENT_COMMENT_ID, null); // Only top-level comments

    // Apply sorting
    if (sort_order === 'pinned_first') {
      query = query.order(COLUMNS.IS_PINNED, { ascending: false })
                   .order(COLUMNS.CREATED_AT, { ascending: false }); // Newest first after pinned
    } else if (sort_order === 'newest') {
      query = query.order(COLUMNS.CREATED_AT, { ascending: false });
    } else {
      query = query.order(COLUMNS.CREATED_AT, { ascending: true });
    }

    const { data: comments, error } = await query.range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching comments:', error);
      return {
        success: false,
        data: [],
        error: error.message,
      };
    }

    if (!comments || comments.length === 0) {
      return {
        success: true,
        data: [],
      };
    }

    // Get user IDs for fetching user information
    const userIds = comments.map(comment => comment.user_id);

    // Fetch business profiles
    const { data: businessProfiles } = await supabase
      .from('business_profiles')
      .select('id, business_name, logo_url')
      .in('id', userIds);

    // Fetch customer profiles (using public view for security)
    const { data: customerProfiles } = await supabase
      .from('customer_profiles_public')
      .select('id, name, avatar_url')
      .in('id', userIds);

    // Fetch comment like counts
    const { data: likeCounts } = await supabase
      .from(TABLES.COMMENT_LIKES)
      .select('comment_id')
      .in('comment_id', comments.map(c => c.id));

    // Fetch current user's likes on these comments
    let userLikes: CommentLikeId[] = [];
    if (currentUserId) {
      const { data } = await supabase
        .from(TABLES.COMMENT_LIKES)
        .select('comment_id')
        .eq('user_id', currentUserId)
        .in('comment_id', comments.map(c => c.id));
      userLikes = data || [];
    }

    // Create lookup maps
    const businessProfileMap = new Map(businessProfiles?.map(bp => [bp.id, bp]) || []);
    const customerProfileMap = new Map(customerProfiles?.map(cp => [cp.id, cp]) || []);
    const likeCountMap = new Map();
    const userLikeMap = new Set(userLikes.map(ul => ul.comment_id));

    // Count likes per comment
    likeCounts?.forEach(like => {
      const count = likeCountMap.get(like.comment_id) || 0;
      likeCountMap.set(like.comment_id, count + 1);
    });

    // Enhance comments with user information and like data
    const commentsWithUser: PostCommentWithUser[] = await Promise.all(
      comments.map(async (comment) => {
        const businessProfile = businessProfileMap.get(comment.user_id);
        const customerProfile = customerProfileMap.get(comment.user_id);

        let user_name = 'Unknown User';
        let user_avatar = undefined;
        let user_type: 'business' | 'customer' = 'customer';

        if (businessProfile) {
          user_name = businessProfile.business_name;
          user_avatar = businessProfile.logo_url || undefined;
          user_type = 'business';
        } else if (customerProfile) {
          user_name = customerProfile.name || 'Customer';
          user_avatar = customerProfile.avatar_url || undefined;
          user_type = 'customer';
        }

        // Fetch replies for this comment
        const replies = await getCommentReplies(comment.id, currentUserId);

        return {
          ...comment,
          post_source: comment.post_source as 'business' | 'customer',
          user_name,
          user_avatar,
          user_type,
          like_count: likeCountMap.get(comment.id) || 0,
          is_liked_by_current_user: userLikeMap.has(comment.id),
          replies,
        };
      })
    );

    return {
      success: true,
      data: commentsWithUser,
    };
  } catch (error) {
    console.error('Error in getPostComments:', error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Get the total comment count for a post
 */
export async function getPostCommentCount(
  postId: string,
  postSource: 'business' | 'customer'
): Promise<{ success: boolean; count: number; error?: string }> {
  try {
    const supabase = createClient();

    const { count, error } = await supabase
      .from(TABLES.POST_COMMENTS)
      .select('*', { count: 'exact', head: true })
      .eq(COLUMNS.POST_ID, postId)
      .eq(COLUMNS.POST_SOURCE, postSource);

    if (error) {
      console.error('Error fetching comment count:', error);
      return {
        success: false,
        count: 0,
        error: error.message,
      };
    }

    return {
      success: true,
      count: count || 0,
    };
  } catch (error) {
    console.error('Error in getPostCommentCount:', error);
    return {
      success: false,
      count: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
