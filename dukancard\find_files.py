import os

def find_files_with_create_client(root_dir):
    """
    Finds all files containing the string 'createClient' and returns a list of their paths,
    excluding files in directories containing 'api' and other common directories.
    """
    file_list = []
    excluded_dirs = ['node_modules', '.git', '.next', '.swc', 'coverage', 'playwright-report', 'api']
    for root, dirs, files in os.walk(root_dir):
        # Exclude directories
        dirs[:] = [d for d in dirs if d not in excluded_dirs]

        if any(excluded_dir in root.split(os.sep) for excluded_dir in excluded_dirs):
            continue

        for file in files:
            file_path = os.path.join(root, file)
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    if 'createClient' in f.read():
                        file_list.append(file_path)
            except (IOError, OSError):
                pass
    return file_list

if __name__ == "__main__":
    dukancard_dir = "C:\\web-app\\dukancard"
    files = find_files_with_create_client(dukancard_dir)
    for file in files:
        print(file)