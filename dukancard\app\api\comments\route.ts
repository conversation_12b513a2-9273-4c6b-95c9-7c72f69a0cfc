import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createClient } from '@/utils/supabase/server';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Apply security middleware for comments API
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  try {
    // 1. Rate limiting
    const ipAddress = getClientIP(req);
    const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
      operation: 'posts_api',
      ipAddress,
    });

    if (bruteForceCheck) {
      return bruteForceCheck;
    }

    // 2. JWT verification
    const token = extractBearerToken(req);
    if (!token) {
      return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
      return new NextResponse(JSON.stringify({ error: jwtResult.error || 'Invalid token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // 3. HMAC verification (for external calls)
    if (requireHMAC) {
      const hmacResult = await verifyHMACMiddleware(req, true);
      if (!hmacResult.success) {
        return new NextResponse(JSON.stringify({ error: hmacResult.error || 'HMAC verification failed' }), {
          status: hmacResult.status || 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      return {
        success: true,
        jwtPayload: jwtResult.payload,
        deviceId: hmacResult.deviceId,
      };
    }

    return {
      success: true,
      jwtPayload: jwtResult.payload,
    };

  } catch (error) {
    console.error('Unexpected error in security middleware:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal security error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for listing comments
const listCommentsSchema = z.object({
  post_id: z.string().uuid(),
  post_source: z.enum(["business", "customer"]),
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  parent_comment_id: z.string().uuid().optional(),
});

/**
 * GET /api/comments - List comments for a post
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for listing comments)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listCommentsSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      post_id,
      post_source,
      page = 1,
      limit = 20,
      parent_comment_id
    } = validation.data;
    const offset = (page - 1) * limit;

    const supabase = await createClient();

    // Build query for comments
    let query = supabase
      .from('post_comments')
      .select(`
        id, content, created_at, updated_at, is_pinned,
        user_id, post_id, post_source, parent_comment_id,
        customer_profiles!user_id(id, name, avatar_url),
        business_profiles!user_id(id, business_name, logo_url)
      `, { count: 'exact' })
      .eq('post_id', post_id)
      .eq('post_source', post_source);

    // Filter by parent comment if specified
    if (parent_comment_id) {
      query = query.eq('parent_comment_id', parent_comment_id);
    } else {
      query = query.is('parent_comment_id', null);
    }

    // Add sorting and pagination
    query = query
      .order('is_pinned', { ascending: false })
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    const { data: comments, error, count } = await query;

    if (error) {
      console.error('Error fetching comments:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch comments' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      comments: comments || [],
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in comments API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for creating comments
const createCommentSchema = z.object({
  post_id: z.string().uuid(),
  post_source: z.enum(["business", "customer"]),
  content: z.string().min(1).max(1000),
  parent_comment_id: z.string().uuid().optional(),
});

/**
 * POST /api/comments - Create a new comment
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = createCommentSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { post_id, post_source, content, parent_comment_id } = validation.data;

    const supabase = await createClient();

    // Create the comment
    const { data: comment, error } = await supabase
      .from('post_comments')
      .insert({
        post_id,
        post_source,
        content,
        user_id: jwtPayload.sub,
        parent_comment_id,
      })
      .select(`
        id, content, created_at, updated_at, is_pinned,
        user_id, post_id, post_source, parent_comment_id,
        customer_profiles!user_id(id, name, avatar_url),
        business_profiles!user_id(id, business_name, logo_url)
      `)
      .single();

    if (error) {
      console.error('Error creating comment:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to create comment' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      comment,
      message: 'Comment created successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create comment API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
