"use server";

import { BusinessProfilePublicData, BusinessSortBy } from "./types";
import { Tables } from "@/types/supabase";

/**
 * Securely fetch all business profiles or search by name and/or location
 */
export async function getSecureBusinessProfiles(
  searchTerm?: string | null,
  pincode?: string | null,
  locality?: string | null,
  page: number = 1,
  limit: number = 20,
  sortBy: BusinessSortBy = "created_desc",
  category?: string | null
): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('status', 'online');

    if (searchTerm) {
      queryParams.set('search', searchTerm.trim());
    }
    if (pincode) {
      queryParams.set('pincode', pincode);
    }
    if (locality) {
      queryParams.set('locality', locality);
    }
    if (category && category.trim()) {
      queryParams.set('category', category.trim());
    }

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch business profiles",
      };
    }

    const businesses = result.businesses || [];
    const count = result.pagination?.total || 0;

    // Transform data to match expected format
    const safeData: BusinessProfilePublicData[] = businesses.map((profile: Tables<'business_profiles'>) => {
      return {
        ...profile,
        city_slug: profile.city_slug || null,
        state_slug: profile.state_slug || null,
        locality_slug: profile.locality_slug || null,
        gallery: profile.gallery || null,
        latitude: profile.latitude || null,
        longitude: profile.longitude || null,
      } as BusinessProfilePublicData;
    });

    return { data: safeData, count };
  } catch (e) {
    console.error("Exception in getSecureBusinessProfiles:", e);
    return { error: "An unexpected error occurred." };
  }
}
