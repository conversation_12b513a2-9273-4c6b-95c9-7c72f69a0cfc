module.exports = {

"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"407591979af61a7d463be0dd2c1b3975f1e37c6d4d":"getSecureBusinessProfileWithProductsBySlug","40e464278039bdd26313983f57189fe5ad16207de5":"getSecureBusinessProfileBySlug"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfileBySlug": (()=>getSecureBusinessProfileBySlug),
    "getSecureBusinessProfileWithProductsBySlug": (()=>getSecureBusinessProfileWithProductsBySlug)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfileBySlug(slug) {
    if (!slug) {
        return {
            error: "Business slug is required."
        };
    }
    try {
        // Use the business profile API endpoint for public access by slug
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/slug/${slug}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profile"
            };
        }
        const profileData = result.business;
        if (!profileData) {
            return {
                error: "Profile not found."
            };
        }
        return {
            data: profileData
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileBySlug:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileWithProductsBySlug(slug) {
    if (!slug) {
        return {
            error: "Business slug is required."
        };
    }
    try {
        // Use the business profile API endpoint for public access by slug
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/slug/${slug}?include_products=true`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profile"
            };
        }
        const profileData = result.business;
        if (!profileData) {
            return {
                error: "Profile not found."
            };
        }
        const safeData = {
            ...profileData,
            products_services: profileData.products_services || []
        };
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileWithProductsBySlug:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfileBySlug,
    getSecureBusinessProfileWithProductsBySlug
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileBySlug, "40e464278039bdd26313983f57189fe5ad16207de5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileWithProductsBySlug, "407591979af61a7d463be0dd2c1b3975f1e37c6d4d", null);
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-rsc] (ecmascript)");
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    let headersList = null;
    let cookieStore = null;
    try {
        // Dynamically import next/headers to avoid issues in edge runtime
        const { headers, cookies } = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)")(__turbopack_context__.i);
        headersList = await headers();
        cookieStore = await cookies();
    } catch (error) {
        // If next/headers is not available (e.g., in edge runtime), continue without it
        console.warn('next/headers not available in this context, using fallback');
    }
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || headersList && headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment && headersList) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    // If cookies are not available, create a basic server client
    if (!cookieStore) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
            cookies: {
                getAll () {
                    return [];
                },
                setAll () {
                // No-op when cookies are not available
                }
            }
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
}}),
"[project]/app/(dashboard)/dashboard/business/gallery/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Removed PlanType import as we're now free for all users
/**
 * Get the gallery image limit - 10 images for all users
 * @returns The gallery image limit (10 images)
 */ __turbopack_context__.s({
    "canAddMoreGalleryImages": (()=>canAddMoreGalleryImages),
    "getGalleryLimit": (()=>getGalleryLimit)
});
function getGalleryLimit() {
    return 10;
}
function canAddMoreGalleryImages(currentCount) {
    const limit = getGalleryLimit();
    return currentCount < limit;
}
}}),
"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"401de3de75c96ed9402f56536c4055a2a1e481faa3":"getBusinessGalleryImagesBySlug","40ab6a62d529134e66e01b1076bd34abc258d01f1a":"getBusinessGalleryImages","40d99fa4a23448e324eb34055f899bfbe14f69c4a4":"getBusinessGalleryImagesForTab","70b33159413534880b95c7d6a0075cc6c1d55e240a":"getBusinessGalleryImagesPaginated"},"",""] */ __turbopack_context__.s({
    "getBusinessGalleryImages": (()=>getBusinessGalleryImages),
    "getBusinessGalleryImagesBySlug": (()=>getBusinessGalleryImagesBySlug),
    "getBusinessGalleryImagesForTab": (()=>getBusinessGalleryImagesForTab),
    "getBusinessGalleryImagesPaginated": (()=>getBusinessGalleryImagesPaginated)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/(dashboard)/dashboard/business/gallery/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function getBusinessGalleryImages(businessId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data: profileData, error } = await supabase.from("business_profiles").select("gallery").eq("id", businessId).single();
        if (error) {
            console.error("Error fetching business profile:", error);
            return {
                images: [],
                error: `Failed to fetch gallery images: ${error.message}`
            };
        }
        const gallery = profileData?.gallery || [];
        const images = Array.isArray(gallery) ? gallery : [];
        // Sort by created_at in descending order
        images.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        return {
            images: images
        };
    } catch (error) {
        console.error("Unexpected error fetching gallery images:", error);
        return {
            images: [],
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function getBusinessGalleryImagesForTab(businessSlug) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First, get the business ID from the slug
        const { data: business, error: businessError } = await supabase.from("business_profiles").select("id").eq("business_slug", businessSlug).eq("status", "online").single();
        if (businessError || !business) {
            console.error("Error fetching business profile:", businessError);
            return {
                images: [],
                totalCount: 0,
                error: "Business not found"
            };
        }
        // Platform is now free for all users - no subscription limits
        const galleryLimit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGalleryLimit"])();
        // Get the business profile with gallery data
        const { data: profileData, error: galleryError } = await supabase.from("business_profiles").select("gallery").eq("id", business.id).single();
        if (galleryError) {
            console.error("Error fetching business gallery:", galleryError);
            return {
                images: [],
                totalCount: 0,
                error: `Failed to fetch gallery images: ${galleryError.message}`
            };
        }
        // Parse gallery data
        const gallery = profileData?.gallery || [];
        const images = Array.isArray(gallery) ? gallery : [];
        // Sort by created_at in descending order (newest first)
        images.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        // Apply plan limit first, then limit to max 4 for gallery tab
        // This ensures we respect both plan restrictions and gallery tab display limits
        const planLimitedImages = images.slice(0, galleryLimit);
        const finalLimit = Math.min(planLimitedImages.length, 4);
        const limitedImages = planLimitedImages.slice(0, finalLimit);
        // Total count should also respect the plan limit, not show all images
        const totalCount = planLimitedImages.length;
        return {
            images: limitedImages,
            totalCount
        };
    } catch (error) {
        console.error("Unexpected error fetching gallery images for tab:", error);
        return {
            images: [],
            totalCount: 0,
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function getBusinessGalleryImagesBySlug(businessSlug) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First, get the business ID from the slug
        const { data: business, error: businessError } = await supabase.from("business_profiles").select("id").eq("business_slug", businessSlug).eq("status", "online").single();
        if (businessError || !business) {
            console.error("Error fetching business profile:", businessError);
            return {
                images: [],
                error: "Business not found"
            };
        }
        // Platform is now free for all users - no subscription limits
        const galleryLimit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGalleryLimit"])();
        // Get the business profile with gallery data
        const { data: profileData, error: galleryError } = await supabase.from("business_profiles").select("gallery").eq("id", business.id).single();
        if (galleryError) {
            console.error("Error fetching business gallery:", galleryError);
            return {
                images: [],
                error: `Failed to fetch gallery images: ${galleryError.message}`
            };
        }
        // Parse gallery data
        const gallery = profileData?.gallery || [];
        const images = Array.isArray(gallery) ? gallery : [];
        // Sort by created_at in descending order (newest first)
        images.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        // Limit the number of images based on the plan
        // This ensures we only return the number of images allowed by the plan
        const limitedImages = images.slice(0, galleryLimit);
        return {
            images: limitedImages
        };
    } catch (error) {
        console.error("Unexpected error fetching gallery images by slug:", error);
        return {
            images: [],
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
async function getBusinessGalleryImagesPaginated(businessSlug, page = 1, limit = 20) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First, get the business ID from the slug
        const { data: business, error: businessError } = await supabase.from("business_profiles").select("id").eq("business_slug", businessSlug).eq("status", "online").single();
        if (businessError || !business) {
            console.error("Error fetching business profile:", businessError);
            return {
                images: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page,
                hasNextPage: false,
                hasPrevPage: false,
                error: "Business not found"
            };
        }
        // Platform is now free for all users - no subscription limits
        const galleryLimit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f28$dashboard$292f$dashboard$2f$business$2f$gallery$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getGalleryLimit"])();
        // Get the business profile with gallery data
        const { data: profileData, error: galleryError } = await supabase.from("business_profiles").select("gallery").eq("id", business.id).single();
        if (galleryError) {
            console.error("Error fetching business gallery:", galleryError);
            return {
                images: [],
                totalCount: 0,
                totalPages: 0,
                currentPage: page,
                hasNextPage: false,
                hasPrevPage: false,
                error: `Failed to fetch gallery images: ${galleryError.message}`
            };
        }
        // Parse gallery data
        const gallery = profileData?.gallery || [];
        const allImages = Array.isArray(gallery) ? gallery : [];
        // Sort by created_at in descending order (newest first)
        allImages.sort((a, b)=>{
            return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        });
        // Apply plan limit first
        const planLimitedImages = allImages.slice(0, galleryLimit);
        const totalCount = planLimitedImages.length;
        // Calculate pagination
        const totalPages = Math.ceil(totalCount / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        // Get paginated images
        const paginatedImages = planLimitedImages.slice(startIndex, endIndex);
        return {
            images: paginatedImages,
            totalCount,
            totalPages,
            currentPage: page,
            hasNextPage: page < totalPages,
            hasPrevPage: page > 1
        };
    } catch (error) {
        console.error("Unexpected error fetching paginated gallery images:", error);
        return {
            images: [],
            totalCount: 0,
            totalPages: 0,
            currentPage: page,
            hasNextPage: false,
            hasPrevPage: false,
            error: `An unexpected error occurred: ${error instanceof Error ? error.message : String(error)}`
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getBusinessGalleryImages,
    getBusinessGalleryImagesForTab,
    getBusinessGalleryImagesBySlug,
    getBusinessGalleryImagesPaginated
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImages, "40ab6a62d529134e66e01b1076bd34abc258d01f1a", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImagesForTab, "40d99fa4a23448e324eb34055f899bfbe14f69c4a4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImagesBySlug, "401de3de75c96ed9402f56536c4055a2a1e481faa3", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getBusinessGalleryImagesPaginated, "70b33159413534880b95c7d6a0075cc6c1d55e240a", null);
}}),
"[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"706494db815230f39fde096a9039730918baa8e0b5":"getSecureBusinessProfileIdsForDiscover","7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33":"getSecureBusinessProfilesForDiscover"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfileIdsForDiscover": (()=>getSecureBusinessProfileIdsForDiscover),
    "getSecureBusinessProfilesForDiscover": (()=>getSecureBusinessProfilesForDiscover)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfilesForDiscover(pincodes, locality, page = 1, limit = 10, sortBy = "created_desc") {
    if (!pincodes || Array.isArray(pincodes) && pincodes.length === 0) {
        return {
            error: "At least one pincode is required."
        };
    }
    // Convert single pincode to array for consistent handling
    const pincodeArray = Array.isArray(pincodes) ? pincodes : [
        pincodes
    ];
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('pincodes', pincodeArray.join(','));
        queryParams.set('page', page.toString());
        queryParams.set('limit', limit.toString());
        queryParams.set('status', 'online');
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        if (locality) {
            queryParams.set('locality', locality);
        }
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles"
            };
        }
        const businesses = result.businesses || [];
        const count = result.pagination?.total || 0;
        // Transform data to match expected format
        const safeData = businesses.map((profile)=>{
            return {
                ...profile,
                // Add missing fields with default values if not present
                total_visits: profile.total_visits || 0,
                today_visits: profile.today_visits || 0,
                yesterday_visits: profile.yesterday_visits || 0,
                visits_7_days: profile.visits_7_days || 0,
                visits_30_days: profile.visits_30_days || 0,
                city_slug: profile.city_slug || null,
                state_slug: profile.state_slug || null,
                locality_slug: profile.locality_slug || null,
                gallery: profile.gallery || null,
                latitude: profile.latitude || null,
                longitude: profile.longitude || null
            };
        });
        return {
            data: safeData,
            count
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfilesForDiscover:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureBusinessProfileIdsForDiscover(pincodes, locality, sortBy = "created_desc") {
    if (!pincodes || Array.isArray(pincodes) && pincodes.length === 0) {
        return {
            error: "At least one pincode is required."
        };
    }
    // Convert single pincode to array for consistent handling
    const pincodeArray = Array.isArray(pincodes) ? pincodes : [
        pincodes
    ];
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('pincodes', pincodeArray.join(','));
        queryParams.set('status', 'online');
        queryParams.set('ids_only', 'true');
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        if (locality) {
            queryParams.set('locality', locality);
        }
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profile IDs"
            };
        }
        const businessIds = result.business_ids || [];
        return {
            data: businessIds
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfileIdsForDiscover:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfilesForDiscover,
    getSecureBusinessProfileIdsForDiscover
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfilesForDiscover, "7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfileIdsForDiscover, "706494db815230f39fde096a9039730918baa8e0b5", null);
}}),
"[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7f19088479bd4247b361efa63e1f08c59cbae5a424":"getSecureBusinessProfiles"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfiles": (()=>getSecureBusinessProfiles)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfiles(searchTerm, pincode, locality, page = 1, limit = 20, sortBy = "created_desc", category) {
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('page', page.toString());
        queryParams.set('limit', limit.toString());
        queryParams.set('status', 'online');
        if (searchTerm) {
            queryParams.set('search', searchTerm.trim());
        }
        if (pincode) {
            queryParams.set('pincode', pincode);
        }
        if (locality) {
            queryParams.set('locality', locality);
        }
        if (category && category.trim()) {
            queryParams.set('category', category.trim());
        }
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles"
            };
        }
        const businesses = result.businesses || [];
        const count = result.pagination?.total || 0;
        // Transform data to match expected format
        const safeData = businesses.map((profile)=>{
            return {
                ...profile,
                // Add missing fields with default values if not present
                total_visits: profile.total_visits || 0,
                today_visits: profile.today_visits || 0,
                yesterday_visits: profile.yesterday_visits || 0,
                visits_7_days: profile.visits_7_days || 0,
                visits_30_days: profile.visits_30_days || 0,
                city_slug: profile.city_slug || null,
                state_slug: profile.state_slug || null,
                locality_slug: profile.locality_slug || null,
                gallery: profile.gallery || null,
                latitude: profile.latitude || null,
                longitude: profile.longitude || null
            };
        });
        return {
            data: safeData,
            count
        };
    } catch (e) {
        console.error("Exception in getSecureBusinessProfiles:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfiles
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfiles, "7f19088479bd4247b361efa63e1f08c59cbae5a424", null);
}}),
"[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00be04a805bc46661f0f4971fc802a16df9b7de3d1":"getSecureBusinessProfilesForSitemap"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfilesForSitemap": (()=>getSecureBusinessProfilesForSitemap)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfilesForSitemap() {
    try {
        // Use the business sitemap API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/sitemap`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles for sitemap"
            };
        }
        const profiles = result.profiles || [];
        // If there are no profiles, return empty array
        if (!profiles || profiles.length === 0) {
            return {
                data: []
            };
        }
        // Create a map to deduplicate by business_slug
        const uniqueProfiles = new Map();
        // Add all profiles to the map (this automatically deduplicates by business_slug)
        profiles.forEach((profile)=>{
            if (profile.business_slug) {
                uniqueProfiles.set(profile.business_slug, {
                    business_slug: profile.business_slug,
                    updated_at: profile.updated_at
                });
            }
        });
        // Convert map values to array
        const combinedProfiles = Array.from(uniqueProfiles.values());
        // Return the deduplicated profiles
        return {
            data: combinedProfiles
        };
    } catch (_e) {
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfilesForSitemap
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfilesForSitemap, "00be04a805bc46661f0f4971fc802a16df9b7de3d1", null);
}}),
"[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede":"getCurrentUserBusinessProfileId","407666f47811af7f53e12500216410b8cf9c72486c":"checkBusinessProfileAccess"},"",""] */ __turbopack_context__.s({
    "checkBusinessProfileAccess": (()=>checkBusinessProfileAccess),
    "getCurrentUserBusinessProfileId": (()=>getCurrentUserBusinessProfileId)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function checkBusinessProfileAccess(businessProfileId) {
    try {
        // Use the business access API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/access`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                business_profile_id: businessProfileId
            })
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                hasAccess: false,
                error: result.error || "Failed to check business profile access"
            };
        }
        return {
            hasAccess: result.has_access
        };
    } catch (e) {
        console.error("Exception in checkBusinessProfileAccess:", e);
        return {
            hasAccess: false,
            error: "An unexpected error occurred"
        };
    }
}
async function getCurrentUserBusinessProfileId() {
    try {
        // Use the business access API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/access/me`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to get current user business profile ID"
            };
        }
        return {
            profileId: result.profile_id
        };
    } catch (e) {
        console.error("Exception in getCurrentUserBusinessProfileId:", e);
        return {
            error: "An unexpected error occurred"
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    checkBusinessProfileAccess,
    getCurrentUserBusinessProfileId
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(checkBusinessProfileAccess, "407666f47811af7f53e12500216410b8cf9c72486c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getCurrentUserBusinessProfileId, "00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede", null);
}}),
"[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"78aef494570fba887a7b263a064fef1e8968bf098e":"getSecureBusinessProfilesByLocation"},"",""] */ __turbopack_context__.s({
    "getSecureBusinessProfilesByLocation": (()=>getSecureBusinessProfilesByLocation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
async function getSecureBusinessProfilesByLocation(location, page = 1, limit = 20, sortBy = "created_desc") {
    try {
        // Build query parameters
        const queryParams = new URLSearchParams();
        queryParams.set('page', page.toString());
        queryParams.set('limit', limit.toString());
        queryParams.set('status', 'online');
        // Add location filters if provided
        if (location.pincode) {
            queryParams.set('pincode', location.pincode);
        }
        if (location.city) {
            queryParams.set('city', location.city);
        }
        if (location.state) {
            queryParams.set('state', location.state);
        }
        if (location.locality) {
            queryParams.set('locality', location.locality);
        }
        // Convert sortBy to API format
        let apiSortBy = 'created_desc';
        switch(sortBy){
            case 'name_asc':
                apiSortBy = 'name_asc';
                break;
            case 'name_desc':
                apiSortBy = 'name_desc';
                break;
            case 'created_asc':
                apiSortBy = 'created_asc';
                break;
            case 'created_desc':
                apiSortBy = 'created_desc';
                break;
            case 'likes_asc':
                apiSortBy = 'likes_asc';
                break;
            case 'likes_desc':
                apiSortBy = 'likes_desc';
                break;
            case 'subscriptions_asc':
                apiSortBy = 'subscriptions_asc';
                break;
            case 'subscriptions_desc':
                apiSortBy = 'subscriptions_desc';
                break;
            case 'rating_asc':
                apiSortBy = 'rating_asc';
                break;
            case 'rating_desc':
                apiSortBy = 'rating_desc';
                break;
        }
        queryParams.set('sort_by', apiSortBy);
        // Use the business profile API endpoint
        const response = await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business?${queryParams.toString()}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        const result = await response.json();
        if (!response.ok) {
            console.error("API Fetch Error:", result);
            return {
                error: result.error || "Failed to fetch business profiles by location"
            };
        }
        const businesses = result.businesses || [];
        const count = result.pagination?.total || 0;
        // Transform data to match expected format
        const safeData = businesses.map((profile)=>{
            return {
                ...profile,
                // Add missing fields with default values if not present
                total_visits: profile.total_visits || 0,
                today_visits: profile.today_visits || 0,
                yesterday_visits: profile.yesterday_visits || 0,
                visits_7_days: profile.visits_7_days || 0,
                visits_30_days: profile.visits_30_days || 0,
                city_slug: profile.city_slug || null,
                state_slug: profile.state_slug || null,
                locality_slug: profile.locality_slug || null,
                gallery: profile.gallery || null,
                latitude: profile.latitude || null,
                longitude: profile.longitude || null
            };
        });
        return {
            data: safeData,
            count
        };
    } catch (error) {
        console.error("Unexpected error in getSecureBusinessProfilesByLocation:", error);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureBusinessProfilesByLocation
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureBusinessProfilesByLocation, "78aef494570fba887a7b263a064fef1e8968bf098e", null);
}}),
"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"4050870674cfbc37404b048f17c031e582c6817414":"likeBusiness","406e7e1a2d209fd5068ed48a9b20838464c164486f":"unlikeBusiness","407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff":"unsubscribeFromBusiness","40ac030879d2dd9afcc77d6d490fed42fed2873adc":"subscribeToBusiness","40bf9c153311b3515e09fea919b70600d754f8d64c":"deleteReview","40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca":"getInteractionStatus","7097170cd67aa830da6c5c6a671c70d1f195006853":"submitReview"},"",""] */ __turbopack_context__.s({
    "deleteReview": (()=>deleteReview),
    "getInteractionStatus": (()=>getInteractionStatus),
    "likeBusiness": (()=>likeBusiness),
    "submitReview": (()=>submitReview),
    "subscribeToBusiness": (()=>subscribeToBusiness),
    "unlikeBusiness": (()=>unlikeBusiness),
    "unsubscribeFromBusiness": (()=>unsubscribeFromBusiness)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/cache.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function subscribeToBusiness(businessProfileId) {
    // const cookieStore = cookies(); // No longer needed here
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from subscribing to their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot subscribe to your own business card."
        };
    }
    // Check if the current user is a business (has a business profile)
    const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
    try {
        // 1. Insert subscription - Use regular client with proper RLS
        const { error: insertError } = await supabase.from("subscriptions").insert({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (insertError) {
            // Handle potential unique constraint violation (already subscribed) gracefully
            if (insertError.code === "23505") {
                // unique_violation
                console.log(`User ${user.id} already subscribed to business ${businessProfileId}.`);
                // Optionally return success true if already subscribed is acceptable
                return {
                    success: true
                };
            }
            console.error("Error inserting subscription:", insertError);
            throw new Error(insertError.message);
        }
        // Note: We don't need to manually update the subscription count
        // The database trigger 'update_total_subscriptions' will handle this automatically
        // 3. Revalidate paths
        // Revalidate the specific card page and potentially the user's dashboard
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer"); // Revalidate customer dashboard
        // Check if the current user is a business and revalidate business dashboard
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in subscribeToBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function unsubscribeFromBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from unsubscribing from their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot unsubscribe from your own business card."
        };
    }
    // Check if the current user is a business (has a business profile)
    const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
    try {
        // 1. Delete subscription - Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("subscriptions").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting subscription:", deleteError);
            throw new Error(deleteError.message);
        }
        // Note: We don't need to manually update the subscription count
        // The database trigger 'update_total_subscriptions' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer");
        // Check if the current user is a business and revalidate business dashboard
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/subscriptions"); // Revalidate business subscriptions page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in unsubscribeFromBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function submitReview(businessProfileId, rating, reviewText// Allow null for review text
) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from reviewing their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot review your own business card."
        };
    }
    if (rating < 1 || rating > 5) {
        return {
            success: false,
            error: "Rating must be between 1 and 5."
        };
    }
    try {
        // Upsert the review: insert if not exists, update if exists - Use regular client with proper RLS
        const { error: upsertError } = await supabase.from("ratings_reviews").upsert({
            user_id: user.id,
            business_profile_id: businessProfileId,
            rating: rating,
            review_text: reviewText,
            updated_at: new Date().toISOString()
        }, {
            onConflict: "user_id, business_profile_id"
        });
        if (upsertError) {
            console.error("Error submitting review:", upsertError);
            throw new Error(upsertError.message);
        }
        // Average rating is handled by the database trigger
        // Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer"); // Revalidate customer dashboard where reviews might be shown
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in submitReview:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function deleteReview(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    try {
        // Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("ratings_reviews").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting review:", deleteError);
            throw new Error(deleteError.message);
        }
        // Average rating is handled by the database trigger
        // Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/customer");
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in deleteReview:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function likeBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from liking their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot like your own business card."
        };
    }
    try {
        // 1. Insert like - Use regular client with proper RLS
        const { error: insertError } = await supabase.from("likes").insert({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (insertError) {
            // Handle potential unique constraint violation (already liked) gracefully
            if (insertError.code === "23505") {
                // unique_violation
                console.log(`User ${user.id} already liked business ${businessProfileId}.`);
                return {
                    success: true
                }; // Consider it success if already liked
            }
            console.error("Error inserting like:", insertError);
            throw new Error(insertError.message);
        }
        // Note: We don't need to manually update the like count
        // The database trigger 'update_total_likes' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        // Check if the current user is a business and revalidate business dashboard
        const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/likes"); // Revalidate business likes page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in likeBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function unlikeBusiness(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
        return {
            success: false,
            error: "User not authenticated."
        };
    }
    // Prevent a business from unliking their own business card
    if (user.id === businessProfileId) {
        return {
            success: false,
            error: "You cannot unlike your own business card."
        };
    }
    try {
        // 1. Delete like - Use regular client with proper RLS
        const { error: deleteError } = await supabase.from("likes").delete().match({
            user_id: user.id,
            business_profile_id: businessProfileId
        });
        if (deleteError) {
            console.error("Error deleting like:", deleteError);
            throw new Error(deleteError.message);
        }
        // Note: We don't need to manually update the like count
        // The database trigger 'update_total_likes' will handle this automatically
        // 3. Revalidate paths
        // Use regular client - business_profiles has public read access
        const { data: cardData } = await supabase.from("business_profiles").select("business_slug").eq("id", businessProfileId).single();
        if (cardData?.business_slug) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/${cardData.business_slug}`);
        }
        // Check if the current user is a business and revalidate business dashboard
        const { data: userBusinessProfile } = await supabase.from("business_profiles").select("id").eq("id", user.id).maybeSingle();
        if (userBusinessProfile) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business"); // Revalidate business dashboard
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])("/dashboard/business/likes"); // Revalidate business likes page
        }
        // Revalidate the activities page for the business
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$cache$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["revalidatePath"])(`/dashboard/business/activities`);
        return {
            success: true
        };
    } catch (error) {
        console.error("Unexpected error in unlikeBusiness:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            success: false,
            error: errorMessage
        };
    }
}
async function getInteractionStatus(businessProfileId) {
    // const cookieStore = cookies();
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])(); // Await the async function
    let userId = null;
    // Try to get authenticated user, but proceed even if not logged in
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
        userId = user.id;
    }
    // Default status for anonymous users
    const defaultStatus = {
        isSubscribed: false,
        hasLiked: false,
        userRating: null,
        userReview: null
    };
    if (!userId) {
        return defaultStatus; // Return default if no user is logged in
    }
    try {
        // Use regular client - all these tables have public read access
        // Fetch all statuses in parallel
        const [subscriptionRes, likeRes, reviewRes] = await Promise.all([
            supabase.from("subscriptions").select("id", {
                count: "exact",
                head: true
            }) // Just check existence
            .match({
                user_id: userId,
                business_profile_id: businessProfileId
            }),
            supabase.from("likes").select("id", {
                count: "exact",
                head: true
            }) // Just check existence
            .match({
                user_id: userId,
                business_profile_id: businessProfileId
            }),
            supabase.from("ratings_reviews").select("rating, review_text").match({
                user_id: userId,
                business_profile_id: businessProfileId
            }).maybeSingle()
        ]);
        // Check for errors in parallel fetches
        if (subscriptionRes.error) throw new Error(`Subscription fetch error: ${subscriptionRes.error.message}`);
        if (likeRes.error) throw new Error(`Like fetch error: ${likeRes.error.message}`);
        if (reviewRes.error) throw new Error(`Review fetch error: ${reviewRes.error.message}`);
        const reviewData = reviewRes.data;
        return {
            isSubscribed: (subscriptionRes.count ?? 0) > 0,
            hasLiked: (likeRes.count ?? 0) > 0,
            userRating: reviewData?.rating ?? null,
            userReview: reviewData?.review_text ?? null
        };
    } catch (error) {
        console.error("Error fetching interaction status:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        // Return default status but include the error message
        return {
            ...defaultStatus,
            error: errorMessage
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    subscribeToBusiness,
    unsubscribeFromBusiness,
    submitReview,
    deleteReview,
    likeBusiness,
    unlikeBusiness,
    getInteractionStatus
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(subscribeToBusiness, "40ac030879d2dd9afcc77d6d490fed42fed2873adc", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unsubscribeFromBusiness, "407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(submitReview, "7097170cd67aa830da6c5c6a671c70d1f195006853", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteReview, "40bf9c153311b3515e09fea919b70600d754f8d64c", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(likeBusiness, "4050870674cfbc37404b048f17c031e582c6817414", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(unlikeBusiness, "406e7e1a2d209fd5068ed48a9b20838464c164486f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getInteractionStatus, "40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca", null);
}}),
"[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// lib/supabase/constants.ts
__turbopack_context__.s({
    "BUCKETS": (()=>BUCKETS),
    "COLUMNS": (()=>COLUMNS),
    "POST_SOURCES": (()=>POST_SOURCES),
    "TABLES": (()=>TABLES)
});
const TABLES = {
    BLOGS: "blogs",
    BUSINESS_POSTS: "business_posts",
    BUSINESS_PROFILES: "business_profiles",
    CUSTOMER_POSTS: "customer_posts",
    CUSTOMER_PROFILES: "customer_profiles",
    CUSTOMER_PROFILES_PUBLIC: "customer_profiles_public",
    LIKES: "likes",
    PINCODES: "pincodes",
    PRODUCTS_SERVICES: "products_services",
    PRODUCT_VARIANTS: "product_variants",
    SUBSCRIPTIONS: "subscriptions",
    STORAGE_CLEANUP_CONFIG: "storage_cleanup_config",
    STORAGE_CLEANUP_PROGRESS: "storage_cleanup_progress",
    RATINGS_REVIEWS: "ratings_reviews",
    POST_LIKES: "post_likes",
    POST_COMMENTS: "post_comments",
    COMMENT_LIKES: "comment_likes"
};
const BUCKETS = {
    BUSINESS: "business",
    CUSTOMERS: "customers"
};
const COLUMNS = {
    ID: "id",
    CREATED_AT: "created_at",
    UPDATED_AT: "updated_at",
    NAME: "name",
    EMAIL: "email",
    PHONE: "phone",
    CITY: "city",
    STATE: "state",
    PINCODE: "pincode",
    PLAN_ID: "plan_id",
    LOCALITY: "locality",
    CITY_SLUG: "city_slug",
    STATE_SLUG: "state_slug",
    LOCALITY_SLUG: "locality_slug",
    LOGO_URL: "logo_url",
    IMAGE_URL: "image_url",
    IMAGES: "images",
    SLUG: "slug",
    STATUS: "status",
    CONTENT: "content",
    GALLERY: "gallery",
    DESCRIPTION: "description",
    TITLE: "title",
    USER_ID: "user_id",
    BUSINESS_ID: "business_id",
    CUSTOMER_ID: "customer_id",
    BUSINESS_NAME: "business_name",
    BUSINESS_SLUG: "business_slug",
    PRODUCT_ID: "product_id",
    PRODUCT_TYPE: "product_type",
    BUSINESS_PROFILE_ID: "business_profile_id",
    RATING: "rating",
    REVIEW_TEXT: "review_text",
    AVATAR_URL: "avatar_url",
    ADDRESS_LINE: "address_line",
    POST_ID: "post_id",
    POST_SOURCE: "post_source",
    COMMENT_ID: "comment_id",
    PARENT_COMMENT_ID: "parent_comment_id",
    IS_PINNED: "is_pinned",
    IS_EDITED: "is_edited",
    EDITED_AT: "edited_at"
};
const POST_SOURCES = {
    BUSINESS: "business",
    CUSTOMER: "customer"
};
}}),
"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"7e2ba45ccb00f66ac30c4be3781c321dc836b9feb2":"fetchMoreProducts"},"",""] */ __turbopack_context__.s({
    "fetchMoreProducts": (()=>fetchMoreProducts)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
// Removed unused import - noStore
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/supabase/constants.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function fetchMoreProducts(businessId, page = 1, sortBy = "created_desc", pageSize = 20, searchTerm, productType) {
    if (!businessId) {
        return {
            error: "Business ID is required."
        };
    }
    if (page < 1) {
        return {
            error: "Page number must be 1 or greater."
        };
    }
    if (pageSize < 1) {
        return {
            error: "Page size must be 1 or greater."
        };
    }
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    const offset = (page - 1) * pageSize;
    // Build count query first to get total count
    let countQuery = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].ID, {
        count: "exact"
    }).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq("is_available", true);
    // Apply search filter if provided
    if (searchTerm && searchTerm.trim().length > 0) {
        countQuery = countQuery.ilike(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].NAME, `%${searchTerm.trim()}%`);
    }
    // Apply product type filter if provided
    if (productType && productType !== "all") {
        countQuery = countQuery.eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].PRODUCT_TYPE, productType);
    }
    // Get total count
    const { count, error: countError } = await countQuery;
    if (countError) {
        return {
            error: "Failed to count products"
        };
    }
    // Build the main query for fetching products
    let query = supabase.from(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["TABLES"].PRODUCTS_SERVICES).select(`
            id,
            business_id,
            name,
            description,
            base_price,
            discounted_price,
            product_type,
            is_available,
            image_url,
            created_at,
            updated_at,
            slug
        `).eq(__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$supabase$2f$constants$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["COLUMNS"].BUSINESS_ID, businessId).eq("is_available", true);
    // Apply search filter if provided
    if (searchTerm && searchTerm.trim().length > 0) {
        query = query.ilike("name", `%${searchTerm.trim()}%`);
    }
    // Apply product type filter if provided
    if (productType && productType !== "all") {
        query = query.eq("product_type", productType);
    }
    // Apply Sorting
    switch(sortBy){
        case "created_asc":
            query = query.order("created_at", {
                ascending: true
            });
            break;
        case "updated_desc":
            query = query.order("updated_at", {
                ascending: false
            });
            break;
        case "price_asc":
            // Sort by discounted_price first, then base_price for price_asc
            query = query.order("discounted_price", {
                ascending: true,
                nullsFirst: false
            }).order("base_price", {
                ascending: true,
                nullsFirst: false
            });
            break;
        case "price_desc":
            // Sort by discounted_price first, then base_price for price_desc
            query = query.order("discounted_price", {
                ascending: false,
                nullsFirst: false
            }).order("base_price", {
                ascending: false,
                nullsFirst: false
            });
            break;
        case "name_asc":
            query = query.order("name", {
                ascending: true
            });
            break;
        case "name_desc":
            query = query.order("name", {
                ascending: false
            });
            break;
        case "created_desc":
        default:
            query = query.order("created_at", {
                ascending: false
            });
            break;
    }
    query = query.range(offset, offset + pageSize - 1);
    const { data, error } = await query;
    if (error) {
        return {
            error: "Failed to fetch products."
        };
    }
    return {
        data: data ?? [],
        totalCount: count || 0
    };
} // Removed visit tracking logic as metrics tables have been deleted
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchMoreProducts
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchMoreProducts, "7e2ba45ccb00f66ac30c4be3781c321dc836b9feb2", null);
}}),
"[project]/lib/actions/secureCustomerProfiles.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"00530736ed9e2f410981f81fb255a43707cbf4f1e5":"getAllSecureCustomerProfiles","4018b299e3c1d222a05577f4321da12b0eb38a6a95":"getSecureCustomerProfilesByIds","409659f0cd54fd1490265212a1777a415ceaacda9d":"getUserProfileForReview","40b26f081e1434339b88122ebd092f8afcb78174c9":"checkUserCustomerProfileAccess","40b9559348aad6ea00a2f188b04b826d6850c7d6c2":"getSecureCustomerProfileById","40e61d2e205375be89a0667f784a03f0f1723fa4d1":"getUserProfilesForReviews"},"",""] */ __turbopack_context__.s({
    "checkUserCustomerProfileAccess": (()=>checkUserCustomerProfileAccess),
    "getAllSecureCustomerProfiles": (()=>getAllSecureCustomerProfiles),
    "getSecureCustomerProfileById": (()=>getSecureCustomerProfileById),
    "getSecureCustomerProfilesByIds": (()=>getSecureCustomerProfilesByIds),
    "getUserProfileForReview": (()=>getUserProfileForReview),
    "getUserProfilesForReviews": (()=>getUserProfilesForReviews)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
async function getSecureCustomerProfileById(userId) {
    if (!userId) {
        return {
            error: "User ID is required."
        };
    }
    try {
        // Use the public view which only exposes safe data
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the customer profile from public view
        const { data, error } = await supabase.from("customer_profiles_public").select("*").eq("id", userId).maybeSingle();
        if (error) {
            console.error("Secure Fetch Error:", error);
            return {
                error: `Failed to fetch customer profile: ${error.message}`
            };
        }
        if (!data) {
            return {
                error: "Profile not found."
            };
        }
        // Data from public view is already safe
        const safeData = {
            id: data.id,
            name: data.name,
            email: null,
            avatar_url: data.avatar_url,
            created_at: data.created_at,
            updated_at: data.updated_at
        };
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureCustomerProfileById:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getSecureCustomerProfilesByIds(userIds) {
    if (!userIds || userIds.length === 0) {
        return {
            data: []
        };
    }
    try {
        // Use the public view which only exposes safe data
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch the customer profiles from public view
        const { data, error } = await supabase.from("customer_profiles_public").select("id, name, avatar_url, created_at, updated_at").in("id", userIds);
        if (error) {
            console.error("Secure Fetch Error:", error);
            return {
                error: `Failed to fetch customer profiles: ${error.message}`
            };
        }
        // Map to expected format (email not available in public view)
        const safeData = data?.map((profile)=>({
                ...profile,
                email: null // Not available in public view
            })) || [];
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getSecureCustomerProfilesByIds:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getAllSecureCustomerProfiles() {
    try {
        // Use the public view which only exposes safe data
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch all customer profiles from public view
        const { data, error } = await supabase.from("customer_profiles_public").select("id, name, avatar_url, created_at, updated_at");
        if (error) {
            console.error("Secure Fetch Error:", error);
            return {
                error: `Failed to fetch customer profiles: ${error.message}`
            };
        }
        // Map to expected format (email not available in public view)
        const safeData = data?.map((profile)=>({
                ...profile,
                email: null // Not available in public view
            })) || [];
        return {
            data: safeData
        };
    } catch (e) {
        console.error("Exception in getAllSecureCustomerProfiles:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function checkUserCustomerProfileAccess(userId) {
    if (!userId) {
        return {
            hasAccess: false,
            error: "User ID is required."
        };
    }
    try {
        // Use regular client for authenticated user
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Check if the user is authenticated
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        if (authError || !user) {
            return {
                hasAccess: false,
                error: "User not authenticated."
            };
        }
        // Verify the requested userId matches the authenticated user
        if (user.id !== userId) {
            return {
                hasAccess: false,
                error: "Unauthorized access attempt."
            };
        }
        // Check if the user has a customer profile
        const { data, error } = await supabase.from("customer_profiles").select("id").eq("id", userId).maybeSingle();
        if (error) {
            console.error("Profile Access Check Error:", error);
            return {
                hasAccess: false,
                error: "Database error checking access."
            };
        }
        return {
            hasAccess: !!data
        };
    } catch (e) {
        console.error("Exception in checkUserCustomerProfileAccess:", e);
        return {
            hasAccess: false,
            error: "An unexpected error occurred."
        };
    }
}
async function getUserProfileForReview(userId) {
    if (!userId) {
        return {
            error: "User ID is required."
        };
    }
    try {
        // Use the public view which only exposes safe data
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // First check customer_profiles_public
        const { data: customerProfile, error: customerError } = await supabase.from("customer_profiles_public").select("id, name, avatar_url").eq("id", userId).maybeSingle();
        if (customerError) {
            console.error("Error fetching customer profile:", customerError);
            return {
                error: `Failed to fetch customer profile: ${customerError.message}`
            };
        }
        if (customerProfile) {
            return {
                data: {
                    id: customerProfile.id,
                    name: customerProfile.name,
                    avatar_url: customerProfile.avatar_url,
                    is_business: false
                }
            };
        }
        // If not found in customer_profiles, check business_profiles
        const { data: businessProfile, error: businessError } = await supabase.from("business_profiles").select("id, business_name, logo_url").eq("id", userId).maybeSingle();
        if (businessError) {
            console.error("Error fetching business profile:", businessError);
            return {
                error: `Failed to fetch business profile: ${businessError.message}`
            };
        }
        if (businessProfile) {
            return {
                data: {
                    id: businessProfile.id,
                    name: businessProfile.business_name,
                    avatar_url: businessProfile.logo_url,
                    is_business: true
                }
            };
        }
        return {
            error: "User profile not found in either customer or business profiles."
        };
    } catch (e) {
        console.error("Exception in getUserProfileForReview:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
async function getUserProfilesForReviews(userIds) {
    if (!userIds || userIds.length === 0) {
        return {
            data: {}
        };
    }
    try {
        // Use the public view which only exposes safe data
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // Fetch customer profiles from public view
        const { data: customerProfiles, error: customerError } = await supabase.from("customer_profiles_public").select("id, name, avatar_url").in("id", userIds);
        if (customerError) {
            console.error("Error fetching customer profiles:", customerError);
            return {
                error: `Failed to fetch customer profiles: ${customerError.message}`
            };
        }
        // Fetch business profiles
        const { data: businessProfiles, error: businessError } = await supabase.from("business_profiles").select("id, business_name, logo_url").in("id", userIds);
        if (businessError) {
            console.error("Error fetching business profiles:", businessError);
            return {
                error: `Failed to fetch business profiles: ${businessError.message}`
            };
        }
        // Combine the results into a map of user ID to profile data
        const profilesMap = {};
        // Add customer profiles to the map
        customerProfiles?.forEach((profile)=>{
            profilesMap[profile.id] = {
                id: profile.id,
                name: profile.name,
                avatar_url: profile.avatar_url,
                is_business: false
            };
        });
        // Add business profiles to the map
        businessProfiles?.forEach((profile)=>{
            // Only add if not already in the map (customer profiles take precedence)
            if (!profilesMap[profile.id]) {
                profilesMap[profile.id] = {
                    id: profile.id,
                    name: profile.business_name,
                    avatar_url: profile.logo_url,
                    is_business: true
                };
            }
        });
        return {
            data: profilesMap
        };
    } catch (e) {
        console.error("Exception in getUserProfilesForReviews:", e);
        return {
            error: "An unexpected error occurred."
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getSecureCustomerProfileById,
    getSecureCustomerProfilesByIds,
    getAllSecureCustomerProfiles,
    checkUserCustomerProfileAccess,
    getUserProfileForReview,
    getUserProfilesForReviews
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureCustomerProfileById, "40b9559348aad6ea00a2f188b04b826d6850c7d6c2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getSecureCustomerProfilesByIds, "4018b299e3c1d222a05577f4321da12b0eb38a6a95", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getAllSecureCustomerProfiles, "00530736ed9e2f410981f81fb255a43707cbf4f1e5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(checkUserCustomerProfileAccess, "40b26f081e1434339b88122ebd092f8afcb78174c9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getUserProfileForReview, "409659f0cd54fd1490265212a1777a415ceaacda9d", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(getUserProfilesForReviews, "40e61d2e205375be89a0667f784a03f0f1723fa4d1", null);
}}),
"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899":"fetchBusinessReviews"},"",""] */ __turbopack_context__.s({
    "fetchBusinessReviews": (()=>fetchBusinessReviews)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/app-render/encryption.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
// revalidatePath is imported but not used in this file
// import { revalidatePath } from 'next/cache';
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$secureCustomerProfiles$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/secureCustomerProfiles.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-rsc] (ecmascript)");
;
;
;
;
async function fetchBusinessReviews(businessProfileId, page = 1, limit = 5, sortBy = "newest") {
    const _supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // Create admin client for secure operations
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
        // First, get the reviews with pagination and sorting
        let query = supabase.from("ratings_reviews").select("*", {
            count: "exact"
        }).eq("business_profile_id", businessProfileId)// Don't show reviews where the user is reviewing their own business
        .neq("user_id", businessProfileId);
        // Apply sorting
        switch(sortBy){
            case "oldest":
                query = query.order("created_at", {
                    ascending: true
                });
                break;
            case "highest_rating":
                query = query.order("rating", {
                    ascending: false
                });
                break;
            case "lowest_rating":
                query = query.order("rating", {
                    ascending: true
                });
                break;
            case "newest":
            default:
                query = query.order("created_at", {
                    ascending: false
                });
                break;
        }
        // Apply pagination
        const from = (page - 1) * limit;
        const to = from + limit - 1;
        query = query.range(from, to);
        // Execute the query
        const { data: reviewsData, error: reviewsError, count } = await query;
        if (reviewsError) {
            console.error("Error fetching reviews:", reviewsError);
            return {
                data: [],
                totalCount: 0,
                error: reviewsError.message
            };
        }
        // If no reviews, return empty array
        if (!reviewsData || reviewsData.length === 0) {
            return {
                data: [],
                totalCount: count || 0
            };
        }
        // Get all user IDs from the reviews
        const userIds = [
            ...new Set(reviewsData.map((review)=>review.user_id))
        ];
        // Use the secure method to fetch user profiles (both customer and business)
        const { data: profilesMap, error: profilesError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$secureCustomerProfiles$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getUserProfilesForReviews"])(userIds);
        if (profilesError) {
            console.error("Error fetching user profiles:", profilesError);
        // Continue without profiles
        }
        // Get business user IDs from the profiles
        const businessUserIds = userIds.filter((id)=>profilesMap?.[id]?.is_business);
        // Create a map of business IDs to their slugs
        let businessSlugMap = {};
        // Fetch business slugs for all business reviewers at once
        if (businessUserIds.length > 0) {
            const { data: businessSlugs } = await supabase.from("business_profiles").select("id, business_slug").in("id", businessUserIds);
            // Create a map of business IDs to their slugs
            if (businessSlugs) {
                businessSlugMap = businessSlugs.reduce((acc, business)=>{
                    acc[business.id] = business.business_slug;
                    return acc;
                }, {});
            }
        }
        // Process the reviews data with profile information
        const processedData = reviewsData.map((review)=>{
            const profile = profilesMap?.[review.user_id];
            const userProfile = profile ? {
                ...profile,
                business_slug: profile.is_business ? businessSlugMap[review.user_id] || null : null
            } : undefined;
            return {
                ...review,
                user_profile: userProfile
            };
        });
        return {
            data: processedData,
            totalCount: count || 0
        };
    } catch (error) {
        console.error("Unexpected error in fetchBusinessReviews:", error);
        const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred.";
        return {
            data: [],
            totalCount: 0,
            error: errorMessage
        };
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchBusinessReviews
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchBusinessReviews, "780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899", null);
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)", ACTIONS_MODULE6 => "[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE7 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE8 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE9 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript) <exports>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getCurrentUserBusinessProfileId"]),
    "00be04a805bc46661f0f4971fc802a16df9b7de3d1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfilesForSitemap"]),
    "401de3de75c96ed9402f56536c4055a2a1e481faa3": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesBySlug"]),
    "4050870674cfbc37404b048f17c031e582c6817414": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["likeBusiness"]),
    "406e7e1a2d209fd5068ed48a9b20838464c164486f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unlikeBusiness"]),
    "407591979af61a7d463be0dd2c1b3975f1e37c6d4d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileWithProductsBySlug"]),
    "407666f47811af7f53e12500216410b8cf9c72486c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["checkBusinessProfileAccess"]),
    "407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["unsubscribeFromBusiness"]),
    "40ab6a62d529134e66e01b1076bd34abc258d01f1a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImages"]),
    "40ac030879d2dd9afcc77d6d490fed42fed2873adc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["subscribeToBusiness"]),
    "40bf9c153311b3515e09fea919b70600d754f8d64c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["deleteReview"]),
    "40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getInteractionStatus"]),
    "40d99fa4a23448e324eb34055f899bfbe14f69c4a4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesForTab"]),
    "40e464278039bdd26313983f57189fe5ad16207de5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"]),
    "706494db815230f39fde096a9039730918baa8e0b5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileIdsForDiscover"]),
    "7097170cd67aa830da6c5c6a671c70d1f195006853": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["submitReview"]),
    "70b33159413534880b95c7d6a0075cc6c1d55e240a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesPaginated"]),
    "780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchBusinessReviews"]),
    "78aef494570fba887a7b263a064fef1e8968bf098e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfilesByLocation"]),
    "7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfilesForDiscover"]),
    "7e2ba45ccb00f66ac30c4be3781c321dc836b9feb2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fetchMoreProducts"]),
    "7f19088479bd4247b361efa63e1f08c59cbae5a424": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfiles"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)", ACTIONS_MODULE6 => "[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE7 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE8 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE9 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <locals>');
}}),
"[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => \"[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE2 => \"[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE3 => \"[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE4 => \"[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE5 => \"[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE6 => \"[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE7 => \"[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE8 => \"[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)\", ACTIONS_MODULE9 => \"[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00bcb7fbb12e893f3ec72c82ad13ff18ddb851bede"]),
    "00be04a805bc46661f0f4971fc802a16df9b7de3d1": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["00be04a805bc46661f0f4971fc802a16df9b7de3d1"]),
    "401de3de75c96ed9402f56536c4055a2a1e481faa3": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["401de3de75c96ed9402f56536c4055a2a1e481faa3"]),
    "4050870674cfbc37404b048f17c031e582c6817414": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["4050870674cfbc37404b048f17c031e582c6817414"]),
    "406e7e1a2d209fd5068ed48a9b20838464c164486f": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["406e7e1a2d209fd5068ed48a9b20838464c164486f"]),
    "407591979af61a7d463be0dd2c1b3975f1e37c6d4d": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["407591979af61a7d463be0dd2c1b3975f1e37c6d4d"]),
    "407666f47811af7f53e12500216410b8cf9c72486c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["407666f47811af7f53e12500216410b8cf9c72486c"]),
    "407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["407db44d489d66c014e8e2ad4a5dc13ddb96c9b3ff"]),
    "40ab6a62d529134e66e01b1076bd34abc258d01f1a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ab6a62d529134e66e01b1076bd34abc258d01f1a"]),
    "40ac030879d2dd9afcc77d6d490fed42fed2873adc": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40ac030879d2dd9afcc77d6d490fed42fed2873adc"]),
    "40bf9c153311b3515e09fea919b70600d754f8d64c": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40bf9c153311b3515e09fea919b70600d754f8d64c"]),
    "40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40c30b92b9bf64d2e37105d38ca4ee3d3f3d10cdca"]),
    "40d99fa4a23448e324eb34055f899bfbe14f69c4a4": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40d99fa4a23448e324eb34055f899bfbe14f69c4a4"]),
    "40e464278039bdd26313983f57189fe5ad16207de5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["40e464278039bdd26313983f57189fe5ad16207de5"]),
    "706494db815230f39fde096a9039730918baa8e0b5": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["706494db815230f39fde096a9039730918baa8e0b5"]),
    "7097170cd67aa830da6c5c6a671c70d1f195006853": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7097170cd67aa830da6c5c6a671c70d1f195006853"]),
    "70b33159413534880b95c7d6a0075cc6c1d55e240a": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["70b33159413534880b95c7d6a0075cc6c1d55e240a"]),
    "780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["780a7afa3a47ecf1914a4bcbb3b7439b80e6c70899"]),
    "78aef494570fba887a7b263a064fef1e8968bf098e": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["78aef494570fba887a7b263a064fef1e8968bf098e"]),
    "7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7cc9f62ccbdcd1bc4ed4f879f91c918806e0e8ca33"]),
    "7e2ba45ccb00f66ac30c4be3781c321dc836b9feb2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7e2ba45ccb00f66ac30c4be3781c321dc836b9feb2"]),
    "7f19088479bd4247b361efa63e1f08c59cbae5a424": (()=>__TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__["7f19088479bd4247b361efa63e1f08c59cbae5a424"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)", ACTIONS_MODULE6 => "[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE7 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE8 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE9 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <module evaluation>');
var __TURBOPACK__imported__module__$5b$project$5d2f2e$next$2d$internal$2f$server$2f$app$2f5b$cardSlug$5d2f$page$2f$actions$2e$js__$7b$__ACTIONS_MODULE0__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE1__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE2__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE3__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE4__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE5__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE6__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE7__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$interactions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE8__$3d3e$__$225b$project$5d2f$app$2f5b$cardSlug$5d2f$actions$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29222c$__ACTIONS_MODULE9__$3d3e$__$225b$project$5d2f$lib$2f$actions$2f$reviews$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$2922$__$7d$__$5b$app$2d$rsc$5d$__$28$server__actions__loader$2c$__ecmascript$29$__$3c$exports$3e$__ = __turbopack_context__.i('[project]/.next-internal/server/app/[cardSlug]/page/actions.js { ACTIONS_MODULE0 => "[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)", ACTIONS_MODULE1 => "[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE2 => "[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)", ACTIONS_MODULE3 => "[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)", ACTIONS_MODULE4 => "[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)", ACTIONS_MODULE5 => "[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)", ACTIONS_MODULE6 => "[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)", ACTIONS_MODULE7 => "[project]/lib/actions/interactions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE8 => "[project]/app/[cardSlug]/actions.ts [app-rsc] (ecmascript)", ACTIONS_MODULE9 => "[project]/lib/actions/reviews.ts [app-rsc] (ecmascript)" } [app-rsc] (server actions loader, ecmascript) <exports>');
}}),
"[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/favicon.ico.mjs { IMAGE => \"[project]/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/opengraph-image.png.mjs { IMAGE => \"[project]/app/opengraph-image.png (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/loading.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/loading.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/PublicCardPageClient.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/PublicCardPageClient.tsx <module evaluation>", "default");
}}),
"[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/PublicCardPageClient.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/PublicCardPageClient.tsx", "default");
}}),
"[project]/app/[cardSlug]/PublicCardPageClient.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/PublicCardPageClient.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/lib/actions/businessProfiles/types.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
;
}}),
"[project]/lib/actions/businessProfiles/utils.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applySorting": (()=>applySorting),
    "createSubscriptionMap": (()=>createSubscriptionMap),
    "getCurrentISOTimestamp": (()=>getCurrentISOTimestamp)
});
function applySorting(query, sortBy) {
    switch(sortBy){
        case "name_asc":
            return query.order("business_name", {
                ascending: true
            });
        case "name_desc":
            return query.order("business_name", {
                ascending: false
            });
        case "created_asc":
            return query.order("created_at", {
                ascending: true
            });
        case "created_desc":
            return query.order("created_at", {
                ascending: false
            });
        case "likes_asc":
            return query.order("total_likes", {
                ascending: true
            });
        case "likes_desc":
            return query.order("total_likes", {
                ascending: false
            });
        case "subscriptions_asc":
            return query.order("total_subscriptions", {
                ascending: true
            });
        case "subscriptions_desc":
            return query.order("total_subscriptions", {
                ascending: false
            });
        case "rating_asc":
            return query.order("average_rating", {
                ascending: true
            });
        case "rating_desc":
            return query.order("average_rating", {
                ascending: false
            });
        default:
            return query.order("created_at", {
                ascending: false
            });
    }
}
function getCurrentISOTimestamp() {
    return new Date().toISOString();
}
function createSubscriptionMap(subscriptionsData) {
    const subscriptionMap = new Map();
    if (subscriptionsData) {
        // Group by business_profile_id and take the most recent one
        subscriptionsData.forEach((sub)=>{
            if (!subscriptionMap.has(sub.business_profile_id)) {
                subscriptionMap.set(sub.business_profile_id, {
                    subscription_status: sub.subscription_status,
                    plan_id: sub.plan_id
                });
            }
        });
    }
    return subscriptionMap;
}
}}),
"[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Re-export types and utility functions
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/utils.ts [app-rsc] (ecmascript)");
// Re-export server actions
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
}}),
"[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$types$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/types.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$discovery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/discovery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$search$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/search.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$sitemap$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/sitemap.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$access$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/access.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$location$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/location.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <locals>");
}}),
"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx <module evaluation>", "default");
}}),
"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server-edge.js [app-rsc] (ecmascript)");
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2d$edge$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call the default export of [project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx", "default");
}}),
"[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$28$client__reference$2f$proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$28$client__reference$2f$proxy$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx (client reference/proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$28$client__reference$2f$proxy$29$__);
}}),
"[project]/app/[cardSlug]/page.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PublicCardPage),
    "generateMetadata": (()=>generateMetadata)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/PublicCardPageClient.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/businessProfiles/profileRetrieval.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/actions/gallery.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[cardSlug]/components/OfflineBusinessMessage.tsx [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
const INITIAL_PRODUCTS_PAGE_SIZE = 20;
// All users now have access to all features - no plan restrictions
// Helper function to determine if platform ads should be shown
const shouldShowPlatformAds = ()=>{
    // Show platform ads for all plans - Pro/Enterprise users can override with their own custom ads
    return true; // Always show platform ads as fallback
};
async function PublicCardPage({ params }) {
    const { cardSlug } = await params;
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["createClient"])();
    // Use the secure method to fetch the business profile
    const { data: businessProfile, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(cardSlug);
    if (profileError || !businessProfile) {
        console.error(`Error fetching profile for slug ${cardSlug}:`, profileError);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Check if the profile is online
    if (businessProfile.status !== "online") {
        console.log(`Business profile ${cardSlug} is not online (status: ${businessProfile.status}).`);
        // Show offline message instead of 404
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/app/[cardSlug]/page.tsx",
            lineNumber: 49,
            columnNumber: 12
        }, this);
    }
    // We no longer check subscription status, only if the business is online
    // The status field is the only thing that matters now
    // Check if required fields are missing but status is still online
    // Define the required fields directly here to avoid type issues
    const requiredFields = [
        "member_name",
        "title",
        "business_name",
        "phone",
        "address_line",
        "pincode",
        "city",
        "state",
        "locality",
        "contact_email"
    ];
    const missingRequiredFields = requiredFields.filter((field)=>!businessProfile[field] || String(businessProfile[field]).trim() === "");
    if (missingRequiredFields.length > 0 && businessProfile.status === "online") {
        console.log(`Business profile ${cardSlug} is missing required fields but status is online, updating to offline. Missing fields: ${missingRequiredFields.join(", ")}`);
        // Update the profile using API route
        try {
            await fetch(`${("TURBOPACK compile-time value", "http://localhost:3000")}/api/business/${businessProfile.id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    status: "offline"
                })
            });
        } catch (error) {
            console.error("Failed to update business profile status:", error);
        }
        // Show offline message instead of 404
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$components$2f$OfflineBusinessMessage$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
            fileName: "[project]/app/[cardSlug]/page.tsx",
            lineNumber: 98,
            columnNumber: 12
        }, this);
    }
    // We no longer check subscription status, only if the business is online
    // The status field is the only thing that matters now
    let topAdData = null;
    // Fetch platform ads for all businesses (Pro/Enterprise can override with their own custom ads)
    if (shouldShowPlatformAds()) {
        try {
            // First, check if the custom_ad_targets table exists (for backward compatibility)
            const { count, error: tableCheckError } = await supabase.from("custom_ad_targets").select("*", {
                count: "exact",
                head: true
            });
            // If the table exists and migration has been applied
            if (count !== null && !tableCheckError) {
                // Use the get_ad_for_pincode function to find the appropriate ad
                const pincode = businessProfile.pincode || "999999"; // Use a dummy pincode if none provided
                const { data: adData, error: adError } = await supabase.rpc("get_ad_for_pincode", {
                    target_pincode: pincode
                });
                if (adData && adData.length > 0) {
                    // Found an ad (either pincode-specific or global)
                    topAdData = {
                        type: "custom",
                        imageUrl: adData[0].ad_image_url,
                        linkUrl: adData[0].ad_link_url
                    };
                } else {
                    // No custom ads found or error occurred
                    if (adError) console.error(`Error fetching ad for pincode ${pincode}:`, adError);
                    topAdData = null; // Show placeholder when no custom ads are available
                }
            } else {
                // Fallback to old approach if migration hasn't been applied yet
                if (businessProfile.pincode) {
                    const { data: customAd } = await supabase.from("custom_ads").select("ad_image_url, ad_link_url").eq("is_active", true).or(`targeting_locations.eq.'"global"',targeting_locations.cs.'["${businessProfile.pincode}"]'`).order("created_at", {
                        ascending: false
                    }).limit(1).maybeSingle();
                    if (customAd) {
                        topAdData = {
                            type: "custom",
                            imageUrl: customAd.ad_image_url,
                            linkUrl: customAd.ad_link_url
                        };
                    } else {
                        // No matching custom ad found
                        topAdData = null;
                    }
                } else {
                    // If business has no pincode, try to find global ads
                    const { data: globalAd } = await supabase.from("custom_ads").select("ad_image_url, ad_link_url").eq("is_active", true).eq("targeting_locations", '"global"').order("created_at", {
                        ascending: false
                    }).limit(1).maybeSingle();
                    if (globalAd) {
                        topAdData = {
                            type: "custom",
                            imageUrl: globalAd.ad_image_url,
                            linkUrl: globalAd.ad_link_url
                        };
                    } else {
                        topAdData = null;
                    }
                }
            }
        } catch (adFetchError) {
            console.error(`Error fetching custom ad:`, adFetchError);
            topAdData = null; // fallback on error
        }
    }
    const defaultSortPreference = "created_desc";
    // Use the admin client to bypass RLS policies
    const productQuery = supabase.from("products_services").select("*", {
        count: "exact"
    }).eq("business_id", businessProfile.id).eq("is_available", true).order("created_at", {
        ascending: false
    }).limit(INITIAL_PRODUCTS_PAGE_SIZE);
    const { data: initialProducts, error: productsError, count: totalProductCount } = await productQuery;
    if (productsError) {
        console.error(`Error fetching initial products for business ${businessProfile.id}:`, productsError);
    // Consider if this should be a fatal error or just log
    }
    // Fetch gallery images for this business (limited to 4 for gallery tab)
    const { images: galleryImages, totalCount: galleryTotalCount, error: galleryError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$gallery$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getBusinessGalleryImagesForTab"])(cardSlug);
    if (galleryError) {
        console.error(`Error fetching gallery images for business ${businessProfile.id}:`, galleryError);
    // Don't fail the page load for gallery error, just log it
    }
    const { data: { user } } = await supabase.auth.getUser();
    const isAuthenticated = !!user;
    const currentUserId = user?.id || null;
    // Fetch total reviews count for the business (excluding self-reviews)
    const { count: totalReviews, error: reviewsCountError } = await supabase.from("ratings_reviews").select("id", {
        count: "exact",
        head: true
    }).eq("business_profile_id", businessProfile.id).neq("user_id", businessProfile.id); // Don't count self-reviews
    if (reviewsCountError) {
        console.error(`Error fetching reviews count for business ${businessProfile.id}:`, reviewsCountError);
    }
    // Create a new object with the total reviews count and ensure all required fields are properly typed
    const businessProfileWithReviews = {
        ...businessProfile,
        total_reviews: totalReviews || 0,
        // Ensure all required fields are properly typed and present
        phone: businessProfile.phone || "",
        city: businessProfile.city || "",
        state: businessProfile.state || "",
        pincode: businessProfile.pincode || "",
        locality: businessProfile.locality || "",
        address_line: businessProfile.address_line || "",
        business_name: businessProfile.business_name || "",
        contact_email: businessProfile.contact_email || "",
        member_name: businessProfile.member_name || "",
        status: businessProfile.status,
        title: businessProfile.title || "",
        business_category: businessProfile.business_category || "",
        // Custom branding and ads features removed - all users now have access to all features
        // custom_branding field removed - all users now have access to all features
        // custom_ads field removed - all users now have access to all features
        whatsapp_number: businessProfile.whatsapp_number || undefined,
        instagram_url: businessProfile.instagram_url || undefined,
        facebook_url: businessProfile.facebook_url || undefined,
        about_bio: businessProfile.about_bio || undefined,
        business_slug: businessProfile.business_slug || undefined,
        // theme_color field removed - using default styling
        delivery_info: businessProfile.delivery_info || undefined,
        total_likes: businessProfile.total_likes || 0,
        total_subscriptions: businessProfile.total_subscriptions || 0,
        average_rating: businessProfile.average_rating || undefined,
        business_hours: businessProfile.business_hours || null,
        // trial_end_date field removed - all users now have access to all features
        created_at: businessProfile.created_at || undefined,
        updated_at: businessProfile.updated_at || undefined,
        established_year: businessProfile.established_year || null
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen flex flex-col",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$cardSlug$5d2f$PublicCardPageClient$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"], {
            businessProfile: businessProfileWithReviews,
            initialProducts: initialProducts ?? [],
            totalProductCount: totalProductCount ?? 0,
            defaultSortPreference: defaultSortPreference,
            isAuthenticated: isAuthenticated,
            currentUserId: currentUserId,
            userPlan: "enterprise",
            topAdData: topAdData,
            galleryImages: galleryImages ?? [],
            galleryTotalCount: galleryTotalCount ?? 0
        }, void 0, false, {
            fileName: "[project]/app/[cardSlug]/page.tsx",
            lineNumber: 295,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/[cardSlug]/page.tsx",
        lineNumber: 294,
        columnNumber: 5
    }, this);
}
async function generateMetadata({ params }) {
    const { cardSlug } = await params;
    const siteUrl = ("TURBOPACK compile-time value", "http://localhost:3000") || "https://dukancard.in";
    const pageUrl = `${siteUrl}/${cardSlug}`;
    // Use the secure method to fetch the business profile
    const { data: businessProfile, error: profileError } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$actions$2f$businessProfiles$2f$profileRetrieval$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getSecureBusinessProfileBySlug"])(cardSlug);
    if (profileError || !businessProfile) {
        // Use notFound() to trigger the 404 page for non-existent business slugs
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    const businessName = businessProfile.business_name || "Business";
    // For offline businesses, only use the business name in the title
    // For online businesses, include the address
    let baseTitle = businessName;
    let fullAddress = "";
    if (businessProfile.status === "online") {
        // Create a complete address string from all address components
        const addressComponents = [
            businessProfile.address_line,
            businessProfile.city,
            businessProfile.state,
            businessProfile.pincode
        ].filter(Boolean);
        fullAddress = addressComponents.join(", ");
        // Create the SEO title with business name and address
        if (fullAddress) {
            baseTitle = `${businessName} - ${fullAddress}`;
        }
    }
    // Create different descriptions for online and offline businesses
    let description = "";
    if (businessProfile.status === "online") {
        description = `Visit ${businessName}'s digital business card on Dukancard. ${businessProfile.about_bio ? businessProfile.about_bio + " " : ""}Find products, services, contact info, and location${fullAddress ? ` at ${fullAddress}` : ""}.`.trim();
    } else {
        description = `${businessName}'s digital business card on Dukancard is currently offline. Check back later or discover other businesses nearby.`;
    }
    const ogImage = businessProfile.logo_url || `${siteUrl}/opengraph-image.png`;
    const keywords = [
        businessName,
        "",
        businessProfile?.city,
        businessProfile?.state,
        "digital business card",
        "online storefront",
        "shop near me",
        "Dukancard"
    ].filter(Boolean);
    const schema = {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        name: businessName,
        description: businessProfile.about_bio || description,
        url: pageUrl,
        image: businessProfile.logo_url,
        telephone: businessProfile?.phone,
        address: {
            "@type": "PostalAddress",
            streetAddress: businessProfile.address_line,
            addressLocality: businessProfile.city,
            addressRegion: businessProfile.state,
            postalCode: businessProfile.pincode,
            addressCountry: "IN"
        }
    };
    return {
        title: baseTitle,
        description,
        keywords: keywords.filter((k)=>k !== null),
        alternates: {
            canonical: `/${cardSlug}`
        },
        openGraph: {
            title: baseTitle,
            description,
            url: pageUrl,
            siteName: "Dukancard",
            type: "profile",
            locale: "en_IN",
            images: [
                {
                    url: ogImage,
                    alt: `${businessName} - Digital Business Card`
                }
            ]
        },
        twitter: {
            card: "summary_large_image",
            title: baseTitle,
            description,
            images: [
                ogImage
            ]
        },
        other: {
            "application-ld+json": JSON.stringify(schema)
        }
    };
}
}}),
"[project]/app/[cardSlug]/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/app/[cardSlug]/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a89eb7ad._.js.map