import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/utils/supabase/server";
import { applySecurityMiddleware } from "@/lib/middleware/security";
import { TABLES, COLUMNS } from "@/lib/supabase/constants";
import { deletePostMedia } from "@/lib/actions/shared/upload-post-media";
import { deleteCustomerPostMedia } from "@/lib/actions/shared/delete-customer-post-media";
import { z } from "zod";

// Validation schemas
const updatePostSchema = z.object({
  content: z.string().min(1, "Content is required").max(2000, "Content too long").optional(),
  image_url: z.string().url().optional().nullable(),
  product_ids: z.array(z.string()).optional(),
  mentioned_business_ids: z.array(z.string()).optional(),
});

/**
 * GET /api/posts/[postId] - Get a single post by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  try {
    // Apply security middleware (no HMAC required for public post viewing)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { postId } = await params;
    const supabase = await createClient();

    if (!postId) {
      return new NextResponse(JSON.stringify({ 
        error: "Post ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Fetch post from unified_posts view
    const { data: post, error } = await supabase
      .from('unified_posts')
      .select('*')
      .eq('id', postId)
      .single();

    if (error) {
      console.error("Error fetching post:", error);
      if (error.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ 
          error: "Post not found" 
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      return new NextResponse(JSON.stringify({ 
        error: "Failed to fetch post" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      post
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in post GET:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * PATCH /api/posts/[postId] - Update a post
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { user } = securityResult;
    const { postId } = await params;
    const supabase = await createClient();

    if (!postId) {
      return new NextResponse(JSON.stringify({ 
        error: "Post ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = updatePostSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid post data',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updateData = validation.data;

    // First, try to find the post in business_posts table
    const { data: businessPost, error: businessPostError } = await supabase
      .from(TABLES.BUSINESS_POSTS)
      .select(COLUMNS.ID)
      .eq(COLUMNS.ID, postId)
      .eq(COLUMNS.BUSINESS_ID, user.id)
      .single();

    let isBusinessPost = !businessPostError && businessPost;

    // If not found in business_posts, try customer_posts
    if (!isBusinessPost) {
      const { data: customerPost, error: customerPostError } = await supabase
        .from('customer_posts')
        .select('id')
        .eq('id', postId)
        .eq('customer_id', user.id)
        .single();

      if (customerPostError || !customerPost) {
        return new NextResponse(JSON.stringify({ 
          error: "Post not found or you don't have permission to update it" 
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Prepare update data based on post type
    const finalUpdateData = {
      ...(updateData.content && { [COLUMNS.CONTENT]: updateData.content }),
      ...(updateData.image_url !== undefined && { [COLUMNS.IMAGE_URL]: updateData.image_url }),
      [COLUMNS.UPDATED_AT]: new Date().toISOString(),
      // Only include business-specific fields for business posts
      ...(isBusinessPost && {
        ...(updateData.product_ids && { product_ids: updateData.product_ids }),
        ...(updateData.mentioned_business_ids && { mentioned_business_ids: updateData.mentioned_business_ids }),
      }),
      // Include customer-specific fields for customer posts
      ...(!isBusinessPost && {
        ...(updateData.mentioned_business_ids && { mentioned_business_ids: updateData.mentioned_business_ids }),
      }),
    };

    // Update the post
    const tableName = isBusinessPost ? TABLES.BUSINESS_POSTS : 'customer_posts';
    const { data: updatedPost, error: updateError } = await supabase
      .from(tableName)
      .update(finalUpdateData)
      .eq(COLUMNS.ID, postId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating post:", updateError);
      return new NextResponse(JSON.stringify({ 
        error: "Failed to update post" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      post: updatedPost,
      message: "Post updated successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in post PATCH:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * DELETE /api/posts/[postId] - Delete a post
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ postId: string }> }
) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { user } = securityResult;
    const { postId } = await params;
    const supabase = await createClient();

    if (!postId) {
      return new NextResponse(JSON.stringify({ 
        error: "Post ID is required" 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // First, try to find the post in business_posts table
    const { data: businessPost, error: businessPostError } = await supabase
      .from(TABLES.BUSINESS_POSTS)
      .select(`${COLUMNS.ID}, ${COLUMNS.CREATED_AT}, ${COLUMNS.IMAGE_URL}`)
      .eq(COLUMNS.ID, postId)
      .eq(COLUMNS.BUSINESS_ID, user.id)
      .single();

    let isBusinessPost = !businessPostError && businessPost;
    let postToDelete = businessPost;

    // If not found in business_posts, try customer_posts
    if (!isBusinessPost) {
      const { data: customerPost, error: customerPostError } = await supabase
        .from('customer_posts')
        .select('id, created_at, image_url')
        .eq('id', postId)
        .eq('customer_id', user.id)
        .single();

      if (customerPostError || !customerPost) {
        return new NextResponse(JSON.stringify({ 
          error: "Post not found or you don't have permission to delete it" 
        }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      postToDelete = customerPost;
    }

    // Delete associated media if exists
    if (postToDelete?.image_url) {
      try {
        if (isBusinessPost) {
          await deletePostMedia(postToDelete.image_url, postToDelete.created_at);
        } else {
          await deleteCustomerPostMedia(postToDelete.image_url, postToDelete.created_at);
        }
      } catch (mediaError) {
        console.error("Error deleting post media:", mediaError);
        // Continue with post deletion even if media deletion fails
      }
    }

    // Delete the post
    const tableName = isBusinessPost ? TABLES.BUSINESS_POSTS : 'customer_posts';
    const { error: deleteError } = await supabase
      .from(tableName)
      .delete()
      .eq(COLUMNS.ID, postId);

    if (deleteError) {
      console.error("Error deleting post:", deleteError);
      return new NextResponse(JSON.stringify({ 
        error: "Failed to delete post" 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      message: "Post deleted successfully"
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error("Unexpected error in post DELETE:", error);
    return new NextResponse(JSON.stringify({ 
      error: "Internal server error" 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
