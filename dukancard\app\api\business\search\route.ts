import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { createClient } from '@/utils/supabase/server';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

// Validation schema for search parameters
const searchBusinessSchema = z.object({
  q: z.string().min(1, "Search query is required"),
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(50)).optional(),
  category: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  status: z.enum(["online", "offline"]).optional(),
  sort_by: z.enum(["relevance", "name", "rating", "likes", "created_at"]).optional(),
  sort_order: z.enum(["asc", "desc"]).optional(),
});

/**
 * Security middleware wrapper for business API routes
 * For search, HMAC might be optional to allow public search
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = false) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_search',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. For search, JWT might be optional
  const token = extractBearerToken(req);
  let jwtPayload = null;
  
  if (token) {
    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
      return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    jwtPayload = jwtResult.payload;
  }

  // 3. Verify HMAC signature (if required)
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, true);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { jwtPayload };
}

/**
 * GET /api/business/search - Search business profiles
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (HMAC not required for public search)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = searchBusinessSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({
        error: 'Invalid search parameters',
        details: validation.error.issues,
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { 
      q: searchQuery, 
      page = 1, 
      limit = 20, 
      category, 
      city, 
      state, 
      status,
      sort_by = 'relevance',
      sort_order = 'desc'
    } = validation.data;

    const offset = (page - 1) * limit;

    const supabase = await createClient();

    // Build the search query
    let query = supabase
      .from('business_profiles')
      .select(`
        id, business_name, business_slug, logo_url, member_name, title,
        business_category, city, state, status, total_likes, total_subscriptions,
        average_rating, created_at, updated_at, about_bio
      `, { count: 'exact' });

    // Apply text search
    query = query.or(`business_name.ilike.%${searchQuery}%,member_name.ilike.%${searchQuery}%,business_category.ilike.%${searchQuery}%,about_bio.ilike.%${searchQuery}%`);

    // Apply filters
    if (category) {
      query = query.eq('business_category', category);
    }
    if (city) {
      query = query.eq('city', city);
    }
    if (state) {
      query = query.eq('state', state);
    }
    if (status) {
      query = query.eq('status', status);
    }

    // Apply sorting
    switch (sort_by) {
      case 'name':
        query = query.order('business_name', { ascending: sort_order === 'asc' });
        break;
      case 'rating':
        query = query.order('average_rating', { ascending: sort_order === 'asc' });
        break;
      case 'likes':
        query = query.order('total_likes', { ascending: sort_order === 'asc' });
        break;
      case 'created_at':
        query = query.order('created_at', { ascending: sort_order === 'asc' });
        break;
      case 'relevance':
      default:
        // For relevance, we'll order by a combination of factors
        // This is a simplified relevance scoring - in production you might want to use full-text search
        query = query.order('total_likes', { ascending: false })
                    .order('average_rating', { ascending: false });
        break;
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: businesses, error, count } = await query;

    if (error) {
      console.error('Error searching business profiles:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to search business profiles' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // For public search, filter out sensitive information
    const publicBusinesses = businesses?.map(business => {
      // Since contact_email is not selected, no need to filter it out
      return business;
    }) || [];

    return NextResponse.json({
      businesses: publicBusinesses,
      search: {
        query: searchQuery,
        total_results: count || 0,
      },
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
      },
      filters: {
        category,
        city,
        state,
        status,
      },
      sorting: {
        sort_by,
        sort_order,
      },
    });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/search:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
