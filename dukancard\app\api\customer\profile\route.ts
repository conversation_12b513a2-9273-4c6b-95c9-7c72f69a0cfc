import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { z } from 'zod';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';
import { 
  extractBearerToken, 
  verifyJWTToken 
} from '@/lib/middleware/jwt';
import { 
  verifyHMACMiddleware 
} from '@/lib/middleware/hmac';

/**
 * Security middleware wrapper for customer profile API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'customer_profile_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature if required
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, requireHMAC);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { success: true, jwtPayload: jwtResult.payload };
}

/**
 * GET /api/customer/profile - Get current user's customer profile
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for profile reading)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Fetch customer profile
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('*, latitude, longitude')
      .eq('id', jwtPayload.sub)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ error: 'Customer profile not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      console.error('Error fetching customer profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch customer profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      profile,
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in customer profile API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for updating customer profile
const updateCustomerProfileSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  phone: z.string().optional(),
  email: z.string().email().optional(),
  avatar_url: z.string().url().optional(),
  pincode: z.string().length(6).optional(),
  state: z.string().optional(),
  city: z.string().optional(),
  locality: z.string().optional(),
  address_line: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
});

/**
 * PATCH /api/customer/profile - Update current user's customer profile
 */
export async function PATCH(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = updateCustomerProfileSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request body',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updateData = validation.data;
    const supabase = await createClient();

    // Update the customer profile
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', jwtPayload.sub)
      .select()
      .single();

    if (error) {
      console.error('Error updating customer profile:', error);
      if (error.code === 'PGRST116') {
        return new NextResponse(JSON.stringify({ error: 'Customer profile not found' }), {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      return new NextResponse(JSON.stringify({ error: 'Failed to update customer profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      profile,
      message: 'Customer profile updated successfully',
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in update customer profile API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * POST /api/customer/profile - Create a new customer profile
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = updateCustomerProfileSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request body',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const profileData = validation.data;
    const supabase = await createClient();

    // Create the customer profile
    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .insert({
        ...profileData,
        id: jwtPayload.sub, // Use user ID from JWT
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating customer profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to create customer profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      profile,
      message: 'Customer profile created successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create customer profile API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
