(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root-of-the-server]__0ee7c35c._.js", {

"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[project]/lib/auth/middleware-jwt.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getJWTFromCookies": (()=>getJWTFromCookies),
    "hasRole": (()=>hasRole),
    "isAdmin": (()=>isAdmin),
    "validateJWTInMiddleware": (()=>validateJWTInMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/jsonwebtoken/index.js [middleware-edge] (ecmascript)");
;
function validateJWTInMiddleware(request) {
    try {
        const authHeader = request.headers.get('Authorization');
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        if (!token) {
            return null;
        }
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            console.error('JWT_SECRET is not defined in environment variables');
            return null;
        }
        // Verify and decode the JWT
        const decoded = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$jsonwebtoken$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["verify"])(token, jwtSecret);
        // Additional validation
        if (!decoded.user_id || !decoded.exp) {
            return null;
        }
        // Check if token is expired (should not happen as verify() checks this, but extra safety)
        const now = Math.floor(Date.now() / 1000);
        if (decoded.exp < now) {
            return null;
        }
        return decoded;
    } catch (_error) {
        // Token is invalid, expired, or malformed
        return null;
    }
}
function getJWTFromCookies(request) {
    try {
        const accessTokenCookie = request.cookies.get('accessToken');
        return accessTokenCookie?.value || null;
    } catch (_error) {
        return null;
    }
}
function hasRole(payload, role) {
    if (!payload || !payload.roles) {
        return false;
    }
    return payload.roles.includes(role);
}
function isAdmin(payload) {
    return hasRole(payload, 'admin');
}
}}),
"[project]/utils/supabase/service-role.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createServiceRoleClient": (()=>createServiceRoleClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [middleware-edge] (ecmascript) <locals>");
;
function createServiceRoleClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceRoleKey) {
        throw new Error('Missing required Supabase environment variables for service role');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceRoleKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
}
}}),
"[project]/utils/auth/jwt-middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "updateSessionWithJWT": (()=>updateSessionWithJWT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2f$middleware$2d$jwt$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/auth/middleware-jwt.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/service-role.ts [middleware-edge] (ecmascript)");
;
;
;
async function updateSessionWithJWT(request) {
    const { pathname } = request.nextUrl;
    // Define protected path prefixes
    const protectedPrefixes = [
        "/dashboard",
        "/onboarding",
        "/choose-role"
    ];
    // Check if the current path is a protected path
    const isProtectedRoute = protectedPrefixes.some((prefix)=>pathname.startsWith(prefix));
    // Try to get JWT from Authorization header (for API calls) or cookies (for web client)
    let jwtPayload = null;
    // First try Authorization header (for authenticated API requests)
    jwtPayload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2f$middleware$2d$jwt$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["validateJWTInMiddleware"])(request);
    // If no JWT in header, try cookies (for web client)
    if (!jwtPayload) {
        const accessTokenCookie = request.cookies.get('accessToken');
        if (accessTokenCookie) {
            // Create a temporary request with the cookie value in Authorization header
            const tempRequest = new Request(request.url, {
                headers: {
                    ...Object.fromEntries(request.headers.entries()),
                    'Authorization': `Bearer ${accessTokenCookie.value}`
                }
            });
            jwtPayload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$auth$2f$middleware$2d$jwt$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["validateJWTInMiddleware"])(tempRequest);
        }
    }
    // Check if the user just logged out
    const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';
    // === UNAUTHENTICATED USER LOGIC ===
    if (!jwtPayload) {
        // Redirect to login if user is not authenticated AND accessing a protected path
        if (isProtectedRoute) {
            const url = request.nextUrl.clone();
            url.pathname = "/login";
            url.searchParams.set("next", pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // === AUTHENTICATED USER LOGIC ===
    const userId = jwtPayload.user_id;
    try {
        // Query user profiles to determine user type and status
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["createServiceRoleClient"])();
        const [customerProfileRes, businessProfileRes] = await Promise.all([
            supabase.from("customer_profiles").select("id").eq("id", userId).maybeSingle(),
            supabase.from("business_profiles").select("id, business_slug").eq("id", userId).maybeSingle()
        ]);
        const customerProfile = customerProfileRes.data;
        const customerError = customerProfileRes.error;
        const businessProfile = businessProfileRes.data;
        const businessError = businessProfileRes.error;
        if (customerError || businessError) {
            // Allow request to proceed - profile fetch errors are not critical for middleware
            console.warn('Profile fetch error in JWT middleware:', {
                customerError,
                businessError
            });
        } else if (!customerProfile && !businessProfile) {
            // No profile found in either table - first time user
            // Allow access ONLY to the choose-role page OR the onboarding page
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname !== "/choose-role" && pathname !== "/onboarding" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/choose-role";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        } else {
            // Profile found - determine user type
            const userType = customerProfile ? "customer" : "business";
            // If business user hasn't completed onboarding (no slug), redirect to onboarding
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (userType === "business" && !businessProfile?.business_slug && pathname !== "/onboarding" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/onboarding";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // Redirect away from public auth pages if logged in and profile exists,
            // UNLESS they just logged out and are heading to the login page.
            if ((pathname === "/login" || pathname === "/choose-role") && !(pathname === "/login" && justLoggedOut)) {
                const redirectPath = userType === "business" ? "/dashboard/business" : "/dashboard/customer";
                const url = request.nextUrl.clone();
                url.pathname = redirectPath;
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // Redirect away from onboarding if user is a customer
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname === "/onboarding" && userType === "customer" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/customer";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // Protect dashboard routes based on user type
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname.startsWith("/dashboard/customer") && userType !== "customer" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/business";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            if (pathname.startsWith("/dashboard/business") && userType !== "business" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/customer";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        }
    } catch (error) {
        console.error('Error in JWT middleware profile check:', error);
    // On error, allow request to proceed but log the issue
    }
    // Create response with JWT payload attached (for downstream use)
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    // Add user info to response headers for server components to access
    response.headers.set('x-user-id', userId);
    response.headers.set('x-user-roles', JSON.stringify(jwtPayload.roles || []));
    return response;
}
}}),
"[project]/middleware.ts [middleware-edge] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/api/server.js [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$auth$2f$jwt$2d$middleware$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/auth/jwt-middleware.ts [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$ratelimit$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@upstash/ratelimit/dist/index.js [middleware-edge] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [middleware-edge] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [middleware-edge] (ecmascript) <locals>");
;
;
;
;
// Initialize Redis client for rate limiting only
const redisUrl = process.env.UPSTASH_REDIS_REST_URL;
const redisToken = process.env.UPSTASH_REDIS_REST_TOKEN;
if (!redisUrl || !redisToken) {
    console.error("Upstash Redis URL or Token is not defined in environment variables.");
}
const redis = redisUrl && redisToken ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Redis"]({
    url: redisUrl,
    token: redisToken
}) : null;
// Initialize Rate Limiter
const maxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "10");
const windowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || "10");
const ratelimit = redis ? new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$ratelimit$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Ratelimit"]({
    redis: redis,
    limiter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$ratelimit$2f$dist$2f$index$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["Ratelimit"].slidingWindow(maxRequests, `${windowSeconds} s`),
    analytics: true,
    prefix: "@upstash/ratelimit/dukancard"
}) : null;
async function middleware(request) {
    // --- Test Environment Bypass START ---
    // Check if we're in a test environment (Playwright E2E tests)
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || request.headers.get('x-playwright-testing') === 'true';
    if (isTestEnvironment) {
        // For test environment, we'll handle auth differently
        // This allows us to test business logic without real authentication
        return await handleTestEnvironment(request);
    }
    // --- Test Environment Bypass END ---
    // --- Domain and HTTPS Redirect Logic START ---
    const url = request.nextUrl.clone();
    const hostname = url.hostname;
    const protocol = url.protocol;
    // Only apply redirects in production environment and exclude development/testing domains
    const isDevelopmentDomain = hostname.includes('localhost') || hostname.includes('ngrok.io') || hostname.includes('ngrok-free.app') || hostname.includes('127.0.0.1');
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // --- Domain and HTTPS Redirect Logic END ---
    // --- Rate Limiting Logic START ---
    // Apply rate limiting to API routes only (skip webhooks)
    if (request.nextUrl.pathname.startsWith("/api/") && !request.nextUrl.pathname.startsWith("/api/webhooks/")) {
        // Skip rate limiting if Redis is not configured
        if (!ratelimit) {
            console.warn("Rate limiting skipped: Redis not configured");
        } else {
            // Get IP address: Check 'x-forwarded-for' header first, then fallback.
            const forwardedFor = request.headers.get('x-forwarded-for');
            // The header can contain multiple IPs (client, proxy1, proxy2). The client IP is usually the first one.
            const ip = forwardedFor ? forwardedFor.split(',')[0].trim() : "127.0.0.1";
            try {
                // Use Upstash rate limiting
                const { success, limit, remaining, reset } = await ratelimit.limit(ip);
                if (!success) {
                    // Rate limit exceeded, return 429
                    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"]("Too Many Requests", {
                        status: 429,
                        headers: {
                            "X-RateLimit-Limit": limit.toString(),
                            "X-RateLimit-Remaining": remaining.toString(),
                            "X-RateLimit-Reset": new Date(reset * 1000).toISOString()
                        }
                    });
                }
            } catch (error) {
                console.error("Rate limiting error:", error);
            // If rate limiting fails, allow the request to proceed
            }
        }
    }
    // --- Rate Limiting Logic END ---
    // Use JWT-based session validation instead of Supabase
    return await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$auth$2f$jwt$2d$middleware$2e$ts__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["updateSessionWithJWT"])(request);
}
/**
 * Handle requests in test environment
 * This replicates the complete Supabase middleware authentication flow
 * without making actual Supabase calls, allowing us to test business logic
 */ async function handleTestEnvironment(request) {
    const { pathname } = request.nextUrl;
    // Check for test authentication headers
    const testAuthState = request.headers.get('x-test-auth-state');
    const testUserType = request.headers.get('x-test-user-type');
    const testHasProfile = request.headers.get('x-test-has-profile') === 'true';
    const testBusinessSlug = request.headers.get('x-test-business-slug');
    const testPlanId = request.headers.get('x-test-plan-id') || 'free';
    // Define protected path prefixes (same as Supabase middleware)
    const protectedPrefixes = [
        "/dashboard",
        "/onboarding",
        "/choose-role"
    ];
    const isProtectedRoute = protectedPrefixes.some((prefix)=>pathname.startsWith(prefix));
    // Check if user just logged out (same logic as Supabase middleware)
    const justLoggedOut = request.nextUrl.searchParams.get('logged_out') === 'true';
    // === UNAUTHENTICATED USER LOGIC ===
    if (testAuthState === 'unauthenticated' || testAuthState !== 'authenticated') {
        // Redirect to login if user is not authenticated AND accessing a protected path
        if (isProtectedRoute) {
            const url = request.nextUrl.clone();
            url.pathname = "/login";
            url.searchParams.set("next", pathname);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
    }
    // === AUTHENTICATED USER LOGIC ===
    if (testAuthState === 'authenticated') {
        // === NO PROFILE LOGIC ===
        if (!testHasProfile) {
            // No profile found - first time user
            // Allow access ONLY to choose-role page OR onboarding page
            // EXCEPTION: If user just logged out, allow them to reach login page
            if (pathname !== "/choose-role" && pathname !== "/onboarding" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/choose-role";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // If already on choose-role or onboarding, allow the request
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
        }
        // === PROFILE EXISTS LOGIC ===
        if (testHasProfile && testUserType) {
            // === BUSINESS USER ONBOARDING CHECK ===
            // If business user hasn't completed onboarding (no slug), redirect to onboarding
            if (testUserType === "business" && !testBusinessSlug && pathname !== "/onboarding" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/onboarding";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // === FREE TIER FEATURE ACCESS CHECKS ===
            if (testUserType === "business") {
                // Free plan restrictions
                if (testPlanId === "free") {
                    // Check for analytics access
                    if (pathname.startsWith("/dashboard/business/analytics")) {
                        // Redirect free tier users away from analytics pages
                        const url = request.nextUrl.clone();
                        url.pathname = "/dashboard/business/plan";
                        url.searchParams.set("upgrade", "analytics");
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
                    }
                }
            }
            // === REDIRECT AWAY FROM AUTH PAGES ===
            // Redirect away from public auth pages if logged in and profile exists
            if ((pathname === "/login" || pathname === "/choose-role") && !(pathname === "/login" && justLoggedOut)) {
                const redirectPath = testUserType === "business" ? "/dashboard/business" : "/dashboard/customer";
                const url = request.nextUrl.clone();
                url.pathname = redirectPath;
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // === ONBOARDING REDIRECT FOR CUSTOMERS ===
            // Redirect away from onboarding if user is a customer
            if (pathname === "/onboarding" && testUserType === "customer" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/customer";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            // === DASHBOARD ROUTE PROTECTION ===
            // Protect dashboard routes based on user type
            if (pathname.startsWith("/dashboard/customer") && testUserType !== "customer" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/business";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
            if (pathname.startsWith("/dashboard/business") && testUserType !== "business" && !justLoggedOut) {
                const url = request.nextUrl.clone();
                url.pathname = "/dashboard/customer";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
            }
        }
    }
    // Default: allow request to proceed
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$2d$edge$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        /*
     * Match all request paths except forhe ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */ "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot-of-the-server%5D__0ee7c35c._.js.map