import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import ModernCustomerFeedList from '@/components/feed/ModernCustomerFeedList';
import { requireCompleteProfile } from '@/lib/actions/customerProfiles/addressValidation';
import { getUnifiedFeedPostsWithAuthors } from '@/lib/actions/posts/unifiedFeed';
import { createClient } from '@/utils/supabase/server';

export const metadata: Metadata = {
  title: "Feed",
  description: "View your personalized feed and stay updated",
};

export default async function CustomerDashboardPage() {
  const supabase = await createClient();

  // Check if user is authenticated
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    redirect('/login?message=Please log in to view your dashboard');
  }

  // Check if customer has complete address
  await requireCompleteProfile(user.id);

  // Get the user's customer profile
  const { data: customerProfile, error: profileError } = await supabase
    .from('customer_profiles')
    .select('name')
    .eq('id', user.id)
    .single();

  if (profileError) {
    console.error('Error fetching customer profile:', profileError);
  }

  // Get the user's city from subscribed businesses
  let citySlug: string | undefined;

  const { data: subscriptions } = await supabase
    .from('subscriptions')
    .select(`
      business_profiles (
        city_slug
      )
    `)
    .eq('user_id', user.id)
    .limit(1);

  if (subscriptions &&
      subscriptions.length > 0 &&
      subscriptions[0].business_profiles) {
    const profile = subscriptions[0].business_profiles;
    citySlug = typeof profile === 'object' && profile !== null && 'city_slug' in profile
      ? (profile.city_slug as string) || undefined
      : undefined;
  }

  // Use smart feed as the default filter
  const initialFilter = 'smart';

  // Get initial posts using the unified smart feed algorithm
  const initialFeedResult = await getUnifiedFeedPostsWithAuthors({
    filter: initialFilter,
    page: 1,
    limit: 10
  });

  const posts = initialFeedResult.success ? initialFeedResult.data?.items || [] : [];
  const hasMore = initialFeedResult.success ? initialFeedResult.data?.hasMore || false : false;

  if (!initialFeedResult.success) {
    console.error('Error fetching initial posts:', initialFeedResult.error);
  }

  const customerName = customerProfile?.name || "Valued Customer";

  return (
    <ModernCustomerFeedList
      initialPosts={posts}
      initialTotalCount={0} // Not needed for infinite scroll
      initialHasMore={hasMore}
      initialFilter={initialFilter}
      citySlug={citySlug}
      userName={customerName}
    />
  );
}
