import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { z } from 'zod';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';
import { 
  extractBearerToken, 
  verifyJWTToken 
} from '@/lib/middleware/jwt';
import { 
  verifyHMACMiddleware 
} from '@/lib/middleware/hmac';
import { TablesInsert } from '@/types/supabase';

/**
 * Security middleware wrapper for posts API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'posts_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature if required
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, requireHMAC);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { success: true, jwtPayload: jwtResult.payload };
}

// Schema for listing posts
const listPostsSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  filter: z.enum(["all", "smart", "subscribed"]).optional(),
  post_type: z.enum(["business", "customer", "all"]).optional(),
  city_slug: z.string().optional(),
  state_slug: z.string().optional(),
  locality_slug: z.string().optional(),
  pincode: z.string().optional(),
  business_id: z.string().optional(),
  customer_id: z.string().optional(),
});

/**
 * GET /api/posts - List posts with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for public post listing)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listPostsSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      page = 1,
      limit = 20,
      filter = "all",
      post_type = "all",
      city_slug,
      state_slug,
      locality_slug,
      pincode,
      business_id,
      customer_id
    } = validation.data;
    const offset = (page - 1) * limit;

    const supabase = await createClient();

    // Use unified_posts view for consistent post fetching
    let query = supabase
      .from('unified_posts')
      .select('*', { count: 'exact' });

    // Apply filters
    if (post_type !== "all") {
      query = query.eq('post_type', post_type);
    }

    if (business_id) {
      query = query.eq('business_id', business_id);
    }

    if (customer_id) {
      query = query.eq('customer_id', customer_id);
    }

    // Location filters
    if (city_slug) {
      query = query.eq('city_slug', city_slug);
    }
    if (state_slug) {
      query = query.eq('state_slug', state_slug);
    }
    if (locality_slug) {
      query = query.eq('locality_slug', locality_slug);
    }
    if (pincode) {
      query = query.eq('pincode', pincode);
    }

    // Apply smart filtering for authenticated users
    if (filter === "smart" && jwtPayload && jwtPayload.sub) {
      // Smart filter: prioritize posts from user's location and subscribed businesses
      // This would need additional logic based on user's location and subscriptions
    } else if (filter === "subscribed" && jwtPayload && jwtPayload.sub) {
      // Only show posts from businesses the user subscribes to
      const { data: subscriptions } = await supabase
        .from('subscriptions')
        .select('business_profile_id')
        .eq('user_id', jwtPayload.sub);

      if (subscriptions && subscriptions.length > 0) {
        const businessIds = subscriptions.map(sub => sub.business_profile_id);
        query = query.in('business_id', businessIds);
      } else {
        // No subscriptions, return empty result
        return new NextResponse(JSON.stringify({
          posts: [],
          pagination: {
            page,
            limit,
            total: 0,
            hasMore: false,
            nextPage: null,
          },
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Add sorting and pagination
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: posts, error, count } = await query;

    if (error) {
      console.error('Error fetching posts:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch posts' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const totalCount = count || 0;
    const hasMore = offset + limit < totalCount;
    const nextPage = hasMore ? page + 1 : null;

    return new NextResponse(JSON.stringify({
      posts: posts || [],
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
        nextPage,
      },
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in posts API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for creating posts
const createPostSchema = z.object({
  content: z.string().min(1).max(2000),
  image_url: z.string().url().optional(),
  images: z.array(z.string().url()).optional(),
  post_type: z.enum(["business", "customer"]),
  business_id: z.string().optional(), // Required for business posts
  customer_id: z.string().optional(), // Required for customer posts
});

/**
 * POST /api/posts - Create a new post
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = createPostSchema.safeParse(body);
    
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid request body',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const { content, image_url, images, post_type, business_id, customer_id } = validation.data;
    const supabase = await createClient();

    let postData;
    let tableName;

    if (post_type === "business") {
      // Verify user owns the business
      if (business_id !== jwtPayload.sub) {
        return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      tableName = 'business_posts';
      postData = {
        content,
        image_url,
        images,
        business_id: jwtPayload.sub,
      };
    } else {
      // Customer post
      if (customer_id !== jwtPayload.sub) {
        return new NextResponse(JSON.stringify({ error: 'Access denied' }), {
          status: 403,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      tableName = 'customer_posts';
      postData = {
        content,
        image_url,
        customer_id: jwtPayload.sub,
      };
    }

    // Create the post
    const { data: post, error } = await supabase
      .from(tableName as 'business_posts' | 'customer_posts')
      .insert(postData as TablesInsert<'business_posts'> | TablesInsert<'customer_posts'>)
      .select()
      .single();

    if (error) {
      console.error('Error creating post:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to create post' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return new NextResponse(JSON.stringify({
      post,
      message: 'Post created successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create post API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
