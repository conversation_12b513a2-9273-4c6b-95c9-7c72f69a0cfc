import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { z } from 'zod';
import {
  bruteForceProtectionMiddleware,
  getClientIP
} from '@/lib/middleware/bruteForceProtection';
import { handleBaseProductImageUpload } from '@/app/(dashboard)/dashboard/business/products/actions/imageHandlers';
import { 
  extractBearerToken, 
  verifyJWTToken 
} from '@/lib/middleware/jwt';
import { 
  verifyHMACMiddleware 
} from '@/lib/middleware/hmac';

/**
 * Security middleware wrapper for products API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'products_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: jwtResult.error }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature if required
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, requireHMAC);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { success: true, jwtPayload: jwtResult.payload };
}

// Schema for listing products
const listProductsSchema = z.object({
  page: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1)).optional(),
  limit: z.string().transform(val => parseInt(val, 10)).pipe(z.number().min(1).max(100)).optional(),
  search: z.string().optional(),
  category: z.string().optional(),
  business_id: z.string().optional(),
  product_type: z.enum(["physical", "service"]).optional(),
  is_available: z.string().transform(val => val === 'true').optional(),
  sort_by: z.enum(["name_asc", "name_desc", "created_asc", "created_desc", "price_asc", "price_desc"]).optional(),
  pincode: z.string().optional(),
  locality: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
});

/**
 * GET /api/products - List products with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware (no HMAC required for public product listing)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Parse and validate query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validation = listProductsSchema.safeParse(queryParams);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({ 
        error: 'Invalid query parameters',
        details: validation.error.errors 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      business_id,
      product_type,
      is_available = true,
      sort_by = "created_desc",
      pincode,
      locality,
      city,
      state
    } = validation.data;
    const offset = (page - 1) * limit;

    // Build query
    const supabase = await createClient();

    let query = supabase
      .from('products_services')
      .select(`
        id, business_id, name, description, base_price, discounted_price,
        product_type, is_available, image_url, images, featured_image_index,
        slug, created_at, updated_at,
        business_profiles!business_id(
          id, business_name, business_slug, city, state, pincode, locality,
          logo_url, status
        )
      `, { count: 'exact' })
      .eq('is_available', is_available);

    // Add filters
    if (search) {
      query = query.ilike('name', `%${search}%`);
    }
    if (business_id) {
      query = query.eq('business_id', business_id);
    }
    if (product_type) {
      query = query.eq('product_type', product_type);
    }

    // Location filters (filter by business location)
    if (pincode) {
      query = query.eq('business_profiles.pincode', pincode);
    }
    if (locality) {
      query = query.eq('business_profiles.locality', locality);
    }
    if (city) {
      query = query.eq('business_profiles.city', city);
    }
    if (state) {
      query = query.eq('business_profiles.state', state);
    }

    // Only show products from online businesses
    query = query.eq('business_profiles.status', 'online');

    // Add sorting
    const [sortField, sortDirection] = sort_by.split('_');
    const ascending = sortDirection === 'asc';
    
    let orderColumn = sortField;
    if (sortField === 'created') {
      orderColumn = 'created_at';
    } else if (sortField === 'price') {
      orderColumn = 'base_price';
    }

    query = query.order(orderColumn, { ascending });

    // Add pagination
    query = query.range(offset, offset + limit - 1);

    const { data: products, error, count } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch products' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const totalCount = count || 0;
    const hasMore = offset + limit < totalCount;
    const nextPage = hasMore ? page + 1 : null;

    return new NextResponse(JSON.stringify({
      products: products || [],
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore,
        nextPage,
      },
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in products API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

// Schema for creating products
const createProductSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  base_price: z.number().min(0),
  discounted_price: z.number().min(0).optional(),
  product_type: z.enum(["physical", "service"]),
  is_available: z.boolean().optional().default(true),
  image_url: z.string().url().optional(),
  images: z.array(z.string().url()).optional(),
  featured_image_index: z.number().min(0).optional(),
  slug: z.string().optional(),
});

// Schema for form data (when images are uploaded)
const createProductFormSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  base_price: z.string().transform(val => parseFloat(val)),
  discounted_price: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  product_type: z.enum(["physical", "service"]),
  is_available: z.string().optional().transform(val => val === 'true').default('true'),
  featured_image_index: z.string().optional().transform(val => val ? parseInt(val) : 0),
});

/**
 * POST /api/products - Create a new product with complete business logic
 */
export async function POST(req: NextRequest) {
  try {
    // Apply security middleware (HMAC required for mutations)
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();
    const userId = jwtPayload.sub;

    // Parse form data or JSON based on content type
    const contentType = req.headers.get('content-type') || '';
    let productData: any;
    let productImages: File[] = [];
    let featuredImageIndex = 0;

    if (contentType.includes('multipart/form-data')) {
      // Handle form data with file uploads
      const formData = await req.formData();

      // Extract product data
      productData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string || '',
        base_price: parseFloat(formData.get('base_price') as string),
        discounted_price: formData.get('discounted_price') ? parseFloat(formData.get('discounted_price') as string) : null,
        product_type: formData.get('product_type') as 'physical' | 'service',
        is_available: formData.get('is_available') === 'true',
      };

      // Extract images
      const imageFiles = formData.getAll('images') as File[];
      productImages = imageFiles.filter(file => file.size > 0);
      featuredImageIndex = formData.get('featured_image_index') ? parseInt(formData.get('featured_image_index') as string) : 0;

      // Validate form data
      const validation = createProductFormSchema.safeParse({
        ...productData,
        base_price: formData.get('base_price') as string,
        discounted_price: formData.get('discounted_price') as string,
        is_available: formData.get('is_available') as string,
        featured_image_index: formData.get('featured_image_index') as string,
      });

      if (!validation.success) {
        return new NextResponse(JSON.stringify({
          error: 'Invalid product data',
          details: validation.error.errors
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    } else {
      // Handle JSON data
      productData = await req.json();
      const validation = createProductSchema.safeParse(productData);

      if (!validation.success) {
        return new NextResponse(JSON.stringify({
          error: 'Invalid product data',
          details: validation.error.errors
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // 1. Insert the product initially without image URLs
    const initialProductData = {
      business_id: userId,
      name: productData.name,
      description: productData.description || '',
      base_price: productData.base_price,
      discounted_price: productData.discounted_price || null,
      product_type: productData.product_type,
      is_available: productData.is_available ?? true,
      image_url: null, // Will be updated after image upload
      images: [], // Will be updated after image upload
      featured_image_index: 0,
    };

    const { data: insertedProduct, error: insertError } = await supabase
      .from('products_services')
      .insert(initialProductData)
      .select('id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug')
      .single();

    if (insertError || !insertedProduct) {
      console.error('Error creating product:', insertError);

      // Check if this is a product limit error from the database trigger
      if (insertError?.message?.includes('Cannot make product available') &&
          insertError?.message?.includes('reached the limit')) {
        return new NextResponse(JSON.stringify({
          error: insertError.message
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        });
      }

      return new NextResponse(JSON.stringify({
        error: 'Failed to create product'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const productId = insertedProduct.id;
    let finalProduct = insertedProduct;

    // 2. Handle Image Upload if images were provided
    if (productImages.length > 0) {
      console.log('Starting image upload for product:', productId);

      try {
        // Use the base product image upload function
        const uploadResult = await handleBaseProductImageUpload(
          userId,
          productId,
          productImages
        );

        if (uploadResult.error) {
          console.error(`Image upload failed: ${uploadResult.error}`);
          return new NextResponse(JSON.stringify({
            error: `Product created, but image upload failed: ${uploadResult.error}`
          }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          });
        }

        const finalImages = uploadResult.urls;

        // Set the featured image URL as the main image_url
        if (finalImages.length > 0) {
          const featuredIdx = Math.min(featuredImageIndex, finalImages.length - 1);
          const finalImageUrl = finalImages[featuredIdx];

          // Update the product with image URLs
          const updateData = {
            image_url: finalImageUrl,
            images: finalImages.filter((image): image is string => image !== null),
            featured_image_index: featuredIdx
          };

          const { data: updatedProduct, error: updateError } = await supabase
            .from('products_services')
            .update(updateData)
            .eq('id', productId)
            .select('id, product_type, name, description, base_price, discounted_price, is_available, image_url, images, featured_image_index, created_at, updated_at, slug')
            .single();

          if (updateError) {
            console.error(`Failed to update product with image URLs: ${updateError.message}`);
            return new NextResponse(JSON.stringify({
              error: `Product created, images uploaded, but failed to save URLs: ${updateError.message}`
            }), {
              status: 500,
              headers: { 'Content-Type': 'application/json' },
            });
          }

          finalProduct = updatedProduct || finalProduct;
        }
      } catch (imageError) {
        console.error('Unexpected error during image upload:', imageError);
        return new NextResponse(JSON.stringify({
          error: `Product created, but image processing failed: ${imageError instanceof Error ? imageError.message : 'Unknown error'}`
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    return new NextResponse(JSON.stringify({
      product: finalProduct,
      message: 'Product created successfully',
    }), {
      status: 201,
      headers: { 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Unexpected error in create product API:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
