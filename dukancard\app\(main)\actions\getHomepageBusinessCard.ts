"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

// Custom branding and ads features removed - all users now have access to all features

/**
 * Get the current user's business card data for homepage display
 * Returns null if user is not authenticated or not a business user
 */
export async function getHomepageBusinessCard(): Promise<{
  data?: BusinessCardData;
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Get the current session
    const {
      data: { session },
      error: authError,
    } = await supabase.auth.getSession();

    if (authError || !session) {
      // Not authenticated - this is expected for homepage, so no error
      return { data: undefined };
    }

    // Use the business profile API endpoint to get current user's profile
    // Pass the JWT token for authentication
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
      },
    });

    if (!response.ok) {
      if (response.status === 404) {
        // User doesn't have a business profile - this is expected
        return { data: undefined };
      }

      const result = await response.json();
      console.error("Error fetching business profile for homepage:", result);
      return { error: `Failed to fetch profile: ${result.error}` };
    }

    const result = await response.json();
    const businessProfile = result.business;

    if (!businessProfile) {
      // User is not a business user - this is expected, so no error
      return { data: undefined };
    }

    // Map the data to BusinessCardData format (simplified for free platform)
    const mappedData: BusinessCardData = {
      id: businessProfile.id,
      business_name: businessProfile.business_name ?? "",
      contact_email: businessProfile.contact_email ?? "",
      created_at: businessProfile.created_at ?? undefined,
      updated_at: businessProfile.updated_at ?? undefined,
      logo_url: businessProfile.logo_url ?? "",
      member_name: businessProfile.member_name ?? "",
      title: businessProfile.title ?? "",
      address_line: businessProfile.address_line ?? "",
      city: businessProfile.city ?? "",
      state: businessProfile.state ?? "",
      pincode: businessProfile.pincode ?? "",
      locality: businessProfile.locality ?? "",
      phone: businessProfile.phone ?? "",
      instagram_url: businessProfile.instagram_url ?? "",
      facebook_url: businessProfile.facebook_url ?? "",
      whatsapp_number: businessProfile.whatsapp_number ?? "",
      about_bio: businessProfile.about_bio ?? "",
      theme_color: "#3b82f6", // Default theme color since custom themes removed
      delivery_info: businessProfile.delivery_info ?? "",
      business_hours: businessProfile.business_hours,
      business_category: businessProfile.business_category ?? "",

      established_year: businessProfile.established_year ?? null,
      status: (businessProfile.status as "online" | "offline") ?? "offline",
      business_slug: businessProfile.business_slug ?? "",
      total_likes: businessProfile.total_likes ?? 0,
      total_subscriptions: businessProfile.total_subscriptions ?? 0,
      average_rating: businessProfile.average_rating ?? 0,
      // total_visits field removed from database
    };

    return { data: mappedData };
  } catch (error) {
    console.error("Unexpected error in getHomepageBusinessCard:", error);
    return { error: "An unexpected error occurred" };
  }
}

/**
 * Get user authentication status and type for homepage
 */
export async function getHomepageUserInfo(): Promise<{
  isAuthenticated: boolean;
  userType: "business" | "customer" | null;
  error?: string;
}> {
  try {
    const supabase = await createClient();

    // Get the current user
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();

    if (authError || !user) {
      return { isAuthenticated: false, userType: null };
    }

    // Check user type by looking at both profile tables
    const [customerRes, businessRes] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle(),
      supabase
        .from("business_profiles")
        .select("id")
        .eq("id", user.id)
        .maybeSingle(),
    ]);

    if (customerRes.error || businessRes.error) {
      console.error("Error checking user type:", customerRes.error || businessRes.error);
      return {
        isAuthenticated: true,
        userType: null,
        error: "Error determining user type"
      };
    }

    let userType: "business" | "customer" | null = null;
    if (customerRes.data) {
      userType = "customer";
    } else if (businessRes.data) {
      userType = "business";
    }

    return { isAuthenticated: true, userType };
  } catch (error) {
    console.error("Unexpected error in getHomepageUserInfo:", error);
    return {
      isAuthenticated: false,
      userType: null,
      error: "An unexpected error occurred"
    };
  }
}
