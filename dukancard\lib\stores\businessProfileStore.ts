import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { authenticatedApiCall } from '@/lib/utils/authenticatedFetch';
import { Tables, TablesInsert, TablesUpdate } from '@/types/supabase';

// Use centralized Supabase types
export type BusinessProfile = Tables<'business_profiles'> & {
  // Optional related data that might be included in API responses
  products_services?: Tables<'products_services'>[];
  gallery?: unknown[];
};

export type BusinessProfileInsert = TablesInsert<'business_profiles'>;
export type BusinessProfileUpdate = TablesUpdate<'business_profiles'>;

// Simplified types for the store
export type BusinessProfileListItem = Pick<BusinessProfile,
  'id' | 'business_name' | 'business_slug' | 'logo_url' | 'member_name' |
  'title' | 'business_category' | 'city' | 'state' | 'status' |
  'total_likes' | 'total_subscriptions' | 'average_rating' | 'created_at' | 'updated_at'
>;

export interface BusinessProfileSearchParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  city?: string;
  status?: 'online' | 'offline';
  sort_by?: 'relevance' | 'name' | 'rating' | 'likes' | 'created_at';
  sort_order?: 'asc' | 'desc';
}

interface BusinessProfileState {
  // Current data
  currentUserProfile: BusinessProfile | null;
  profiles: BusinessProfileListItem[];

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;

  // Error states
  error: string | null;

  // Actions
  getCurrentUserProfile: (_options?: { includeProducts?: boolean; includeGallery?: boolean; includeMetrics?: boolean; forceRefresh?: boolean }) => Promise<BusinessProfile | null>;
  getProfile: (_id: string, _options?: { includeProducts?: boolean; includeGallery?: boolean }) => Promise<BusinessProfile | null>;
  getProfileBySlug: (_slug: string, _options?: { includeProducts?: boolean; includeGallery?: boolean }) => Promise<BusinessProfile | null>;
  getProfiles: (_params?: BusinessProfileSearchParams) => Promise<BusinessProfileListItem[]>;
  searchProfiles: (_query: string, _params?: Omit<BusinessProfileSearchParams, 'search'>) => Promise<BusinessProfileListItem[]>;
  createProfile: (_data: BusinessProfileInsert) => Promise<BusinessProfile | null>;
  updateProfile: (_id: string, _data: BusinessProfileUpdate) => Promise<BusinessProfile | null>;
  deleteProfile: (_id: string) => Promise<boolean>;

  // State management
  setCurrentUserProfile: (_profile: BusinessProfile | null) => void;
  clearError: () => void;
}

export const useBusinessProfileStore = create<BusinessProfileState>()(
  devtools((set, get) => ({
    // Initial state
    currentUserProfile: null,
    profiles: [],
    isLoading: false,
    isCreating: false,
    isUpdating: false,
    isDeleting: false,
    error: null,

    // Get current user's profile
    getCurrentUserProfile: async (options = {}) => {
      const { includeProducts = false, includeGallery = false, includeMetrics = false } = options;

      set({ isLoading: true, error: null });

      try {
        const queryParams = new URLSearchParams();
        if (includeProducts) queryParams.set('include_products', 'true');
        if (includeGallery) queryParams.set('include_gallery', 'true');
        if (includeMetrics) queryParams.set('include_metrics', 'true');

        const url = `/api/business/me${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await authenticatedApiCall<{ business: BusinessProfile }>(url);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch current user business profile');
        }

        const profile = response.data.business;
        set({ currentUserProfile: profile, isLoading: false });
        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch current user business profile';
        set({ error: errorMessage, isLoading: false });
        return null;
      }
    },

    // Get profile by ID
    getProfile: async (id: string, options = {}) => {
      const { includeProducts = false, includeGallery = false } = options;

      set({ isLoading: true, error: null });

      try {
        const queryParams = new URLSearchParams();
        if (includeProducts) queryParams.set('include_products', 'true');
        if (includeGallery) queryParams.set('include_gallery', 'true');

        const url = `/api/business/${id}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await authenticatedApiCall<{ business: BusinessProfile }>(url);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch business profile');
        }

        const profile = response.data.business;
        set({ isLoading: false });
        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch business profile';
        set({ error: errorMessage, isLoading: false });
        return null;
      }
    },

    // Get profile by slug
    getProfileBySlug: async (slug: string, options = {}) => {
      const { includeProducts = false, includeGallery = false } = options;

      set({ isLoading: true, error: null });

      try {
        const queryParams = new URLSearchParams();
        if (includeProducts) queryParams.set('include_products', 'true');
        if (includeGallery) queryParams.set('include_gallery', 'true');

        const url = `/api/business/slug/${slug}${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
        const response = await authenticatedApiCall<{ business: BusinessProfile }>(url);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch business profile');
        }

        const profile = response.data.business;
        set({ isLoading: false });
        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch business profile';
        set({ error: errorMessage, isLoading: false });
        return null;
      }
    },

    // Get list of profiles
    getProfiles: async (params = {}) => {
      set({ isLoading: true, error: null });

      try {
        const queryParams = new URLSearchParams();
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.set(key, value.toString());
          }
        });

        const url = `/api/business${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

        const response = await authenticatedApiCall<{
          businesses: BusinessProfileListItem[];
          pagination: Pagination;
        }>(url);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to fetch business profiles');
        }

        const { businesses } = response.data;
        set({ profiles: businesses, isLoading: false });
        return businesses;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch business profiles';
        set({ error: errorMessage, isLoading: false });
        return [];
      }
    },

    // Search profiles
    searchProfiles: async (query: string, params = {}) => {
      set({ isLoading: true, error: null });

      try {
        const queryParams = new URLSearchParams();
        queryParams.set('q', query);
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            queryParams.set(key, value.toString());
          }
        });

        const url = `/api/business/search?${queryParams.toString()}`;
interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

        const response = await authenticatedApiCall<{
          businesses: BusinessProfileListItem[];
          pagination: Pagination;
        }>(url);

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to search business profiles');
        }

        const { businesses } = response.data;
        set({ profiles: businesses, isLoading: false });
        return businesses;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to search business profiles';
        set({ error: errorMessage, isLoading: false });
        return [];
      }
    },

    // Create new profile
    createProfile: async (data: BusinessProfileInsert) => {
      set({ isCreating: true, error: null });

      try {
        const response = await authenticatedApiCall<{
          business: BusinessProfile;
          message: string;
        }>('/api/business', {
          method: 'POST',
          body: JSON.stringify(data),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to create business profile');
        }

        const profile = response.data.business;
        set({ currentUserProfile: profile, isCreating: false });
        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create business profile';
        set({ error: errorMessage, isCreating: false });
        return null;
      }
    },

    // Update profile
    updateProfile: async (id: string, data: BusinessProfileUpdate) => {
      set({ isUpdating: true, error: null });

      try {
        const response = await authenticatedApiCall<{
          business: BusinessProfile;
          message: string;
        }>(`/api/business/${id}`, {
          method: 'PATCH',
          body: JSON.stringify(data),
        });

        if (!response.success || !response.data) {
          throw new Error(response.error || 'Failed to update business profile');
        }

        const profile = response.data.business;

        // Update current user profile if it's the same
        const currentProfile = get().currentUserProfile;
        if (currentProfile?.id === id) {
          set({ currentUserProfile: profile });
        }

        set({ isUpdating: false });
        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update business profile';
        set({ error: errorMessage, isUpdating: false });
        return null;
      }
    },

    // Delete profile
    deleteProfile: async (id: string) => {
      set({ isDeleting: true, error: null });

      try {
        const response = await authenticatedApiCall<{ message: string }>(`/api/business/${id}`, {
          method: 'DELETE',
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to delete business profile');
        }

        // Clear current user profile if it's the same
        const currentProfile = get().currentUserProfile;
        if (currentProfile?.id === id) {
          set({ currentUserProfile: null });
        }

        set({ isDeleting: false });
        return true;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete business profile';
        set({ error: errorMessage, isDeleting: false });
        return false;
      }
    },

    // Set current user profile
    setCurrentUserProfile: (profile: BusinessProfile | null) => {
      set({ currentUserProfile: profile });
    },

    // Clear error
    clearError: () => {
      set({ error: null });
    },
  }), {
    name: 'business-profile-store',
  })
);