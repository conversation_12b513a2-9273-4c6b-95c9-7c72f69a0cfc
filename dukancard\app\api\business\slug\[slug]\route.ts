import { NextRequest, NextResponse } from 'next/server';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { createClient } from '@/utils/supabase/server';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

/**
 * Security middleware wrapper for business API routes
 * For public routes like slug lookup, HMAC might be optional
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = false) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. For routes that don't require authentication, JWT might be optional
  const token = extractBearerToken(req);
  let jwtPayload = null;
  
  if (token) {
    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
      return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }
    jwtPayload = jwtResult.payload;
  }

  // 3. Verify HMAC signature (if required)
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, true);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { jwtPayload };
}

/**
 * GET /api/business/slug/[slug] - Get a business profile by slug
 * This endpoint supports both authenticated and public access
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;

    // Apply security middleware (HMAC not required for public access)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    // Validate slug format
    if (!slug || typeof slug !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid business slug' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Parse query parameters for additional data
    const url = new URL(req.url);
    const includeProducts = url.searchParams.get('include_products') === 'true';
    const includeGallery = url.searchParams.get('include_gallery') === 'true';

    // Build the select query based on what's requested
    let selectQuery = `
      id, business_name, business_slug, contact_email, member_name, title,
      business_category, phone, whatsapp_number, address_line, city, state,
      pincode, locality, about_bio, status, logo_url, instagram_url,
      facebook_url, established_year, delivery_info, business_hours,
      latitude, longitude, total_likes, total_subscriptions, average_rating,
      created_at, updated_at
    `;

    // Add products if requested
    if (includeProducts) {
      selectQuery += `, products_services (
        id, name, description, base_price, discounted_price, is_available, 
        image_url, images, featured_image_index, product_type, slug, created_at, updated_at
      )`;
    }

    // Add gallery if requested
    if (includeGallery) {
      selectQuery += `, gallery`;
    }

    // Fetch business profile
    const { data: business, error } = await supabase
      .from('business_profiles')
      .select(selectQuery)
      .eq('business_slug', slug)
      .maybeSingle();

    if (error) {
      console.error('Error fetching business profile by slug:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!business) {
      return new NextResponse(JSON.stringify({ error: 'Business profile not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

        const businessData = business as Tables<"business_profiles">;
    const isOwner = jwtPayload && businessData && jwtPayload.user_id === businessData.id;

    if (!isOwner && businessData && businessData.contact_email) {
      // Remove sensitive fields for non-owners
      const { contact_email: _contact_email, ...publicBusiness } = businessData;
      return NextResponse.json({ business: publicBusiness });
    }

    return NextResponse.json({ business });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/slug/[slug]:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
