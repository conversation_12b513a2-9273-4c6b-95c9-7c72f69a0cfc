import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { getClientIP, bruteForceProtectionMiddleware } from '@/lib/middleware/bruteForceProtection';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';

/**
 * Security middleware wrapper for business API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtPayload = jwtResult.payload;

  if (!jwtPayload) {
    return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // 3. Verify HMAC signature (if required)
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, true);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { jwtPayload };
}


/**
 * GET /api/business/me - Get the current user's business profile
 */
export async function GET(req: NextRequest) {
  try {
    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    const supabase = await createClient();

    // Parse query parameters for additional data
    const url = new URL(req.url);
    const includeProducts = url.searchParams.get('include_products') === 'true';
    const includeGallery = url.searchParams.get('include_gallery') === 'true';
    const includeMetrics = url.searchParams.get('include_metrics') === 'true';

    // Build the select query based on what's requested
    let selectQuery = `
      id, business_name, business_slug, contact_email, member_name, title,
      business_category, phone, whatsapp_number, address_line, city, state,
      pincode, locality, about_bio, status, logo_url, instagram_url,
      facebook_url, established_year, delivery_info, business_hours,
      latitude, longitude, created_at, updated_at
    `;

    // Add metrics if requested
    if (includeMetrics) {
      selectQuery += `, total_likes, total_subscriptions, average_rating, total_visits, today_visits, yesterday_visits`;
    }

    // Add products if requested
    if (includeProducts) {
      selectQuery += `, products_services (
        id, name, description, base_price, discounted_price, is_available, 
        image_url, images, featured_image_index, product_type, slug, created_at, updated_at
      )`;
    }

    // Add gallery if requested
    if (includeGallery) {
      selectQuery += `, gallery`;
    }

    // Fetch the user's business profile
    const { data: business, error } = await supabase
      .from('business_profiles')
      .select(selectQuery)
      .eq('id', jwtPayload.user_id)
      .maybeSingle();

    if (error) {
      console.error('Error fetching user business profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!business) {
      return new NextResponse(JSON.stringify({ error: 'Business profile not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({ business });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/me:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
