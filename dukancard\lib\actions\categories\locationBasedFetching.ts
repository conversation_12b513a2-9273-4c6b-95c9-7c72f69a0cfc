"use server";

import { createClient } from "@/utils/supabase/client";
import { BusinessProfilePublicData, BusinessSortBy } from "../businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";
import { Tables } from "@/types/supabase";

// Type for location parameters
export type LocationParams = {
  categoryName: string;
  state?: string | null;
  city?: string | null;
  pincode?: string | null;
  locality?: string | null;
  stateSlug?: string | null;
  citySlug?: string | null;
  localitySlug?: string | null;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  businessName?: string | null;
};

// Type for product location parameters
export type ProductLocationParams = LocationParams & {
  productType?: "physical" | "service" | null;
  productName?: string | null;
  productSortBy?: string;
};

/**
 * Unified function to fetch businesses based on location parameters
 */
export async function fetchBusinessesByLocation(params: LocationParams): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  const {
    categoryName,
    state,
    city,
    pincode,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    businessName
  } = params;

  console.log(`fetchBusinessesByLocation called with pincode: ${pincode}, city: ${city}, state: ${state}, locality: ${locality}, stateSlug: ${stateSlug}, citySlug: ${citySlug}, localitySlug: ${localitySlug}`);

  try {
    // Build query parameters for the API call
    const queryParams = new URLSearchParams();
    queryParams.set('status', 'online');
    queryParams.set('category', categoryName);
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());

    // Add location filters if provided
    if (pincode) {
      queryParams.set('pincode', pincode);
    }

    // Prefer slug-based filtering when available
    if (stateSlug) {
      queryParams.set('state_slug', stateSlug);
    } else if (state) {
      queryParams.set('state', state);
    }

    if (citySlug) {
      queryParams.set('city_slug', citySlug);
    } else if (city) {
      queryParams.set('city', city);
    }

    if (localitySlug) {
      queryParams.set('locality_slug', localitySlug);
    } else if (locality) {
      console.log(`Filtering businesses by exact locality: "${locality}"`);
      queryParams.set('locality', locality);
    }

    // Add business name search if provided
    if (businessName) {
      queryParams.set('search', businessName);
    }

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
      default:
        apiSortBy = 'created_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch businesses",
      };
    }

    const businesses = result.businesses || [];
    const pagination = result.pagination || {};
    const totalCount = pagination.total || 0;

    return {
      data: businesses as BusinessProfilePublicData[],
      count: totalCount
    };
  } catch (error) {
    console.error("Unexpected error in fetchBusinessesByLocation:", error);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Unified function to fetch products based on location parameters
 */
export async function fetchProductsByLocation(params: ProductLocationParams): Promise<{
  data?: NearbyProduct[];
  count?: number;
  error?: string;
}> {
  const {
    categoryName,
    state,
    city,
    pincode,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    page = 1,
    limit = 20,
    productType,
    productName,
    productSortBy = "created_desc"
  } = params;

  console.log(`fetchProductsByLocation called with pincode: ${pincode}, city: ${city}, state: ${state}, stateSlug: ${stateSlug}, citySlug: ${citySlug}, localitySlug: ${localitySlug}`);

  try {
    // Use regular client - business_profiles has public read access
    const supabase = createClient();
    const offset = (page - 1) * limit;

    // First, get business IDs that match the category and location
    let businessQuery = supabase
      .from("business_profiles")
      .select("id")
      .eq("status", "online")
      .eq("business_category", categoryName);

    // Add location filters if provided
    if (pincode) {
      businessQuery = businessQuery.eq("pincode", pincode);
    }

    // Prefer slug-based filtering when available
    if (stateSlug) {
      businessQuery = businessQuery.eq("state_slug", stateSlug);
    } else if (state) {
      // Fall back to name-based filtering if slug not provided
      businessQuery = businessQuery.eq("state", state);
    }

    if (citySlug) {
      businessQuery = businessQuery.eq("city_slug", citySlug);
    } else if (city) {
      // Fall back to name-based filtering if slug not provided
      businessQuery = businessQuery.eq("city", city);
    }

    if (localitySlug) {
      businessQuery = businessQuery.eq("locality_slug", localitySlug);
    } else if (locality) {
      // Fall back to name-based filtering if slug not provided
      console.log(`Filtering products by exact locality: "${locality}"`);
      businessQuery = businessQuery.eq("locality", locality);
    }

    const { data: businesses, error: businessError } = await businessQuery;

    if (businessError) {
      console.error("Error fetching businesses for products:", businessError);
      return { error: "Failed to fetch businesses for products." };
    }

    // If no businesses found, return empty result
    if (!businesses || businesses.length === 0) {
      return {
        data: [],
        count: 0
      };
    }

    // Get business IDs
    const businessIds = businesses.map((b: { id: string }) => b.id);

    // Now fetch products from these businesses
    let productQuery = supabase
      .from("products_services")
      .select(
        `
        *,
        business_profiles:business_id (
          business_slug
        )
      `,
        { count: "exact" }
      )
      .in("business_id", businessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productQuery = productQuery.eq("product_type", productType);
    }

    // Add product name search if provided
    if (productName) {
      productQuery = productQuery.ilike("name", `%${productName}%`);
    }

    // Add sorting
    const [column, direction] = productSortBy.split('_');
    const isAscending = direction === 'asc';

    // Map column names to actual database column names
    let orderColumn = column;
    if (column === 'created') {
      orderColumn = 'created_at';
    } else if (column === 'updated') {
      orderColumn = 'updated_at';
    } else if (column === 'price') {
      // Special handling for price sorting to use discounted_price when available, otherwise base_price
      if (isAscending) {
        productQuery = productQuery.order('discounted_price', { ascending: true, nullsFirst: false })
                          .order('base_price', { ascending: true, nullsFirst: false });
      } else {
        productQuery = productQuery.order('discounted_price', { ascending: false, nullsFirst: false })
                          .order('base_price', { ascending: false, nullsFirst: false });
      }
    } else if (column === 'newest') {
      // Handle 'newest' as a special case - sort by created_at descending
      productQuery = productQuery.order('created_at', { ascending: false });
      // Skip the generic ordering below since we've already applied a special case
    } else {
      // Only apply generic ordering if we haven't applied a special case above
      productQuery = productQuery.order(orderColumn, { ascending: isAscending });
    }

    // Execute the query with pagination
    const { data: products, count: productCount, error: productError } = await productQuery
      .range(offset, offset + limit - 1);

    if (productError) {
      console.error("Error fetching products:", productError);
      return { error: "Failed to fetch products." };
    }

    // Transform the products to include business_slug
    const transformedProducts = products.map((product: Tables<'products_services'> & { business_profiles: { business_slug: string | null } | null }) => {
      // Extract business_slug from the nested business_profiles object
      const businessSlug = product.business_profiles?.business_slug;

      // Create a new object without the nested business_profiles property
      // to avoid issues with serialization and type conflicts
      const { ...productData } = product;

      return {
        ...productData,
        business_slug: businessSlug || null
      } as NearbyProduct;
    });

    return {
      data: transformedProducts,
      count: productCount || 0
    };
  } catch (error) {
    console.error("Unexpected error in fetchProductsByLocation:", error);
    return { error: "An unexpected error occurred." };
  }
}
