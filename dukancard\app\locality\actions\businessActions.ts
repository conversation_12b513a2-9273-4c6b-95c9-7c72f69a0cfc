"use server";

import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

// Fetch businesses by locality search
export async function fetchBusinessesByLocalitySearch(params: {
  localityName: string;
  pincode: string;
  businessName?: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
}): Promise<{
  data?: {
    businesses: BusinessCardData[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    pincode,
    businessName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
  } = params;

  try {
    // Build query parameters for the API call
    const queryParams = new URLSearchParams();
    queryParams.set('pincode', pincode);
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('status', 'online');

    if (businessName) {
      queryParams.set('search', businessName);
    }

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
      // Note: price sorting is not supported by the API yet
      default:
        apiSortBy = 'created_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch businesses",
      };
    }

    const businesses = result.businesses || [];
    const pagination = result.pagination || {};

    // Calculate if there are more results
    const hasMore = pagination.hasMore || false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        businesses: businesses as BusinessCardData[],
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchBusinessesByLocalitySearch:", error);
    return { error: "An error occurred while fetching businesses." };
  }
}

// Fetch businesses by locality and location
export async function fetchBusinessesByLocalityAndLocation(params: {
  localityName: string;
  pincode: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
}): Promise<{
  data?: {
    businesses: BusinessCardData[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    pincode,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
  } = params;

  try {
    // Build query parameters for the API call
    const queryParams = new URLSearchParams();
    queryParams.set('pincode', pincode);
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('status', 'online');

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
        apiSortBy = 'likes_asc';
        break;
      case 'likes_desc':
        apiSortBy = 'likes_desc';
        break;
      case 'subscriptions_asc':
        apiSortBy = 'subscriptions_asc';
        break;
      case 'subscriptions_desc':
        apiSortBy = 'subscriptions_desc';
        break;
      case 'rating_asc':
        apiSortBy = 'rating_asc';
        break;
      case 'rating_desc':
        apiSortBy = 'rating_desc';
        break;
      default:
        apiSortBy = 'created_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the business profile API endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/business?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch businesses",
      };
    }

    const businesses = result.businesses || [];
    const pagination = result.pagination || {};

    // Calculate if there are more results
    const hasMore = pagination.hasMore || false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        businesses: businesses as BusinessCardData[],
        totalCount: pagination.totalCount,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Error in fetchBusinessesByLocalityAndLocation:", error);
    return { error: "An error occurred while fetching businesses." };
  }
}

// Fetch more business cards by locality combined
export async function fetchMoreBusinessCardsByLocalityCombined(params: {
  localityName: string;
  pincode: string;
  businessName?: string;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy | "price_asc" | "price_desc";
}): Promise<{
  data?: {
    businesses: BusinessCardData[];
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    localityName,
    pincode,
    businessName,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
  } = params;

  try {
    // If business name is provided, use search
    if (businessName) {
      const result = await fetchBusinessesByLocalitySearch({
        localityName,
        pincode,
        businessName,
        page,
        limit,
        sortBy,
      });

      if (result.error) {
        return { error: result.error };
      }

      return {
        data: {
          businesses: result.data?.businesses || [],
          hasMore: result.data?.hasMore || false,
          nextPage: result.data?.nextPage || null,
        },
      };
    }

    // Otherwise, use location-based fetch
    const result = await fetchBusinessesByLocalityAndLocation({
      localityName,
      pincode,
      page,
      limit,
      sortBy,
    });

    if (result.error) {
      return { error: result.error };
    }

    return {
      data: {
        businesses: result.data?.businesses || [],
        hasMore: result.data?.hasMore || false,
        nextPage: result.data?.nextPage || null,
      },
    };
  } catch (error) {
    console.error("Error in fetchMoreBusinessCardsByLocalityCombined:", error);
    return { error: "An error occurred while fetching businesses." };
  }
}
