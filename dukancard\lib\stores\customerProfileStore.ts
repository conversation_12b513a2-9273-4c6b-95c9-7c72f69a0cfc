import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { authenticatedApiCall } from '@/lib/utils/authenticatedFetch';
import { Tables, TablesInsert, TablesUpdate } from '@/types/supabase';

// Use centralized Supabase types
export type CustomerProfile = Tables<'customer_profiles'>;
export type CustomerProfileInsert = TablesInsert<'customer_profiles'>;
export type CustomerProfileUpdate = TablesUpdate<'customer_profiles'>;

interface CustomerProfileState {
  // Current data
  currentUserProfile: CustomerProfile | null;

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;

  // Error states
  error: string | null;

  // Actions
  getCurrentUserProfile: () => Promise<CustomerProfile | null>;
  createProfile: (_data: Omit<CustomerProfileInsert, 'id'>) => Promise<CustomerProfile | null>;
  updateProfile: (_data: CustomerProfileUpdate) => Promise<CustomerProfile | null>;
  checkProfileExists: () => Promise<boolean>;

  // State management
  setCurrentUserProfile: (_profile: CustomerProfile | null) => void;
  clearError: () => void;
}

export const useCustomerProfileStore = create<CustomerProfileState>()(
  devtools((set, _get) => ({
    // Initial state
    currentUserProfile: null,
    isLoading: false,
    isCreating: false,
    isUpdating: false,
    error: null,

    // Get current user's profile
    getCurrentUserProfile: async () => {
      set({ isLoading: true, error: null });

      try {
        const result = await authenticatedApiCall('/api/customer/profile', {
          method: 'GET',
        });

        if (!result.success) {
          throw new Error(result.error || 'Failed to fetch customer profile');
        }

        const profile = result.data?.profile;

        set({ 
          currentUserProfile: profile,
          isLoading: false,
          error: null 
        });

        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch customer profile';
        set({ 
          isLoading: false, 
          error: errorMessage,
          currentUserProfile: null 
        });
        return null;
      }
    },

    // Create new customer profile
    createProfile: async (data: Omit<CustomerProfileInsert, 'id'>) => {
      set({ isCreating: true, error: null });

      try {
        const result = await authenticatedApiCall('/api/customer/profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!result.success) {
          throw new Error(result.error || 'Failed to create customer profile');
        }

        const profile = result.data?.profile;

        set({ 
          currentUserProfile: profile,
          isCreating: false,
          error: null 
        });

        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create customer profile';
        set({ 
          isCreating: false, 
          error: errorMessage 
        });
        return null;
      }
    },

    // Update customer profile
    updateProfile: async (data: CustomerProfileUpdate) => {
      set({ isUpdating: true, error: null });

      try {
        const result = await authenticatedApiCall('/api/customer/profile', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!result.success) {
          throw new Error(result.error || 'Failed to update customer profile');
        }

        const profile = result.data?.profile;

        set({ 
          currentUserProfile: profile,
          isUpdating: false,
          error: null 
        });

        return profile;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update customer profile';
        set({ 
          isUpdating: false, 
          error: errorMessage 
        });
        return null;
      }
    },

    // Check if profile exists
    checkProfileExists: async () => {
      try {
        const result = await authenticatedApiCall('/api/customer/profile/exists', {
          method: 'GET',
        });

        if (!result.success) {
          return false;
        }

        return result.data?.exists || false;
      } catch (error) {
        console.error('Error checking profile existence:', error);
        return false;
      }
    },

    // Set current user profile
    setCurrentUserProfile: (profile: CustomerProfile | null) => {
      set({ currentUserProfile: profile });
    },

    // Clear error
    clearError: () => {
      set({ error: null });
    },
  }), {
    name: 'customer-profile-store',
  })
);
