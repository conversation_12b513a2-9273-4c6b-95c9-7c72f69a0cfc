"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { internalPost, internalDelete, internalGet } from "@/lib/utils/internalApiClient";
// getSecureBusinessProfileBySlug is imported but not used in this file
// import { getSecureBusinessProfileBySlug } from './secureBusinessProfiles';
// import { cookies } from 'next/headers'; // Removed unused import

export async function subscribeToBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to create subscription
    const response = await internalPost('/api/subscriptions', {
      business_profile_id: businessProfileId,
    }, { userId: user.id });

    if (!response.success) {
      // Handle specific error cases
      if (response.error?.includes('cannot subscribe to your own business')) {
        return { success: false, error: "You cannot subscribe to your own business card." };
      }
      if (response.error?.includes('Already subscribed')) {
        return { success: true }; // Already subscribed is acceptable
      }
      return { success: false, error: response.error || "Failed to subscribe to business." };
    }

    // Get business slug for revalidation
    const businessResponse = await internalGet(`/api/business/${businessProfileId}`, { userId: user.id });
    const businessSlug = businessResponse.data?.business?.business_slug;

    // Revalidate paths
    if (businessSlug) {
      revalidatePath(`/${businessSlug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if user is a business and revalidate business dashboard
    const userBusinessResponse = await internalGet('/api/business/me', { userId: user.id });
    if (userBusinessResponse.success && userBusinessResponse.data?.profile) {
      revalidatePath("/dashboard/business");
      revalidatePath("/dashboard/business/subscriptions");
    }

    

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in subscribeToBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

// --- Implementation for other actions ---

export async function unsubscribeFromBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to delete subscription
    const response = await internalDelete('/api/subscriptions', {
      userId: user.id,
      body: JSON.stringify({
        business_profile_id: businessProfileId,
      }),
    });

    if (!response.success) {
      return { success: false, error: response.error || "Failed to unsubscribe from business." };
    }

    // Get business slug for revalidation
    const businessResponse = await internalGet(`/api/business/${businessProfileId}`, { userId: user.id });
    const businessSlug = businessResponse.data?.business?.business_slug;

    // Revalidate paths
    if (businessSlug) {
      revalidatePath(`/${businessSlug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if user is a business and revalidate business dashboard
    const userBusinessResponse = await internalGet('/api/business/me', { userId: user.id });
    if (userBusinessResponse.success && userBusinessResponse.data?.profile) {
      revalidatePath("/dashboard/business");
      revalidatePath("/dashboard/business/subscriptions");
    }

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in unsubscribeFromBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function submitReview(
  businessProfileId: string,
  rating: number,
  reviewText?: string | null // Allow null for review text
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  if (rating < 1 || rating > 5) {
    return { success: false, error: "Rating must be between 1 and 5." };
  }

  try {
    // Use internal API client to submit review
    const response = await internalPost('/api/reviews', {
      business_profile_id: businessProfileId,
      rating,
      review_text: reviewText,
    }, { userId: user.id });

    if (!response.success) {
      // Handle specific error cases
      if (response.error?.includes('cannot review your own business')) {
        return { success: false, error: "You cannot review your own business card." };
      }
      return { success: false, error: response.error || "Failed to submit review." };
    }

    // Get business slug for revalidation
    const businessResponse = await internalGet(`/api/business/${businessProfileId}`, { userId: user.id });
    const businessSlug = businessResponse.data?.business?.business_slug;

    // Revalidate paths
    if (businessSlug) {
      revalidatePath(`/${businessSlug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if user is a business and revalidate business dashboard
    const userBusinessResponse = await internalGet('/api/business/me', { userId: user.id });
    if (userBusinessResponse.success && userBusinessResponse.data?.profile) {
      revalidatePath("/dashboard/business");
    }

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in submitReview:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function deleteReview(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to delete review
    const response = await internalDelete('/api/reviews', {
      userId: user.id,
      body: JSON.stringify({
        business_profile_id: businessProfileId,
      }),
    });

    if (!response.success) {
      return { success: false, error: response.error || "Failed to delete review." };
    }

    // Get business slug for revalidation
    const businessResponse = await internalGet(`/api/business/${businessProfileId}`, { userId: user.id });
    const businessSlug = businessResponse.data?.business?.business_slug;

    // Revalidate paths
    if (businessSlug) {
      revalidatePath(`/${businessSlug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if user is a business and revalidate business dashboard
    const userBusinessResponse = await internalGet('/api/business/me', { userId: user.id });
    if (userBusinessResponse.success && userBusinessResponse.data?.profile) {
      revalidatePath("/dashboard/business");
        }

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in deleteReview:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function likeBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to create like
    const response = await internalPost('/api/likes', {
      like_type: 'business',
      business_id: businessProfileId,
    }, { userId: user.id });

    if (!response.success) {
      // Handle specific error cases
      if (response.error?.includes('cannot like your own business')) {
        return { success: false, error: "You cannot like your own business card." };
      }
      if (response.error?.includes('Already liked')) {
        return { success: true }; // Already liked is acceptable
      }
      return { success: false, error: response.error || "Failed to like business." };
    }

    // Get business slug for revalidation
    const businessResponse = await internalGet(`/api/business/${businessProfileId}`, { userId: user.id });
    const businessSlug = businessResponse.data?.business?.business_slug;

    // Revalidate paths
    if (businessSlug) {
      revalidatePath(`/${businessSlug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if user is a business and revalidate business dashboard
    const userBusinessResponse = await internalGet('/api/business/me', { userId: user.id });
    if (userBusinessResponse.success && userBusinessResponse.data?.profile) {
      revalidatePath("/dashboard/business");
      revalidatePath("/dashboard/business/likes");
      revalidatePath("/dashboard/business/activities");
    }

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in likeBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function unlikeBusiness(
  businessProfileId: string
): Promise<{ success: boolean; error?: string }> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();

  if (authError || !user) {
    return { success: false, error: "User not authenticated." };
  }

  try {
    // Use internal API client to delete like
    const response = await internalDelete('/api/likes', {
      userId: user.id,
      body: JSON.stringify({
        like_type: 'business',
        business_id: businessProfileId,
      }),
    });

    if (!response.success) {
      return { success: false, error: response.error || "Failed to unlike business." };
    }

    // Get business slug for revalidation
    const businessResponse = await internalGet(`/api/business/${businessProfileId}`, { userId: user.id });
    const businessSlug = businessResponse.data?.business?.business_slug;

    // Revalidate paths
    if (businessSlug) {
      revalidatePath(`/${businessSlug}`);
    }
    revalidatePath("/dashboard/customer");

    // Check if user is a business and revalidate business dashboard
    const userBusinessResponse = await internalGet('/api/business/me', { userId: user.id });
    if (userBusinessResponse.success && userBusinessResponse.data?.profile) {
      revalidatePath("/dashboard/business");
      revalidatePath("/dashboard/business/likes");
      revalidatePath("/dashboard/business/activities");
    }

    return { success: true };
  } catch (error) {
    console.error("Unexpected error in unlikeBusiness:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    return { success: false, error: errorMessage };
  }
}

export async function getInteractionStatus(businessProfileId: string): Promise<{
  isSubscribed: boolean;
  hasLiked: boolean;
  userRating: number | null;
  userReview: string | null;
  error?: string;
}> {
  // const cookieStore = cookies();
  const supabase = await createClient(); // Await the async function
  let userId: string | null = null;

  // Try to get authenticated user, but proceed even if not logged in
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (user) {
    userId = user.id;
  }

  // Default status for anonymous users
  const defaultStatus = {
    isSubscribed: false,
    hasLiked: false,
    userRating: null,
    userReview: null,
  };

  if (!userId) {
    return defaultStatus; // Return default if no user is logged in
  }

  try {
    // Use regular client - all these tables have public read access
    // Fetch all statuses in parallel
    const [subscriptionRes, likeRes, reviewRes] = await Promise.all([
      supabase
        .from("subscriptions")
        .select("id", { count: "exact", head: true }) // Just check existence
        .match({ user_id: userId, business_profile_id: businessProfileId }),
      supabase
        .from("likes")
        .select("id", { count: "exact", head: true }) // Just check existence
        .match({ user_id: userId, business_profile_id: businessProfileId }),
      supabase
        .from("ratings_reviews")
        .select("rating, review_text")
        .match({ user_id: userId, business_profile_id: businessProfileId })
        .maybeSingle(), // Use maybeSingle as user might not have reviewed
    ]);

    // Check for errors in parallel fetches
    if (subscriptionRes.error)
      throw new Error(
        `Subscription fetch error: ${subscriptionRes.error.message}`
      );
    if (likeRes.error)
      throw new Error(`Like fetch error: ${likeRes.error.message}`);
    if (reviewRes.error)
      throw new Error(`Review fetch error: ${reviewRes.error.message}`);

    const reviewData = reviewRes.data;

    return {
      isSubscribed: (subscriptionRes.count ?? 0) > 0,
      hasLiked: (likeRes.count ?? 0) > 0,
      userRating: reviewData?.rating ?? null,
      userReview: reviewData?.review_text ?? null,
    };
  } catch (error) {
    console.error("Error fetching interaction status:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unexpected error occurred.";
    // Return default status but include the error message
    return { ...defaultStatus, error: errorMessage };
  }
}
