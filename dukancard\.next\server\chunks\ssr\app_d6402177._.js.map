{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/reviews/AuthMessage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\ninterface AuthMessageProps {\r\n  isAuthenticated: boolean;\r\n}\r\n\r\nexport default function AuthMessage({ isAuthenticated }: AuthMessageProps) {\r\n  return isAuthenticated ? (\r\n    <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800/30 text-green-700 dark:text-green-400 text-xs sm:text-sm p-2.5 sm:p-3 rounded-md mb-4 sm:mb-6 w-full overflow-hidden\">\r\n      You&apos;re logged in and can leave a review. Your feedback helps others discover great businesses!\r\n    </div>\r\n  ) : (\r\n    <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800/30 text-blue-700 dark:text-blue-400 text-xs sm:text-sm p-2.5 sm:p-3 rounded-md mb-4 sm:mb-6 w-full overflow-hidden\">\r\n      <strong>Note:</strong> You need to be logged in to leave a review. Your feedback helps others discover great businesses!\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAQe,SAAS,YAAY,EAAE,eAAe,EAAoB;IACvE,OAAO,gCACL,8OAAC;QAAI,WAAU;kBAAsM;;;;;6BAIrN,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BAAO;;;;;;YAAc;;;;;;;AAG5B", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/reviews/ReviewsSummary.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Star, SortAsc } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ReviewSortBy } from \"@/lib/actions/reviews\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface ReviewsSummaryProps {\r\n  averageRating: number;\r\n  totalReviews: number;\r\n  _sortBy: ReviewSortBy;\r\n  onSortChange: (_sortBy: ReviewSortBy) => void;\r\n}\r\n\r\nexport default function ReviewsSummary({\r\n  averageRating,\r\n  totalReviews,\r\n  _sortBy,\r\n  onSortChange,\r\n}: ReviewsSummaryProps) {\r\n  // No rating distribution calculation needed\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      className=\"rounded-xl border border-neutral-200 dark:border-neutral-700 bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-900 dark:to-neutral-800 p-4 sm:p-6 shadow-md mb-6 sm:mb-8 overflow-hidden\"\r\n    >\r\n      <div className=\"flex flex-col md:flex-row md:items-center justify-between gap-4 sm:gap-6 overflow-hidden\">\r\n        <div className=\"flex items-start gap-4\">\r\n          {/* Animated rating icon */}\r\n          <motion.div\r\n            initial={{ scale: 0.8, rotate: -10 }}\r\n            animate={{ scale: 1, rotate: 0 }}\r\n            transition={{\r\n              type: \"spring\",\r\n              stiffness: 260,\r\n              damping: 20,\r\n              delay: 0.1,\r\n            }}\r\n            className=\"p-2 sm:p-3 rounded-full bg-gradient-to-br from-yellow-100 to-amber-200 dark:from-yellow-800/80 dark:to-amber-900/80 text-amber-600 dark:text-amber-300 shadow-md flex-shrink-0\"\r\n          >\r\n            <Star className=\"w-5 h-5 sm:w-6 sm:h-6\" />\r\n          </motion.div>\r\n\r\n          <div className=\"space-y-3\">\r\n            <div className=\"space-y-1\">\r\n              <h3 className=\"text-lg sm:text-xl font-bold text-neutral-800 dark:text-neutral-100\">\r\n                Customer Reviews\r\n              </h3>\r\n              <div className=\"flex items-center gap-2 text-sm text-neutral-600 dark:text-neutral-400\">\r\n                <div className=\"flex items-center\">\r\n                  {[1, 2, 3, 4, 5].map((star, index) => (\r\n                    <motion.div\r\n                      key={star}\r\n                      initial={{ opacity: 0, scale: 0.5 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      transition={{ duration: 0.3, delay: index * 0.05 + 0.2 }}\r\n                    >\r\n                      <Star\r\n                        className={cn(\r\n                          \"w-4 h-4 mx-0.5\",\r\n                          star <= Math.round(averageRating)\r\n                            ? \"text-yellow-400 fill-yellow-400\"\r\n                            : \"text-neutral-300 dark:text-neutral-600\"\r\n                        )}\r\n                      />\r\n                    </motion.div>\r\n                  ))}\r\n                </div>\r\n                <motion.span\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  transition={{ duration: 0.5, delay: 0.5 }}\r\n                  className=\"font-medium\"\r\n                >\r\n                  {averageRating.toFixed(1)} out of 5\r\n                </motion.span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex flex-col gap-3\">\r\n          {/* Total reviews badge */}\r\n          <motion.div\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            transition={{ duration: 0.4, delay: 0.3 }}\r\n            className=\"flex items-center justify-center px-4 py-2 bg-neutral-100 dark:bg-neutral-800 rounded-lg border border-neutral-200 dark:border-neutral-700 shadow-sm\"\r\n          >\r\n            <span className=\"text-lg font-bold text-neutral-800 dark:text-neutral-200\">\r\n              {totalReviews}\r\n            </span>\r\n            <span className=\"ml-2 text-sm text-neutral-600 dark:text-neutral-400\">\r\n              {totalReviews === 1 ? \"Review\" : \"Reviews\"}\r\n            </span>\r\n          </motion.div>\r\n\r\n          {/* Sort dropdown with enhanced styling */}\r\n          <motion.div\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.4, delay: 0.4 }}\r\n          >\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  className=\"gap-1.5 w-full bg-white dark:bg-neutral-900 border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-all duration-200\"\r\n                >\r\n                  <SortAsc className=\"h-4 w-4\" />\r\n                  <span>Sort by: </span>\r\n                  <span className=\"font-medium text-[var(--brand-gold)]\">\r\n                    {_sortBy === \"newest\"\r\n                      ? \"Newest\"\r\n                      : _sortBy === \"oldest\"\r\n                      ? \"Oldest\"\r\n                      : _sortBy === \"highest_rating\"\r\n                      ? \"Highest rated\"\r\n                      : \"Lowest rated\"}\r\n                  </span>\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"end\" className=\"w-48 p-1\">\r\n                <DropdownMenuItem\r\n                  onClick={() => onSortChange(\"newest\")}\r\n                  className={cn(\r\n                    \"cursor-pointer transition-colors duration-200\",\r\n                    _sortBy === \"newest\"\r\n                      ? \"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]\"\r\n                      : \"\"\r\n                  )}\r\n                >\r\n                  <motion.div\r\n                    whileTap={{ scale: 0.97 }}\r\n                    className=\"flex items-center w-full\"\r\n                  >\r\n                    Newest first\r\n                  </motion.div>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={() => onSortChange(\"oldest\")}\r\n                  className={cn(\r\n                    \"cursor-pointer transition-colors duration-200\",\r\n                    _sortBy === \"oldest\"\r\n                      ? \"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]\"\r\n                      : \"\"\r\n                  )}\r\n                >\r\n                  <motion.div\r\n                    whileTap={{ scale: 0.97 }}\r\n                    className=\"flex items-center w-full\"\r\n                  >\r\n                    Oldest first\r\n                  </motion.div>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={() => onSortChange(\"highest_rating\")}\r\n                  className={cn(\r\n                    \"cursor-pointer transition-colors duration-200\",\r\n                    _sortBy === \"highest_rating\"\r\n                      ? \"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]\"\r\n                      : \"\"\r\n                  )}\r\n                >\r\n                  <motion.div\r\n                    whileTap={{ scale: 0.97 }}\r\n                    className=\"flex items-center w-full\"\r\n                  >\r\n                    Highest rating\r\n                  </motion.div>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem\r\n                  onClick={() => onSortChange(\"lowest_rating\")}\r\n                  className={cn(\r\n                    \"cursor-pointer transition-colors duration-200\",\r\n                    _sortBy === \"lowest_rating\"\r\n                      ? \"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)]\"\r\n                      : \"\"\r\n                  )}\r\n                >\r\n                  <motion.div\r\n                    whileTap={{ scale: 0.97 }}\r\n                    className=\"flex items-center w-full\"\r\n                  >\r\n                    Lowest rating\r\n                  </motion.div>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AACA;AAEA;AARA;;;;;;;AAsBe,SAAS,eAAe,EACrC,aAAa,EACb,YAAY,EACZ,OAAO,EACP,YAAY,EACQ;IACpB,4CAA4C;IAE5C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAK,QAAQ,CAAC;4BAAG;4BACnC,SAAS;gCAAE,OAAO;gCAAG,QAAQ;4BAAE;4BAC/B,YAAY;gCACV,MAAM;gCACN,WAAW;gCACX,SAAS;gCACT,OAAO;4BACT;4BACA,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAGlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsE;;;;;;kDAGpF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ;oDAAC;oDAAG;oDAAG;oDAAG;oDAAG;iDAAE,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;4DAAG,OAAO;wDAAI;wDAClC,SAAS;4DAAE,SAAS;4DAAG,OAAO;wDAAE;wDAChC,YAAY;4DAAE,UAAU;4DAAK,OAAO,QAAQ,OAAO;wDAAI;kEAEvD,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DACH,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kBACA,QAAQ,KAAK,KAAK,CAAC,iBACf,oCACA;;;;;;uDAVH;;;;;;;;;;0DAgBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;gDACxC,WAAU;;oDAET,cAAc,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpC,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAK,WAAU;8CACb;;;;;;8CAEH,8OAAC;oCAAK,WAAU;8CACb,iBAAiB,IAAI,WAAW;;;;;;;;;;;;sCAKrC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC,qIAAA,CAAA,eAAY;;kDACX,8OAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,8OAAC,8NAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DACb,YAAY,WACT,WACA,YAAY,WACZ,WACA,YAAY,mBACZ,kBACA;;;;;;;;;;;;;;;;;kDAIV,8OAAC,qIAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,8OAAC,qIAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,YAAY,WACR,uDACA;0DAGN,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC,qIAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,YAAY,WACR,uDACA;0DAGN,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC,qIAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,YAAY,mBACR,uDACA;0DAGN,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DACX;;;;;;;;;;;0DAIH,8OAAC,qIAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,aAAa;gDAC5B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDACA,YAAY,kBACR,uDACA;0DAGN,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAU;wDAAE,OAAO;oDAAK;oDACxB,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/reviews/ReviewForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { Star, Loader2 } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface ReviewFormProps {\r\n  userHasReviewed: boolean;\r\n  ratingValue: number;\r\n  reviewText: string;\r\n  isSubmittingReview: boolean;\r\n  onRatingChange: (_rating: number) => void;\r\n  onReviewTextChange: (_text: string) => void;\r\n  onSubmit: (_e: React.FormEvent) => void;\r\n  onDelete: () => void;\r\n}\r\n\r\nexport default function ReviewForm({\r\n  userHasReviewed,\r\n  ratingValue,\r\n  reviewText,\r\n  isSubmittingReview,\r\n  onRatingChange,\r\n  onReviewTextChange,\r\n  onSubmit,\r\n  onDelete,\r\n}: ReviewFormProps) {\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.6, ease: \"easeOut\" }}\r\n      className=\"relative p-4 sm:p-6 md:p-8 border rounded-xl bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800/90 dark:to-neutral-900/90 shadow-lg hover:shadow-xl transition-all duration-300 border-neutral-200 dark:border-neutral-700/80 mb-6 sm:mb-10 overflow-hidden\"\r\n    >\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute -top-12 -right-12 w-40 h-40 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-2xl pointer-events-none\" />\r\n      <div className=\"absolute -bottom-16 -left-16 w-48 h-48 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl pointer-events-none\" />\r\n\r\n      {/* Header with icon */}\r\n      <div className=\"flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6\">\r\n        <motion.div\r\n          initial={{ scale: 0, rotate: -20 }}\r\n          animate={{ scale: 1, rotate: 0 }}\r\n          transition={{\r\n            type: \"spring\",\r\n            stiffness: 260,\r\n            damping: 20,\r\n            delay: 0.2\r\n          }}\r\n          className=\"p-2 sm:p-2.5 rounded-lg bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/20 text-[var(--brand-gold)] shadow-sm border border-[var(--brand-gold)]/10\"\r\n        >\r\n          {userHasReviewed ? (\r\n            <Star className=\"w-5 h-5 sm:w-6 sm:h-6 fill-[var(--brand-gold)]\" />\r\n          ) : (\r\n            <Star className=\"w-5 h-5 sm:w-6 sm:h-6\" />\r\n          )}\r\n        </motion.div>\r\n        <h3 className=\"text-lg sm:text-xl font-bold text-neutral-800 dark:text-neutral-100\">\r\n          {userHasReviewed ? 'Your Review' : 'Write a Review'}\r\n        </h3>\r\n        {userHasReviewed && (\r\n          <motion.div\r\n            initial={{ opacity: 0, x: 20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{ duration: 0.4, delay: 0.3 }}\r\n          >\r\n            <Badge className=\"bg-[var(--brand-gold)]/10 text-[var(--brand-gold)] border-[var(--brand-gold)]/20\">\r\n              Submitted\r\n            </Badge>\r\n          </motion.div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Helpful tip */}\r\n      {!userHasReviewed && (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.4, delay: 0.3 }}\r\n          className=\"mb-4 sm:mb-6 bg-blue-50 dark:bg-blue-900/20 p-3 sm:p-4 rounded-lg border border-blue-100 dark:border-blue-800/30 flex items-start gap-2 sm:gap-3\"\r\n        >\r\n          <div className=\"text-blue-500 dark:text-blue-400 mt-0.5\">\r\n            <Star className=\"w-4 h-4 sm:w-5 sm:h-5\" />\r\n          </div>\r\n          <div>\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium\">Your review matters!</p>\r\n            <p className=\"text-xs text-blue-600/80 dark:text-blue-400/80 mt-0.5\">\r\n              Honest feedback helps others discover great businesses and helps improve services.\r\n            </p>\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      <form onSubmit={onSubmit} className=\"space-y-6\">\r\n        {/* Rating stars with enhanced animation */}\r\n        <div>\r\n          <Label htmlFor=\"rating\" className=\"mb-3 text-sm font-medium text-neutral-700 dark:text-neutral-300 flex items-center gap-2\">\r\n            <span>How would you rate your experience?</span>\r\n            <span className=\"text-red-500\">*</span>\r\n          </Label>\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.4, delay: 0.4 }}\r\n            className=\"flex items-center justify-center bg-gradient-to-r from-neutral-50 to-white dark:from-neutral-800/50 dark:to-neutral-800/30 p-2 sm:p-4 rounded-xl border border-neutral-200 dark:border-neutral-700/50 mb-2 shadow-sm overflow-hidden\"\r\n          >\r\n            {[1, 2, 3, 4, 5].map((star, index) => (\r\n              <motion.button\r\n                key={star}\r\n                type=\"button\"\r\n                initial={{ opacity: 0, scale: 0.5, y: 10 }}\r\n                animate={{ opacity: 1, scale: 1, y: 0 }}\r\n                transition={{\r\n                  duration: 0.4,\r\n                  delay: 0.5 + index * 0.1,\r\n                  type: \"spring\",\r\n                  stiffness: 200,\r\n                }}\r\n                whileHover={{\r\n                  scale: 1.2,\r\n                  transition: { duration: 0.15 }\r\n                }}\r\n                whileTap={{ scale: 0.9, rotate: 0 }}\r\n                onClick={() => onRatingChange(star)}\r\n                className={cn(\r\n                  \"p-1 sm:p-2 mx-1 sm:mx-2 rounded-full transition-all duration-300 cursor-pointer\",\r\n                  star <= ratingValue\r\n                    ? \"text-yellow-400 hover:text-yellow-300 scale-110 drop-shadow-md\"\r\n                    : \"text-neutral-300 dark:text-neutral-600 hover:text-neutral-400 dark:hover:text-neutral-500\"\r\n                )}\r\n                aria-label={`Rate ${star} out of 5 stars`}\r\n              >\r\n                <>\r\n                  {star <= ratingValue ? (\r\n                    <Star className=\"w-6 h-6 sm:w-8 sm:h-8 fill-current\" />\r\n                  ) : (\r\n                    <Star className=\"w-6 h-6 sm:w-8 sm:h-8 fill-current drop-shadow-sm\" />\r\n                  )}\r\n                  {star <= ratingValue && (\r\n                    <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none\">\r\n                      <div className=\"w-full h-full absolute bg-yellow-300/20 dark:bg-yellow-500/30 rounded-full blur-md\" />\r\n                    </div>\r\n                  )}\r\n                </>\r\n              </motion.button>\r\n            ))}\r\n          </motion.div>\r\n\r\n          {/* Rating label with animation */}\r\n          <AnimatePresence mode=\"wait\">\r\n            {ratingValue > 0 ? (\r\n              <motion.div\r\n                key=\"rating-label\"\r\n                initial={{ opacity: 0, y: -10 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                exit={{ opacity: 0, y: -10 }}\r\n                className=\"flex justify-center\"\r\n              >\r\n                <Badge className={cn(\r\n                  \"px-3 py-1 text-sm font-medium\",\r\n                  ratingValue === 5 ? \"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300\" :\r\n                  ratingValue === 4 ? \"bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300\" :\r\n                  ratingValue === 3 ? \"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300\" :\r\n                  ratingValue === 2 ? \"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300\" :\r\n                  \"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300\"\r\n                )}>\r\n                  {ratingValue === 5 ? \"Excellent\" :\r\n                   ratingValue === 4 ? \"Very Good\" :\r\n                   ratingValue === 3 ? \"Good\" :\r\n                   ratingValue === 2 ? \"Fair\" :\r\n                   \"Poor\"}\r\n                </Badge>\r\n              </motion.div>\r\n            ) : (\r\n              <motion.p\r\n                key=\"select-rating\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                exit={{ opacity: 0 }}\r\n                className=\"text-center text-sm text-neutral-500 dark:text-neutral-400\"\r\n              >\r\n                Select a rating\r\n              </motion.p>\r\n            )}\r\n          </AnimatePresence>\r\n        </div>\r\n\r\n        {/* Review text with enhanced styling */}\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 10 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.4, delay: 0.6 }}\r\n        >\r\n          <Label htmlFor=\"reviewText\" className=\"mb-2 block text-sm font-medium text-neutral-700 dark:text-neutral-300\">\r\n            Tell us more (Optional)\r\n          </Label>\r\n          <div className=\"relative\">\r\n            <Textarea\r\n              id=\"reviewText\"\r\n              value={reviewText}\r\n              onChange={(e) => onReviewTextChange(e.target.value)}\r\n              placeholder=\"What did you like or dislike? What stood out about your experience?\"\r\n              rows={4}\r\n              className=\"bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-[var(--brand-gold)] focus:border-[var(--brand-gold)] pr-3 transition-all duration-200 resize-none shadow-sm\"\r\n              maxLength={500}\r\n            />\r\n            <div className=\"absolute bottom-3 right-3 text-xs text-neutral-400 dark:text-neutral-500\">\r\n              {reviewText.length}/500\r\n            </div>\r\n          </div>\r\n        </motion.div>\r\n\r\n        {/* Action buttons with enhanced styling */}\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.4, delay: 0.7 }}\r\n          className=\"flex flex-col sm:flex-row justify-end items-center gap-3 pt-2\"\r\n        >\r\n          {userHasReviewed && (\r\n            <motion.div\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              className=\"w-full sm:w-auto order-2 sm:order-1\"\r\n            >\r\n              <Button\r\n                type=\"button\"\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"w-full sm:w-auto text-red-600 hover:text-red-700 border-red-200 hover:border-red-300 hover:bg-red-50 dark:border-red-800/50 dark:hover:bg-red-900/20 transition-all duration-300 font-medium cursor-pointer h-10\"\r\n                onClick={onDelete}\r\n                disabled={isSubmittingReview}\r\n              >\r\n                {isSubmittingReview ? <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" /> : null}\r\n                Delete Review\r\n              </Button>\r\n            </motion.div>\r\n          )}\r\n\r\n          <motion.div\r\n            whileHover={{ scale: 1.02, translateY: -2 }}\r\n            whileTap={{ scale: 0.98 }}\r\n            className=\"w-full sm:w-auto order-1 sm:order-2\"\r\n          >\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={isSubmittingReview || ratingValue === 0}\r\n              className=\"w-full sm:w-auto bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] cursor-pointer shadow-md hover:shadow-lg transition-all duration-300 py-3 px-8 text-base font-medium rounded-lg h-12\"\r\n            >\r\n              {isSubmittingReview ? (\r\n                <>\r\n                  <Loader2 className=\"mr-2 h-5 w-5 animate-spin\" />\r\n                  {userHasReviewed ? 'Updating...' : 'Submitting...'}\r\n                </>\r\n              ) : (\r\n                <>\r\n                  {userHasReviewed ? 'Update Review' : 'Submit Review'}\r\n                </>\r\n              )}\r\n            </Button>\r\n          </motion.div>\r\n        </motion.div>\r\n      </form>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAsBe,SAAS,WAAW,EACjC,eAAe,EACf,WAAW,EACX,UAAU,EACV,kBAAkB,EAClB,cAAc,EACd,kBAAkB,EAClB,QAAQ,EACR,QAAQ,EACQ;IAChB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAG,QAAQ,CAAC;wBAAG;wBACjC,SAAS;4BAAE,OAAO;4BAAG,QAAQ;wBAAE;wBAC/B,YAAY;4BACV,MAAM;4BACN,WAAW;4BACX,SAAS;4BACT,OAAO;wBACT;wBACA,WAAU;kCAET,gCACC,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;iDAEhB,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGpB,8OAAC;wBAAG,WAAU;kCACX,kBAAkB,gBAAgB;;;;;;oBAEpC,iCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,0HAAA,CAAA,QAAK;4BAAC,WAAU;sCAAmF;;;;;;;;;;;;;;;;;YAQzG,CAAC,iCACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;;0CACC,8OAAC;gCAAE,WAAU;0CAAuD;;;;;;0CACpE,8OAAC;gCAAE,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;0BAO3E,8OAAC;gBAAK,UAAU;gBAAU,WAAU;;kCAElC,8OAAC;;0CACC,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAS,WAAU;;kDAChC,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;0CAET;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,MAAK;wCACL,SAAS;4CAAE,SAAS;4CAAG,OAAO;4CAAK,GAAG;wCAAG;wCACzC,SAAS;4CAAE,SAAS;4CAAG,OAAO;4CAAG,GAAG;wCAAE;wCACtC,YAAY;4CACV,UAAU;4CACV,OAAO,MAAM,QAAQ;4CACrB,MAAM;4CACN,WAAW;wCACb;wCACA,YAAY;4CACV,OAAO;4CACP,YAAY;gDAAE,UAAU;4CAAK;wCAC/B;wCACA,UAAU;4CAAE,OAAO;4CAAK,QAAQ;wCAAE;wCAClC,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mFACA,QAAQ,cACJ,mEACA;wCAEN,cAAY,CAAC,KAAK,EAAE,KAAK,eAAe,CAAC;kDAEzC,cAAA;;gDACG,QAAQ,4BACP,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;yEAEhB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAEjB,QAAQ,6BACP,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;uCAhChB;;;;;;;;;;0CAyCX,8OAAC,yLAAA,CAAA,kBAAe;gCAAC,MAAK;0CACnB,cAAc,kBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,WAAU;8CAEV,cAAA,8OAAC,0HAAA,CAAA,QAAK;wCAAC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACjB,iCACA,gBAAgB,IAAI,yEACpB,gBAAgB,IAAI,qEACpB,gBAAgB,IAAI,qEACpB,gBAAgB,IAAI,6EACpB;kDAEC,gBAAgB,IAAI,cACpB,gBAAgB,IAAI,cACpB,gBAAgB,IAAI,SACpB,gBAAgB,IAAI,SACpB;;;;;;mCAlBC;;;;yDAsBN,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCAEP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,MAAM;wCAAE,SAAS;oCAAE;oCACnB,WAAU;8CACX;mCALK;;;;;;;;;;;;;;;;kCAaZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,8OAAC,0HAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAa,WAAU;0CAAwE;;;;;;0CAG9G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,aAAY;wCACZ,MAAM;wCACN,WAAU;wCACV,WAAW;;;;;;kDAEb,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;;4BAET,iCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,UAAU;;wCAET,mCAAqB,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;mDAAiC;wCAAK;;;;;;;;;;;;0CAMrF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,YAAY;oCAAE,OAAO;oCAAM,YAAY,CAAC;gCAAE;gCAC1C,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;0CAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,sBAAsB,gBAAgB;oCAChD,WAAU;8CAET,mCACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,kBAAkB,gBAAgB;;qEAGrC;kDACG,kBAAkB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvD", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/reviews/ReviewCard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Star, Edit, Trash2, Loader2, ExternalLink, Check, X, User, Building2 } from \"lucide-react\";\r\nimport { deleteReview } from \"@/lib/actions/interactions\";\r\nimport { toast } from \"sonner\";\r\nimport { formatDistanceToNow } from \"date-fns\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\n\r\ninterface BusinessProfileDataForReview {\r\n  id: string;\r\n  business_name: string | null;\r\n  business_slug: string | null;\r\n  logo_url: string | null;\r\n}\r\n\r\ninterface ReviewData {\r\n  id: string;\r\n  rating: number;\r\n  review_text: string | null;\r\n  created_at: string;\r\n  updated_at: string;\r\n  business_profile_id: string;\r\n  user_id: string;\r\n  business_profiles: BusinessProfileDataForReview | null;\r\n  reviewer_type?: 'business' | 'customer';\r\n  reviewer_slug?: string | null;\r\n}\r\n\r\ninterface ReviewCardProps {\r\n  review: ReviewData;\r\n  onDeleteSuccess: ((_reviewId: string) => void) | null;\r\n  isReviewsReceivedTab?: boolean;\r\n}\r\n\r\nexport default function ReviewCard({\r\n  review,\r\n  onDeleteSuccess,\r\n  isReviewsReceivedTab = false,\r\n}: ReviewCardProps) {\r\n  const [isDeleting, setIsDeleting] = useState(false);\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [isUpdating, setIsUpdating] = useState(false);\r\n  const [editedRating, setEditedRating] = useState(review.rating);\r\n  const [editedReviewText, setEditedReviewText] = useState(review.review_text || \"\");\r\n\r\n  // Access business_profiles directly from the review object\r\n  const business = review.business_profiles;\r\n\r\n  // Handle potential issues with updated_at\r\n  let timeAgo;\r\n  try {\r\n    timeAgo = formatDistanceToNow(new Date(review.updated_at || review.created_at), { addSuffix: true });\r\n  } catch (_error) {\r\n    timeAgo = 'recently';\r\n  }\r\n\r\n  // Handle delete\r\n  const handleDelete = async () => {\r\n    if (!review.business_profile_id) {\r\n      toast.error('Cannot delete review: Missing business information');\r\n      return;\r\n    }\r\n\r\n    const confirmation = confirm(\"Are you sure you want to delete this review?\");\r\n    if (!confirmation) return;\r\n\r\n    try {\r\n      setIsDeleting(true);\r\n      const result = await deleteReview(review.business_profile_id);\r\n\r\n      if (result.success) {\r\n        toast.success(`Review for ${business?.business_name || 'business'} deleted.`);\r\n        if (onDeleteSuccess) {\r\n          onDeleteSuccess(review.id);\r\n        }\r\n      } else {\r\n        toast.error(`Failed to delete review: ${result.error || 'Unknown error'}`);\r\n      }\r\n    } catch (_error) {\r\n      toast.error('An error occurred while deleting the review');\r\n    } finally {\r\n      setIsDeleting(false);\r\n    }\r\n  };\r\n\r\n  // Handle edit mode toggle\r\n  const handleEdit = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle cancel edit\r\n  const handleCancelEdit = () => {\r\n    setIsEditing(false);\r\n    setEditedRating(review.rating);\r\n    setEditedReviewText(review.review_text || \"\");\r\n  };\r\n\r\n  // Handle save edit\r\n  const handleSaveEdit = async () => {\r\n    setIsUpdating(true);\r\n    try {\r\n      const response = await fetch(\"/api/customer/reviews/update\", {\r\n        method: \"PATCH\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          reviewId: review.id,\r\n          rating: editedRating,\r\n          reviewText: editedReviewText,\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (response.ok) {\r\n        toast.success(\"Review updated successfully\");\r\n        setIsEditing(false);\r\n        // Update the local review data\r\n        review.rating = editedRating;\r\n        review.review_text = editedReviewText;\r\n      } else {\r\n        toast.error(data.error || \"Failed to update review\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error updating review:\", error);\r\n      toast.error(\"An unexpected error occurred\");\r\n    } finally {\r\n      setIsUpdating(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.div\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.4 }}\r\n      whileHover={!isEditing ? { y: -5, transition: { duration: 0.2 } } : {}}\r\n      className={cn(\r\n        \"rounded-xl border border-neutral-200 dark:border-neutral-800\",\r\n        \"bg-white dark:bg-black\", // Changed to black in dark mode\r\n        \"shadow-sm p-4 transition-all duration-300 hover:shadow-md\",\r\n        \"relative overflow-hidden group\",\r\n        isEditing && \"ring-2 ring-amber-400 dark:ring-amber-600\"\r\n      )}\r\n    >\r\n      {/* Card background with subtle pattern */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none opacity-5 dark:opacity-10\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n          backgroundRepeat: \"no-repeat\",\r\n        }}\r\n      ></div>\r\n\r\n      <div className=\"relative z-10\">\r\n        {/* Business info and rating */}\r\n        <div className=\"flex items-start mb-3\">\r\n          <div className=\"flex items-center gap-3\">\r\n            {/* Make avatar clickable if it's a business reviewer */}\r\n            {review.reviewer_type === 'business' && review.reviewer_slug ? (\r\n              <Link\r\n                href={`/${review.reviewer_slug}`}\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"hover:opacity-80 transition-opacity\"\r\n              >\r\n                <Avatar className=\"h-10 w-10 border border-neutral-200 dark:border-neutral-800\">\r\n                  {business?.logo_url ? (\r\n                    <AvatarImage\r\n                      src={business.logo_url}\r\n                      alt={business?.business_name || \"Business\"}\r\n                    />\r\n                  ) : null}\r\n                  <AvatarFallback className=\"bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300\">\r\n                    {business?.business_name?.[0]?.toUpperCase() || \"B\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n              </Link>\r\n            ) : (\r\n              <Avatar className=\"h-10 w-10 border border-neutral-200 dark:border-neutral-800\">\r\n                {business?.logo_url ? (\r\n                  <AvatarImage\r\n                    src={business.logo_url}\r\n                    alt={business?.business_name || \"Business\"}\r\n                  />\r\n                ) : null}\r\n                <AvatarFallback className=\"bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300\">\r\n                  {business?.business_name?.[0]?.toUpperCase() || \"B\"}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n            )}\r\n\r\n            <div className=\"flex-1\">\r\n              {/* Business name with redirect icon */}\r\n              <div className=\"flex items-center gap-1\">\r\n                {review.reviewer_type === 'business' && review.reviewer_slug ? (\r\n                  <Link\r\n                    href={`/${review.reviewer_slug}`}\r\n                    target=\"_blank\"\r\n                    rel=\"noopener noreferrer\"\r\n                    className=\"hover:text-[var(--brand-gold)] transition-colors inline-flex items-center gap-1\"\r\n                  >\r\n                    <h3 className=\"font-medium text-neutral-800 dark:text-neutral-200\">\r\n                      {business?.business_name || \"Unknown Business\"}\r\n                    </h3>\r\n                    <ExternalLink className=\"h-3.5 w-3.5 opacity-70\" />\r\n                  </Link>\r\n                ) : (\r\n                  <h3 className=\"font-medium text-neutral-800 dark:text-neutral-200\">\r\n                    {business?.business_name || \"Unknown User\"}\r\n                  </h3>\r\n                )}\r\n              </div>\r\n\r\n              {/* Reviewer type badge - only show in Reviews Received tab */}\r\n              {isReviewsReceivedTab && (\r\n                <div className=\"flex items-center gap-1 mt-1\">\r\n                  {review.reviewer_type === 'business' ? (\r\n                    <div className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-medium\">\r\n                      <Building2 className=\"h-3 w-3\" />\r\n                      Business\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"inline-flex items-center gap-1 px-2 py-1 rounded-full bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 text-xs font-medium\">\r\n                      <User className=\"h-3 w-3\" />\r\n                      Customer\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n              {/* Rating stars - only show in non-editing mode */}\r\n              {!isEditing && (\r\n                <div className=\"flex items-center gap-1 mt-1\">\r\n                  {Array.from({ length: 5 }).map((_, i) => (\r\n                    <Star\r\n                      key={i}\r\n                      className={cn(\r\n                        \"h-4 w-4\",\r\n                        i < review.rating\r\n                          ? \"text-amber-500 fill-amber-500\"\r\n                          : \"text-neutral-300 dark:text-neutral-700\"\r\n                      )}\r\n                    />\r\n                  ))}\r\n                  <span className=\"text-xs text-neutral-500 dark:text-neutral-400 ml-1\">\r\n                    {timeAgo}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Review content */}\r\n        {isEditing ? (\r\n          // Edit mode content\r\n          <div className=\"space-y-3\">\r\n            {/* Star rating selector */}\r\n            <div className=\"flex items-center gap-1\">\r\n              {Array.from({ length: 5 }).map((_, i) => (\r\n                <button\r\n                  key={i}\r\n                  type=\"button\"\r\n                  onClick={() => setEditedRating(i + 1)}\r\n                  className=\"focus:outline-none\"\r\n                >\r\n                  <Star\r\n                    className={cn(\r\n                      \"h-6 w-6 transition-colors\",\r\n                      i < editedRating\r\n                        ? \"text-amber-500 fill-amber-500\"\r\n                        : \"text-neutral-300 dark:text-neutral-700 hover:text-amber-400\"\r\n                    )}\r\n                  />\r\n                </button>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Review text input */}\r\n            <Textarea\r\n              value={editedReviewText}\r\n              onChange={(e) => setEditedReviewText(e.target.value)}\r\n              placeholder=\"What did you like or dislike? What stood out about your experience?\"\r\n              className=\"w-full bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-lg focus:ring-amber-400 focus:border-amber-400 transition-all duration-200 resize-none\"\r\n              maxLength={500}\r\n              rows={4}\r\n            />\r\n            <div className=\"text-xs text-neutral-500 dark:text-neutral-400 mt-1 text-right\">\r\n              {editedReviewText.length}/500\r\n            </div>\r\n\r\n            {/* Edit mode buttons */}\r\n            <div className=\"flex items-center justify-end gap-2 mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                className=\"text-xs h-8 gap-1\"\r\n                onClick={handleCancelEdit}\r\n              >\r\n                <X className=\"h-3.5 w-3.5\" />\r\n                Cancel\r\n              </Button>\r\n\r\n              <Button\r\n                variant=\"default\"\r\n                size=\"sm\"\r\n                className=\"text-xs h-8 gap-1 bg-amber-500 hover:bg-amber-600 text-white\"\r\n                onClick={handleSaveEdit}\r\n                disabled={isUpdating}\r\n              >\r\n                {isUpdating ? (\r\n                  <Loader2 className=\"h-3.5 w-3.5 animate-spin\" />\r\n                ) : (\r\n                  <Check className=\"h-3.5 w-3.5\" />\r\n                )}\r\n                Save\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {/* Display mode content */}\r\n            {review.review_text && (\r\n              <div className=\"mb-4\">\r\n                <p className=\"text-neutral-700 dark:text-neutral-300 text-sm leading-relaxed\">\r\n                  {review.review_text}\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            {/* Action buttons - only show in \"My Reviews\" tab */}\r\n            {!isReviewsReceivedTab && (\r\n              <div className=\"flex items-center justify-between mt-3 pt-3 border-t border-neutral-100 dark:border-neutral-800/50\">\r\n                {/* View Business button - only show for business reviews */}\r\n                {review.reviewer_type === 'business' && business?.business_slug ? (\r\n                  <Button\r\n                    asChild\r\n                    variant=\"outline\"\r\n                    size=\"sm\"\r\n                    className=\"text-xs h-8 gap-1\"\r\n                  >\r\n                    <Link\r\n                      href={`/${business.business_slug}`}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                    >\r\n                      <ExternalLink className=\"h-3.5 w-3.5\" />\r\n                      View Business\r\n                    </Link>\r\n                  </Button>\r\n                ) : (\r\n                  <div></div> // Empty div to maintain layout when no View Business button\r\n                )}\r\n\r\n                {/* Only show edit/delete buttons if onDeleteSuccess is provided */}\r\n                {onDeleteSuccess && (\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"text-xs h-8 gap-1 text-blue-600 border-blue-200 hover:bg-blue-50 hover:border-blue-300 dark:text-blue-400 dark:border-blue-900 dark:hover:bg-blue-950/30\"\r\n                      onClick={handleEdit}\r\n                    >\r\n                      <Edit className=\"h-3.5 w-3.5\" />\r\n                      Edit\r\n                    </Button>\r\n\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      className=\"text-xs h-8 gap-1 text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300 dark:text-red-400 dark:border-red-900 dark:hover:bg-red-950/30\"\r\n                      onClick={handleDelete}\r\n                      disabled={isDeleting}\r\n                    >\r\n                      {isDeleting ? (\r\n                        <Loader2 className=\"h-3.5 w-3.5 animate-spin\" />\r\n                      ) : (\r\n                        <Trash2 className=\"h-3.5 w-3.5\" />\r\n                      )}\r\n                      Delete\r\n                    </Button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAwCe,SAAS,WAAW,EACjC,MAAM,EACN,eAAe,EACf,uBAAuB,KAAK,EACZ;IAChB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,MAAM;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,WAAW,IAAI;IAE/E,2DAA2D;IAC3D,MAAM,WAAW,OAAO,iBAAiB;IAEzC,0CAA0C;IAC1C,IAAI;IACJ,IAAI;QACF,UAAU,CAAA,GAAA,mJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,OAAO,UAAU,IAAI,OAAO,UAAU,GAAG;YAAE,WAAW;QAAK;IACpG,EAAE,OAAO,QAAQ;QACf,UAAU;IACZ;IAEA,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI,CAAC,OAAO,mBAAmB,EAAE;YAC/B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,eAAe,QAAQ;QAC7B,IAAI,CAAC,cAAc;QAEnB,IAAI;YACF,cAAc;YACd,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,mBAAmB;YAE5D,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,UAAU,iBAAiB,WAAW,SAAS,CAAC;gBAC5E,IAAI,iBAAiB;oBACnB,gBAAgB,OAAO,EAAE;gBAC3B;YACF,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,KAAK,IAAI,iBAAiB;YAC3E;QACF,EAAE,OAAO,QAAQ;YACf,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,0BAA0B;IAC1B,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,aAAa;QACb,gBAAgB,OAAO,MAAM;QAC7B,oBAAoB,OAAO,WAAW,IAAI;IAC5C;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,cAAc;QACd,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gCAAgC;gBAC3D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU,OAAO,EAAE;oBACnB,QAAQ;oBACR,YAAY;gBACd;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,aAAa;gBACb,+BAA+B;gBAC/B,OAAO,MAAM,GAAG;gBAChB,OAAO,WAAW,GAAG;YACvB,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI;YAC5B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,YAAY,CAAC,YAAY;YAAE,GAAG,CAAC;YAAG,YAAY;gBAAE,UAAU;YAAI;QAAE,IAAI,CAAC;QACrE,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,gEACA,0BACA,6DACA,kCACA,aAAa;;0BAIf,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,mCAAmC,CAAC;oBACtD,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCAEZ,OAAO,aAAa,KAAK,cAAc,OAAO,aAAa,iBAC1D,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,EAAE;oCAChC,QAAO;oCACP,KAAI;oCACJ,WAAU;8CAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;wCAAC,WAAU;;4CACf,UAAU,yBACT,8OAAC,2HAAA,CAAA,cAAW;gDACV,KAAK,SAAS,QAAQ;gDACtB,KAAK,UAAU,iBAAiB;;;;;uDAEhC;0DACJ,8OAAC,2HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,UAAU,eAAe,CAAC,EAAE,EAAE,iBAAiB;;;;;;;;;;;;;;;;yDAKtD,8OAAC,2HAAA,CAAA,SAAM;oCAAC,WAAU;;wCACf,UAAU,yBACT,8OAAC,2HAAA,CAAA,cAAW;4CACV,KAAK,SAAS,QAAQ;4CACtB,KAAK,UAAU,iBAAiB;;;;;mDAEhC;sDACJ,8OAAC,2HAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,UAAU,eAAe,CAAC,EAAE,EAAE,iBAAiB;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,OAAO,aAAa,KAAK,cAAc,OAAO,aAAa,iBAC1D,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,CAAC,EAAE,OAAO,aAAa,EAAE;gDAChC,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;wDAAG,WAAU;kEACX,UAAU,iBAAiB;;;;;;kEAE9B,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;qEAG1B,8OAAC;gDAAG,WAAU;0DACX,UAAU,iBAAiB;;;;;;;;;;;wCAMjC,sCACC,8OAAC;4CAAI,WAAU;sDACZ,OAAO,aAAa,KAAK,2BACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAY;;;;;;qEAInC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;wCAQnC,CAAC,2BACA,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,IAAI,CAAC;oDAAE,QAAQ;gDAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,kMAAA,CAAA,OAAI;wDAEH,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,WACA,IAAI,OAAO,MAAM,GACb,kCACA;uDALD;;;;;8DAST,8OAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASZ,YACC,oBAAoB;kCACpB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,gBAAgB,IAAI;wCACnC,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CACH,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6BACA,IAAI,eACA,kCACA;;;;;;uCAVH;;;;;;;;;;0CAkBX,8OAAC,6HAAA,CAAA,WAAQ;gCACP,OAAO;gCACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACnD,aAAY;gCACZ,WAAU;gCACV,WAAW;gCACX,MAAM;;;;;;0CAER,8OAAC;gCAAI,WAAU;;oCACZ,iBAAiB,MAAM;oCAAC;;;;;;;0CAI3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;;0DAET,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;;4CAAgB;;;;;;;kDAI/B,8OAAC,2HAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS;wCACT,UAAU;;4CAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CACjB;;;;;;;;;;;;;;;;;;6CAMR;;4BAEG,OAAO,WAAW,kBACjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CACV,OAAO,WAAW;;;;;;;;;;;4BAMxB,CAAC,sCACA,8OAAC;gCAAI,WAAU;;oCAEZ,OAAO,aAAa,KAAK,cAAc,UAAU,8BAChD,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,SAAQ;wCACR,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,CAAC,EAAE,SAAS,aAAa,EAAE;4CAClC,QAAO;4CACP,KAAI;;8DAEJ,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAgB;;;;;;;;;;;6DAK5C,8OAAC;;;;6CAAW,4DAA4D;;oCAIzE,iCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;;kEAET,8OAAC,2MAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAgB;;;;;;;0DAIlC,8OAAC,2HAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS;gDACT,UAAU;;oDAET,2BACC,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxB", "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/reviews/ReviewsList.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Loader2, Star } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { ReviewWithUser } from \"@/lib/actions/reviews\";\r\nimport ReviewCard from \"@/app/components/shared/reviews/ReviewCard\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\n\r\ninterface ReviewsListProps {\r\n  reviews: ReviewWithUser[];\r\n  totalReviews: number;\r\n  totalPages: number;\r\n  currentPage: number;\r\n  isLoadingReviews: boolean;\r\n  isAuthenticated: boolean;\r\n  currentUserId?: string | null;\r\n  onPageChange: (_page: number) => void;\r\n  onDeleteReview: () => void;\r\n}\r\n\r\nexport default function ReviewsList({\r\n  reviews,\r\n  totalReviews,\r\n  totalPages,\r\n  currentPage,\r\n  isLoadingReviews,\r\n  isAuthenticated,\r\n  currentUserId,\r\n  onPageChange,\r\n  onDeleteReview,\r\n}: ReviewsListProps) {\r\n  // Generate pagination items\r\n  const generatePaginationItems = () => {\r\n    const items = [];\r\n\r\n    // Always show first page\r\n    items.push(\r\n      <PaginationItem key=\"page-1\">\r\n        <PaginationLink\r\n          href=\"#\"\r\n          size=\"icon\"\r\n          onClick={(e) => {\r\n            e.preventDefault();\r\n            onPageChange(1);\r\n          }}\r\n          isActive={currentPage === 1}\r\n        >\r\n          1\r\n        </PaginationLink>\r\n      </PaginationItem>\r\n    );\r\n\r\n    // Show ellipsis if needed\r\n    if (currentPage > 3) {\r\n      items.push(\r\n        <PaginationItem key=\"ellipsis-1\">\r\n          <PaginationEllipsis />\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    // Show current page and surrounding pages\r\n    for (\r\n      let i = Math.max(2, currentPage - 1);\r\n      i <= Math.min(totalPages - 1, currentPage + 1);\r\n      i++\r\n    ) {\r\n      if (i === 1 || i === totalPages) continue; // Skip first and last page as they're always shown\r\n\r\n      items.push(\r\n        <PaginationItem key={`page-${i}`}>\r\n          <PaginationLink\r\n            href=\"#\"\r\n            size=\"icon\"\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              onPageChange(i);\r\n            }}\r\n            isActive={currentPage === i}\r\n          >\r\n            {i}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    // Show ellipsis if needed\r\n    if (currentPage < totalPages - 2) {\r\n      items.push(\r\n        <PaginationItem key=\"ellipsis-2\">\r\n          <PaginationEllipsis />\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    // Always show last page if there's more than one page\r\n    if (totalPages > 1) {\r\n      items.push(\r\n        <PaginationItem key={`page-${totalPages}`}>\r\n          <PaginationLink\r\n            href=\"#\"\r\n            size=\"icon\"\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              onPageChange(totalPages);\r\n            }}\r\n            isActive={currentPage === totalPages}\r\n          >\r\n            {totalPages}\r\n          </PaginationLink>\r\n        </PaginationItem>\r\n      );\r\n    }\r\n\r\n    return items;\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <motion.h3\r\n        initial={{ opacity: 0, y: 10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.4 }}\r\n        className=\"text-base sm:text-lg font-semibold text-neutral-800 dark:text-neutral-200 pl-1\"\r\n      >\r\n        {isLoadingReviews ? (\r\n          <span className=\"flex items-center gap-2\">\r\n            <Loader2 className=\"w-4 h-4 animate-spin text-[var(--brand-gold)]\" />\r\n            Loading reviews...\r\n          </span>\r\n        ) : reviews.length > 0 ? (\r\n          <span>Customer Reviews ({totalReviews})</span>\r\n        ) : (\r\n          <span>Reviews</span>\r\n        )}\r\n      </motion.h3>\r\n\r\n      {isLoadingReviews ? (\r\n        // Enhanced loading skeleton with staggered animation\r\n        <div className=\"space-y-6\">\r\n          {[...Array(3)].map((_, i) => (\r\n            <motion.div\r\n              key={i}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.4, delay: i * 0.1 }}\r\n              className=\"p-4 sm:p-6 border border-neutral-200 dark:border-neutral-700 rounded-xl bg-white dark:bg-neutral-900 animate-pulse shadow-sm overflow-hidden\"\r\n            >\r\n              <div className=\"flex justify-between items-start mb-4\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"h-12 w-12 rounded-full bg-neutral-200 dark:bg-neutral-700 shadow-sm\"></div>\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"h-5 w-32 bg-neutral-200 dark:bg-neutral-700 rounded-md\"></div>\r\n                    <div className=\"h-3 w-24 bg-neutral-200 dark:bg-neutral-700 rounded-md\"></div>\r\n                  </div>\r\n                </div>\r\n                <div className=\"h-6 w-20 bg-neutral-200 dark:bg-neutral-700 rounded-md\"></div>\r\n              </div>\r\n              <div className=\"flex mb-4 bg-neutral-100 dark:bg-neutral-800 p-2 rounded-lg\">\r\n                {[...Array(5)].map((_, j) => (\r\n                  <div\r\n                    key={j}\r\n                    className=\"h-5 w-5 mx-0.5 bg-neutral-200 dark:bg-neutral-700 rounded-full\"\r\n                  ></div>\r\n                ))}\r\n              </div>\r\n              <div className=\"space-y-2 pl-6\">\r\n                <div className=\"h-4 w-full bg-neutral-200 dark:bg-neutral-700 rounded-md\"></div>\r\n                <div className=\"h-4 w-full bg-neutral-200 dark:bg-neutral-700 rounded-md\"></div>\r\n                <div className=\"h-4 w-3/4 bg-neutral-200 dark:bg-neutral-700 rounded-md\"></div>\r\n              </div>\r\n            </motion.div>\r\n          ))}\r\n        </div>\r\n      ) : reviews.length > 0 ? (\r\n        <motion.div\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.4 }}\r\n          className=\"space-y-6\"\r\n        >\r\n          {/* Reviews with staggered animation */}\r\n          {reviews.map((review, index) => (\r\n            <motion.div\r\n              key={review.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.4, delay: index * 0.1 }}\r\n            >\r\n              <ReviewCard\r\n                review={{\r\n                  id: review.id,\r\n                  rating: review.rating,\r\n                  review_text: review.review_text,\r\n                  created_at: review.created_at,\r\n                  updated_at: review.updated_at,\r\n                  business_profile_id: '', // This will need to be provided from the business context\r\n                  user_id: review.user_id,\r\n                  business_profiles: {\r\n                    id: review.user_id,\r\n                    business_name: review.user_profile?.name || \"Anonymous User\",\r\n                    business_slug: review.user_profile?.business_slug || null,\r\n                    logo_url: review.user_profile?.avatar_url || null,\r\n                  },\r\n                  reviewer_type: review.user_profile?.is_business ? 'business' : 'customer',\r\n                  reviewer_slug: review.user_profile?.business_slug || null,\r\n                }}\r\n                onDeleteSuccess={\r\n                  currentUserId === review.user_id ? () => onDeleteReview() : null\r\n                }\r\n              />\r\n            </motion.div>\r\n          ))}\r\n\r\n          {/* Enhanced Pagination */}\r\n          {totalPages > 1 && (\r\n            <motion.div\r\n              initial={{ opacity: 0, y: 10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.4, delay: 0.2 }}\r\n            >\r\n              <Pagination className=\"mt-6 sm:mt-8 overflow-x-auto pb-2\">\r\n                <PaginationContent>\r\n                  {currentPage > 1 && (\r\n                    <PaginationItem>\r\n                      <motion.div\r\n                        whileHover={{ x: -2 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <PaginationPrevious\r\n                          href=\"#\"\r\n                          size=\"default\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            onPageChange(Math.max(1, currentPage - 1));\r\n                          }}\r\n                          className=\"border-neutral-200 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200 cursor-pointer\"\r\n                        />\r\n                      </motion.div>\r\n                    </PaginationItem>\r\n                  )}\r\n\r\n                  {generatePaginationItems().map((item, index) => (\r\n                    <motion.div\r\n                      key={`pagination-${index}`}\r\n                      initial={{ opacity: 0, scale: 0.8 }}\r\n                      animate={{ opacity: 1, scale: 1 }}\r\n                      transition={{ duration: 0.3, delay: 0.3 + index * 0.05 }}\r\n                    >\r\n                      {item}\r\n                    </motion.div>\r\n                  ))}\r\n\r\n                  {currentPage < totalPages && (\r\n                    <PaginationItem>\r\n                      <motion.div\r\n                        whileHover={{ x: 2 }}\r\n                        whileTap={{ scale: 0.95 }}\r\n                      >\r\n                        <PaginationNext\r\n                          href=\"#\"\r\n                          size=\"default\"\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            onPageChange(Math.min(totalPages, currentPage + 1));\r\n                          }}\r\n                          className=\"border-neutral-200 dark:border-neutral-700 hover:border-[var(--brand-gold)] dark:hover:border-[var(--brand-gold)] hover:text-[var(--brand-gold)] transition-colors duration-200 cursor-pointer\"\r\n                        />\r\n                      </motion.div>\r\n                    </PaginationItem>\r\n                  )}\r\n                </PaginationContent>\r\n              </Pagination>\r\n            </motion.div>\r\n          )}\r\n        </motion.div>\r\n      ) : (\r\n        <motion.div\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.5 }}\r\n          className=\"p-6 sm:p-10 text-center border border-dashed border-neutral-200 dark:border-neutral-700 rounded-xl bg-gradient-to-br from-neutral-50 to-white dark:from-neutral-900/50 dark:to-neutral-800/50 shadow-sm overflow-hidden\"\r\n        >\r\n          <motion.div\r\n            initial={{ scale: 0.8, opacity: 0 }}\r\n            animate={{ scale: 1, opacity: 1 }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n            className=\"bg-neutral-100 dark:bg-neutral-800 w-12 h-12 sm:w-16 sm:h-16 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4\"\r\n          >\r\n            <Star className=\"w-6 h-6 sm:w-8 sm:h-8 text-neutral-400 dark:text-neutral-500\" />\r\n          </motion.div>\r\n\r\n          <motion.h4\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.5, delay: 0.3 }}\r\n            className=\"text-base sm:text-lg font-semibold text-neutral-700 dark:text-neutral-300 mb-2\"\r\n          >\r\n            No reviews yet\r\n          </motion.h4>\r\n\r\n          <motion.p\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.5, delay: 0.4 }}\r\n            className=\"text-neutral-600 dark:text-neutral-400 max-w-md mx-auto\"\r\n          >\r\n            {isAuthenticated ? (\r\n              <span className=\"text-[var(--brand-gold)] font-medium\">\r\n                Be the first to share your experience!\r\n              </span>\r\n            ) : (\r\n              <span>\r\n                <Link\r\n                  href={`/login?redirect=${encodeURIComponent(\r\n                    window.location.pathname\r\n                  )}`}\r\n                  className=\"text-[var(--brand-gold)] hover:underline font-medium\"\r\n                >\r\n                  Sign in\r\n                </Link>{\" \"}\r\n                to leave a review and help others discover this business.\r\n              </span>\r\n            )}\r\n          </motion.p>\r\n        </motion.div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAEA;AACA;AARA;;;;;;;AA8Be,SAAS,YAAY,EAClC,OAAO,EACP,YAAY,EACZ,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,YAAY,EACZ,cAAc,EACG;IACjB,4BAA4B;IAC5B,MAAM,0BAA0B;QAC9B,MAAM,QAAQ,EAAE;QAEhB,yBAAyB;QACzB,MAAM,IAAI,eACR,8OAAC,+HAAA,CAAA,iBAAc;sBACb,cAAA,8OAAC,+HAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,MAAK;gBACL,SAAS,CAAC;oBACR,EAAE,cAAc;oBAChB,aAAa;gBACf;gBACA,UAAU,gBAAgB;0BAC3B;;;;;;WATiB;;;;;QAetB,0BAA0B;QAC1B,IAAI,cAAc,GAAG;YACnB,MAAM,IAAI,eACR,8OAAC,+HAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,+HAAA,CAAA,qBAAkB;;;;;eADD;;;;;QAIxB;QAEA,0CAA0C;QAC1C,IACE,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,IAClC,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,IAC5C,IACA;YACA,IAAI,MAAM,KAAK,MAAM,YAAY,UAAU,mDAAmD;YAE9F,MAAM,IAAI,eACR,8OAAC,+HAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,+HAAA,CAAA,iBAAc;oBACb,MAAK;oBACL,MAAK;oBACL,SAAS,CAAC;wBACR,EAAE,cAAc;wBAChB,aAAa;oBACf;oBACA,UAAU,gBAAgB;8BAEzB;;;;;;eAVgB,CAAC,KAAK,EAAE,GAAG;;;;;QAcpC;QAEA,0BAA0B;QAC1B,IAAI,cAAc,aAAa,GAAG;YAChC,MAAM,IAAI,eACR,8OAAC,+HAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,+HAAA,CAAA,qBAAkB;;;;;eADD;;;;;QAIxB;QAEA,sDAAsD;QACtD,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,eACR,8OAAC,+HAAA,CAAA,iBAAc;0BACb,cAAA,8OAAC,+HAAA,CAAA,iBAAc;oBACb,MAAK;oBACL,MAAK;oBACL,SAAS,CAAC;wBACR,EAAE,cAAc;wBAChB,aAAa;oBACf;oBACA,UAAU,gBAAgB;8BAEzB;;;;;;eAVgB,CAAC,KAAK,EAAE,YAAY;;;;;QAc7C;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAET,iCACC,8OAAC;oBAAK,WAAU;;sCACd,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;wBAAkD;;;;;;2BAGrE,QAAQ,MAAM,GAAG,kBACnB,8OAAC;;wBAAK;wBAAmB;wBAAa;;;;;;yCAEtC,8OAAC;8BAAK;;;;;;;;;;;YAIT,mBACC,qDAAqD;0BACrD,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,IAAI;wBAAI;wBAC5C,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;kDAGnB,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wCAEC,WAAU;uCADL;;;;;;;;;;0CAKX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;;uBA3BZ;;;;;;;;;uBAgCT,QAAQ,MAAM,GAAG,kBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;oBAGT,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;sCAEhD,cAAA,8OAAC,qJAAA,CAAA,UAAU;gCACT,QAAQ;oCACN,IAAI,OAAO,EAAE;oCACb,QAAQ,OAAO,MAAM;oCACrB,aAAa,OAAO,WAAW;oCAC/B,YAAY,OAAO,UAAU;oCAC7B,YAAY,OAAO,UAAU;oCAC7B,qBAAqB;oCACrB,SAAS,OAAO,OAAO;oCACvB,mBAAmB;wCACjB,IAAI,OAAO,OAAO;wCAClB,eAAe,OAAO,YAAY,EAAE,QAAQ;wCAC5C,eAAe,OAAO,YAAY,EAAE,iBAAiB;wCACrD,UAAU,OAAO,YAAY,EAAE,cAAc;oCAC/C;oCACA,eAAe,OAAO,YAAY,EAAE,cAAc,aAAa;oCAC/D,eAAe,OAAO,YAAY,EAAE,iBAAiB;gCACvD;gCACA,iBACE,kBAAkB,OAAO,OAAO,GAAG,IAAM,mBAAmB;;;;;;2BAxB3D,OAAO,EAAE;;;;;oBA+BjB,aAAa,mBACZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,+HAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC,+HAAA,CAAA,oBAAiB;;oCACf,cAAc,mBACb,8OAAC,+HAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,GAAG,CAAC;4CAAE;4CACpB,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,8OAAC,+HAAA,CAAA,qBAAkB;gDACjB,MAAK;gDACL,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,aAAa,KAAK,GAAG,CAAC,GAAG,cAAc;gDACzC;gDACA,WAAU;;;;;;;;;;;;;;;;oCAMjB,0BAA0B,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO,MAAM,QAAQ;4CAAK;sDAEtD;2CALI,CAAC,WAAW,EAAE,OAAO;;;;;oCAS7B,cAAc,4BACb,8OAAC,+HAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,YAAY;gDAAE,GAAG;4CAAE;4CACnB,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,8OAAC,+HAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,MAAK;gDACL,SAAS,CAAC;oDACR,EAAE,cAAc;oDAChB,aAAa,KAAK,GAAG,CAAC,YAAY,cAAc;gDAClD;gDACA,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAW5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCACX;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAET,gCACC,8OAAC;4BAAK,WAAU;sCAAuC;;;;;iDAIvD,8OAAC;;8CACC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,gBAAgB,EAAE,mBACvB,OAAO,QAAQ,CAAC,QAAQ,GACvB;oCACH,WAAU;8CACX;;;;;;gCAEO;gCAAI;;;;;;;;;;;;;;;;;;;;;;;;AAS5B", "debugId": null}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/reviews/ReviewSignInPrompt.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { Star } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { motion } from \"framer-motion\";\r\n\r\ninterface ReviewSignInPromptProps {\r\n  pathname: string;\r\n}\r\n\r\nexport default function ReviewSignInPrompt({ pathname }: ReviewSignInPromptProps) {\r\n  return (\r\n    <motion.div\r\n      className=\"relative p-4 sm:p-6 md:p-8 border rounded-xl bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800/90 dark:to-neutral-900/90 shadow-lg text-center border-neutral-200 dark:border-neutral-700/80 mt-6 sm:mt-10 overflow-hidden\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      {/* Background decorative elements */}\r\n      <div className=\"absolute -top-20 -right-20 w-60 h-60 bg-[var(--brand-gold)]/5 dark:bg-[var(--brand-gold)]/10 rounded-full blur-3xl pointer-events-none\" />\r\n      <div className=\"absolute -bottom-20 -left-20 w-60 h-60 bg-blue-500/5 dark:bg-blue-500/10 rounded-full blur-3xl pointer-events-none\" />\r\n\r\n      <div className=\"relative flex flex-col items-center justify-center gap-6 max-w-md mx-auto\">\r\n        {/* Animated icon */}\r\n        <motion.div\r\n          className=\"p-3 sm:p-4 rounded-full bg-gradient-to-br from-[var(--brand-gold)]/20 to-amber-500/20 text-[var(--brand-gold)] shadow-md border border-[var(--brand-gold)]/10\"\r\n          initial={{ opacity: 0, scale: 0.8 }}\r\n          animate={{ opacity: 1, scale: 1 }}\r\n          transition={{ duration: 0.5, delay: 0.1 }}\r\n        >\r\n          <motion.div\r\n            animate={{ rotate: [0, 360] }}\r\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\r\n          >\r\n            <Star className=\"w-6 h-6 sm:w-8 sm:h-8\" />\r\n          </motion.div>\r\n        </motion.div>\r\n\r\n        <div className=\"space-y-4\">\r\n          <motion.h3\r\n            className=\"text-lg sm:text-xl font-bold text-neutral-800 dark:text-neutral-200\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.2 }}\r\n          >\r\n            Write a Review\r\n          </motion.h3>\r\n\r\n          <motion.div\r\n            className=\"bg-blue-50 dark:bg-blue-900/20 p-3 sm:p-4 rounded-lg border border-blue-100 dark:border-blue-800/30 text-left\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.3 }}\r\n          >\r\n            <p className=\"text-sm text-blue-700 dark:text-blue-300 font-medium mb-1\">\r\n              Your opinion matters!\r\n            </p>\r\n            <p className=\"text-xs text-blue-600/80 dark:text-blue-400/80\">\r\n              Sign in to share your experience and help others discover great businesses. Your feedback makes a difference!\r\n            </p>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"flex flex-col sm:flex-row gap-3 pt-2\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5, delay: 0.4 }}\r\n          >\r\n            <motion.div\r\n              className=\"w-full\"\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              transition={{ duration: 0.2 }}\r\n            >\r\n              <Button\r\n                asChild\r\n                className=\"w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold-light)] text-[var(--brand-gold-foreground)] font-medium shadow-md hover:shadow-lg transition-all duration-300 py-3 rounded-lg text-base cursor-pointer h-12\"\r\n              >\r\n                <a href={`/login?message=Please log in to leave a review&redirect=${encodeURIComponent(pathname)}`}>\r\n                  Sign In\r\n                </a>\r\n              </Button>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              className=\"w-full\"\r\n              whileHover={{ scale: 1.05 }}\r\n              whileTap={{ scale: 0.95 }}\r\n              transition={{ duration: 0.2 }}\r\n            >\r\n              <Button\r\n                asChild\r\n                variant=\"outline\"\r\n                className=\"w-full border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10 font-medium transition-all duration-300 py-3 rounded-lg text-base cursor-pointer h-12\"\r\n              >\r\n                <a href={`/login?message=Create an account to leave a review&redirect=${encodeURIComponent(pathname)}`}>\r\n                  Create Account\r\n                </a>\r\n              </Button>\r\n            </motion.div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"pt-2 text-xs text-neutral-500 dark:text-neutral-400\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            transition={{ duration: 0.5, delay: 0.5 }}\r\n          >\r\n            No account required to view reviews\r\n          </motion.div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWe,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IAC9E,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,QAAQ;oCAAC;oCAAG;iCAAI;4BAAC;4BAC5B,YAAY;gCAAE,UAAU;gCAAG,QAAQ;gCAAU,MAAM;4BAAS;sCAE5D,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;0CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC;wCAAE,WAAU;kDAA4D;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;kDAAiD;;;;;;;;;;;;0CAKhE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;kDAExC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,WAAU;sDAEV,cAAA,8OAAC;gDAAE,MAAM,CAAC,wDAAwD,EAAE,mBAAmB,WAAW;0DAAE;;;;;;;;;;;;;;;;kDAMxG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC,2HAAA,CAAA,SAAM;4CACL,OAAO;4CACP,SAAQ;4CACR,WAAU;sDAEV,cAAA,8OAAC;gDAAE,MAAM,CAAC,4DAA4D,EAAE,mBAAmB,WAAW;0DAAE;;;;;;;;;;;;;;;;;;;;;;0CAO9G,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2432, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/reviews/ReviewsTab.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  ReviewWithUser,\r\n  ReviewSortBy,\r\n  fetchBusinessReviews,\r\n} from \"@/lib/actions/reviews\";\r\nimport { submitReview, deleteReview } from \"@/lib/actions/interactions\";\r\nimport AuthMessage from \"./AuthMessage\";\r\nimport ReviewsSummary from \"./ReviewsSummary\";\r\nimport ReviewForm from \"./ReviewForm\";\r\nimport ReviewsList from \"./ReviewsList\";\r\nimport ReviewSignInPrompt from \"./ReviewSignInPrompt\";\r\n\r\ninterface ReviewsTabProps {\r\n  businessProfileId: string;\r\n  isAuthenticated: boolean;\r\n  currentUserId?: string | null;\r\n  averageRating: number;\r\n  totalReviews: number;\r\n}\r\n\r\nexport default function ReviewsTab({\r\n  businessProfileId,\r\n  isAuthenticated,\r\n  currentUserId,\r\n  averageRating,\r\n  totalReviews,\r\n}: ReviewsTabProps) {\r\n  // State for reviews list\r\n  const [reviews, setReviews] = useState<ReviewWithUser[]>([]);\r\n  const [totalCount, setTotalCount] = useState<number>(0);\r\n  const [currentPage, setCurrentPage] = useState<number>(1);\r\n  const [sortBy, setSortBy] = useState<ReviewSortBy>(\"newest\");\r\n  const [isLoadingReviews, setIsLoadingReviews] = useState<boolean>(true);\r\n\r\n  // State for review form\r\n  const [ratingValue, setRatingValue] = useState<number>(0);\r\n  const [reviewText, setReviewText] = useState<string>(\"\");\r\n  const [isSubmittingReview, setIsSubmittingReview] = useState<boolean>(false);\r\n  const [userHasReviewed, setUserHasReviewed] = useState<boolean>(false);\r\n  // This state is used when setting the user's review ID\r\n  const [_userReviewId, setUserReviewId] = useState<string | null>(null);\r\n\r\n  // Constants\r\n  const REVIEWS_PER_PAGE = 5;\r\n\r\n  // Load reviews on mount and when page or sort changes\r\n  useEffect(() => {\r\n    loadReviews();\r\n  }, [currentPage, sortBy, businessProfileId]); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  // Function to load reviews\r\n  const loadReviews = async () => {\r\n    if (!businessProfileId) return;\r\n\r\n    setIsLoadingReviews(true);\r\n    try {\r\n      const result = await fetchBusinessReviews(\r\n        businessProfileId,\r\n        currentPage,\r\n        REVIEWS_PER_PAGE,\r\n        sortBy\r\n      );\r\n\r\n      if (result.error) {\r\n        toast.error(`Error loading reviews: ${result.error}`);\r\n      } else {\r\n        setReviews(result.data);\r\n        setTotalCount(result.totalCount);\r\n\r\n        // Check if current user has already reviewed\r\n        if (isAuthenticated && currentUserId) {\r\n          const userReview = result.data.find(\r\n            (review) => review.user_id === currentUserId\r\n          );\r\n          if (userReview) {\r\n            setUserHasReviewed(true);\r\n            setUserReviewId(userReview.id);\r\n            setRatingValue(userReview.rating);\r\n            setReviewText(userReview.review_text || \"\");\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading reviews:\", error);\r\n      toast.error(\"Failed to load reviews. Please try again later.\");\r\n    } finally {\r\n      setIsLoadingReviews(false);\r\n    }\r\n  };\r\n\r\n  // Handle review submission\r\n  const handleReviewSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!businessProfileId || ratingValue === 0) {\r\n      toast.warning(\"Please select a rating before submitting.\");\r\n      return;\r\n    }\r\n\r\n    setIsSubmittingReview(true);\r\n    try {\r\n      const result = await submitReview(\r\n        businessProfileId,\r\n        ratingValue,\r\n        reviewText.trim() || null\r\n      );\r\n\r\n      if (result.success) {\r\n        toast.success(\r\n          userHasReviewed\r\n            ? \"Review updated successfully!\"\r\n            : \"Review submitted successfully!\"\r\n        );\r\n        setUserHasReviewed(true);\r\n\r\n        // Reload reviews to show the new/updated review\r\n        await loadReviews();\r\n      } else {\r\n        toast.error(\r\n          `Failed to submit review: ${result.error || \"Unknown error\"}`\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error submitting review:\", error);\r\n      toast.error(\"Failed to submit review. Please try again later.\");\r\n    } finally {\r\n      setIsSubmittingReview(false);\r\n    }\r\n  };\r\n\r\n  // Handle review deletion\r\n  const handleDeleteReview = async () => {\r\n    if (!businessProfileId) return;\r\n\r\n    const confirmation = confirm(\r\n      \"Are you sure you want to delete your review?\"\r\n    );\r\n    if (!confirmation) return;\r\n\r\n    setIsSubmittingReview(true);\r\n    try {\r\n      const result = await deleteReview(businessProfileId);\r\n\r\n      if (result.success) {\r\n        toast.success(\"Review deleted successfully.\");\r\n        setUserHasReviewed(false);\r\n        setUserReviewId(null);\r\n        setRatingValue(0);\r\n        setReviewText(\"\");\r\n\r\n        // Reload reviews\r\n        await loadReviews();\r\n      } else {\r\n        toast.error(\r\n          `Failed to delete review: ${result.error || \"Unknown error\"}`\r\n        );\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting review:\", error);\r\n      toast.error(\"Failed to delete review. Please try again later.\");\r\n    } finally {\r\n      setIsSubmittingReview(false);\r\n    }\r\n  };\r\n\r\n  // Calculate total pages for pagination\r\n  const totalPages = Math.ceil(totalCount / REVIEWS_PER_PAGE);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Authentication Status Message */}\r\n      <AuthMessage isAuthenticated={isAuthenticated} />\r\n\r\n      {/* Reviews Summary */}\r\n      <ReviewsSummary\r\n        averageRating={averageRating}\r\n        totalReviews={totalReviews}\r\n        _sortBy={sortBy}\r\n        onSortChange={setSortBy}\r\n      />\r\n\r\n      {/* Reviews List */}\r\n      <ReviewsList\r\n        reviews={reviews}\r\n        totalReviews={totalCount}\r\n        totalPages={totalPages}\r\n        currentPage={currentPage}\r\n        isLoadingReviews={isLoadingReviews}\r\n        isAuthenticated={isAuthenticated}\r\n        currentUserId={currentUserId}\r\n        onPageChange={setCurrentPage}\r\n        onDeleteReview={handleDeleteReview}\r\n      />\r\n\r\n      {/* Write a Review Section */}\r\n      {isAuthenticated && currentUserId !== businessProfileId && (\r\n        <ReviewForm\r\n          userHasReviewed={userHasReviewed}\r\n          ratingValue={ratingValue}\r\n          reviewText={reviewText}\r\n          isSubmittingReview={isSubmittingReview}\r\n          onRatingChange={setRatingValue}\r\n          onReviewTextChange={setReviewText}\r\n          onSubmit={handleReviewSubmit}\r\n          onDelete={handleDeleteReview}\r\n        />\r\n      )}\r\n\r\n      {/* Message for business owners */}\r\n      {isAuthenticated && currentUserId === businessProfileId && (\r\n        <div className=\"relative p-4 sm:p-6 md:p-8 border rounded-xl bg-gradient-to-br from-white to-neutral-50 dark:from-neutral-800/90 dark:to-neutral-900/90 shadow-lg hover:shadow-xl transition-all duration-300 border-neutral-200 dark:border-neutral-700/80 mb-6 sm:mb-10 overflow-hidden\">\r\n          <div className=\"flex flex-col items-center justify-center text-center p-4\">\r\n            <div className=\"text-amber-500 dark:text-amber-400 mb-2\">\r\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-10 w-10\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n              </svg>\r\n            </div>\r\n            <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-200 mb-2\">\r\n              Business Owner Notice\r\n            </h3>\r\n            <p className=\"text-neutral-600 dark:text-neutral-400 mb-1\">\r\n              You cannot review your own business card.\r\n            </p>\r\n            <p className=\"text-sm text-neutral-500 dark:text-neutral-500\">\r\n              Business owners cannot leave reviews for their own businesses.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Sign in prompt for non-authenticated users */}\r\n      {!isAuthenticated && (\r\n        <ReviewSignInPrompt pathname={window.location.pathname} />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAKA;AAAA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;AAwBe,SAAS,WAAW,EACjC,iBAAiB,EACjB,eAAe,EACf,aAAa,EACb,aAAa,EACb,YAAY,EACI;IAChB,yBAAyB;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAElE,wBAAwB;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChE,uDAAuD;IACvD,MAAM,CAAC,eAAe,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEjE,YAAY;IACZ,MAAM,mBAAmB;IAEzB,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;QAAQ;KAAkB,GAAG,kDAAkD;IAEhG,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI,CAAC,mBAAmB;QAExB,oBAAoB;QACpB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD,EACtC,mBACA,aACA,kBACA;YAGF,IAAI,OAAO,KAAK,EAAE;gBAChB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,OAAO,KAAK,EAAE;YACtD,OAAO;gBACL,WAAW,OAAO,IAAI;gBACtB,cAAc,OAAO,UAAU;gBAE/B,6CAA6C;gBAC7C,IAAI,mBAAmB,eAAe;oBACpC,MAAM,aAAa,OAAO,IAAI,CAAC,IAAI,CACjC,CAAC,SAAW,OAAO,OAAO,KAAK;oBAEjC,IAAI,YAAY;wBACd,mBAAmB;wBACnB,gBAAgB,WAAW,EAAE;wBAC7B,eAAe,WAAW,MAAM;wBAChC,cAAc,WAAW,WAAW,IAAI;oBAC1C;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,2BAA2B;IAC3B,MAAM,qBAAqB,OAAO;QAChC,EAAE,cAAc;QAChB,IAAI,CAAC,qBAAqB,gBAAgB,GAAG;YAC3C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF;QAEA,sBAAsB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAC9B,mBACA,aACA,WAAW,IAAI,MAAM;YAGvB,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,kBACI,iCACA;gBAEN,mBAAmB;gBAEnB,gDAAgD;gBAChD,MAAM;YACR,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,yBAAyB,EAAE,OAAO,KAAK,IAAI,iBAAiB;YAEjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB;QAExB,MAAM,eAAe,QACnB;QAEF,IAAI,CAAC,cAAc;QAEnB,sBAAsB;QACtB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,eAAY,AAAD,EAAE;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,mBAAmB;gBACnB,gBAAgB;gBAChB,eAAe;gBACf,cAAc;gBAEd,iBAAiB;gBACjB,MAAM;YACR,OAAO;gBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,yBAAyB,EAAE,OAAO,KAAK,IAAI,iBAAiB;YAEjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,uCAAuC;IACvC,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;IAE1C,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,4IAAA,CAAA,UAAW;gBAAC,iBAAiB;;;;;;0BAG9B,8OAAC,+IAAA,CAAA,UAAc;gBACb,eAAe;gBACf,cAAc;gBACd,SAAS;gBACT,cAAc;;;;;;0BAIhB,8OAAC,4IAAA,CAAA,UAAW;gBACV,SAAS;gBACT,cAAc;gBACd,YAAY;gBACZ,aAAa;gBACb,kBAAkB;gBAClB,iBAAiB;gBACjB,eAAe;gBACf,cAAc;gBACd,gBAAgB;;;;;;YAIjB,mBAAmB,kBAAkB,mCACpC,8OAAC,2IAAA,CAAA,UAAU;gBACT,iBAAiB;gBACjB,aAAa;gBACb,YAAY;gBACZ,oBAAoB;gBACpB,gBAAgB;gBAChB,oBAAoB;gBACpB,UAAU;gBACV,UAAU;;;;;;YAKb,mBAAmB,kBAAkB,mCACpC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAAY,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACnG,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;sCAA8C;;;;;;sCAG3D,8OAAC;4BAAE,WAAU;sCAAiD;;;;;;;;;;;;;;;;;YAQnE,CAAC,iCACA,8OAAC,mJAAA,CAAA,UAAkB;gBAAC,UAAU,OAAO,QAAQ,CAAC,QAAQ;;;;;;;;;;;;AAI9D", "debugId": null}}, {"offset": {"line": 2700, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ReviewsTab.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport ReviewsTabComponent from \"./reviews/ReviewsTab\";\r\n\r\ninterface ReviewsTabProps {\r\n  businessProfileId: string;\r\n  isAuthenticated: boolean;\r\n  currentUserId?: string | null;\r\n  averageRating: number;\r\n  totalReviews: number;\r\n}\r\n\r\n// This is a wrapper component that re-exports the new modular ReviewsTab\r\nexport default function ReviewsTab(props: ReviewsTabProps) {\r\n  return <ReviewsTabComponent {...props} />;\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAce,SAAS,WAAW,KAAsB;IACvD,qBAAO,8OAAC,2IAAA,CAAA,UAAmB;QAAE,GAAG,KAAK;;;;;;AACvC", "debugId": null}}, {"offset": {"line": 2723, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/EnhancedTabsToggle.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { ShoppingBag, MessageSquare, Images } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\ninterface EnhancedTabsToggleProps {\r\n  activeTab: string;\r\n  onTabChange: (_tab: string) => void;\r\n  galleryCount?: number;\r\n}\r\n\r\nexport default function EnhancedTabsToggle({\r\n  activeTab,\r\n  onTabChange,\r\n  galleryCount = 0,\r\n}: EnhancedTabsToggleProps) {\r\n  return (\r\n    <motion.div\r\n      className=\"flex flex-col items-center mb-8\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n    >\r\n      <motion.h2\r\n        className=\"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 text-center\"\r\n        initial={{ opacity: 0, y: 10 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        Products, Gallery & Reviews\r\n      </motion.h2>\r\n\r\n      <motion.div\r\n        className=\"relative bg-white/90 dark:bg-neutral-800/90 p-1 sm:p-1.5 rounded-xl border border-neutral-200/70 dark:border-neutral-700/70 flex gap-1 backdrop-blur-md overflow-hidden shadow-sm\"\r\n        whileHover={{ boxShadow: \"0 8px 30px rgba(0, 0, 0, 0.06)\" }}\r\n      >\r\n        {/* Decorative elements */}\r\n        <div className=\"absolute -top-6 -right-6 w-12 h-12 bg-[var(--brand-gold)]/10 blur-xl rounded-full\"></div>\r\n        <div className=\"absolute -bottom-6 -left-6 w-12 h-12 bg-purple-500/10 blur-xl rounded-full\"></div>\r\n\r\n        {/* Products Tab */}\r\n        <motion.div\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"relative z-10\"\r\n        >\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => onTabChange(\"products\")}\r\n            className={`\r\n              relative px-2 sm:px-3 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\r\n              ${\r\n                activeTab === \"products\"\r\n                  ? \"text-black dark:text-white font-medium\"\r\n                  : \"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300\"\r\n              }\r\n            `}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <div className=\"relative\">\r\n                <ShoppingBag className=\"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4\" />\r\n              </div>\r\n              <span className=\"text-xs sm:text-xs md:text-sm font-medium\">Products</span>\r\n            </div>\r\n\r\n            {/* Active indicator */}\r\n            {activeTab === \"products\" && (\r\n              <motion.div\r\n                layoutId=\"activeTabIndicator\"\r\n                className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10 shadow-inner\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n            )}\r\n          </Button>\r\n        </motion.div>\r\n\r\n        {/* Gallery Tab */}\r\n        {galleryCount > 0 && (\r\n          <motion.div\r\n            whileHover={{ scale: 1.05 }}\r\n            whileTap={{ scale: 0.95 }}\r\n            className=\"relative z-10\"\r\n          >\r\n            <Button\r\n              variant=\"ghost\"\r\n              onClick={() => onTabChange(\"gallery\")}\r\n              className={`\r\n                relative px-2 sm:px-3 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\r\n                ${\r\n                  activeTab === \"gallery\"\r\n                    ? \"text-black dark:text-white font-medium\"\r\n                    : \"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300\"\r\n                }\r\n              `}\r\n            >\r\n              <div className=\"flex items-center\">\r\n                <div className=\"relative\">\r\n                  <Images className=\"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4\" />\r\n                </div>\r\n                <span className=\"text-xs sm:text-xs md:text-sm font-medium\">Gallery</span>\r\n                {galleryCount > 0 && (\r\n                  <span className=\"ml-1 text-xs text-neutral-400 dark:text-neutral-500\">\r\n                    ({galleryCount})\r\n                  </span>\r\n                )}\r\n              </div>\r\n\r\n              {/* Active indicator */}\r\n              {activeTab === \"gallery\" && (\r\n                <motion.div\r\n                  layoutId=\"activeTabIndicator\"\r\n                  className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10\"\r\n                  initial={{ opacity: 0 }}\r\n                  animate={{ opacity: 1 }}\r\n                  transition={{ duration: 0.3 }}\r\n                />\r\n              )}\r\n            </Button>\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Reviews Tab */}\r\n        <motion.div\r\n          whileHover={{ scale: 1.05 }}\r\n          whileTap={{ scale: 0.95 }}\r\n          className=\"relative z-10\"\r\n        >\r\n          <Button\r\n            variant=\"ghost\"\r\n            onClick={() => onTabChange(\"reviews\")}\r\n            className={`\r\n              relative px-2 sm:px-3 py-1 sm:py-1.5 h-8 sm:h-9 rounded-lg transition-all duration-300\r\n              ${\r\n                activeTab === \"reviews\"\r\n                  ? \"text-black dark:text-white font-medium\"\r\n                  : \"text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300\"\r\n              }\r\n            `}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <div className=\"relative\">\r\n                <MessageSquare className=\"mr-1 sm:mr-1.5 h-3.5 w-3.5 sm:h-4 sm:w-4\" />\r\n              </div>\r\n              <span className=\"text-xs sm:text-xs md:text-sm font-medium\">\r\n                Reviews\r\n              </span>\r\n            </div>\r\n\r\n            {/* Active indicator */}\r\n            {activeTab === \"reviews\" && (\r\n              <motion.div\r\n                layoutId=\"activeTabIndicator\"\r\n                className=\"absolute inset-0 bg-gradient-to-br from-[var(--brand-gold)]/10 to-[var(--brand-gold)]/20 dark:from-[var(--brand-gold)]/15 dark:to-[var(--brand-gold)]/25 border border-[var(--brand-gold)]/30 rounded-lg -z-10\"\r\n                initial={{ opacity: 0 }}\r\n                animate={{ opacity: 1 }}\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n            )}\r\n          </Button>\r\n        </motion.div>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAYe,SAAS,mBAAmB,EACzC,SAAS,EACT,WAAW,EACX,eAAe,CAAC,EACQ;IACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gBACR,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAC7B;;;;;;0BAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,YAAY;oBAAE,WAAW;gBAAiC;;kCAG1D,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,YAAY;4BAC3B,WAAW,CAAC;;cAEV,EACE,cAAc,aACV,2CACA,4FACL;YACH,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAEzB,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;gCAI7D,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;oBAOnC,eAAe,mBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,YAAY;4BAC3B,WAAW,CAAC;;gBAEV,EACE,cAAc,YACV,2CACA,4FACL;cACH,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;sDAEpB,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;wCAC3D,eAAe,mBACd,8OAAC;4CAAK,WAAU;;gDAAsD;gDAClE;gDAAa;;;;;;;;;;;;;gCAMpB,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;kCAQtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,YAAY;4BAC3B,WAAW,CAAC;;cAEV,EACE,cAAc,YACV,2CACA,4FACL;YACH,CAAC;;8CAED,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;;gCAM7D,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,UAAS;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}, {"offset": {"line": 3047, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/schema.ts"], "sourcesContent": ["import * as z from \"zod\";\r\nimport { IndianMobileSchema } from \"@/lib/schemas/authSchemas\";\r\n\r\n// Regular expression for validating hex color codes (e.g., #RRGGBB, #RGB)\r\n// const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/; // Removed as theme_color is removed\r\n\r\n// Zod schema for business card data validation (Phase 1)\r\nexport const businessCardSchema = z.object({\r\n  // Optional fields first\r\n  logo_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for logo/profile photo.\" })\r\n    .optional()\r\n    .or(z.literal(\"\"))\r\n    .nullable(), // Allow empty string, null, or valid URL\r\n  established_year: z\r\n    .number()\r\n    .int({ message: \"Established year must be a whole number.\" })\r\n    .min(1800, { message: \"Established year must be after 1800.\" })\r\n    .max(new Date().getFullYear(), { message: \"Established year cannot be in the future.\" })\r\n    .optional()\r\n    .nullable(),\r\n  // Address broken down - NOW REQUIRED (from onboarding)\r\n  address_line: z\r\n    .string()\r\n    .min(1, { message: \"Address line is required.\" })\r\n    .max(100, { message: \"Address line cannot exceed 100 characters.\" }),\r\n  locality: z\r\n    .string()\r\n    .min(1, { message: \"Locality/area is required.\" }),\r\n  city: z\r\n    .string()\r\n    .min(1, { message: \"City is required.\" }),\r\n  state: z\r\n    .string()\r\n    .min(1, { message: \"State is required.\" }),\r\n  pincode: z\r\n    .string()\r\n    .min(6, { message: \"Pincode must be 6 digits.\" })\r\n    .max(6, { message: \"Pincode must be 6 digits.\" })\r\n    .regex(/^\\d+$/, { message: \"Pincode must contain only digits.\" }),\r\n  phone: IndianMobileSchema, // Primary display phone - NOW REQUIRED\r\n  // timing_info removed\r\n  // delivery_info removed\r\n  // website_url removed\r\n  instagram_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Instagram.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  facebook_url: z\r\n    .string()\r\n    .url({ message: \"Invalid URL format for Facebook.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // linkedin_url removed\r\n  // twitter_url removed\r\n  // youtube_url removed\r\n  whatsapp_number: IndianMobileSchema // For wa.me link\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // call_number removed\r\n  about_bio: z\r\n    .string()\r\n    .max(100, { message: \"Bio cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  theme_color: z // Added for Growth plan\r\n    .string()\r\n    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n      message: \"Invalid hex color format (e.g., #RRGGBB or #RGB).\",\r\n    })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  // card_texture field removed as it doesn't exist in the database\r\n  business_hours: z.any().optional().nullable(), // Added for Growth plan - Using z.any() for now, refine if specific structure needed\r\n  delivery_info: z // Added for Growth plan\r\n    .string()\r\n    .max(100, { message: \"Delivery info cannot exceed 100 characters.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n  business_category: z\r\n    .string()\r\n    .min(1, { message: \"Business category is required.\" }),\r\n  \r\n  status: z.enum([\"online\", \"offline\"]).default(\"offline\"),\r\n  business_slug: z\r\n    .string()\r\n    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {\r\n      message:\r\n        \"Slug must be lowercase letters, numbers, or hyphens, and cannot start/end with a hyphen.\",\r\n    })\r\n    .min(3, { message: \"Slug must be at least 3 characters long.\" })\r\n    .optional()\r\n    .or(z.literal(\"\")),\r\n\r\n  // Required fields\r\n  member_name: z\r\n    .string()\r\n    .min(1, { message: \"Member name is required.\" })\r\n    .max(50, { message: \"Name cannot exceed 50 characters.\" }),\r\n  title: z\r\n    .string()\r\n    .min(1, { message: \"Title/Designation is required.\" })\r\n    .max(50, { message: \"Title cannot exceed 50 characters.\" }),\r\n  business_name: z\r\n    .string()\r\n    .min(1, { message: \"Business name is required.\" })\r\n    .max(100, { message: \"Business name cannot exceed 100 characters.\" }),\r\n\r\n  // Read-only/managed fields (keep for type safety if needed)\r\n  id: z.string().uuid().optional(),\r\n  contact_email: z.string().email({ message: \"Please enter a valid email address\" }).min(1, { message: \"Contact email is required\" }),\r\n  created_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n  updated_at: z.union([z.string(), z.date()]).optional().transform((val) => {\r\n    if (val instanceof Date) return val.toISOString();\r\n    return val;\r\n  }), // Handle both Date objects and strings\r\n\r\n  // Interaction fields (added in Phase 2) - make optional as they might not always be fetched\r\n  total_likes: z.number().int().nonnegative().optional(),\r\n  total_subscriptions: z.number().int().nonnegative().optional(),\r\n  average_rating: z.number().nonnegative().optional(),\r\n  total_visits: z.number().int().nonnegative().optional(),\r\n});\r\n\r\n// TypeScript type inferred from the Zod schema\r\nexport type BusinessCardData = z.infer<typeof businessCardSchema>;\r\n\r\n// Default values for initializing the form or preview (Phase 1)\r\nexport const defaultBusinessCardData: Partial<BusinessCardData> = {\r\n  member_name: \"\",\r\n  title: \"\",\r\n  business_name: \"\",\r\n  logo_url: null,\r\n  established_year: null,\r\n  address_line: \"\",\r\n  locality: \"\",\r\n  city: \"\",\r\n  state: \"\",\r\n  pincode: \"\",\r\n  phone: \"\",\r\n  instagram_url: \"\",\r\n  facebook_url: \"\",\r\n  whatsapp_number: \"\",\r\n  about_bio: \"\",\r\n  theme_color: \"\",\r\n  business_hours: null,\r\n  delivery_info: \"\",\r\n  business_category: \"\",\r\n  \r\n  status: \"offline\",\r\n  business_slug: \"\",\r\n  contact_email: \"\", // Added contact_email field\r\n};\r\n\r\n// Define which fields are strictly required to go online\r\nexport const requiredFieldsForOnline: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\",\r\n  \"contact_email\", // Added contact_email as required for online status\r\n  \"business_category\" // Added business_category as required for online status\r\n];\r\n\r\n// Define which fields are required for saving regardless of status (all onboarding fields except plan)\r\nexport const requiredFieldsForSaving: (keyof BusinessCardData)[] = [\r\n  \"member_name\",\r\n  \"title\",\r\n  \"business_name\",\r\n  \"phone\",\r\n  \"contact_email\",\r\n  \"business_category\",\r\n  \"address_line\",\r\n  \"pincode\",\r\n  \"city\",\r\n  \"state\",\r\n  \"locality\"\r\n];\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAMO,MAAM,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACzC,wBAAwB;IACxB,UAAU,CAAA,GAAA,oIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA6C,GAC5D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE,KACb,QAAQ;IACX,kBAAkB,CAAA,GAAA,oIAAA,CAAA,SACT,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAA2C,GAC1D,GAAG,CAAC,MAAM;QAAE,SAAS;IAAuC,GAC5D,GAAG,CAAC,IAAI,OAAO,WAAW,IAAI;QAAE,SAAS;IAA4C,GACrF,QAAQ,GACR,QAAQ;IACX,uDAAuD;IACvD,cAAc,CAAA,GAAA,oIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA6C;IACpE,UAAU,CAAA,GAAA,oIAAA,CAAA,SACD,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B;IAClD,MAAM,CAAA,GAAA,oIAAA,CAAA,SACG,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAoB;IACzC,OAAO,CAAA,GAAA,oIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;IAC1C,SAAS,CAAA,GAAA,oIAAA,CAAA,SACA,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B,GAC9C,KAAK,CAAC,SAAS;QAAE,SAAS;IAAoC;IACjE,OAAO,6HAAA,CAAA,qBAAkB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,sBAAsB;IACtB,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAoC,GACnD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,cAAc,CAAA,GAAA,oIAAA,CAAA,SACL,AAAD,IACL,GAAG,CAAC;QAAE,SAAS;IAAmC,GAClD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,uBAAuB;IACvB,sBAAsB;IACtB,sBAAsB;IACtB,iBAAiB,8HAAmB,iBAAiB;IAApC,CAAA,qBAAkB,CAChC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,sBAAsB;IACtB,WAAW,CAAA,GAAA,oIAAA,CAAA,SACF,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAAoC,GACxD,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,aAAa,CAAA,GAAA,oIAAA,CAAA,SACJ,AAAD,IACL,KAAK,CAAC,sCAAsC;QAC3C,SAAS;IACX,GACC,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,iEAAiE;IACjE,gBAAgB,CAAA,GAAA,oIAAA,CAAA,MAAK,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C,GAClE,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAChB,mBAAmB,CAAA,GAAA,oIAAA,CAAA,SACV,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC;IAEtD,QAAQ,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;KAAU,EAAE,OAAO,CAAC;IAC9C,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,KAAK,CAAC,8BAA8B;QACnC,SACE;IACJ,GACC,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2C,GAC7D,QAAQ,GACR,EAAE,CAAC,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,EAAE;IAEhB,kBAAkB;IAClB,aAAa,CAAA,GAAA,oIAAA,CAAA,SACJ,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA2B,GAC7C,GAAG,CAAC,IAAI;QAAE,SAAS;IAAoC;IAC1D,OAAO,CAAA,GAAA,oIAAA,CAAA,SACE,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAAiC,GACnD,GAAG,CAAC,IAAI;QAAE,SAAS;IAAqC;IAC3D,eAAe,CAAA,GAAA,oIAAA,CAAA,SACN,AAAD,IACL,GAAG,CAAC,GAAG;QAAE,SAAS;IAA6B,GAC/C,GAAG,CAAC,KAAK;QAAE,SAAS;IAA8C;IAErE,4DAA4D;IAC5D,IAAI,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,IAAI,GAAG,QAAQ;IAC9B,eAAe,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,KAAK,CAAC;QAAE,SAAS;IAAqC,GAAG,GAAG,CAAC,GAAG;QAAE,SAAS;IAA4B;IACjI,YAAY,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IACA,YAAY,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;QAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;QAAK,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;KAAI,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;QAChE,IAAI,eAAe,MAAM,OAAO,IAAI,WAAW;QAC/C,OAAO;IACT;IAEA,4FAA4F;IAC5F,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IACpD,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;IAC5D,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,WAAW,GAAG,QAAQ;IACjD,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,GAAG,WAAW,GAAG,QAAQ;AACvD;AAMO,MAAM,0BAAqD;IAChE,aAAa;IACb,OAAO;IACP,eAAe;IACf,UAAU;IACV,kBAAkB;IAClB,cAAc;IACd,UAAU;IACV,MAAM;IACN,OAAO;IACP,SAAS;IACT,OAAO;IACP,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,WAAW;IACX,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,mBAAmB;IAEnB,QAAQ;IACR,eAAe;IACf,eAAe;AACjB;AAGO,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,oBAAoB,wDAAwD;CAC7E;AAGM,MAAM,0BAAsD;IACjE;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD", "debugId": null}}, {"offset": {"line": 3229, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/WhatsAppIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Directly use React.SVGProps for type safety without an empty interface\r\nconst WhatsAppIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (\r\n  <svg\r\n    xmlns=\"http://www.w3.org/2000/svg\"\r\n    viewBox=\"0 0 24 24\"\r\n    fill=\"currentColor\" // Reverted to fill\r\n    {...props} // Spread any additional props like className, style, etc.\r\n  >\r\n    <path\r\n      d={\r\n        \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413z\"\r\n      }\r\n    />\r\n  </svg>\r\n);\r\n\r\nexport default WhatsAppIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,yEAAyE;AACzE,MAAM,eAAwD,CAAC,sBAC7D,8OAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK,eAAe,mBAAmB;;QACtC,GAAG,KAAK;kBAET,cAAA,8OAAC;YACC,GACE;;;;;;;;;;;uCAMO", "debugId": null}}, {"offset": {"line": 3260, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/InstagramIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Use React.SVGProps directly instead of an empty interface\r\nconst InstagramIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"currentColor\"\r\n      {...props} // Pass className and other props\r\n    >\r\n      <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zM12 0C8.741 0 8.333.014 7.053.072 2.695.272.273 2.69.073 7.052.014 8.333 0 8.741 0 12c0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98C8.333 23.986 8.741 24 12 24c3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98C15.668.014 15.259 0 12 0zm0 5.838a6.162 6.162 0 100 12.324 6.162 6.162 0 000-12.324zM12 16a4 4 0 110-8 4 4 0 010 8zm6.406-11.845a1.44 1.44 0 100 2.881 1.44 1.44 0 000-2.881z\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default InstagramIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,4DAA4D;AAC5D,MAAM,gBAAyD,CAAC;IAC9D,qBACE,8OAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 3292, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/icons/FacebookIcon.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\n// Use React.SVGProps directly instead of an empty interface\r\nconst FacebookIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => {\r\n  return (\r\n    <svg\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      viewBox=\"0 0 24 24\"\r\n      fill=\"currentColor\"\r\n      {...props} // Pass className and other props\r\n    >\r\n      <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\r\n    </svg>\r\n  );\r\n};\r\n\r\nexport default FacebookIcon;\r\n"], "names": [], "mappings": ";;;;;AAEA,4DAA4D;AAC5D,MAAM,eAAwD,CAAC;IAC7D,qBACE,8OAAC;QACC,OAAM;QACN,SAAQ;QACR,MAAK;QACJ,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 3324, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/utils/cardUtils.ts"], "sourcesContent": ["export const formatWhatsAppUrl = (number?: string): string | undefined => {\r\n  if (!number) return undefined;\r\n  const digits = number.replace(/\\D/g, \"\");\r\n  if (digits.length < 10) return undefined;\r\n  const formattedNumber = digits.startsWith(\"91\") ? digits : `91${digits}`;\r\n  return `https://wa.me/${formattedNumber}`;\r\n};\r\n\r\nexport const formatTelUrl = (number?: string): string | undefined => {\r\n  if (!number) return undefined;\r\n  const digits = number.replace(/\\D/g, \"\");\r\n  if (digits.length < 10) return undefined;\r\n  return `tel:+91${digits}`;\r\n};\r\n\r\nexport const formatPrice = (price: number | null | undefined): string => {\r\n  if (price === null || price === undefined) return \"N/A\";\r\n  return `₹${price.toLocaleString(\"en-IN\")}`;\r\n};\r\n\r\nexport const formatTimeTo12Hour = (time24: string): string => {\r\n  if (!time24 || time24.length < 5) return time24;\r\n\r\n  const [hourStr, minuteStr] = time24.split(\":\");\r\n  const hour = parseInt(hourStr, 10);\r\n\r\n  if (isNaN(hour)) return time24;\r\n\r\n  const period = hour >= 12 ? \"PM\" : \"AM\";\r\n  const hour12 = hour % 12 || 12;\r\n  return `${hour12}:${minuteStr} ${period}`;\r\n};\r\n\r\nexport const formatDayGroup = (days: string[]): string => {\r\n  const dayAbbreviations: Record<string, string> = {\r\n    monday: \"Mon\",\r\n    tuesday: \"Tue\",\r\n    wednesday: \"Wed\",\r\n    thursday: \"Thu\",\r\n    friday: \"Fri\",\r\n    saturday: \"Sat\",\r\n    sunday: \"Sun\",\r\n  };\r\n\r\n  const dayOrder = [\r\n    \"monday\",\r\n    \"tuesday\",\r\n    \"wednesday\",\r\n    \"thursday\",\r\n    \"friday\",\r\n    \"saturday\",\r\n    \"sunday\",\r\n  ];\r\n  const sortedDays = [...days].sort(\r\n    (a, b) => dayOrder.indexOf(a) - dayOrder.indexOf(b)\r\n  );\r\n\r\n  const shortDays = sortedDays.map((day) => dayAbbreviations[day] || day);\r\n\r\n  const weekdays = [\"monday\", \"tuesday\", \"wednesday\", \"thursday\", \"friday\"];\r\n  if (\r\n    sortedDays.length === 5 &&\r\n    weekdays.every((day) => sortedDays.includes(day))\r\n  ) {\r\n    return \"Mon-Fri\";\r\n  }\r\n\r\n  if (\r\n    sortedDays.length === 2 &&\r\n    sortedDays.includes(\"saturday\") &&\r\n    sortedDays.includes(\"sunday\")\r\n  ) {\r\n    return \"Sat-Sun\";\r\n  }\r\n\r\n  if (sortedDays.length === 7) {\r\n    return \"All days\";\r\n  }\r\n\r\n  if (isConsecutive(sortedDays, dayOrder)) {\r\n    return `${shortDays[0]}-${shortDays[shortDays.length - 1]}`;\r\n  }\r\n\r\n  return shortDays.join(\", \");\r\n};\r\n\r\nexport const isConsecutive = (days: string[], dayOrder: string[]): boolean => {\r\n  if (days.length <= 1) return true;\r\n\r\n  const indices = days\r\n    .map((day) => dayOrder.indexOf(day))\r\n    .sort((a, b) => a - b);\r\n\r\n  for (let i = 1; i < indices.length; i++) {\r\n    if (indices[i] !== indices[i - 1] + 1) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\n// Generate card style with theme colors\r\nexport const generateCardStyle = (\r\n  finalThemeColor: string\r\n): React.CSSProperties => {\r\n  return {\r\n    \"--theme-color\": finalThemeColor,\r\n    \"--theme-color-80\": `${finalThemeColor}CC`,\r\n    \"--theme-color-50\": `${finalThemeColor}80`,\r\n    \"--theme-color-30\": `${finalThemeColor}4D`,\r\n    \"--theme-color-20\": `${finalThemeColor}33`,\r\n    \"--theme-color-10\": `${finalThemeColor}1A`,\r\n    \"--theme-color-5\": `${finalThemeColor}0D`,\r\n    \"--theme-accent-end\": \"#E5C76E\", // Less yellow accent\r\n  } as React.CSSProperties;\r\n};"], "names": [], "mappings": ";;;;;;;;;AAAO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,SAAS,OAAO,OAAO,CAAC,OAAO;IACrC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO;IAC/B,MAAM,kBAAkB,OAAO,UAAU,CAAC,QAAQ,SAAS,CAAC,EAAE,EAAE,QAAQ;IACxE,OAAO,CAAC,cAAc,EAAE,iBAAiB;AAC3C;AAEO,MAAM,eAAe,CAAC;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,SAAS,OAAO,OAAO,CAAC,OAAO;IACrC,IAAI,OAAO,MAAM,GAAG,IAAI,OAAO;IAC/B,OAAO,CAAC,OAAO,EAAE,QAAQ;AAC3B;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,OAAO,CAAC,CAAC,EAAE,MAAM,cAAc,CAAC,UAAU;AAC5C;AAEO,MAAM,qBAAqB,CAAC;IACjC,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG,OAAO;IAEzC,MAAM,CAAC,SAAS,UAAU,GAAG,OAAO,KAAK,CAAC;IAC1C,MAAM,OAAO,SAAS,SAAS;IAE/B,IAAI,MAAM,OAAO,OAAO;IAExB,MAAM,SAAS,QAAQ,KAAK,OAAO;IACnC,MAAM,SAAS,OAAO,MAAM;IAC5B,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,mBAA2C;QAC/C,QAAQ;QACR,SAAS;QACT,WAAW;QACX,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,aAAa;WAAI;KAAK,CAAC,IAAI,CAC/B,CAAC,GAAG,IAAM,SAAS,OAAO,CAAC,KAAK,SAAS,OAAO,CAAC;IAGnD,MAAM,YAAY,WAAW,GAAG,CAAC,CAAC,MAAQ,gBAAgB,CAAC,IAAI,IAAI;IAEnE,MAAM,WAAW;QAAC;QAAU;QAAW;QAAa;QAAY;KAAS;IACzE,IACE,WAAW,MAAM,KAAK,KACtB,SAAS,KAAK,CAAC,CAAC,MAAQ,WAAW,QAAQ,CAAC,OAC5C;QACA,OAAO;IACT;IAEA,IACE,WAAW,MAAM,KAAK,KACtB,WAAW,QAAQ,CAAC,eACpB,WAAW,QAAQ,CAAC,WACpB;QACA,OAAO;IACT;IAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IAEA,IAAI,cAAc,YAAY,WAAW;QACvC,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,EAAE;IAC7D;IAEA,OAAO,UAAU,IAAI,CAAC;AACxB;AAEO,MAAM,gBAAgB,CAAC,MAAgB;IAC5C,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;IAE7B,MAAM,UAAU,KACb,GAAG,CAAC,CAAC,MAAQ,SAAS,OAAO,CAAC,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE,GAAG,GAAG;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAC/B;IAEA,OAAO;QACL,iBAAiB;QACjB,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,oBAAoB,GAAG,gBAAgB,EAAE,CAAC;QAC1C,mBAAmB,GAAG,gBAAgB,EAAE,CAAC;QACzC,sBAAsB;IACxB;AACF", "debugId": null}}, {"offset": {"line": 3431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardTextures.ts"], "sourcesContent": ["// Define available card textures\r\nexport type CardTexture = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  path: string;\r\n  category: \"paper\" | \"premium\" | \"modern\";\r\n  darkModeOpacity: number;\r\n  lightModeOpacity: number;\r\n  cssClass?: string; // Optional CSS class for static textures\r\n};\r\n\r\n// Array of available textures\r\nexport const cardTextures: CardTexture[] = [\r\n  {\r\n    id: \"linen-paper\",\r\n    name: \"Linen Paper\",\r\n    description: \"Classic linen texture with subtle cross-hatching\",\r\n    path: \"/textures/linen-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"cotton-paper\",\r\n    name: \"Cotton Paper\",\r\n    description: \"Soft, fibrous cotton paper texture\",\r\n    path: \"/textures/cotton-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"recycled-paper\",\r\n    name: \"Recycled Paper\",\r\n    description: \"Eco-friendly recycled paper with small flecks\",\r\n    path: \"/textures/recycled-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"laid-paper\",\r\n    name: \"Laid Paper\",\r\n    description: \"Traditional laid paper with horizontal lines\",\r\n    path: \"/textures/laid-paper.svg\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"marble\",\r\n    name: \"Marble\",\r\n    description: \"Elegant marble texture with subtle veining\",\r\n    path: \"/textures/marble.svg\",\r\n    category: \"premium\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"brushed-metal\",\r\n    name: \"Brushed Metal\",\r\n    description: \"Sophisticated brushed metal finish\",\r\n    path: \"/textures/brushed-metal.svg\",\r\n    category: \"premium\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"subtle-dots\",\r\n    name: \"Subtle Dots\",\r\n    description: \"Modern pattern with subtle dot grid\",\r\n    path: \"/textures/subtle-dots.svg\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"geometric\",\r\n    name: \"Geometric\",\r\n    description: \"Contemporary geometric pattern\",\r\n    path: \"/textures/geometric.svg\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"texture-png\",\r\n    name: \"Classic Texture\",\r\n    description: \"Original texture from the application\",\r\n    path: \"/texture.png\",\r\n    category: \"paper\",\r\n    darkModeOpacity: 0.3,\r\n    lightModeOpacity: 0.2,\r\n  },\r\n  {\r\n    id: \"none\",\r\n    name: \"No Texture\",\r\n    description: \"Clean look without any texture\",\r\n    path: \"\",\r\n    category: \"modern\",\r\n    darkModeOpacity: 0,\r\n    lightModeOpacity: 0,\r\n  },\r\n];\r\n\r\n// Function to get a texture by ID\r\nexport const getTextureById = (id: string): CardTexture => {\r\n  return cardTextures.find((texture) => texture.id === id) || cardTextures[0];\r\n};\r\n\r\n// Default texture ID\r\nexport const DEFAULT_TEXTURE_ID = \"linen-paper\";\r\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AAa1B,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,iBAAiB;QACjB,kBAAkB;IACpB;CACD;AAGM,MAAM,iBAAiB,CAAC;IAC7B,OAAO,aAAa,IAAI,CAAC,CAAC,UAAY,QAAQ,EAAE,KAAK,OAAO,YAAY,CAAC,EAAE;AAC7E;AAGO,MAAM,qBAAqB", "debugId": null}}, {"offset": {"line": 3539, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardBackgroundEffects.tsx"], "sourcesContent": ["import { getTextureById, DEFAULT_TEXTURE_ID } from \"./CardTextures\";\r\nimport { useMemo } from \"react\";\r\n\r\ninterface CardBackgroundEffectsProps {\r\n  finalThemeColor: string;\r\n}\r\n\r\nexport default function CardBackgroundEffects({ finalThemeColor: _finalThemeColor }: CardBackgroundEffectsProps) {\r\n  // Get texture details based on default texture ID\r\n  const textureDetails = useMemo(() => getTextureById(DEFAULT_TEXTURE_ID), []);\r\n\r\n  return (\r\n    <>\r\n      {/* Card background with subtle pattern */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none z-5\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/card-texture.svg\")`,\r\n          backgroundSize: \"cover\",\r\n          backgroundPosition: \"center\",\r\n          backgroundRepeat: \"no-repeat\",\r\n          opacity: 0.6,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Custom texture overlay with subtle theme compatibility */}\r\n      {textureDetails.path && (\r\n        <div\r\n          className=\"absolute inset-0 mix-blend-overlay pointer-events-none texture-background z-10 dark:opacity-[var(--dark-opacity)] opacity-[var(--light-opacity)]\"\r\n          style={\r\n            {\r\n              backgroundImage: textureDetails.path\r\n                ? `url(${textureDetails.path})`\r\n                : \"none\",\r\n              \"--dark-opacity\": `${textureDetails.darkModeOpacity * 0.7}`,\r\n              \"--light-opacity\": `${textureDetails.lightModeOpacity * 0.7}`,\r\n              backgroundSize: \"cover\",\r\n              backgroundPosition: \"center\",\r\n              backgroundRepeat: \"repeat\",\r\n            } as React.CSSProperties\r\n          }\r\n        ></div>\r\n      )}\r\n\r\n      {/* Subtle pattern overlay */}\r\n      <div\r\n        className=\"absolute inset-0 pointer-events-none z-10\"\r\n        style={{\r\n          backgroundImage: `url(\"/decorative/subtle-pattern.svg\")`,\r\n          backgroundRepeat: \"repeat\",\r\n          backgroundSize: \"20px 20px\",\r\n          opacity: 0.15,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Subtle embossed effect overlay */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-black/20 mix-blend-overlay pointer-events-none z-15 opacity-20 dark:opacity-15\"></div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAMe,SAAS,sBAAsB,EAAE,iBAAiB,gBAAgB,EAA8B;IAC7G,kDAAkD;IAClD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM,CAAA,GAAA,mLAAA,CAAA,iBAAc,AAAD,EAAE,mLAAA,CAAA,qBAAkB,GAAG,EAAE;IAE3E,qBACE;;0BAEE,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,mCAAmC,CAAC;oBACtD,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;oBAClB,SAAS;gBACX;;;;;;YAID,eAAe,IAAI,kBAClB,8OAAC;gBACC,WAAU;gBACV,OACE;oBACE,iBAAiB,eAAe,IAAI,GAChC,CAAC,IAAI,EAAE,eAAe,IAAI,CAAC,CAAC,CAAC,GAC7B;oBACJ,kBAAkB,GAAG,eAAe,eAAe,GAAG,KAAK;oBAC3D,mBAAmB,GAAG,eAAe,gBAAgB,GAAG,KAAK;oBAC7D,gBAAgB;oBAChB,oBAAoB;oBACpB,kBAAkB;gBACpB;;;;;;0BAMN,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC,qCAAqC,CAAC;oBACxD,kBAAkB;oBAClB,gBAAgB;oBAChB,SAAS;gBACX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 3611, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardCornerDecorations.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\nexport default function CardCornerDecorations() {\r\n  return (\r\n    <>\r\n      {/* Subtle corner decorations */}\r\n      <div className=\"absolute top-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(0deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute top-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(90deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 right-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(180deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n      <div className=\"absolute bottom-0 left-0 w-12 h-12 pointer-events-none z-20 opacity-20\">\r\n        <div style={{ transform: \"rotate(270deg)\" }}>\r\n          <Image\r\n            src=\"/decorative/card-border.svg\"\r\n            alt=\"\"\r\n            width={48}\r\n            height={48}\r\n            className=\"w-full h-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAe;8BACtC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAgB;8BACvC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAiB;8BACxC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,OAAO;wBAAE,WAAW;oBAAiB;8BACxC,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;;;;;;;;;;;;;AAMtB", "debugId": null}}, {"offset": {"line": 3738, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardGlowEffects.tsx"], "sourcesContent": ["export default function CardGlowEffects() {\r\n  return (\r\n    <>\r\n      {/* Subtle glow effects */}\r\n      <div className=\"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15\"></div>\r\n      <div className=\"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--theme-color-10)] blur-3xl dark:bg-[var(--theme-color-20)] dark:blur-2xl opacity-20 dark:opacity-15\"></div>\r\n      <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 rounded-full bg-[var(--theme-color-5)] blur-3xl opacity-15 dark:opacity-10\"></div>\r\n\r\n      {/* Enhanced inner glow effect with theme color */}\r\n      <div className=\"absolute inset-0 rounded-xl shadow-[inset_0_0_20px_rgba(0,0,0,0.1),inset_0_0_5px_var(--theme-color-20)] dark:shadow-[inset_0_0_20px_rgba(255,255,255,0.03),inset_0_0_5px_var(--theme-color-30)] pointer-events-none z-25\"></div>\r\n\r\n      {/* Vibrant edge glow effect */}\r\n      <div\r\n        className=\"absolute inset-x-0 bottom-0 h-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to top, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-y-0 right-0 w-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to left, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-x-0 top-0 h-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to bottom, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n      <div\r\n        className=\"absolute inset-y-0 left-0 w-[3px] pointer-events-none z-30\"\r\n        style={{\r\n          background: `linear-gradient(to right, var(--theme-color), transparent)`,\r\n          opacity: 0.8,\r\n        }}\r\n      ></div>\r\n\r\n      {/* Enhanced edge effects to simulate premium card thickness */}\r\n      <div className=\"absolute inset-x-0 bottom-0 h-4 bg-gradient-to-t from-black/40 to-transparent pointer-events-none z-25\"></div>\r\n      <div className=\"absolute inset-y-0 right-0 w-4 bg-gradient-to-l from-black/40 to-transparent pointer-events-none z-25\"></div>\r\n\r\n      {/* Enhanced highlight on top edge for premium look */}\r\n      <div className=\"absolute inset-x-0 top-0 h-[4px] bg-gradient-to-b from-white/60 to-transparent pointer-events-none z-25\"></div>\r\n      <div className=\"absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-white/60 to-transparent pointer-events-none z-25\"></div>\r\n\r\n      {/* Colorful edge highlight */}\r\n      <div className=\"absolute inset-x-0 bottom-0 h-1 bg-gradient-to-t from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70\"></div>\r\n      <div className=\"absolute inset-y-0 right-0 w-1 bg-gradient-to-l from-[var(--theme-color-30)] to-transparent pointer-events-none z-26 opacity-70\"></div>\r\n\r\n      {/* Enhanced shadow for depth with theme color influence */}\r\n      <div className=\"absolute inset-0 shadow-[0_15px_60px_rgba(0,0,0,0.3),0_5px_20px_var(--theme-color-20)] dark:shadow-[0_15px_60px_rgba(0,0,0,0.5),0_5px_20px_var(--theme-color-30)] rounded-xl pointer-events-none z-25\"></div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,wDAAwD,CAAC;oBACtE,SAAS;gBACX;;;;;;0BAEF,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,yDAAyD,CAAC;oBACvE,SAAS;gBACX;;;;;;0BAEF,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,2DAA2D,CAAC;oBACzE,SAAS;gBACX;;;;;;0BAEF,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0DAA0D,CAAC;oBACxE,SAAS;gBACX;;;;;;0BAIF,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 3876, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardHeader.tsx"], "sourcesContent": ["import { Calendar } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport NextImage from \"next/image\";\r\nimport { getThemeSpecificHeaderImage, getBrandingText, hasCustomBrandingAccess, shouldShowDukancardBranding } from \"@/lib/utils/customBranding\";\r\n\r\ninterface CardHeaderProps {\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n  establishedYear?: number | null;\r\n  customBranding?: {\r\n    hide_dukancard_branding?: boolean;\r\n    custom_header_text?: string;\r\n    custom_header_image_url?: string; // Legacy field\r\n    custom_header_image_light_url?: string; // Light theme\r\n    custom_header_image_dark_url?: string; // Dark theme\r\n  };\r\n}\r\n\r\nexport default function CardHeader({ userPlan, establishedYear, customBranding }: CardHeaderProps) {\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  // Get theme-specific header image with fallback logic\r\n  const themeSpecificHeaderImage = getThemeSpecificHeaderImage(\r\n    userPlan,\r\n    customBranding,\r\n    resolvedTheme\r\n  );\r\n\r\n  // Get custom branding text\r\n  const customBrandingText = getBrandingText(userPlan, customBranding);\r\n\r\n  // Check if user has Pro/Enterprise access\r\n  const hasProEnterpriseAccess = hasCustomBrandingAccess(userPlan);\r\n\r\n  // Determine what to show in the header based on plan and toggle state\r\n  const shouldShowCustomHeaderImage = hasProEnterpriseAccess &&\r\n    customBranding?.hide_dukancard_branding &&\r\n    themeSpecificHeaderImage;\r\n\r\n  const shouldShowCustomHeaderText = hasProEnterpriseAccess &&\r\n    customBranding?.hide_dukancard_branding &&\r\n    customBrandingText &&\r\n    !shouldShowCustomHeaderImage; // Only show text if no image (image has priority)\r\n\r\n  // Use the centralized function for consistent branding logic\r\n  const shouldShowDukancardBrandingResult = shouldShowDukancardBranding(userPlan, customBranding);\r\n\r\n  return (\r\n    <div className=\"flex justify-between items-start\">\r\n      <div className=\"flex items-center\">\r\n        {/* Priority: Theme-specific header image > Custom header text > Dukancard branding */}\r\n        {shouldShowCustomHeaderImage ? (\r\n          <div className=\"max-w-[120px] max-h-[32px] overflow-hidden\">\r\n            <NextImage\r\n              src={themeSpecificHeaderImage}\r\n              alt=\"Custom header\"\r\n              width={120}\r\n              height={32}\r\n              className=\"h-8 w-auto object-contain\"\r\n              style={{ maxWidth: '120px', maxHeight: '32px' }}\r\n              onError={(e) => {\r\n                // Gracefully handle broken images by hiding the element\r\n                e.currentTarget.style.display = 'none';\r\n              }}\r\n            />\r\n          </div>\r\n        ) : shouldShowCustomHeaderText ? (\r\n          <span className=\"font-medium text-sm text-[var(--theme-color)]\">\r\n            {customBrandingText}\r\n          </span>\r\n        ) : (\r\n          /* Show Dukancard branding if toggle is OFF or no custom content */\r\n          shouldShowDukancardBrandingResult && (\r\n            <span className=\"font-bold text-md text-[var(--theme-color)]\">\r\n              Dukan\r\n              <span className=\"text-neutral-900 dark:text-white\">card</span>\r\n            </span>\r\n          )\r\n        )}\r\n      </div>\r\n\r\n      {/* Established Year badge */}\r\n      {establishedYear && (\r\n        <div className=\"flex items-center justify-center text-xs py-0.5 px-2 rounded-full bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)]\">\r\n          <Calendar className=\"w-3 h-3 mr-1 text-[var(--theme-color)]\" />\r\n          <span className=\"font-semibold\">Est. {establishedYear}</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAce,SAAS,WAAW,EAAE,QAAQ,EAAE,eAAe,EAAE,cAAc,EAAmB;IAC/F,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEjC,sDAAsD;IACtD,MAAM,2BAA2B,CAAA,GAAA,8HAAA,CAAA,8BAA2B,AAAD,EACzD,UACA,gBACA;IAGF,2BAA2B;IAC3B,MAAM,qBAAqB,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,UAAU;IAErD,0CAA0C;IAC1C,MAAM,yBAAyB,CAAA,GAAA,8HAAA,CAAA,0BAAuB,AAAD,EAAE;IAEvD,sEAAsE;IACtE,MAAM,8BAA8B,0BAClC,gBAAgB,2BAChB;IAEF,MAAM,6BAA6B,0BACjC,gBAAgB,2BAChB,sBACA,CAAC,6BAA6B,kDAAkD;IAElF,6DAA6D;IAC7D,MAAM,oCAAoC,CAAA,GAAA,8HAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU;IAEhF,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEZ,4CACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAS;wBACR,KAAK;wBACL,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,OAAO;4BAAE,UAAU;4BAAS,WAAW;wBAAO;wBAC9C,SAAS,CAAC;4BACR,wDAAwD;4BACxD,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;wBAClC;;;;;;;;;;2BAGF,2CACF,8OAAC;oBAAK,WAAU;8BACb;;;;;2BAGH,iEAAiE,GACjE,mDACE,8OAAC;oBAAK,WAAU;;wBAA8C;sCAE5D,8OAAC;4BAAK,WAAU;sCAAmC;;;;;;;;;;;;;;;;;YAO1D,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC;wBAAK,WAAU;;4BAAgB;4BAAM;;;;;;;;;;;;;;;;;;;AAKhD", "debugId": null}}, {"offset": {"line": 4002, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardProfile.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  Too<PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>Provider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { User, Building, Info, Loader2 } from \"lucide-react\";\r\n\r\ntype LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface CardProfileProps {\r\n  logo_url?: string | null;\r\n  localPreviewUrl?: string | null;\r\n  logoUploadStatus: LogoUploadStatus;\r\n  member_name?: string;\r\n  business_name?: string;\r\n  title?: string;\r\n  about_bio?: string;\r\n  finalThemeColor: string;\r\n}\r\n\r\nexport default function CardProfile({\r\n  logo_url,\r\n  localPreviewUrl,\r\n  logoUploadStatus,\r\n  member_name: _member_name,\r\n  business_name,\r\n  title: _title,\r\n  about_bio,\r\n  finalThemeColor,\r\n}: CardProfileProps) {\r\n  return (\r\n    <div className=\"flex flex-col items-center\">\r\n      <div className=\"relative w-20 h-20 sm:w-24 sm:h-24 rounded-full border-3 border-[var(--theme-color)] overflow-hidden flex items-center justify-center shadow-lg mb-2 sm:mb-3 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 transform hover:scale-105 transition-transform duration-300\">\r\n        {/* Only render Image if we have a valid URL */}\r\n        {(localPreviewUrl || (logo_url && typeof logo_url === 'string' && logo_url.trim() !== \"\")) && (\r\n          <Image\r\n            src={localPreviewUrl || (logo_url || \"\")}\r\n            alt={`${business_name} logo`}\r\n            width={96}\r\n            height={96}\r\n            className=\"object-cover w-full h-full\"\r\n            onError={(e) => (e.currentTarget.style.display = \"none\")}\r\n          />\r\n        )}\r\n        {!localPreviewUrl &&\r\n          (!logo_url || (typeof logo_url === 'string' && logo_url.trim() === \"\")) &&\r\n          logoUploadStatus !== \"uploading\" && (\r\n            <User\r\n              className=\"w-12 h-12 opacity-50\"\r\n              color={finalThemeColor}\r\n            />\r\n          )}\r\n        {logoUploadStatus === \"uploading\" && (\r\n          <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\r\n            <Loader2 className=\"w-8 h-8 text-white animate-spin\" />\r\n          </div>\r\n        )}\r\n\r\n        {/* Subtle glow effect for the profile image */}\r\n        <div className=\"absolute inset-0 bg-gradient-to-tr from-[var(--theme-color-10)] via-transparent to-[var(--theme-color-10)] opacity-40\"></div>\r\n        <div className=\"absolute -bottom-1 -right-1 w-full h-full rounded-full bg-black/5 blur-sm -z-10\"></div>\r\n      </div>\r\n\r\n      {/* Business name only */}\r\n      <h3 className=\"text-base sm:text-lg font-bold text-[--theme-color] mb-2 tracking-wide px-2 text-center\">\r\n        {business_name ? (\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <span className=\"line-clamp-1 relative cursor-default\">\r\n                  {/* Simple text without shadow effect */}\r\n                  <span className=\"relative\">\r\n                    {business_name}\r\n                  </span>\r\n                </span>\r\n              </TooltipTrigger>\r\n              <TooltipContent>\r\n                <p>{business_name}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        ) : (\r\n          <Building\r\n            className=\"inline-block h-5 w-5 opacity-50\"\r\n            color={finalThemeColor}\r\n          />\r\n        )}\r\n      </h3>\r\n\r\n      {about_bio && (\r\n        <div className=\"flex items-start text-xs text-neutral-600 dark:text-neutral-300 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 rounded-lg max-w-xs mx-auto mb-2 sm:mb-3\">\r\n          <Info className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n          <TooltipProvider>\r\n            <Tooltip>\r\n              <TooltipTrigger asChild>\r\n                <p className=\"text-start line-clamp-2 cursor-default\">\r\n                  {about_bio}\r\n                </p>\r\n              </TooltipTrigger>\r\n              <TooltipContent className=\"max-w-xs\">\r\n                <p>{about_bio}</p>\r\n              </TooltipContent>\r\n            </Tooltip>\r\n          </TooltipProvider>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AAMA;AAAA;AAAA;AAAA;;;;;AAee,SAAS,YAAY,EAClC,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,aAAa,YAAY,EACzB,aAAa,EACb,OAAO,MAAM,EACb,SAAS,EACT,eAAe,EACE;IACjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBAEZ,CAAC,mBAAoB,YAAY,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,EAAG,mBACvF,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,mBAAoB,YAAY;wBACrC,KAAK,GAAG,cAAc,KAAK,CAAC;wBAC5B,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,SAAS,CAAC,IAAO,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;;oBAGpD,CAAC,mBACA,CAAC,CAAC,YAAa,OAAO,aAAa,YAAY,SAAS,IAAI,OAAO,EAAG,KACtE,qBAAqB,6BACnB,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,OAAO;;;;;;oBAGZ,qBAAqB,6BACpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAKvB,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAG,WAAU;0BACX,8BACC,8OAAC,4HAAA,CAAA,kBAAe;8BACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0CACN,8OAAC,4HAAA,CAAA,iBAAc;gCAAC,OAAO;0CACrB,cAAA,8OAAC;oCAAK,WAAU;8CAEd,cAAA,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;;;;;0CAIP,8OAAC,4HAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;yCAKV,8OAAC,0MAAA,CAAA,WAAQ;oBACP,WAAU;oBACV,OAAO;;;;;;;;;;;YAKZ,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC,4HAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;8CACN,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;8CAGL,8OAAC,4HAAA,CAAA,iBAAc;oCAAC,WAAU;8CACxB,cAAA,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 4212, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardDivider.tsx"], "sourcesContent": ["export default function CardDivider() {\r\n  return (\r\n    <div className=\"h-6 w-full mx-auto max-w-xs mb-3 relative flex items-center justify-center\">\r\n      <div\r\n        className=\"h-px w-full absolute\"\r\n        style={{\r\n          background: `linear-gradient(to right, transparent, var(--theme-color-30), transparent)`,\r\n        }}\r\n      ></div>\r\n      <div className=\"relative z-10 flex items-center justify-center space-x-2\">\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]\"></div>\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-30)]\"></div>\r\n        <div className=\"w-1.5 h-1.5 rounded-full bg-[var(--theme-color-50)]\"></div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0EAA0E,CAAC;gBAC1F;;;;;;0BAEF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 4274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/CardBusinessInfo.tsx"], "sourcesContent": ["import {\r\n  Toolt<PERSON>,\r\n  TooltipContent,\r\n  Toolt<PERSON><PERSON>rovider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { Phone, MapPin, Clock, Truck, Mail } from \"lucide-react\";\r\nimport { formatTimeTo12Hour, formatDayGroup } from \"./utils/cardUtils\";\r\n\r\ninterface CardBusinessInfoProps {\r\n  fullAddress?: string;\r\n  displayAddressLine?: string;\r\n  locality?: string;\r\n  displayCityStatePin?: string;\r\n  phone?: string;\r\n  displayPhone?: string;\r\n  isAuthenticated: boolean;\r\n  telUrl?: string;\r\n  displayEmail?: string;\r\n  mailtoUrl?: string;\r\n  business_hours?: Record<string, { isOpen?: boolean; openTime?: string; closeTime?: string }> | null;\r\n  delivery_info?: string;\r\n}\r\n\r\nexport default function CardBusinessInfo({\r\n  fullAddress,\r\n  displayAddressLine,\r\n  locality,\r\n  displayCityStatePin,\r\n  phone,\r\n  displayPhone,\r\n  isAuthenticated,\r\n  telUrl,\r\n  displayEmail,\r\n  mailtoUrl,\r\n  business_hours,\r\n  delivery_info,\r\n}: CardBusinessInfoProps) {\r\n  return (\r\n    <div className=\"flex flex-col gap-2 sm:gap-3 max-w-xs mx-auto overflow-hidden\">\r\n      {/* Address section (on top) */}\r\n      {fullAddress && (\r\n        <div className=\"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full\">\r\n          <div className=\"flex items-start mb-2.5\">\r\n            <MapPin className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"flex flex-col overflow-hidden\">\r\n              {displayAddressLine && locality ? (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <span className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                        {displayAddressLine}, {locality}\r\n                      </span>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>\r\n                        {displayAddressLine}, {locality}\r\n                      </p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              ) : (\r\n                <>\r\n                  {displayAddressLine && (\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <span className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                            {displayAddressLine}\r\n                          </span>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>{displayAddressLine}</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  )}\r\n                  {locality && (\r\n                    <TooltipProvider>\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <span className=\"text-xs text-neutral-600 dark:text-neutral-300 line-clamp-1 cursor-default\">\r\n                            {locality}\r\n                          </span>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>{locality}</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    </TooltipProvider>\r\n                  )}\r\n                </>\r\n              )}\r\n              {displayCityStatePin && (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <span className=\"text-xs text-neutral-500 dark:text-neutral-400 line-clamp-1 cursor-default\">\r\n                        {displayCityStatePin}\r\n                      </span>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{displayCityStatePin}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n\r\n        </div>\r\n      )}\r\n      {/* Contact info section (below address) */}\r\n      <div className=\"text-sm text-neutral-700 dark:text-neutral-100 bg-neutral-800/5 dark:bg-neutral-300/5 p-2 sm:p-2.5 rounded-lg w-full\">\r\n        {/* Phone */}\r\n        {phone && (\r\n          <div className=\"flex items-center mb-2.5\">\r\n            <Phone className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"overflow-hidden\">\r\n              {displayPhone && (\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <a\r\n                        href={isAuthenticated && telUrl ? telUrl : \"#\"}\r\n                        className={\r\n                          isAuthenticated && telUrl\r\n                            ? \"hover:underline font-medium text-xs truncate block\"\r\n                            : \"cursor-default font-medium text-xs truncate block\"\r\n                        }\r\n                      >\r\n                        {displayPhone}\r\n                      </a>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>{displayPhone}</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              )}\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Email */}\r\n        {displayEmail && (\r\n          <div className=\"flex items-center mb-2.5\">\r\n            <Mail className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <div className=\"overflow-hidden\">\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <a\r\n                      href={\r\n                        isAuthenticated && mailtoUrl ? mailtoUrl : \"#\"\r\n                      }\r\n                      className={\r\n                        isAuthenticated\r\n                          ? \"hover:underline font-medium text-xs truncate block\"\r\n                          : \"cursor-default font-medium text-xs truncate block\"\r\n                      }\r\n                    >\r\n                      {displayEmail}\r\n                    </a>\r\n                  </TooltipTrigger>\r\n                  <TooltipContent>\r\n                    <p>{displayEmail}</p>\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {/* Business hours - only show if at least one day is open */}\r\n        {(() => {\r\n          // Check if business_hours exists and is an object\r\n          if (!business_hours || typeof business_hours !== \"object\" || Object.keys(business_hours).length === 0) {\r\n            return null;\r\n          }\r\n\r\n          try {\r\n            // Check if at least one day has isOpen: true\r\n            const hasOpenDay = Object.values(business_hours).some(\r\n              (hours) => hours && typeof hours === \"object\" && (hours as { isOpen?: boolean }).isOpen\r\n            );\r\n\r\n            // If no days are open, don't show the business hours section\r\n            if (!hasOpenDay) {\r\n              return null;\r\n            }\r\n\r\n            // Get open days and their hours\r\n            const openDays = Object.entries(\r\n              business_hours as Record<string, unknown>\r\n            )\r\n              .filter(([, hours]) => {\r\n                return (\r\n                  hours &&\r\n                  typeof hours === \"object\" &&\r\n                  (hours as { isOpen?: boolean }).isOpen\r\n                );\r\n              })\r\n              .map(([day, hours]) => {\r\n                const hourData = hours as {\r\n                  isOpen: boolean;\r\n                  openTime?: string;\r\n                  closeTime?: string;\r\n                };\r\n                return {\r\n                  day,\r\n                  hours:\r\n                    hourData.openTime && hourData.closeTime\r\n                      ? `${formatTimeTo12Hour(\r\n                          hourData.openTime\r\n                        )} - ${formatTimeTo12Hour(\r\n                          hourData.closeTime\r\n                        )}`\r\n                      : \"\",\r\n                };\r\n              })\r\n              .filter((item) => item.hours);\r\n\r\n            // If no valid open days with hours, return null\r\n            if (openDays.length === 0) {\r\n              return null;\r\n            }\r\n\r\n            // Group days with the same hours\r\n            const hourGroups: Record<string, string[]> = {};\r\n            openDays.forEach(({ day, hours }) => {\r\n              if (!hourGroups[hours]) {\r\n                hourGroups[hours] = [];\r\n              }\r\n              hourGroups[hours].push(day);\r\n            });\r\n\r\n            // Return the business hours section with formatted day groups\r\n            return (\r\n              <div className=\"flex items-start mb-2.5\">\r\n                <Clock className=\"w-4 h-4 mr-2 mt-0.5 flex-shrink-0 text-[var(--theme-color)]\" />\r\n                <div className=\"text-xs font-medium\">\r\n                  {Object.entries(hourGroups).map(\r\n                    ([hours, days], index) => {\r\n                      // Format days (e.g., \"Mon, Tue, Wed\" or \"Mon-Wed\")\r\n                      const formattedDays = formatDayGroup(days);\r\n\r\n                      return (\r\n                        <div\r\n                          key={index}\r\n                          className=\"flex justify-between\"\r\n                        >\r\n                          <span className=\"capitalize\">\r\n                            {formattedDays}:\r\n                          </span>\r\n                          <span className=\"ml-2\">{hours}</span>\r\n                        </div>\r\n                      );\r\n                    }\r\n                  )}\r\n                </div>\r\n              </div>\r\n            );\r\n          } catch (error) {\r\n            // If there's an error parsing the business hours, return null\r\n            console.error(\"Error parsing business hours:\", error);\r\n            return null;\r\n          }\r\n        })()}\r\n        {/* Delivery info - now available for all users */}\r\n        {delivery_info && (\r\n          <div className=\"flex items-center\">\r\n            <Truck className=\"w-4 h-4 mr-2 flex-shrink-0 text-[var(--theme-color)]\" />\r\n            <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <p className=\"font-medium text-xs line-clamp-1 cursor-default\">\r\n                    {delivery_info}\r\n                  </p>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>{delivery_info}</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AAMA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAiBe,SAAS,iBAAiB,EACvC,WAAW,EACX,kBAAkB,EAClB,QAAQ,EACR,mBAAmB,EACnB,KAAK,EACL,YAAY,EACZ,eAAe,EACf,MAAM,EACN,YAAY,EACZ,SAAS,EACT,cAAc,EACd,aAAa,EACS;IACtB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAI,WAAU;;gCACZ,sBAAsB,yBACrB,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC;oDAAK,WAAU;;wDACb;wDAAmB;wDAAG;;;;;;;;;;;;0DAG3B,8OAAC,4HAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;;wDACE;wDAAmB;wDAAG;;;;;;;;;;;;;;;;;;;;;;yDAM/B;;wCACG,oCACC,8OAAC,4HAAA,CAAA,kBAAe;sDACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;kEACN,8OAAC,4HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;kEAGL,8OAAC,4HAAA,CAAA,iBAAc;kEACb,cAAA,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;wCAKX,0BACC,8OAAC,4HAAA,CAAA,kBAAe;sDACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;kEACN,8OAAC,4HAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC;4DAAK,WAAU;sEACb;;;;;;;;;;;kEAGL,8OAAC,4HAAA,CAAA,iBAAc;kEACb,cAAA,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;gCAOf,qCACC,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC;oDAAK,WAAU;8DACb;;;;;;;;;;;0DAGL,8OAAC,4HAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYpB,8OAAC;gBAAI,WAAU;;oBAEZ,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAI,WAAU;0CACZ,8BACC,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC;oDACC,MAAM,mBAAmB,SAAS,SAAS;oDAC3C,WACE,mBAAmB,SACf,uDACA;8DAGL;;;;;;;;;;;0DAGL,8OAAC,4HAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBASjB,8BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC;oDACC,MACE,mBAAmB,YAAY,YAAY;oDAE7C,WACE,kBACI,uDACA;8DAGL;;;;;;;;;;;0DAGL,8OAAC,4HAAA,CAAA,iBAAc;0DACb,cAAA,8OAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQf,CAAC;wBACA,kDAAkD;wBAClD,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,YAAY,OAAO,IAAI,CAAC,gBAAgB,MAAM,KAAK,GAAG;4BACrG,OAAO;wBACT;wBAEA,IAAI;4BACF,6CAA6C;4BAC7C,MAAM,aAAa,OAAO,MAAM,CAAC,gBAAgB,IAAI,CACnD,CAAC,QAAU,SAAS,OAAO,UAAU,YAAY,AAAC,MAA+B,MAAM;4BAGzF,6DAA6D;4BAC7D,IAAI,CAAC,YAAY;gCACf,OAAO;4BACT;4BAEA,gCAAgC;4BAChC,MAAM,WAAW,OAAO,OAAO,CAC7B,gBAEC,MAAM,CAAC,CAAC,GAAG,MAAM;gCAChB,OACE,SACA,OAAO,UAAU,YACjB,AAAC,MAA+B,MAAM;4BAE1C,GACC,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;gCAChB,MAAM,WAAW;gCAKjB,OAAO;oCACL;oCACA,OACE,SAAS,QAAQ,IAAI,SAAS,SAAS,GACnC,GAAG,CAAA,GAAA,yLAAA,CAAA,qBAAkB,AAAD,EAClB,SAAS,QAAQ,EACjB,GAAG,EAAE,CAAA,GAAA,yLAAA,CAAA,qBAAkB,AAAD,EACtB,SAAS,SAAS,GACjB,GACH;gCACR;4BACF,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,KAAK;4BAE9B,gDAAgD;4BAChD,IAAI,SAAS,MAAM,KAAK,GAAG;gCACzB,OAAO;4BACT;4BAEA,iCAAiC;4BACjC,MAAM,aAAuC,CAAC;4BAC9C,SAAS,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE;gCAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;oCACtB,UAAU,CAAC,MAAM,GAAG,EAAE;gCACxB;gCACA,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;4BACzB;4BAEA,8DAA8D;4BAC9D,qBACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,YAAY,GAAG,CAC7B,CAAC,CAAC,OAAO,KAAK,EAAE;4CACd,mDAAmD;4CACnD,MAAM,gBAAgB,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD,EAAE;4CAErC,qBACE,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC;wDAAK,WAAU;;4DACb;4DAAc;;;;;;;kEAEjB,8OAAC;wDAAK,WAAU;kEAAQ;;;;;;;+CANnB;;;;;wCASX;;;;;;;;;;;;wBAKV,EAAE,OAAO,OAAO;4BACd,8DAA8D;4BAC9D,QAAQ,KAAK,CAAC,iCAAiC;4BAC/C,OAAO;wBACT;oBACF,CAAC;oBAEA,+BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC,4HAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;sDACN,8OAAC,4HAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC;gDAAE,WAAU;0DACV;;;;;;;;;;;sDAGL,8OAAC,4HAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 4812, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%28dashboard%29/dashboard/business/card/components/BusinessCardPreview.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useMemo } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { BusinessCardData, defaultBusinessCardData } from \"../schema\";\r\nimport QRCode from \"react-qr-code\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  Phone,\r\n  ShoppingBag,\r\n  Star,\r\n  Heart,\r\n  UserPlus,\r\n  QrCode, // Added for fake QR code in demo mode\r\n} from \"lucide-react\";\r\nimport WhatsAppIcon from \"@/app/components/icons/WhatsAppIcon\";\r\nimport InstagramIcon from \"@/app/components/icons/InstagramIcon\";\r\nimport FacebookIcon from \"@/app/components/icons/FacebookIcon\";\r\nimport { ProductServiceData } from \"../../products/actions\";\r\nimport {\r\n  maskEmail,\r\n  maskPhoneNumber,\r\n  formatIndianNumberShort,\r\n} from \"@/lib/utils\";\r\nimport {\r\n  formatWhatsAppUrl,\r\n  formatTelUrl,\r\n  formatPrice,\r\n} from \"./utils/cardUtils\";\r\nimport CardBackgroundEffects from \"./CardBackgroundEffects\";\r\nimport CardCornerDecorations from \"./CardCornerDecorations\";\r\nimport CardGlowEffects from \"./CardGlowEffects\";\r\nimport CardHeader from \"./CardHeader\";\r\nimport CardProfile from \"./CardProfile\";\r\nimport CardDivider from \"./CardDivider\";\r\nimport CardBusinessInfo from \"./CardBusinessInfo\";\r\n\r\n\r\ntype LogoUploadStatus = \"idle\" | \"uploading\" | \"success\" | \"error\";\r\n\r\ninterface BusinessCardPreviewProps {\r\n  data: BusinessCardData & {\r\n    products_services?: ProductServiceData[];\r\n  };\r\n  totalLikes?: number;\r\n  totalSubscriptions?: number;\r\n  averageRating?: number;\r\n  isSubscribed?: boolean;\r\n  hasLiked?: boolean;\r\n  isLoadingInteraction?: boolean;\r\n  logoUploadStatus?: LogoUploadStatus;\r\n  localPreviewUrl?: string | null;\r\n  isAuthenticated?: boolean;\r\n  isCurrentUserBusiness?: boolean;\r\n  isDemo?: boolean; // New prop to indicate if this is a demo card for homepage\r\n}\r\n\r\nexport default function BusinessCardPreview({\r\n  data,\r\n  logoUploadStatus = \"idle\",\r\n  localPreviewUrl = null,\r\n  isAuthenticated = false,\r\n  totalLikes = 0,\r\n  totalSubscriptions = 0,\r\n  averageRating = 0,\r\n  isDemo = false, // New prop with default value\r\n}: BusinessCardPreviewProps) {\r\n  const {\r\n    logo_url = data.logo_url ?? defaultBusinessCardData.logo_url,\r\n    member_name = defaultBusinessCardData.member_name || \"Your Name\",\r\n    business_name = defaultBusinessCardData.business_name ||\r\n      \"Your Business Name\",\r\n    about_bio = defaultBusinessCardData.about_bio,\r\n    address_line = defaultBusinessCardData.address_line,\r\n    locality = data.locality ?? defaultBusinessCardData.locality,\r\n    city = defaultBusinessCardData.city,\r\n    state = defaultBusinessCardData.state,\r\n    established_year = data.established_year ?? defaultBusinessCardData.established_year,\r\n    pincode = defaultBusinessCardData.pincode,\r\n    phone = defaultBusinessCardData.phone,\r\n    instagram_url = defaultBusinessCardData.instagram_url,\r\n    facebook_url = defaultBusinessCardData.facebook_url,\r\n    whatsapp_number = defaultBusinessCardData.whatsapp_number,\r\n    theme_color = data.theme_color,\r\n    business_hours = data.business_hours,\r\n    delivery_info = data.delivery_info,\r\n    business_slug = defaultBusinessCardData.business_slug || \"\",\r\n    products_services = [],\r\n    contact_email = defaultBusinessCardData.contact_email,\r\n    title = data.title || \"\",\r\n  } = data;\r\n\r\n  const whatsappUrl = formatWhatsAppUrl(whatsapp_number);\r\n  const telUrl = formatTelUrl(phone);\r\n  const mailtoUrl = contact_email ? `mailto:${contact_email}` : undefined;\r\n\r\n  const fullAddress = [address_line, locality, city, state, pincode]\r\n    .filter(Boolean)\r\n    .join(\", \");\r\n\r\n  const displayPhone = isAuthenticated ? phone : maskPhoneNumber(phone);\r\n  const displayEmail = isAuthenticated\r\n    ? contact_email\r\n    : maskEmail(contact_email);\r\n  // Always show address_line regardless of authentication status\r\n  const displayAddressLine = address_line?.trim() || \"\";\r\n  const displayCityStatePin = `${city || \"\"}, ${state || \"\"} ${\r\n    pincode ? `- ${pincode}` : \"\"\r\n  }`.trim();\r\n\r\n  const qrValue = business_slug\r\n    ? `https://dukancard.in/${business_slug}`\r\n    : null;\r\n  const qrDisplayUrl = business_slug\r\n    ? `dukancard.in/${business_slug}`\r\n    : \"Set Slug to activate\";\r\n\r\n  // Platform is now free for all - no custom branding restrictions\r\n  const finalThemeColor = theme_color || \"#3b82f6\";\r\n\r\n  // Simple card style without custom branding\r\n  const cardStyle = useMemo(() => {\r\n    return {};\r\n  }, []);\r\n\r\n  return (\r\n    <motion.div\r\n      data-card-element\r\n      className={cn(`\r\n        relative w-full max-w-sm\r\n        rounded-xl overflow-hidden\r\n        transition-all duration-500\r\n\r\n        bg-gradient-to-br from-neutral-100 to-white dark:from-neutral-900 dark:to-neutral-950\r\n        shadow-xl\r\n        transform-gpu\r\n        border-0\r\n      `)}\r\n      style={cardStyle}\r\n    >\r\n      <CardBackgroundEffects finalThemeColor={finalThemeColor} />\r\n\r\n      <CardCornerDecorations />\r\n\r\n      {/* Content container */}\r\n      <div className=\"relative p-3 xs:p-4 sm:p-5 flex flex-col justify-between text-neutral-800 dark:text-white z-10\">\r\n        <CardHeader\r\n          establishedYear={established_year}\r\n        />\r\n\r\n        {/* Floating interaction buttons are rendered at the bottom */}\r\n\r\n        {/* Main content - Profile section */}\r\n        <div className=\"mt-1\">\r\n          <CardProfile\r\n            logo_url={logo_url}\r\n            localPreviewUrl={localPreviewUrl}\r\n            logoUploadStatus={logoUploadStatus}\r\n            member_name={member_name}\r\n            business_name={business_name}\r\n            title={title}\r\n            about_bio={about_bio}\r\n            finalThemeColor={finalThemeColor}\r\n          />\r\n\r\n          <CardDivider />\r\n\r\n          <CardBusinessInfo\r\n            fullAddress={fullAddress}\r\n            displayAddressLine={displayAddressLine}\r\n            locality={locality}\r\n            displayCityStatePin={displayCityStatePin}\r\n            phone={phone}\r\n            displayPhone={displayPhone}\r\n            isAuthenticated={isAuthenticated}\r\n            telUrl={telUrl}\r\n            displayEmail={displayEmail}\r\n            mailtoUrl={mailtoUrl}\r\n            business_hours={business_hours}\r\n            delivery_info={delivery_info}\r\n          />\r\n\r\n          {/* Products & Services section - Full width */}\r\n          {products_services && products_services.length > 0 && (\r\n            <div className=\"mt-3 max-w-xs mx-auto\">\r\n              <div className=\"flex items-center text-xs uppercase font-bold tracking-wider text-[--theme-color] mb-2 justify-center\">\r\n                <ShoppingBag\r\n                  className=\"w-3 h-3 mr-1.5\"\r\n                  color={finalThemeColor}\r\n                />\r\n                Products & Services\r\n              </div>\r\n\r\n              <div className=\"space-y-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 p-2.5 rounded-lg\">\r\n                {products_services.slice(0, 3).map((item) => (\r\n                  <div\r\n                    key={item.id}\r\n                    className=\"text-neutral-700 dark:text-neutral-200\"\r\n                  >\r\n                    <div className=\"flex justify-between items-baseline gap-2\">\r\n                      <TooltipProvider>\r\n                        <Tooltip>\r\n                          <TooltipTrigger asChild>\r\n                            <span className=\"font-medium text-xs truncate cursor-default\">\r\n                              {item.name}\r\n                            </span>\r\n                          </TooltipTrigger>\r\n                          <TooltipContent>\r\n                            <p>{item.name}</p>\r\n                          </TooltipContent>\r\n                        </Tooltip>\r\n                      </TooltipProvider>\r\n                      <span className=\"text-xs font-bold bg-[var(--theme-color-10)] dark:bg-[var(--theme-color-20)] py-0.5 px-2 rounded-full flex-shrink-0\">\r\n                        {formatPrice(item.base_price)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* QR Code section */}\r\n          <div className=\"mt-3 max-w-xs mx-auto\">\r\n            {isDemo ? (\r\n              // Demo mode - show fake QR code\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <p className=\"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default\">\r\n                    dukancard.in/demo-business\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0\">\r\n                  <QrCode\r\n                    className=\"w-14 h-14 text-neutral-800\"\r\n                    strokeWidth={1.5}\r\n                  />\r\n                </div>\r\n              </div>\r\n            ) : qrValue ? (\r\n              // Real QR code for actual business cards\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <TooltipProvider>\r\n                    <Tooltip>\r\n                      <TooltipTrigger asChild>\r\n                        <p className=\"text-xs font-mono text-[--theme-color] font-semibold line-clamp-1 cursor-default\">\r\n                          {qrDisplayUrl}\r\n                        </p>\r\n                      </TooltipTrigger>\r\n                      <TooltipContent>\r\n                        <p>{qrDisplayUrl}</p>\r\n                      </TooltipContent>\r\n                    </Tooltip>\r\n                  </TooltipProvider>\r\n                </div>\r\n                <div\r\n                  id=\"business-card-qrcode\"\r\n                  className=\"bg-white p-1.5 rounded-lg shadow-md flex-shrink-0\"\r\n                >\r\n                  <QRCode\r\n                    value={qrValue}\r\n                    size={60}\r\n                    level=\"M\"\r\n                    bgColor=\"#FFFFFF\"\r\n                    fgColor=\"#000000\"\r\n                  />\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Placeholder for when no slug is set\r\n              <div className=\"flex items-center justify-between gap-3 bg-neutral-800/5 dark:bg-neutral-300/10 p-2.5 rounded-lg border border-neutral-300/60 dark:border-neutral-700/60\">\r\n                <div className=\"flex-1 min-w-0\">\r\n                  <p className=\"text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1\">\r\n                    Scan for Dukancard Profile\r\n                  </p>\r\n                  <p className=\"text-xs font-mono text-neutral-500 dark:text-neutral-500 line-clamp-1\">\r\n                    {qrDisplayUrl}\r\n                  </p>\r\n                </div>\r\n                <div className=\"bg-white p-1.5 rounded-lg shadow-md opacity-50 flex-shrink-0\">\r\n                  <svg\r\n                    className=\"w-14 h-14 text-[--theme-color]\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Interaction metrics */}\r\n          <div className=\"flex justify-center items-center gap-2 sm:gap-4 text-xs text-neutral-500 dark:text-neutral-400 mt-3 mb-2 flex-wrap\">\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <Heart className=\"w-4 h-4 text-red-500\" />\r\n              <span className=\"font-medium\">\r\n                {formatIndianNumberShort(totalLikes)}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <UserPlus className=\"w-4 h-4 text-blue-500\" />\r\n              <span className=\"font-medium\">\r\n                {formatIndianNumberShort(totalSubscriptions)}\r\n              </span>\r\n            </div>\r\n            <div className=\"flex items-center gap-1.5 bg-neutral-800/5 dark:bg-neutral-300/5 py-1.5 px-3 rounded-full\">\r\n              <Star className=\"w-4 h-4 text-amber-500 fill-current\" />\r\n              <span className=\"font-medium\">{averageRating.toFixed(1)}</span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Interaction buttons have been moved outside the card */}\r\n        </div>\r\n\r\n        {/* Social Links */}\r\n        <div className=\"pt-3 pb-2\">\r\n          <TooltipProvider>\r\n            <div className=\"flex justify-center items-center space-x-2\">\r\n              {instagram_url && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <InstagramIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={instagram_url}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <InstagramIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Instagram Button\" : \"Instagram\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {facebook_url && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <FacebookIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={facebook_url}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <FacebookIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Facebook Button\" : \"Facebook\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {whatsappUrl && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <WhatsAppIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable link\r\n                      <a\r\n                        href={whatsappUrl}\r\n                        target=\"_blank\"\r\n                        rel=\"noopener noreferrer\"\r\n                        className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all hover:scale-110 hover:shadow-md\"\r\n                      >\r\n                        <WhatsAppIcon className=\"w-4 h-4 text-[--theme-color]\" />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo WhatsApp Button\" : \"Chat on WhatsApp\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n\r\n              {displayPhone && telUrl && (\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    {isDemo ? (\r\n                      // Demo mode - non-clickable button\r\n                      <div className=\"w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center cursor-default\">\r\n                        <Phone className=\"w-4 h-4\" color={finalThemeColor} />\r\n                      </div>\r\n                    ) : (\r\n                      // Real mode - clickable or non-clickable based on authentication\r\n                      <a\r\n                        href={isAuthenticated ? telUrl : \"#\"}\r\n                        className={`w-8 h-8 rounded-full bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-[var(--theme-color-20)] dark:to-[var(--theme-color-10)] flex items-center justify-center transition-all ${\r\n                          isAuthenticated\r\n                            ? \"hover:scale-110 hover:shadow-md\"\r\n                            : \"cursor-default opacity-70\"\r\n                        }`}\r\n                      >\r\n                        <Phone className=\"w-4 h-4\" color={finalThemeColor} />\r\n                      </a>\r\n                    )}\r\n                  </TooltipTrigger>\r\n                  <TooltipContent className=\"bg-neutral-800 text-xs text-white border-neutral-700\">\r\n                    {isDemo ? \"Demo Call Button\" : \"Call directly\"}\r\n                  </TooltipContent>\r\n                </Tooltip>\r\n              )}\r\n            </div>\r\n          </TooltipProvider>\r\n        </div>\r\n\r\n        {/* Bottom accent bar */}\r\n        <div\r\n          className=\"absolute bottom-0 left-0 right-0 h-1.5 mt-2\"\r\n          style={{\r\n            background: `linear-gradient(to right, var(--theme-color), var(--theme-accent-end), var(--theme-color))`,\r\n          }}\r\n        ></div>\r\n      </div>\r\n\r\n      <CardGlowEffects />\r\n    </motion.div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAMA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AAOA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAzCA;;;;;;;;;;;;;;;;;;;;;AA+De,SAAS,oBAAoB,EAC1C,IAAI,EACJ,mBAAmB,MAAM,EACzB,kBAAkB,IAAI,EACtB,kBAAkB,KAAK,EACvB,aAAa,CAAC,EACd,qBAAqB,CAAC,EACtB,gBAAgB,CAAC,EACjB,SAAS,KAAK,EACW;IACzB,MAAM,EACJ,WAAW,KAAK,QAAQ,IAAI,+JAAA,CAAA,0BAAuB,CAAC,QAAQ,EAC5D,cAAc,+JAAA,CAAA,0BAAuB,CAAC,WAAW,IAAI,WAAW,EAChE,gBAAgB,+JAAA,CAAA,0BAAuB,CAAC,aAAa,IACnD,oBAAoB,EACtB,YAAY,+JAAA,CAAA,0BAAuB,CAAC,SAAS,EAC7C,eAAe,+JAAA,CAAA,0BAAuB,CAAC,YAAY,EACnD,WAAW,KAAK,QAAQ,IAAI,+JAAA,CAAA,0BAAuB,CAAC,QAAQ,EAC5D,OAAO,+JAAA,CAAA,0BAAuB,CAAC,IAAI,EACnC,QAAQ,+JAAA,CAAA,0BAAuB,CAAC,KAAK,EACrC,mBAAmB,KAAK,gBAAgB,IAAI,+JAAA,CAAA,0BAAuB,CAAC,gBAAgB,EACpF,UAAU,+JAAA,CAAA,0BAAuB,CAAC,OAAO,EACzC,QAAQ,+JAAA,CAAA,0BAAuB,CAAC,KAAK,EACrC,gBAAgB,+JAAA,CAAA,0BAAuB,CAAC,aAAa,EACrD,eAAe,+JAAA,CAAA,0BAAuB,CAAC,YAAY,EACnD,kBAAkB,+JAAA,CAAA,0BAAuB,CAAC,eAAe,EACzD,cAAc,KAAK,WAAW,EAC9B,iBAAiB,KAAK,cAAc,EACpC,gBAAgB,KAAK,aAAa,EAClC,gBAAgB,+JAAA,CAAA,0BAAuB,CAAC,aAAa,IAAI,EAAE,EAC3D,oBAAoB,EAAE,EACtB,gBAAgB,+JAAA,CAAA,0BAAuB,CAAC,aAAa,EACrD,QAAQ,KAAK,KAAK,IAAI,EAAE,EACzB,GAAG;IAEJ,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,oBAAiB,AAAD,EAAE;IACtC,MAAM,SAAS,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE;IAC5B,MAAM,YAAY,gBAAgB,CAAC,OAAO,EAAE,eAAe,GAAG;IAE9D,MAAM,cAAc;QAAC;QAAc;QAAU;QAAM;QAAO;KAAQ,CAC/D,MAAM,CAAC,SACP,IAAI,CAAC;IAER,MAAM,eAAe,kBAAkB,QAAQ,CAAA,GAAA,4GAAA,CAAA,kBAAe,AAAD,EAAE;IAC/D,MAAM,eAAe,kBACjB,gBACA,CAAA,GAAA,4GAAA,CAAA,YAAS,AAAD,EAAE;IACd,+DAA+D;IAC/D,MAAM,qBAAqB,cAAc,UAAU;IACnD,MAAM,sBAAsB,GAAG,QAAQ,GAAG,EAAE,EAAE,SAAS,GAAG,CAAC,EACzD,UAAU,CAAC,EAAE,EAAE,SAAS,GAAG,IAC3B,CAAC,IAAI;IAEP,MAAM,UAAU,gBACZ,CAAC,qBAAqB,EAAE,eAAe,GACvC;IACJ,MAAM,eAAe,gBACjB,CAAC,aAAa,EAAE,eAAe,GAC/B;IAEJ,iEAAiE;IACjE,MAAM,kBAAkB,eAAe;IAEvC,4CAA4C;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,OAAO,CAAC;IACV,GAAG,EAAE;IAEL,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,mBAAiB;QACjB,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,CAAC;;;;;;;;;MASf,CAAC;QACD,OAAO;;0BAEP,8OAAC,6LAAA,CAAA,UAAqB;gBAAC,iBAAiB;;;;;;0BAExC,8OAAC,6LAAA,CAAA,UAAqB;;;;;0BAGtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kLAAA,CAAA,UAAU;wBACT,iBAAiB;;;;;;kCAMnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mLAAA,CAAA,UAAW;gCACV,UAAU;gCACV,iBAAiB;gCACjB,kBAAkB;gCAClB,aAAa;gCACb,eAAe;gCACf,OAAO;gCACP,WAAW;gCACX,iBAAiB;;;;;;0CAGnB,8OAAC,mLAAA,CAAA,UAAW;;;;;0CAEZ,8OAAC,wLAAA,CAAA,UAAgB;gCACf,aAAa;gCACb,oBAAoB;gCACpB,UAAU;gCACV,qBAAqB;gCACrB,OAAO;gCACP,cAAc;gCACd,iBAAiB;gCACjB,QAAQ;gCACR,cAAc;gCACd,WAAW;gCACX,gBAAgB;gCAChB,eAAe;;;;;;4BAIhB,qBAAqB,kBAAkB,MAAM,GAAG,mBAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDACV,WAAU;gDACV,OAAO;;;;;;4CACP;;;;;;;kDAIJ,8OAAC;wCAAI,WAAU;kDACZ,kBAAkB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAClC,8OAAC;gDAEC,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4HAAA,CAAA,kBAAe;sEACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;kFACN,8OAAC,4HAAA,CAAA,iBAAc;wEAAC,OAAO;kFACrB,cAAA,8OAAC;4EAAK,WAAU;sFACb,KAAK,IAAI;;;;;;;;;;;kFAGd,8OAAC,4HAAA,CAAA,iBAAc;kFACb,cAAA,8OAAC;sFAAG,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sEAInB,8OAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE,KAAK,UAAU;;;;;;;;;;;;+CAjB3B,KAAK,EAAE;;;;;;;;;;;;;;;;0CA2BtB,8OAAC;gCAAI,WAAU;0CACZ,SACC,gCAAgC;8CAChC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,8OAAC;oDAAE,WAAU;8DAAmF;;;;;;;;;;;;sDAIlG,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDACL,WAAU;gDACV,aAAa;;;;;;;;;;;;;;;;2CAIjB,UACF,yCAAyC;8CACzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,8OAAC,4HAAA,CAAA,kBAAe;8DACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0EACN,8OAAC,4HAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC;oEAAE,WAAU;8EACV;;;;;;;;;;;0EAGL,8OAAC,4HAAA,CAAA,iBAAc;0EACb,cAAA,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAKZ,8OAAC;4CACC,IAAG;4CACH,WAAU;sDAEV,cAAA,8OAAC,mJAAA,CAAA,UAAM;gDACL,OAAO;gDACP,MAAM;gDACN,OAAM;gDACN,SAAQ;gDACR,SAAQ;;;;;;;;;;;;;;;;2CAKd,sCAAsC;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAkE;;;;;;8DAG/E,8OAAC;oDAAE,WAAU;8DACV;;;;;;;;;;;;sDAGL,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAU;gDACV,SAAQ;gDACR,MAAK;gDACL,QAAO;0DAEP,cAAA,8OAAC;oDACC,eAAc;oDACd,gBAAe;oDACf,aAAa;oDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;;;;;;;;;;;;kDAG7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;;;;;;;;;;;;kDAG7B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;gDAAK,WAAU;0DAAe,cAAc,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4HAAA,CAAA,kBAAe;sCACd,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,+BACC,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,4IAAA,CAAA,UAAa;wDAAC,WAAU;;;;;;;;;;2DAG3B,6BAA6B;8DAC7B,8OAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,4IAAA,CAAA,UAAa;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI/B,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,0BAA0B;;;;;;;;;;;;oCAKzC,8BACC,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,2IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;2DAG1B,6BAA6B;8DAC7B,8OAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,2IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,yBAAyB;;;;;;;;;;;;oCAKxC,6BACC,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,2IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;2DAG1B,6BAA6B;8DAC7B,8OAAC;oDACC,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC,2IAAA,CAAA,UAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAI9B,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,yBAAyB;;;;;;;;;;;;oCAKxC,gBAAgB,wBACf,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACpB,SACC,mCAAmC;8DACnC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,OAAO;;;;;;;;;;2DAGpC,iEAAiE;8DACjE,8OAAC;oDACC,MAAM,kBAAkB,SAAS;oDACjC,WAAW,CAAC,yLAAyL,EACnM,kBACI,oCACA,6BACJ;8DAEF,cAAA,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;wDAAU,OAAO;;;;;;;;;;;;;;;;0DAIxC,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,SAAS,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3C,8OAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY,CAAC,0FAA0F,CAAC;wBAC1G;;;;;;;;;;;;0BAIJ,8OAAC,uLAAA,CAAA,UAAe;;;;;;;;;;;AAGtB", "debugId": null}}, {"offset": {"line": 5629, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/shared/EnhancedCardActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport { Download, FileDown, QrCode as QrCodeIcon, CreditCard } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { motion } from \"framer-motion\";\r\nimport QRCode from \"react-qr-code\";\r\nimport { generateAndDownloadQRCode, downloadRawQRImage } from \"@/lib/qrCodeGenerator\";\r\nimport { downloadBusinessCard, findBusinessCardElement } from \"@/lib/cardDownloader\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuSeparator,\r\n} from \"@/components/ui/dropdown-menu\";\r\n\r\ninterface EnhancedCardActionsProps {\r\n  businessSlug: string;\r\n  businessName: string;\r\n  ownerName?: string;\r\n  businessAddress?: string;\r\n  themeColor?: string;\r\n  className?: string;\r\n}\r\n\r\nexport default function EnhancedCardActions({\r\n  businessSlug,\r\n  businessName,\r\n  ownerName = \"\",\r\n  businessAddress = \"\",\r\n  themeColor = \"#F59E0B\",\r\n  className,\r\n}: EnhancedCardActionsProps) {\r\n  const [qrCodeSvg, setQrCodeSvg] = useState<SVGSVGElement | null>(null);\r\n  const qrCodeRef = useRef<HTMLDivElement>(null);\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Set isClient to true after component mounts\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Get QR code SVG element after component mounts\r\n  useEffect(() => {\r\n    if (qrCodeRef.current) {\r\n      const svg = qrCodeRef.current.querySelector(\"svg\");\r\n      if (svg instanceof SVGSVGElement) {\r\n        setQrCodeSvg(svg);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Download A4 formatted QR code\r\n  const handleDownloadA4QR = async () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    if (!qrCodeSvg) {\r\n      toast.error(\"QR code not available for download.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Format the address for display\r\n      const formattedAddress =\r\n        businessAddress.trim() || \"Address not available\";\r\n      const formattedOwnerName = ownerName.trim() || \"Owner\";\r\n\r\n      await generateAndDownloadQRCode(qrCodeSvg, {\r\n        businessName,\r\n        ownerName: formattedOwnerName,\r\n        address: formattedAddress,\r\n        slug: businessSlug,\r\n        qrValue: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}`,\r\n        themeColor,\r\n      });\r\n\r\n      toast.success(\"A4 QR code downloaded!\");\r\n    } catch (error) {\r\n      console.error(\"Error generating QR code:\", error);\r\n      toast.error(\"Could not download QR code.\");\r\n    }\r\n  };\r\n\r\n  // Download raw QR image\r\n  const handleDownloadRawQR = async () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    if (!qrCodeSvg) {\r\n      toast.error(\"QR code not available for download.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await downloadRawQRImage(qrCodeSvg, businessSlug);\r\n      toast.success(\"High-quality QR image downloaded!\");\r\n    } catch (error) {\r\n      console.error(\"Error downloading QR image:\", error);\r\n      toast.error(\"Could not download QR image.\");\r\n    }\r\n  };\r\n\r\n  // Download business card as PNG\r\n  const handleDownloadCardPNG = async () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Find the business card element\r\n      let cardElement = findBusinessCardElement();\r\n\r\n      // Additional validation to ensure we have the right element\r\n      if (cardElement) {\r\n        const rect = cardElement.getBoundingClientRect();\r\n\r\n        // If the element is too large, it might be a container, try to find the actual card\r\n        if (rect.width > 500) {\r\n          const actualCard = cardElement.querySelector('[data-card-element]') as HTMLElement;\r\n          if (actualCard) {\r\n            cardElement = actualCard;\r\n          }\r\n        }\r\n      }\r\n\r\n      if (!cardElement) {\r\n        toast.error(\"Business card not found for download.\");\r\n        return;\r\n      }\r\n\r\n      await downloadBusinessCard(cardElement, {\r\n        businessName,\r\n        businessSlug,\r\n      });\r\n\r\n      toast.success(\"Digital card downloaded as PNG!\");\r\n    } catch (error) {\r\n      console.error(\"Error downloading business card as PNG:\", error);\r\n      toast.error(\"Could not download business card.\");\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Hidden QR code for download\r\n  const qrValue = businessSlug ? `https://dukancard.in/${businessSlug}` : \"\";\r\n\r\n  return (\r\n    <div className={cn(\"w-full max-w-sm mx-auto space-y-5 mt-6\", className)}>\r\n      {/* Hidden QR code for download */}\r\n      <div className=\"hidden\">\r\n        <div id=\"public-card-qrcode\" ref={qrCodeRef}>\r\n          <QRCode\r\n            value={qrValue}\r\n            size={300}\r\n            level=\"M\"\r\n            bgColor=\"#FFFFFF\"\r\n            fgColor=\"#000000\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Enhanced Download Button with Glow Effect */}\r\n      <div className=\"w-full relative group\">\r\n        {/* Button glow effect with properly rounded corners */}\r\n        {isClient && (\r\n          <motion.div\r\n            className=\"absolute -inset-0.5 rounded-full blur-md\"\r\n            style={{\r\n              background: \"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))\"\r\n            }}\r\n            initial={{ opacity: 0.7 }}\r\n            animate={{\r\n              opacity: [0.7, 0.9, 0.7],\r\n              boxShadow: [\r\n                \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\",\r\n                \"0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)\",\r\n                \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\"\r\n              ]\r\n            }}\r\n            transition={{\r\n              duration: 2,\r\n              repeat: Infinity,\r\n              repeatType: \"loop\",\r\n              ease: \"easeInOut\"\r\n            }}\r\n          />\r\n        )}\r\n\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <Button\r\n              className={cn(\r\n                \"w-full py-6 relative overflow-hidden group\",\r\n                \"bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90\",\r\n                \"text-black dark:text-neutral-900 font-medium text-base\",\r\n                \"border-none rounded-full shadow-lg hover:shadow-xl\",\r\n                \"transition-all duration-300 ease-out\"\r\n              )}\r\n            >\r\n              {/* Shimmer effect */}\r\n              <span className=\"absolute inset-0 w-full h-full overflow-hidden\">\r\n                <span className=\"absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none\" />\r\n              </span>\r\n\r\n              <div className=\"flex items-center justify-center gap-3 relative z-10\">\r\n                <div className=\"bg-white/20 p-2 rounded-lg\">\r\n                  <Download className=\"h-5 w-5 text-black dark:text-neutral-900\" />\r\n                </div>\r\n                <span className=\"text-black dark:text-neutral-900 font-semibold\">Download Options</span>\r\n              </div>\r\n            </Button>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent align=\"center\" className=\"w-56 bg-white dark:bg-neutral-900 border border-neutral-200 dark:border-neutral-800\">\r\n            <DropdownMenuItem onClick={handleDownloadCardPNG} className=\"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800\">\r\n              <CreditCard className=\"mr-2 h-4 w-4\" />\r\n              <span>Download Digital Card (PNG)</span>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem onClick={handleDownloadA4QR} className=\"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800\">\r\n              <FileDown className=\"mr-2 h-4 w-4\" />\r\n              <span>Download A4 Size QR</span>\r\n            </DropdownMenuItem>\r\n            <DropdownMenuItem onClick={handleDownloadRawQR} className=\"cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-800\">\r\n              <QrCodeIcon className=\"mr-2 h-4 w-4\" />\r\n              <span>Download High-Quality QR Image</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AA4Be,SAAS,oBAAoB,EAC1C,YAAY,EACZ,YAAY,EACZ,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,aAAa,SAAS,EACtB,SAAS,EACgB;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,MAAM,MAAM,UAAU,OAAO,CAAC,aAAa,CAAC;YAC5C,IAAI,eAAe,eAAe;gBAChC,aAAa;YACf;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,WAAW;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iCAAiC;YACjC,MAAM,mBACJ,gBAAgB,IAAI,MAAM;YAC5B,MAAM,qBAAqB,UAAU,IAAI,MAAM;YAE/C,MAAM,CAAA,GAAA,sHAAA,CAAA,4BAAyB,AAAD,EAAE,WAAW;gBACzC;gBACA,WAAW;gBACX,SAAS;gBACT,MAAM;gBACN,SAAS,GAAG,6DAAoC,uBAAuB,CAAC,EAAE,cAAc;gBACxF;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,WAAW;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE,WAAW;YACpC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,gCAAgC;IAChC,MAAM,wBAAwB;QAC5B,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI;YACF,iCAAiC;YACjC,IAAI,cAAc,CAAA,GAAA,qHAAA,CAAA,0BAAuB,AAAD;YAExC,4DAA4D;YAC5D,IAAI,aAAa;gBACf,MAAM,OAAO,YAAY,qBAAqB;gBAE9C,oFAAoF;gBACpF,IAAI,KAAK,KAAK,GAAG,KAAK;oBACpB,MAAM,aAAa,YAAY,aAAa,CAAC;oBAC7C,IAAI,YAAY;wBACd,cAAc;oBAChB;gBACF;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,MAAM,CAAA,GAAA,qHAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa;gBACtC;gBACA;YACF;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAIA,8BAA8B;IAC9B,MAAM,UAAU,eAAe,CAAC,qBAAqB,EAAE,cAAc,GAAG;IAExE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;0BAE3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,IAAG;oBAAqB,KAAK;8BAChC,cAAA,8OAAC,mJAAA,CAAA,UAAM;wBACL,OAAO;wBACP,MAAM;wBACN,OAAM;wBACN,SAAQ;wBACR,SAAQ;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAI,WAAU;;oBAEZ,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BACL,YAAY;wBACd;wBACA,SAAS;4BAAE,SAAS;wBAAI;wBACxB,SAAS;4BACP,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;4BACxB,WAAW;gCACT;gCACA;gCACA;6BACD;wBACH;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,YAAY;4BACZ,MAAM;wBACR;;;;;;kCAIJ,8OAAC,qIAAA,CAAA,eAAY;;0CACX,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8CACA,0DACA,0DACA,sDACA;;sDAIF,8OAAC;4CAAK,WAAU;sDACd,cAAA,8OAAC;gDAAK,WAAU;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;8DAAiD;;;;;;;;;;;;;;;;;;;;;;;0CAIvE,8OAAC,qIAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAS,WAAU;;kDAC5C,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS;wCAAuB,WAAU;;0DAC1D,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qIAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS;wCAAoB,WAAU;;0DACvD,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,qIAAA,CAAA,mBAAgB;wCAAC,SAAS;wCAAqB,WAAU;;0DACxD,8OAAC,0MAAA,CAAA,SAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB", "debugId": null}}, {"offset": {"line": 5995, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/EnhancedPublicCardActions.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport EnhancedCardActions from \"@/app/components/shared/EnhancedCardActions\";\r\n\r\ninterface EnhancedPublicCardActionsProps {\r\n  businessSlug: string;\r\n  businessName: string;\r\n  ownerName?: string;\r\n  businessAddress?: string;\r\n  themeColor?: string;\r\n}\r\n\r\nexport default function EnhancedPublicCardActions({\r\n  businessSlug,\r\n  businessName,\r\n  ownerName = \"\",\r\n  businessAddress = \"\",\r\n  themeColor = \"#F59E0B\",\r\n}: EnhancedPublicCardActionsProps) {\r\n  return (\r\n    <EnhancedCardActions\r\n      businessSlug={businessSlug}\r\n      businessName={businessName}\r\n      ownerName={ownerName}\r\n      businessAddress={businessAddress}\r\n      themeColor={themeColor}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYe,SAAS,0BAA0B,EAChD,YAAY,EACZ,YAAY,EACZ,YAAY,EAAE,EACd,kBAAkB,EAAE,EACpB,aAAa,SAAS,EACS;IAC/B,qBACE,8OAAC,mJAAA,CAAA,UAAmB;QAClB,cAAc;QACd,cAAc;QACd,WAAW;QACX,iBAAiB;QACjB,YAAY;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 6022, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/FloatingInteractionButtons.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { useInView } from \"react-intersection-observer\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  Heart,\r\n  UserPlus,\r\n  UserMinus,\r\n  Loader2,\r\n  Star,\r\n  Share2,\r\n} from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\n// Glow effect component using simple styling\r\nconst GlowEffect = ({ color }: { color: string }) => {\r\n  return (\r\n    <div\r\n      className=\"absolute inset-0 rounded-full blur-md pointer-events-none opacity-50\"\r\n      style={{ backgroundColor: color }}\r\n    />\r\n  );\r\n};\r\n\r\n// Pulse effect component using simple styling\r\nconst PulseEffect = ({ color }: { color: string }) => {\r\n  return (\r\n    <div\r\n      className=\"absolute inset-0 rounded-full pointer-events-none\"\r\n      style={{\r\n        boxShadow: `0 0 0 2px ${color}`,\r\n      }}\r\n    />\r\n  );\r\n};\r\n\r\ninterface FloatingInteractionButtonsProps {\r\n  hasLiked: boolean;\r\n  isSubscribed: boolean;\r\n  isLoadingInteraction: boolean;\r\n  onLike: () => void;\r\n  onUnlike: () => void;\r\n  onSubscribe: () => void;\r\n  onUnsubscribe: () => void;\r\n  isAuthenticated: boolean;\r\n  isOwnBusiness?: boolean;\r\n  isCurrentUserBusiness?: boolean;\r\n  themeColor?: string;\r\n  onReviewClick?: () => void;\r\n  businessSlug?: string;\r\n  businessName?: string;\r\n}\r\n\r\nexport default function FloatingInteractionButtons({\r\n  hasLiked,\r\n  isSubscribed,\r\n  isLoadingInteraction,\r\n  onLike,\r\n  onUnlike,\r\n  onSubscribe,\r\n  onUnsubscribe,\r\n  isAuthenticated,\r\n  isOwnBusiness = false,\r\n  onReviewClick,\r\n  businessSlug = \"\",\r\n  businessName = \"\",\r\n}: FloatingInteractionButtonsProps) {\r\n  const [prevLiked, setPrevLiked] = useState(hasLiked);\r\n  const [prevSubscribed, setPrevSubscribed] = useState(isSubscribed);\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  const { ref } = useInView({\r\n    triggerOnce: true,\r\n    threshold: 0.5,\r\n  });\r\n\r\n  // Detect client-side rendering\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  // Detect changes in liked/subscribed state\r\n  useEffect(() => {\r\n    if (hasLiked !== prevLiked) {\r\n      setPrevLiked(hasLiked);\r\n    }\r\n  }, [hasLiked, prevLiked]);\r\n\r\n  useEffect(() => {\r\n    if (isSubscribed !== prevSubscribed) {\r\n      setPrevSubscribed(isSubscribed);\r\n    }\r\n  }, [isSubscribed, prevSubscribed]);\r\n\r\n  const handleLikeClick = () => {\r\n    if (isOwnBusiness) {\r\n      toast.error(\"You cannot like your own business card\", {\r\n        description: \"Business owners cannot like their own cards\",\r\n        position: \"top-center\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!isAuthenticated) {\r\n      // Get the current path \r\n      const currentPath = window.location.pathname;\r\n      window.location.href = `/login?message=Please log in to like this business card&redirect=${encodeURIComponent(currentPath)}`;\r\n      return;\r\n    }\r\n\r\n    if (hasLiked) {\r\n      onUnlike();\r\n    } else {\r\n      onLike();\r\n    }\r\n  };\r\n\r\n  const handleSubscribeClick = () => {\r\n    if (isOwnBusiness) {\r\n      toast.error(\"You cannot subscribe to your own business card\", {\r\n        description: \"Business owners cannot subscribe to their own cards\",\r\n        position: \"top-center\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    if (!isAuthenticated) {\r\n      // Get the current path \r\n      const currentPath = window.location.pathname;\r\n      window.location.href = `/login?message=Please log in to subscribe to this business card&redirect=${encodeURIComponent(currentPath)}`;\r\n      return;\r\n    }\r\n\r\n    if (isSubscribed) {\r\n      onUnsubscribe();\r\n    } else {\r\n      onSubscribe();\r\n    }\r\n  };\r\n\r\n  // Share handler\r\n  const handleShare = () => {\r\n    if (!businessSlug) {\r\n      toast.error(\"Business slug not available.\");\r\n      return;\r\n    }\r\n\r\n    const shareUrl = `${process.env.NEXT_PUBLIC_BASE_URL || 'https://dukancard.in'}/${businessSlug}`;\r\n    const shareText = `Check out ${businessName || \"this business\"}'s digital card on Dukancard!`;\r\n\r\n    // Use Web Share API if available\r\n    if (navigator.share) {\r\n      navigator.share({\r\n        title: `${businessName} - Digital Business Card`,\r\n        text: shareText,\r\n        url: shareUrl,\r\n      }).catch((error) => {\r\n        console.error('Error sharing:', error);\r\n        // Fallback to clipboard\r\n        copyToClipboard(shareUrl);\r\n      });\r\n    } else {\r\n      // Fallback to clipboard\r\n      copyToClipboard(shareUrl);\r\n    }\r\n  };\r\n\r\n  // Helper function to copy to clipboard\r\n  const copyToClipboard = (text: string) => {\r\n    navigator.clipboard.writeText(text)\r\n      .then(() => toast.success(\"Link copied to clipboard!\"))\r\n      .catch(() => toast.error(\"Failed to copy link.\"));\r\n  };\r\n\r\n  if (!isClient) {\r\n    return null; // Don't render during SSR\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop and tablet version - buttons at the bottom */}\r\n      <div className=\"absolute -right-16 bottom-4 z-20 flex flex-col hidden sm:block\">\r\n        <div className=\"relative group\">\r\n          <div\r\n            ref={ref}\r\n            className=\"relative flex flex-col space-y-3 p-2\"\r\n          >\r\n            {/* Like Button */}\r\n            <div className=\"relative\">\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className={cn(\r\n                        \"h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent\",\r\n                        hasLiked\r\n                          ? \"text-red-500 border-red-500 hover:text-red-600 hover:bg-transparent hover:border-red-600\"\r\n                          : \"text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent\"\r\n                      )}\r\n                      onClick={handleLikeClick}\r\n                      disabled={isLoadingInteraction}\r\n                      aria-label={hasLiked ? \"Unlike\" : \"Like\"}\r\n                    >\r\n                      {isLoadingInteraction ? (\r\n                        <Loader2 className=\"h-6 w-6 animate-spin\" />\r\n                      ) : (\r\n                        <Heart\r\n                          className={cn(\"h-6 w-6\", hasLiked && \"fill-current\")}\r\n                        />\r\n                      )}\r\n\r\n                      {/* Background effects */}\r\n                      {hasLiked && <GlowEffect color=\"rgba(239, 68, 68, 0.5)\" />}\r\n                      {hasLiked && <PulseEffect color=\"rgba(239, 68, 68, 0.7)\" />}\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  {(!isAuthenticated || isOwnBusiness) && (\r\n                    <TooltipContent\r\n                      side=\"right\"\r\n                      className=\"bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700\"\r\n                    >\r\n                      {isOwnBusiness ? (\r\n                        <div className=\"flex flex-col gap-1\">\r\n                          <p className=\"font-medium text-red-500 dark:text-red-400\">\r\n                            You cannot like your own business card\r\n                          </p>\r\n                          <p className=\"text-xs text-neutral-500 dark:text-neutral-400\">\r\n                            Business owners cannot like their own cards\r\n                          </p>\r\n                        </div>\r\n                      ) : (\r\n                        <p>Please log in to like this business card</p>\r\n                      )}\r\n                    </TooltipContent>\r\n                  )}\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n\r\n            {/* Subscribe Button */}\r\n            <div className=\"relative\">\r\n              <TooltipProvider>\r\n                <Tooltip>\r\n                  <TooltipTrigger asChild>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"icon\"\r\n                      className={cn(\r\n                        \"h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent\",\r\n                        isSubscribed\r\n                          ? \"text-blue-500 border-blue-500 hover:text-blue-600 hover:bg-transparent hover:border-blue-600\"\r\n                          : \"text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent\"\r\n                      )}\r\n                      onClick={handleSubscribeClick}\r\n                      disabled={isLoadingInteraction || isOwnBusiness}\r\n                      aria-label={isSubscribed ? \"Unsubscribe\" : \"Subscribe\"}\r\n                    >\r\n                      {isLoadingInteraction ? (\r\n                        <Loader2 className=\"h-6 w-6 animate-spin\" />\r\n                      ) : (\r\n                        isSubscribed ? (\r\n                          <UserMinus className=\"h-6 w-6\" />\r\n                        ) : (\r\n                          <UserPlus className=\"h-6 w-6\" />\r\n                        )\r\n                      )}\r\n\r\n                      {/* Background effects */}\r\n                      {isSubscribed && <GlowEffect color=\"rgba(59, 130, 246, 0.5)\" />}\r\n                      {isSubscribed && <PulseEffect color=\"rgba(59, 130, 246, 0.7)\" />}\r\n                    </Button>\r\n                  </TooltipTrigger>\r\n                  {(!isAuthenticated || isOwnBusiness) && (\r\n                    <TooltipContent\r\n                      side=\"right\"\r\n                      className=\"bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700\"\r\n                    >\r\n                      {isOwnBusiness ? (\r\n                        <div className=\"flex flex-col gap-1\">\r\n                          <p className=\"font-medium text-red-500 dark:text-red-400\">\r\n                            You cannot subscribe to your own business card\r\n                          </p>\r\n                          <p className=\"text-xs text-neutral-500 dark:text-neutral-400\">\r\n                            Business owners cannot subscribe to their own cards\r\n                          </p>\r\n                        </div>\r\n                      ) : (\r\n                        <p>Please log in to subscribe to this business card</p>\r\n                      )}\r\n                    </TooltipContent>\r\n                  )}\r\n                </Tooltip>\r\n              </TooltipProvider>\r\n            </div>\r\n\r\n            {/* Review Button */}\r\n            {onReviewClick && (\r\n              <div className=\"relative\">\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent text-amber-500 border-amber-500 hover:text-amber-600 hover:bg-transparent hover:border-amber-600\"\r\n                        onClick={(e) => {\r\n                          e.preventDefault();\r\n                          if (onReviewClick) onReviewClick();\r\n                        }}\r\n                        aria-label=\"Leave a Review\"\r\n                      >\r\n                        <Star className=\"h-6 w-6\" />\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent\r\n                      side=\"right\"\r\n                      className=\"bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700\"\r\n                    >\r\n                      Leave a Review\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              </div>\r\n            )}\r\n\r\n            {/* Share Button */}\r\n            {businessSlug && (\r\n              <div className=\"relative\">\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"icon\"\r\n                        className=\"h-10 w-10 rounded-full relative overflow-hidden transition-all duration-300 shadow-none bg-transparent text-green-500 border-green-500 hover:text-green-600 hover:bg-transparent hover:border-green-600\"\r\n                        onClick={handleShare}\r\n                        aria-label=\"Share\"\r\n                      >\r\n                        <Share2 className=\"h-6 w-6\" />\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent\r\n                      side=\"right\"\r\n                      className=\"bg-white dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 text-xs p-2 shadow-md border border-neutral-200 dark:border-neutral-700\"\r\n                    >\r\n                      Share this card\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile version - buttons below the card in a horizontal row */}\r\n      <div className=\"w-full flex justify-center space-x-4 sm:hidden\">\r\n        {/* Like Button */}\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className={cn(\r\n            \"h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none\",\r\n            hasLiked\r\n              ? \"text-red-500 border-red-500 hover:text-red-600 hover:bg-transparent hover:border-red-600\"\r\n              : \"text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent\"\r\n          )}\r\n          onClick={handleLikeClick}\r\n          disabled={isLoadingInteraction || isOwnBusiness}\r\n          aria-label={hasLiked ? \"Unlike\" : \"Like\"}\r\n        >\r\n          {isLoadingInteraction ? (\r\n            <Loader2 className=\"h-6 w-6 animate-spin\" />\r\n          ) : (\r\n            <Heart\r\n              className={cn(\"h-6 w-6\", hasLiked && \"fill-current\")}\r\n            />\r\n          )}\r\n          {hasLiked && <GlowEffect color=\"rgba(239, 68, 68, 0.5)\" />}\r\n        </Button>\r\n\r\n        {/* Subscribe Button */}\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"icon\"\r\n          className={cn(\r\n            \"h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none\",\r\n            isSubscribed\r\n              ? \"text-blue-500 border-blue-500 hover:text-blue-600 hover:bg-transparent hover:border-blue-600\"\r\n              : \"text-neutral-700 dark:text-neutral-300 border-neutral-400 dark:border-neutral-600 hover:bg-transparent\"\r\n          )}\r\n          onClick={handleSubscribeClick}\r\n          disabled={isLoadingInteraction || isOwnBusiness}\r\n          aria-label={isSubscribed ? \"Unsubscribe\" : \"Subscribe\"}\r\n        >\r\n          {isLoadingInteraction ? (\r\n            <Loader2 className=\"h-6 w-6 animate-spin\" />\r\n          ) : (\r\n            isSubscribed ? (\r\n              <UserMinus className=\"h-6 w-6\" />\r\n            ) : (\r\n              <UserPlus className=\"h-6 w-6\" />\r\n            )\r\n          )}\r\n          {isSubscribed && <GlowEffect color=\"rgba(59, 130, 246, 0.5)\" />}\r\n        </Button>\r\n\r\n        {/* Review Button */}\r\n        {onReviewClick && (\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none text-amber-500 border-amber-500 hover:text-amber-600 hover:bg-transparent hover:border-amber-600\"\r\n            onClick={(e) => {\r\n              e.preventDefault();\r\n              if (onReviewClick) onReviewClick();\r\n            }}\r\n            aria-label=\"Leave a Review\"\r\n          >\r\n            <Star className=\"h-6 w-6\" />\r\n          </Button>\r\n        )}\r\n\r\n        {/* Share Button */}\r\n        {businessSlug && (\r\n          <Button\r\n            variant=\"outline\"\r\n            size=\"icon\"\r\n            className=\"h-10 w-10 rounded-full relative overflow-hidden shadow-md bg-transparent sm:shadow-none text-green-500 border-green-500 hover:text-green-600 hover:bg-transparent hover:border-green-600\"\r\n            onClick={handleShare}\r\n            aria-label=\"Share\"\r\n          >\r\n            <Share2 className=\"h-6 w-6\" />\r\n          </Button>\r\n        )}\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAMA;AApBA;;;;;;;;;AAsBA,6CAA6C;AAC7C,MAAM,aAAa,CAAC,EAAE,KAAK,EAAqB;IAC9C,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YAAE,iBAAiB;QAAM;;;;;;AAGtC;AAEA,8CAA8C;AAC9C,MAAM,cAAc,CAAC,EAAE,KAAK,EAAqB;IAC/C,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,WAAW,CAAC,UAAU,EAAE,OAAO;QACjC;;;;;;AAGN;AAmBe,SAAS,2BAA2B,EACjD,QAAQ,EACR,YAAY,EACZ,oBAAoB,EACpB,MAAM,EACN,QAAQ,EACR,WAAW,EACX,aAAa,EACb,eAAe,EACf,gBAAgB,KAAK,EACrB,aAAa,EACb,eAAe,EAAE,EACjB,eAAe,EAAE,EACe;IAChC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QACxB,aAAa;QACb,WAAW;IACb;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,WAAW;YAC1B,aAAa;QACf;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB,gBAAgB;YACnC,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAc;KAAe;IAEjC,MAAM,kBAAkB;QACtB,IAAI,eAAe;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0CAA0C;gBACpD,aAAa;gBACb,UAAU;YACZ;YACA;QACF;QAEA,IAAI,CAAC,iBAAiB;YACpB,wBAAwB;YACxB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;YAC5C,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,iEAAiE,EAAE,mBAAmB,cAAc;YAC5H;QACF;QAEA,IAAI,UAAU;YACZ;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,eAAe;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kDAAkD;gBAC5D,aAAa;gBACb,UAAU;YACZ;YACA;QACF;QAEA,IAAI,CAAC,iBAAiB;YACpB,wBAAwB;YACxB,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;YAC5C,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,yEAAyE,EAAE,mBAAmB,cAAc;YACpI;QACF;QAEA,IAAI,cAAc;YAChB;QACF,OAAO;YACL;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,cAAc;QAClB,IAAI,CAAC,cAAc;YACjB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,WAAW,GAAG,6DAAoC,uBAAuB,CAAC,EAAE,cAAc;QAChG,MAAM,YAAY,CAAC,UAAU,EAAE,gBAAgB,gBAAgB,6BAA6B,CAAC;QAE7F,iCAAiC;QACjC,IAAI,UAAU,KAAK,EAAE;YACnB,UAAU,KAAK,CAAC;gBACd,OAAO,GAAG,aAAa,wBAAwB,CAAC;gBAChD,MAAM;gBACN,KAAK;YACP,GAAG,KAAK,CAAC,CAAC;gBACR,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,wBAAwB;gBACxB,gBAAgB;YAClB;QACF,OAAO;YACL,wBAAwB;YACxB,gBAAgB;QAClB;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC,MAC3B,IAAI,CAAC,IAAM,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,8BACzB,KAAK,CAAC,IAAM,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;IAC7B;IAEA,IAAI,CAAC,UAAU;QACb,OAAO,MAAM,0BAA0B;IACzC;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,KAAK;wBACL,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0GACA,WACI,6FACA;oDAEN,SAAS;oDACT,UAAU;oDACV,cAAY,WAAW,WAAW;;wDAEjC,qCACC,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,8OAAC,oMAAA,CAAA,QAAK;4DACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,YAAY;;;;;;wDAKxC,0BAAY,8OAAC;4DAAW,OAAM;;;;;;wDAC9B,0BAAY,8OAAC;4DAAY,OAAM;;;;;;;;;;;;;;;;;4CAGnC,CAAC,CAAC,mBAAmB,aAAa,mBACjC,8OAAC,4HAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,WAAU;0DAET,8BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAG1D,8OAAC;4DAAE,WAAU;sEAAiD;;;;;;;;;;;yEAKhE,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0GACA,eACI,iGACA;oDAEN,SAAS;oDACT,UAAU,wBAAwB;oDAClC,cAAY,eAAe,gBAAgB;;wDAE1C,qCACC,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;mEAEnB,6BACE,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;iFAErB,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAKvB,8BAAgB,8OAAC;4DAAW,OAAM;;;;;;wDAClC,8BAAgB,8OAAC;4DAAY,OAAM;;;;;;;;;;;;;;;;;4CAGvC,CAAC,CAAC,mBAAmB,aAAa,mBACjC,8OAAC,4HAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,WAAU;0DAET,8BACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAA6C;;;;;;sEAG1D,8OAAC;4DAAE,WAAU;sEAAiD;;;;;;;;;;;yEAKhE,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;4BASd,+BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,CAAC;wDACR,EAAE,cAAc;wDAChB,IAAI,eAAe;oDACrB;oDACA,cAAW;8DAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAGpB,8OAAC,4HAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;4BASR,8BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4HAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,4HAAA,CAAA,UAAO;;0DACN,8OAAC,4HAAA,CAAA,iBAAc;gDAAC,OAAO;0DACrB,cAAA,8OAAC,2HAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS;oDACT,cAAW;8DAEX,cAAA,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAGtB,8OAAC,4HAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2FACA,WACI,6FACA;wBAEN,SAAS;wBACT,UAAU,wBAAwB;wBAClC,cAAY,WAAW,WAAW;;4BAEjC,qCACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;qDAEnB,8OAAC,oMAAA,CAAA,QAAK;gCACJ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,WAAW,YAAY;;;;;;4BAGxC,0BAAY,8OAAC;gCAAW,OAAM;;;;;;;;;;;;kCAIjC,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2FACA,eACI,iGACA;wBAEN,SAAS;wBACT,UAAU,wBAAwB;wBAClC,cAAY,eAAe,gBAAgB;;4BAE1C,qCACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;uCAEnB,6BACE,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;qDAErB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAGvB,8BAAgB,8OAAC;gCAAW,OAAM;;;;;;;;;;;;oBAIpC,+BACC,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,cAAc;4BAChB,IAAI,eAAe;wBACrB;wBACA,cAAW;kCAEX,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;oBAKnB,8BACC,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;wBACT,cAAW;kCAEX,cAAA,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;AAM9B", "debugId": null}}, {"offset": {"line": 6665, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/animations.ts"], "sourcesContent": ["\"use client\";\r\n\r\n// Animation variants for the public card page\r\n// These are optimized for faster, smoother animations\r\n\r\n// Container animation variants\r\nexport const containerVariants = {\r\n  hidden: { opacity: 0 },\r\n  visible: {\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.3,\r\n      staggerChildren: 0.08, // Faster stagger for children\r\n    },\r\n  },\r\n};\r\n\r\n// Item animation variants\r\nexport const itemVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: {\r\n      type: \"spring\",\r\n      stiffness: 300,\r\n      damping: 24,\r\n      duration: 0.3,\r\n    },\r\n  },\r\n};\r\n\r\n// Card animation variants\r\nexport const cardVariants = {\r\n  hidden: { opacity: 0, y: 15 },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: {\r\n      type: \"spring\",\r\n      stiffness: 300,\r\n      damping: 24,\r\n      duration: 0.4,\r\n    },\r\n  },\r\n  hover: {\r\n    y: -5,\r\n    boxShadow: \"0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)\",\r\n    transition: {\r\n      type: \"spring\",\r\n      stiffness: 300,\r\n      damping: 20,\r\n    },\r\n  },\r\n};\r\n\r\n// Tab animation variants\r\nexport const tabVariants = {\r\n  hidden: { opacity: 0, y: 20 },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: {\r\n      type: \"spring\",\r\n      stiffness: 300,\r\n      damping: 24,\r\n      duration: 0.4,\r\n    },\r\n  },\r\n};\r\n\r\n// Tab content animation variants\r\nexport const tabContentVariants = {\r\n  hidden: { opacity: 0 },\r\n  visible: {\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.3,\r\n    },\r\n  },\r\n  exit: {\r\n    opacity: 0,\r\n    transition: {\r\n      duration: 0.2,\r\n    },\r\n  },\r\n};\r\n\r\n// Button animation variants\r\nexport const buttonVariants = {\r\n  hover: {\r\n    scale: 1.03,\r\n    transition: {\r\n      duration: 0.2,\r\n    },\r\n  },\r\n  tap: {\r\n    scale: 0.98,\r\n  },\r\n};\r\n\r\n// Glow effect animation\r\nexport const glowVariants = {\r\n  initial: {\r\n    opacity: 0.5,\r\n    scale: 1,\r\n  },\r\n  animate: {\r\n    opacity: [0.5, 0.8, 0.5],\r\n    scale: [1, 1.05, 1],\r\n    transition: {\r\n      duration: 2,\r\n      repeat: Infinity,\r\n      repeatType: \"reverse\",\r\n    },\r\n  },\r\n};\r\n\r\n// Scroll-triggered animation for sections\r\nexport const scrollAnimationVariants = {\r\n  hidden: { opacity: 0, y: 30 },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    transition: {\r\n      duration: 0.5,\r\n      ease: \"easeOut\",\r\n    },\r\n  },\r\n};\r\n\r\n// Product grid item animation variants\r\nexport const productItemVariants = {\r\n  hidden: { opacity: 0, scale: 0.95 },\r\n  visible: (i: number) => ({\r\n    opacity: 1,\r\n    scale: 1,\r\n    transition: {\r\n      delay: i * 0.05, // Stagger based on index\r\n      duration: 0.3,\r\n      ease: \"easeOut\",\r\n    },\r\n  }),\r\n  hover: {\r\n    y: -5,\r\n    boxShadow: \"0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)\",\r\n    transition: {\r\n      duration: 0.2,\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAMO,MAAM,oBAAoB;IAC/B,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,iBAAiB;QACnB;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,UAAU;QACZ;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,UAAU;QACZ;IACF;IACA,OAAO;QACL,GAAG,CAAC;QACJ,WAAW;QACX,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,UAAU;QACZ;IACF;AACF;AAGO,MAAM,qBAAqB;IAChC,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QACP,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;IACA,MAAM;QACJ,SAAS;QACT,YAAY;YACV,UAAU;QACZ;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,OAAO;QACL,OAAO;QACP,YAAY;YACV,UAAU;QACZ;IACF;IACA,KAAK;QACH,OAAO;IACT;AACF;AAGO,MAAM,eAAe;IAC1B,SAAS;QACP,SAAS;QACT,OAAO;IACT;IACA,SAAS;QACP,SAAS;YAAC;YAAK;YAAK;SAAI;QACxB,OAAO;YAAC;YAAG;YAAM;SAAE;QACnB,YAAY;YACV,UAAU;YACV,QAAQ;YACR,YAAY;QACd;IACF;AACF;AAGO,MAAM,0BAA0B;IACrC,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,MAAM;QACR;IACF;AACF;AAGO,MAAM,sBAAsB;IACjC,QAAQ;QAAE,SAAS;QAAG,OAAO;IAAK;IAClC,SAAS,CAAC,IAAc,CAAC;YACvB,SAAS;YACT,OAAO;YACP,YAAY;gBACV,OAAO,IAAI;gBACX,UAAU;gBACV,MAAM;YACR;QACF,CAAC;IACD,OAAO;QACL,GAAG,CAAC;QACJ,WAAW;QACX,YAAY;YACV,UAAU;QACZ;IACF;AACF", "debugId": null}}, {"offset": {"line": 6839, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/EnhancedBusinessCardSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { useRef } from \"react\";\r\nimport BusinessCardPreview from \"@/app/(dashboard)/dashboard/business/card/components/BusinessCardPreview\";\r\nimport EnhancedPublicCardActions from \"@/app/components/EnhancedPublicCardActions\";\r\nimport FloatingInteractionButtons from \"@/app/components/FloatingInteractionButtons\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { cardVariants } from \"./animations\";\r\n\r\n// Define the BusinessProfile type\r\ntype BusinessProfile = BusinessCardData & {\r\n  total_reviews?: number;\r\n  subscription_status?: string;\r\n  has_active_subscription?: boolean;\r\n  trial_end_date?: Date | string | null;\r\n};\r\n\r\ninterface EnhancedBusinessCardSectionProps {\r\n  businessProfile: BusinessProfile;\r\n  isAuthenticated: boolean;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | undefined;\r\n  totalLikes: number;\r\n  totalSubscriptions: number;\r\n  averageRating: number;\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  isLoadingInteraction: boolean;\r\n  onSubscribe: () => void;\r\n  onUnsubscribe: () => void;\r\n  onLike: () => void;\r\n  onUnlike: () => void;\r\n  onReviewClick?: () => void;\r\n  isOwnBusiness?: boolean;\r\n  isCurrentUserBusiness?: boolean;\r\n}\r\n\r\nexport default function EnhancedBusinessCardSection({\r\n  businessProfile,\r\n  isAuthenticated,\r\n  userPlan: _userPlan,\r\n  totalLikes,\r\n  totalSubscriptions,\r\n  averageRating,\r\n  isSubscribed,\r\n  hasLiked,\r\n  isLoadingInteraction,\r\n  onSubscribe,\r\n  onUnsubscribe,\r\n  onLike,\r\n  onUnlike,\r\n  onReviewClick,\r\n  isOwnBusiness = false,\r\n  isCurrentUserBusiness = false,\r\n}: EnhancedBusinessCardSectionProps) {\r\n  const cardRef = useRef<HTMLDivElement>(null);\r\n  const isInView = useInView(cardRef, { once: false, amount: 0.2 });\r\n\r\n  // Helper function to format the address from business profile data\r\n  const formatAddress = (profile: BusinessProfile): string => {\r\n    const addressParts = [\r\n      profile.address_line,\r\n      profile.locality,\r\n      profile.city,\r\n      profile.state,\r\n      profile.pincode,\r\n    ].filter(Boolean);\r\n\r\n    return addressParts.join(\", \");\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <motion.div\r\n        ref={cardRef}\r\n        initial=\"hidden\"\r\n        animate={isInView ? \"visible\" : \"hidden\"}\r\n        variants={cardVariants}\r\n        className=\"transition-all duration-300\"\r\n      >\r\n        {/* Card container with relative positioning for floating buttons */}\r\n        <div className=\"max-w-[350px] w-full mx-auto relative\">\r\n          {/* Business Card Preview */}\r\n          <div className=\"relative overflow-visible\">\r\n            <BusinessCardPreview\r\n              data={businessProfile}\r\n              isAuthenticated={isAuthenticated}\r\n              totalLikes={totalLikes}\r\n              totalSubscriptions={totalSubscriptions}\r\n              averageRating={averageRating}\r\n              isSubscribed={isSubscribed}\r\n              hasLiked={hasLiked}\r\n              isLoadingInteraction={isLoadingInteraction}\r\n              isCurrentUserBusiness={isCurrentUserBusiness}\r\n            />\r\n\r\n            {/* Desktop/Tablet Floating Interaction Buttons - positioned relative to the card only */}\r\n            <div className=\"hidden sm:block\">\r\n              <FloatingInteractionButtons\r\n                hasLiked={hasLiked}\r\n                isSubscribed={isSubscribed}\r\n                isLoadingInteraction={isLoadingInteraction}\r\n                onLike={onLike}\r\n                onUnlike={onUnlike}\r\n                onSubscribe={onSubscribe}\r\n                onUnsubscribe={onUnsubscribe}\r\n                isAuthenticated={isAuthenticated}\r\n                isOwnBusiness={isOwnBusiness}\r\n                isCurrentUserBusiness={isCurrentUserBusiness}\r\n                themeColor={businessProfile.theme_color}\r\n                onReviewClick={onReviewClick}\r\n                businessSlug={businessProfile.business_slug}\r\n                businessName={businessProfile.business_name}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Mobile Interaction Buttons - positioned below the card */}\r\n          <div className=\"block sm:hidden mt-4 mb-2 w-full\">\r\n            <FloatingInteractionButtons\r\n              hasLiked={hasLiked}\r\n              isSubscribed={isSubscribed}\r\n              isLoadingInteraction={isLoadingInteraction}\r\n              onLike={onLike}\r\n              onUnlike={onUnlike}\r\n              onSubscribe={onSubscribe}\r\n              onUnsubscribe={onUnsubscribe}\r\n              isAuthenticated={isAuthenticated}\r\n              isOwnBusiness={isOwnBusiness}\r\n              isCurrentUserBusiness={isCurrentUserBusiness}\r\n              themeColor={businessProfile.theme_color}\r\n              onReviewClick={onReviewClick}\r\n              businessSlug={businessProfile.business_slug}\r\n              businessName={businessProfile.business_name}\r\n            />\r\n          </div>\r\n\r\n          {/* QR Code Download and Share Buttons - separate from card */}\r\n          <div className=\"mt-6\">\r\n            <EnhancedPublicCardActions\r\n              businessSlug={businessProfile.business_slug || \"\"}\r\n              businessName={businessProfile.business_name || \"\"}\r\n              ownerName={businessProfile.member_name || \"\"}\r\n              businessAddress={formatAddress(businessProfile)}\r\n              themeColor={businessProfile.theme_color || \"#F59E0B\"}\r\n            />\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AARA;;;;;;;;AAqCe,SAAS,4BAA4B,EAClD,eAAe,EACf,eAAe,EACf,UAAU,SAAS,EACnB,UAAU,EACV,kBAAkB,EAClB,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACpB,WAAW,EACX,aAAa,EACb,MAAM,EACN,QAAQ,EACR,aAAa,EACb,gBAAgB,KAAK,EACrB,wBAAwB,KAAK,EACI;IACjC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAAE,MAAM;QAAO,QAAQ;IAAI;IAE/D,mEAAmE;IACnE,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAAe;YACnB,QAAQ,YAAY;YACpB,QAAQ,QAAQ;YAChB,QAAQ,IAAI;YACZ,QAAQ,KAAK;YACb,QAAQ,OAAO;SAChB,CAAC,MAAM,CAAC;QAET,OAAO,aAAa,IAAI,CAAC;IAC3B;IAEA,qBACE;kBACE,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,KAAK;YACL,SAAQ;YACR,SAAS,WAAW,YAAY;YAChC,UAAU,+IAAA,CAAA,eAAY;YACtB,WAAU;sBAGV,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2LAAA,CAAA,UAAmB;gCAClB,MAAM;gCACN,iBAAiB;gCACjB,YAAY;gCACZ,oBAAoB;gCACpB,eAAe;gCACf,cAAc;gCACd,UAAU;gCACV,sBAAsB;gCACtB,uBAAuB;;;;;;0CAIzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gJAAA,CAAA,UAA0B;oCACzB,UAAU;oCACV,cAAc;oCACd,sBAAsB;oCACtB,QAAQ;oCACR,UAAU;oCACV,aAAa;oCACb,eAAe;oCACf,iBAAiB;oCACjB,eAAe;oCACf,uBAAuB;oCACvB,YAAY,gBAAgB,WAAW;oCACvC,eAAe;oCACf,cAAc,gBAAgB,aAAa;oCAC3C,cAAc,gBAAgB,aAAa;;;;;;;;;;;;;;;;;kCAMjD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gJAAA,CAAA,UAA0B;4BACzB,UAAU;4BACV,cAAc;4BACd,sBAAsB;4BACtB,QAAQ;4BACR,UAAU;4BACV,aAAa;4BACb,eAAe;4BACf,iBAAiB;4BACjB,eAAe;4BACf,uBAAuB;4BACvB,YAAY,gBAAgB,WAAW;4BACvC,eAAe;4BACf,cAAc,gBAAgB,aAAa;4BAC3C,cAAc,gBAAgB,aAAa;;;;;;;;;;;kCAK/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+IAAA,CAAA,UAAyB;4BACxB,cAAc,gBAAgB,aAAa,IAAI;4BAC/C,cAAc,gBAAgB,aAAa,IAAI;4BAC/C,WAAW,gBAAgB,WAAW,IAAI;4BAC1C,iBAAiB,cAAc;4BAC/B,YAAY,gBAAgB,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 7000, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/EnhancedAdSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { AdData } from \"@/types/ad\";\r\nimport { cardVariants } from \"./animations\";\r\n\r\ninterface EnhancedAdSectionProps {\r\n  topAdData: AdData | null;\r\n  businessCustomAd?: {\r\n    enabled?: boolean;\r\n    image_url?: string;\r\n    link_url?: string;\r\n  } | null;\r\n  userPlan?: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\";\r\n}\r\n\r\n// Simple URL validation utility\r\nconst isValidUrl = (url: string): boolean => {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch {\r\n    return false;\r\n  }\r\n};\r\n\r\nexport default function EnhancedAdSection({ topAdData, businessCustomAd, userPlan }: EnhancedAdSectionProps) {\r\n  const adRef = useRef<HTMLDivElement>(null);\r\n  const isInView = useInView(adRef, { once: false, amount: 0.2 });\r\n\r\n  // Check if business owner has Pro/Enterprise access for custom ads\r\n  const hasProEnterpriseAccess = userPlan === \"pro\" || userPlan === \"enterprise\";\r\n\r\n  // Validate custom ad data - must have Pro/Enterprise plan, enabled=true, and valid image URL\r\n  const isCustomAdValid = businessCustomAd &&\r\n    typeof businessCustomAd === 'object' &&\r\n    businessCustomAd.enabled === true &&\r\n    businessCustomAd.image_url &&\r\n    typeof businessCustomAd.image_url === 'string' &&\r\n    businessCustomAd.image_url.trim() !== \"\" &&\r\n    isValidUrl(businessCustomAd.image_url);\r\n\r\n  // Validate custom ad link URL if provided\r\n  const hasValidLinkUrl = businessCustomAd?.link_url &&\r\n    typeof businessCustomAd.link_url === 'string' &&\r\n    businessCustomAd.link_url.trim() !== \"\" &&\r\n    isValidUrl(businessCustomAd.link_url);\r\n\r\n  // Determine which ad to show - business custom ad takes priority (only for Pro/Enterprise with valid data and enabled=true)\r\n  const shouldShowBusinessAd = hasProEnterpriseAccess && isCustomAdValid;\r\n  const shouldShowTopAd = !shouldShowBusinessAd && topAdData && topAdData.imageUrl;\r\n\r\n\r\n\r\n  return (\r\n    <motion.div\r\n      ref={adRef}\r\n      initial=\"hidden\"\r\n      animate={isInView ? \"visible\" : \"hidden\"}\r\n      variants={cardVariants}\r\n      className=\"transition-all duration-300\"\r\n    >\r\n      {/* Ad Slot with enhanced animations */}\r\n      <div className=\"flex-shrink-0 w-full overflow-hidden\">\r\n        {shouldShowBusinessAd ? (\r\n          /* Business Custom Ad */\r\n          hasValidLinkUrl ? (\r\n            <Link\r\n              href={businessCustomAd!.link_url!}\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              className=\"block w-full rounded-lg overflow-hidden group cursor-pointer\"\r\n            >\r\n            <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n              <motion.div\r\n                initial={{ scale: 1 }}\r\n                whileHover={{ scale: 1.03 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                <Image\r\n                  src={businessCustomAd!.image_url!}\r\n                  alt=\"Business Advertisement\"\r\n                  width={1200}\r\n                  height={675} // 16:9 aspect ratio\r\n                  className=\"w-full h-auto rounded-lg object-cover max-w-full transition-all duration-300\"\r\n                  unoptimized\r\n                />\r\n              </motion.div>\r\n\r\n              {/* Subtle overlay on hover */}\r\n              <motion.div\r\n                className=\"absolute inset-0 bg-black/0 rounded-lg\"\r\n                initial={{ opacity: 0 }}\r\n                whileHover={{\r\n                  opacity: 1,\r\n                  background: \"linear-gradient(to bottom, rgba(0,0,0,0) 80%, rgba(0,0,0,0.1) 100%)\"\r\n                }}\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n\r\n              {/* Business ad indicator */}\r\n              <div className=\"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\r\n                Sponsored\r\n              </div>\r\n            </div>\r\n            </Link>\r\n          ) : (\r\n            /* Non-clickable Business Custom Ad (no link URL) */\r\n            <div className=\"block w-full rounded-lg overflow-hidden\">\r\n              <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n                <motion.div\r\n                  initial={{ scale: 1 }}\r\n                  whileHover={{ scale: 1.02 }}\r\n                  transition={{ duration: 0.3 }}\r\n                >\r\n                  <Image\r\n                    src={businessCustomAd!.image_url!}\r\n                    alt=\"Business Advertisement\"\r\n                    width={1200}\r\n                    height={675} // 16:9 aspect ratio\r\n                    className=\"w-full h-auto rounded-lg object-cover max-w-full transition-all duration-300\"\r\n                    unoptimized\r\n                  />\r\n                </motion.div>\r\n\r\n                {/* Business ad indicator */}\r\n                <div className=\"absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded\">\r\n                  Sponsored\r\n                </div>\r\n              </div>\r\n            </div>\r\n          )\r\n        ) : shouldShowTopAd ? (\r\n          /* Platform/Global Ad */\r\n          <Link\r\n            href={topAdData!.linkUrl || \"#\"}\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"block w-full rounded-lg overflow-hidden group\"\r\n          >\r\n            <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n              <motion.div\r\n                initial={{ scale: 1 }}\r\n                whileHover={{ scale: 1.03 }}\r\n                transition={{ duration: 0.3 }}\r\n              >\r\n                <Image\r\n                  src={topAdData!.imageUrl}\r\n                  alt=\"Advertisement\"\r\n                  width={1200}\r\n                  height={675} // 16:9 aspect ratio\r\n                  className=\"w-full h-auto rounded-lg object-contain max-w-full transition-all duration-300\"\r\n                  unoptimized\r\n                />\r\n              </motion.div>\r\n\r\n              {/* Subtle overlay on hover */}\r\n              <motion.div\r\n                className=\"absolute inset-0 bg-black/0 rounded-lg\"\r\n                initial={{ opacity: 0 }}\r\n                whileHover={{\r\n                  opacity: 1,\r\n                  background: \"linear-gradient(to bottom, rgba(0,0,0,0) 80%, rgba(0,0,0,0.1) 100%)\"\r\n                }}\r\n                transition={{ duration: 0.3 }}\r\n              />\r\n            </div>\r\n          </Link>\r\n        ) : (\r\n          <div className=\"w-full h-32 md:h-48 lg:h-64 bg-gradient-to-r from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-700 rounded-lg flex items-center justify-center\">\r\n            <p className=\"text-neutral-500 dark:text-neutral-400 text-sm\">\r\n              Advertisement space\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAmBA,gCAAgC;AAChC,MAAM,aAAa,CAAC;IAClB,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEe,SAAS,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAA0B;IACzG,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACrC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QAAE,MAAM;QAAO,QAAQ;IAAI;IAE7D,mEAAmE;IACnE,MAAM,yBAAyB,aAAa,SAAS,aAAa;IAElE,6FAA6F;IAC7F,MAAM,kBAAkB,oBACtB,OAAO,qBAAqB,YAC5B,iBAAiB,OAAO,KAAK,QAC7B,iBAAiB,SAAS,IAC1B,OAAO,iBAAiB,SAAS,KAAK,YACtC,iBAAiB,SAAS,CAAC,IAAI,OAAO,MACtC,WAAW,iBAAiB,SAAS;IAEvC,0CAA0C;IAC1C,MAAM,kBAAkB,kBAAkB,YACxC,OAAO,iBAAiB,QAAQ,KAAK,YACrC,iBAAiB,QAAQ,CAAC,IAAI,OAAO,MACrC,WAAW,iBAAiB,QAAQ;IAEtC,4HAA4H;IAC5H,MAAM,uBAAuB,0BAA0B;IACvD,MAAM,kBAAkB,CAAC,wBAAwB,aAAa,UAAU,QAAQ;IAIhF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS,WAAW,YAAY;QAChC,UAAU,+IAAA,CAAA,eAAY;QACtB,WAAU;kBAGV,cAAA,8OAAC;YAAI,WAAU;sBACZ,uBACC,sBAAsB,GACtB,gCACE,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,iBAAkB,QAAQ;gBAChC,QAAO;gBACP,KAAI;gBACJ,WAAU;0BAEZ,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,iBAAkB,SAAS;gCAChC,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,WAAW;;;;;;;;;;;sCAKf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCACV,SAAS;gCACT,YAAY;4BACd;4BACA,YAAY;gCAAE,UAAU;4BAAI;;;;;;sCAI9B,8OAAC;4BAAI,WAAU;sCAA0E;;;;;;;;;;;;;;;;uBAM3F,kDAAkD,iBAClD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,iBAAkB,SAAS;gCAChC,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,WAAW;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;sCAA0E;;;;;;;;;;;;;;;;uBAM7F,kBACF,sBAAsB,iBACtB,8OAAC,4JAAA,CAAA,UAAI;gBACH,MAAM,UAAW,OAAO,IAAI;gBAC5B,QAAO;gBACP,KAAI;gBACJ,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,UAAW,QAAQ;gCACxB,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,WAAW;;;;;;;;;;;sCAKf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCACV,SAAS;gCACT,YAAY;4BACd;4BACA,YAAY;gCAAE,UAAU;4BAAI;;;;;;;;;;;;;;;;qCAKlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;8BAAiD;;;;;;;;;;;;;;;;;;;;;AAQ1E", "debugId": null}}, {"offset": {"line": 7263, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/BusinessDetails/ProfessionalBusinessTable.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport {\r\n  Building2,\r\n  User,\r\n  Phone,\r\n  Mail,\r\n  MapPin,\r\n  Globe,\r\n  Clock,\r\n  Truck,\r\n  MessageCircle,\r\n  Calendar,\r\n  Navigation,\r\n} from \"lucide-react\";\r\nimport { Table, TableBody, TableCell, TableRow } from \"@/components/ui/table\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport WhatsAppIcon from \"@/app/components/icons/WhatsAppIcon\";\r\nimport FacebookIcon from \"@/app/components/icons/FacebookIcon\";\r\nimport InstagramIcon from \"@/app/components/icons/InstagramIcon\";\r\n\r\ninterface BusinessHours {\r\n  [key: string]: {\r\n    isOpen: boolean;\r\n    openTime: string;\r\n    closeTime: string;\r\n  };\r\n}\r\n\r\ninterface ProfessionalBusinessTableProps {\r\n  businessProfile: {\r\n    business_name?: string;\r\n    member_name?: string;\r\n    title?: string;\r\n    business_category?: string;\r\n    phone?: string;\r\n    contact_email?: string;\r\n    address_line?: string;\r\n    locality?: string;\r\n    city?: string;\r\n    state?: string;\r\n    pincode?: string;\r\n    delivery_info?: string;\r\n    business_hours?: BusinessHours;\r\n    instagram_url?: string;\r\n    facebook_url?: string;\r\n    whatsapp_number?: string;\r\n    established_year?: number | null;\r\n  };\r\n  isAuthenticated: boolean;\r\n}\r\n\r\n// Helper function to get current day and business status\r\nconst getCurrentDay = (): string => {\r\n  const days = [\r\n    \"sunday\",\r\n    \"monday\",\r\n    \"tuesday\",\r\n    \"wednesday\",\r\n    \"thursday\",\r\n    \"friday\",\r\n    \"saturday\",\r\n  ];\r\n  return days[new Date().getDay()];\r\n};\r\n\r\nconst getBusinessStatus = (\r\n  businessHours?: BusinessHours\r\n): { isOpen: boolean; status: string; statusColor: string } => {\r\n  if (!businessHours || typeof businessHours !== \"object\") {\r\n    return {\r\n      isOpen: false,\r\n      status: \"Hours not available\",\r\n      statusColor: \"text-neutral-500\",\r\n    };\r\n  }\r\n\r\n  const currentDay = getCurrentDay();\r\n  const currentTime = new Date();\r\n  const currentHour = currentTime.getHours();\r\n  const currentMinute = currentTime.getMinutes();\r\n  const currentTimeInMinutes = currentHour * 60 + currentMinute;\r\n\r\n  const todayHours = businessHours[currentDay];\r\n\r\n  if (!todayHours || !todayHours.isOpen) {\r\n    return {\r\n      isOpen: false,\r\n      status: \"Closed today\",\r\n      statusColor: \"text-red-600 dark:text-red-400\",\r\n    };\r\n  }\r\n\r\n  const openTime = todayHours.openTime?.split(\":\");\r\n  const closeTime = todayHours.closeTime?.split(\":\");\r\n\r\n  if (!openTime || !closeTime) {\r\n    return {\r\n      isOpen: false,\r\n      status: \"Hours not available\",\r\n      statusColor: \"text-neutral-500\",\r\n    };\r\n  }\r\n\r\n  const openTimeInMinutes = parseInt(openTime[0]) * 60 + parseInt(openTime[1]);\r\n  const closeTimeInMinutes =\r\n    parseInt(closeTime[0]) * 60 + parseInt(closeTime[1]);\r\n\r\n  if (\r\n    currentTimeInMinutes >= openTimeInMinutes &&\r\n    currentTimeInMinutes <= closeTimeInMinutes\r\n  ) {\r\n    const minutesUntilClose = closeTimeInMinutes - currentTimeInMinutes;\r\n    if (minutesUntilClose <= 60) {\r\n      return {\r\n        isOpen: true,\r\n        status: `Closes in ${minutesUntilClose} minute${\r\n          minutesUntilClose !== 1 ? \"s\" : \"\"\r\n        }`,\r\n        statusColor: \"text-yellow-600 dark:text-yellow-400\",\r\n      };\r\n    } else {\r\n      const closeHour = parseInt(closeTime[0]);\r\n      const closeMinute = parseInt(closeTime[1]);\r\n      const closeTimeFormatted = `${\r\n        closeHour > 12 ? closeHour - 12 : closeHour\r\n      }:${closeMinute.toString().padStart(2, \"0\")} ${\r\n        closeHour >= 12 ? \"PM\" : \"AM\"\r\n      }`;\r\n      return {\r\n        isOpen: true,\r\n        status: `Open until ${closeTimeFormatted}`,\r\n        statusColor: \"text-green-600 dark:text-green-400\",\r\n      };\r\n    }\r\n  } else if (currentTimeInMinutes < openTimeInMinutes) {\r\n    const minutesUntilOpen = openTimeInMinutes - currentTimeInMinutes;\r\n    if (minutesUntilOpen <= 60) {\r\n      return {\r\n        isOpen: false,\r\n        status: `Opens in ${minutesUntilOpen} minute${\r\n          minutesUntilOpen !== 1 ? \"s\" : \"\"\r\n        }`,\r\n        statusColor: \"text-blue-600 dark:text-blue-400\",\r\n      };\r\n    } else {\r\n      const openHour = parseInt(openTime[0]);\r\n      const openMinute = parseInt(openTime[1]);\r\n      const openTimeFormatted = `${\r\n        openHour > 12 ? openHour - 12 : openHour\r\n      }:${openMinute.toString().padStart(2, \"0\")} ${\r\n        openHour >= 12 ? \"PM\" : \"AM\"\r\n      }`;\r\n      return {\r\n        isOpen: false,\r\n        status: `Opens at ${openTimeFormatted}`,\r\n        statusColor: \"text-orange-600 dark:text-orange-400\",\r\n      };\r\n    }\r\n  } else {\r\n    return {\r\n      isOpen: false,\r\n      status: \"Closed for today\",\r\n      statusColor: \"text-red-600 dark:text-red-400\",\r\n    };\r\n  }\r\n};\r\n\r\nconst formatBusinessHoursForDisplay = (\r\n  businessHours?: BusinessHours\r\n): Array<{ day: string; hours: string }> => {\r\n  if (!businessHours || typeof businessHours !== \"object\") {\r\n    return [];\r\n  }\r\n\r\n  const days = [\r\n    \"monday\",\r\n    \"tuesday\",\r\n    \"wednesday\",\r\n    \"thursday\",\r\n    \"friday\",\r\n    \"saturday\",\r\n    \"sunday\",\r\n  ];\r\n  const dayNames = [\r\n    \"Monday\",\r\n    \"Tuesday\",\r\n    \"Wednesday\",\r\n    \"Thursday\",\r\n    \"Friday\",\r\n    \"Saturday\",\r\n    \"Sunday\",\r\n  ];\r\n\r\n  return days.map((day, index) => {\r\n    const dayData = businessHours[day];\r\n    const dayName = dayNames[index];\r\n\r\n    if (!dayData || !dayData.isOpen) {\r\n      return { day: dayName, hours: \"Closed\" };\r\n    }\r\n\r\n    const openTime = formatTime(dayData.openTime);\r\n    const closeTime = formatTime(dayData.closeTime);\r\n    return { day: dayName, hours: `${openTime} - ${closeTime}` };\r\n  });\r\n};\r\n\r\nconst formatTime = (time: string): string => {\r\n  const [hour, minute] = time.split(\":\");\r\n  const hourNum = parseInt(hour);\r\n  const period = hourNum >= 12 ? \"PM\" : \"AM\";\r\n  const displayHour =\r\n    hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;\r\n  return `${displayHour}:${minute} ${period}`;\r\n};\r\n\r\nexport default function ProfessionalBusinessTable({\r\n  businessProfile,\r\n  isAuthenticated: _isAuthenticated,\r\n}: ProfessionalBusinessTableProps) {\r\n  const businessStatus = getBusinessStatus(businessProfile.business_hours);\r\n\r\n  // Format address\r\n  const fullAddress = [\r\n    businessProfile.address_line,\r\n    businessProfile.locality,\r\n    businessProfile.city,\r\n    businessProfile.state,\r\n    businessProfile.pincode,\r\n  ]\r\n    .filter(Boolean)\r\n    .join(\", \");\r\n\r\n  // Get business hours for display\r\n  const businessHoursData = formatBusinessHoursForDisplay(\r\n    businessProfile.business_hours\r\n  );\r\n\r\n  // Helper functions for contact actions\r\n  const handlePhoneClick = () => {\r\n    if (businessProfile.phone) {\r\n      const formattedNumber = businessProfile.phone.replace(/\\D/g, \"\");\r\n      window.open(`tel:+91${formattedNumber}`, \"_self\");\r\n    }\r\n  };\r\n\r\n  const handleEmailClick = () => {\r\n    if (businessProfile.contact_email) {\r\n      window.open(`mailto:${businessProfile.contact_email}`, \"_self\");\r\n    }\r\n  };\r\n\r\n  const handleWhatsAppClick = () => {\r\n    if (businessProfile.whatsapp_number) {\r\n      const formattedNumber = businessProfile.whatsapp_number.replace(\r\n        /\\D/g,\r\n        \"\"\r\n      );\r\n      const whatsappNumber = formattedNumber.startsWith(\"91\")\r\n        ? formattedNumber\r\n        : `91${formattedNumber}`;\r\n      const message = encodeURIComponent(\r\n        `Hi ${businessProfile.business_name}, I found your business on Dukancard and would like to know more about your services.`\r\n      );\r\n      window.open(`https://wa.me/${whatsappNumber}?text=${message}`, \"_blank\");\r\n    }\r\n  };\r\n\r\n  const handleFacebookClick = () => {\r\n    if (businessProfile.facebook_url) {\r\n      window.open(businessProfile.facebook_url, \"_blank\");\r\n    }\r\n  };\r\n\r\n  const handleInstagramClick = () => {\r\n    if (businessProfile.instagram_url) {\r\n      window.open(businessProfile.instagram_url, \"_blank\");\r\n    }\r\n  };\r\n\r\n  const tableData = [\r\n    // Business Identity\r\n    {\r\n      icon: Building2,\r\n      label: \"Business Name\",\r\n      value: businessProfile.business_name || \"Not specified\",\r\n      category: \"identity\",\r\n    },\r\n    {\r\n      icon: User,\r\n      label: \"Owner Name\",\r\n      value: businessProfile.member_name || \"Not specified\",\r\n      category: \"identity\",\r\n    },\r\n    {\r\n      icon: Globe,\r\n      label: \"Business Category\",\r\n      value: businessProfile.business_category || \"Not specified\",\r\n      category: \"identity\",\r\n    },\r\n    {\r\n      icon: Calendar,\r\n      label: \"Established Year\",\r\n      value: businessProfile.established_year\r\n        ? `${businessProfile.established_year}`\r\n        : \"Not specified\",\r\n      category: \"identity\",\r\n    },\r\n    {\r\n      icon: Clock,\r\n      label: \"Business Status\",\r\n      value: businessStatus.status,\r\n      category: \"identity\",\r\n      statusColor: businessStatus.statusColor,\r\n    },\r\n\r\n    // Contact Information - Always show as clickable buttons\r\n    ...(businessProfile.phone\r\n      ? [\r\n          {\r\n            icon: Phone,\r\n            label: \"Phone Number\",\r\n            value: \"Call Now\",\r\n            category: \"contact\",\r\n            isContactButton: true,\r\n            contactType: \"phone\",\r\n            onClick: handlePhoneClick,\r\n          },\r\n        ]\r\n      : []),\r\n    ...(businessProfile.contact_email\r\n      ? [\r\n          {\r\n            icon: Mail,\r\n            label: \"Email Address\",\r\n            value: \"Send Email\",\r\n            category: \"contact\",\r\n            isContactButton: true,\r\n            contactType: \"email\",\r\n            onClick: handleEmailClick,\r\n          },\r\n        ]\r\n      : []),\r\n    {\r\n      icon: MapPin,\r\n      label: \"Full Address\",\r\n      value: fullAddress || \"Not provided\",\r\n      category: \"contact\",\r\n    },\r\n    {\r\n      icon: Truck,\r\n      label: \"Delivery Info\",\r\n      value: businessProfile.delivery_info || \"Not specified\",\r\n      category: \"contact\",\r\n    },\r\n\r\n    // Business Hours & Operations - separate row for each day\r\n    ...businessHoursData.map(({ day, hours }) => ({\r\n      icon: Clock,\r\n      label: day,\r\n      value: hours,\r\n      category: \"operations\" as const,\r\n    })),\r\n\r\n    // Social Media - Show as clickable buttons\r\n    ...(businessProfile.whatsapp_number\r\n      ? [\r\n          {\r\n            icon: MessageCircle,\r\n            label: \"WhatsApp\",\r\n            value: \"Chat on WhatsApp\",\r\n            category: \"social\",\r\n            isContactButton: true,\r\n            contactType: \"whatsapp\",\r\n            onClick: handleWhatsAppClick,\r\n          },\r\n        ]\r\n      : []),\r\n    ...(businessProfile.facebook_url\r\n      ? [\r\n          {\r\n            icon: Globe,\r\n            label: \"Facebook\",\r\n            value: \"Visit Facebook Page\",\r\n            category: \"social\",\r\n            isContactButton: true,\r\n            contactType: \"facebook\",\r\n            onClick: handleFacebookClick,\r\n          },\r\n        ]\r\n      : []),\r\n    ...(businessProfile.instagram_url\r\n      ? [\r\n          {\r\n            icon: Globe,\r\n            label: \"Instagram\",\r\n            value: \"Visit Instagram Profile\",\r\n            category: \"social\",\r\n            isContactButton: true,\r\n            contactType: \"instagram\",\r\n            onClick: handleInstagramClick,\r\n          },\r\n        ]\r\n      : []),\r\n  ];\r\n\r\n  const categories = [\r\n    {\r\n      id: \"identity\",\r\n      name: \"Business Information\",\r\n      color: \"bg-white dark:bg-black\",\r\n    },\r\n    { id: \"contact\", name: \"Contact Details\", color: \"bg-white dark:bg-black\" },\r\n    {\r\n      id: \"operations\",\r\n      name: \"Weekly Schedule\",\r\n      color: \"bg-white dark:bg-black\",\r\n    },\r\n    { id: \"social\", name: \"Social Media\", color: \"bg-white dark:bg-black\" },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-6\">\r\n      <div className=\"space-y-4\">\r\n        {categories.map((category, categoryIndex) => {\r\n          const categoryData = tableData.filter(\r\n            (item) => item.category === category.id\r\n          );\r\n\r\n          return (\r\n            <motion.div\r\n              key={category.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ delay: categoryIndex * 0.1, duration: 0.5 }}\r\n              className={`rounded-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden ${category.color}`}\r\n            >\r\n              <div className=\"p-4 border-b border-neutral-200 dark:border-neutral-700 bg-neutral-50 dark:bg-neutral-900\">\r\n                <h4 className=\"font-semibold text-neutral-900 dark:text-neutral-100\">\r\n                  {category.name}\r\n                </h4>\r\n              </div>\r\n\r\n              <Table>\r\n                <TableBody>\r\n                  {categoryData.map((row, index) => {\r\n                    const IconComponent = row.icon;\r\n                    return (\r\n                      <TableRow\r\n                        key={`${category.id}-${index}`}\r\n                        className=\"hover:bg-white/50 dark:hover:bg-neutral-800/50\"\r\n                      >\r\n                        <TableCell className=\"font-medium w-1/3\">\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <IconComponent className=\"w-4 h-4 text-neutral-600 dark:text-neutral-400\" />\r\n                            <span className=\"text-sm\">{row.label}</span>\r\n                          </div>\r\n                        </TableCell>\r\n                        <TableCell className=\"w-2/3\">\r\n                          <div className=\"flex items-center gap-2\">\r\n                            {(\r\n                              row as {\r\n                                isContactButton?: boolean;\r\n                                contactType?: string;\r\n                                onClick?: () => void;\r\n                              }\r\n                            ).isContactButton ? (\r\n                              <Button\r\n                                onClick={\r\n                                  (row as { onClick: () => void }).onClick\r\n                                }\r\n                                size=\"sm\"\r\n                                className={`font-medium text-xs py-1.5 px-3 h-7 shadow-sm hover:shadow-md transition-all duration-200 ${\r\n                                  (row as { contactType: string })\r\n                                    .contactType === \"phone\"\r\n                                    ? \"bg-blue-600 hover:bg-blue-700 text-white\"\r\n                                    : (row as { contactType: string })\r\n                                        .contactType === \"email\"\r\n                                    ? \"bg-purple-600 hover:bg-purple-700 text-white\"\r\n                                    : (row as { contactType: string })\r\n                                        .contactType === \"whatsapp\"\r\n                                    ? \"bg-green-600 hover:bg-green-700 text-white\"\r\n                                    : (row as { contactType: string })\r\n                                        .contactType === \"facebook\"\r\n                                    ? \"bg-blue-800 hover:bg-blue-900 text-white\"\r\n                                    : (row as { contactType: string })\r\n                                        .contactType === \"instagram\"\r\n                                    ? \"bg-pink-600 hover:bg-pink-700 text-white\"\r\n                                    : \"bg-gray-600 hover:bg-gray-700 text-white\"\r\n                                }`}\r\n                              >\r\n                                {(row as { contactType: string })\r\n                                  .contactType === \"phone\" && (\r\n                                  <Phone className=\"mr-2 h-3 w-3\" />\r\n                                )}\r\n                                {(row as { contactType: string })\r\n                                  .contactType === \"email\" && (\r\n                                  <Mail className=\"mr-2 h-3 w-3\" />\r\n                                )}\r\n                                {(row as { contactType: string })\r\n                                  .contactType === \"whatsapp\" && (\r\n                                  <WhatsAppIcon className=\"mr-2 h-3 w-3\" />\r\n                                )}\r\n                                {(row as { contactType: string })\r\n                                  .contactType === \"facebook\" && (\r\n                                  <FacebookIcon className=\"mr-2 h-3 w-3\" />\r\n                                )}\r\n                                {(row as { contactType: string })\r\n                                  .contactType === \"instagram\" && (\r\n                                  <InstagramIcon className=\"mr-2 h-3 w-3\" />\r\n                                )}\r\n                                {row.value}\r\n                              </Button>\r\n                            ) : (row as { isButton?: boolean; url?: string })\r\n                                .isButton ? (\r\n                              <Button\r\n                                onClick={() => {\r\n                                  const rowWithUrl = row as { url?: string };\r\n                                  if (rowWithUrl.url) {\r\n                                    window.open(rowWithUrl.url, \"_blank\");\r\n                                  }\r\n                                }}\r\n                                size=\"sm\"\r\n                                className=\"bg-blue-600 hover:bg-blue-700 text-white font-medium text-xs py-1.5 px-3 h-7 shadow-sm hover:shadow-md transition-all duration-200\"\r\n                              >\r\n                                <Navigation className=\"mr-2 h-3 w-3\" />\r\n                                Get Directions\r\n                              </Button>\r\n                            ) : row.statusColor ? (\r\n                              <span\r\n                                className={`text-sm font-medium ${row.statusColor}`}\r\n                              >\r\n                                {row.value}\r\n                              </span>\r\n                            ) : row.label.includes(\"Status\") &&\r\n                              row.value === \"online\" ? (\r\n                              <Badge\r\n                                variant=\"default\"\r\n                                className=\"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\"\r\n                              >\r\n                                {row.value}\r\n                              </Badge>\r\n                            ) : row.label.includes(\"Status\") &&\r\n                              row.value === \"offline\" ? (\r\n                              <Badge\r\n                                variant=\"secondary\"\r\n                                className=\"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200\"\r\n                              >\r\n                                {row.value}\r\n                              </Badge>\r\n                            ) : row.label.includes(\"Subscription\") ? (\r\n                              <Badge\r\n                                variant={\r\n                                  row.value === \"Yes\" ? \"default\" : \"secondary\"\r\n                                }\r\n                              >\r\n                                {row.value}\r\n                              </Badge>\r\n                            ) : (\r\n                              <span className=\"text-sm text-neutral-900 dark:text-neutral-100\">\r\n                                {row.value}\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        </TableCell>\r\n                      </TableRow>\r\n                    );\r\n                  })}\r\n                </TableBody>\r\n              </Table>\r\n            </motion.div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;;AAsDA,yDAAyD;AACzD,MAAM,gBAAgB;IACpB,MAAM,OAAO;QACX;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,IAAI,CAAC,IAAI,OAAO,MAAM,GAAG;AAClC;AAEA,MAAM,oBAAoB,CACxB;IAEA,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa;IACnB,MAAM,cAAc,IAAI;IACxB,MAAM,cAAc,YAAY,QAAQ;IACxC,MAAM,gBAAgB,YAAY,UAAU;IAC5C,MAAM,uBAAuB,cAAc,KAAK;IAEhD,MAAM,aAAa,aAAa,CAAC,WAAW;IAE5C,IAAI,CAAC,cAAc,CAAC,WAAW,MAAM,EAAE;QACrC,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,WAAW,QAAQ,EAAE,MAAM;IAC5C,MAAM,YAAY,WAAW,SAAS,EAAE,MAAM;IAE9C,IAAI,CAAC,YAAY,CAAC,WAAW;QAC3B,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,SAAS,QAAQ,CAAC,EAAE,IAAI,KAAK,SAAS,QAAQ,CAAC,EAAE;IAC3E,MAAM,qBACJ,SAAS,SAAS,CAAC,EAAE,IAAI,KAAK,SAAS,SAAS,CAAC,EAAE;IAErD,IACE,wBAAwB,qBACxB,wBAAwB,oBACxB;QACA,MAAM,oBAAoB,qBAAqB;QAC/C,IAAI,qBAAqB,IAAI;YAC3B,OAAO;gBACL,QAAQ;gBACR,QAAQ,CAAC,UAAU,EAAE,kBAAkB,OAAO,EAC5C,sBAAsB,IAAI,MAAM,IAChC;gBACF,aAAa;YACf;QACF,OAAO;YACL,MAAM,YAAY,SAAS,SAAS,CAAC,EAAE;YACvC,MAAM,cAAc,SAAS,SAAS,CAAC,EAAE;YACzC,MAAM,qBAAqB,GACzB,YAAY,KAAK,YAAY,KAAK,UACnC,CAAC,EAAE,YAAY,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAC3C,aAAa,KAAK,OAAO,MACzB;YACF,OAAO;gBACL,QAAQ;gBACR,QAAQ,CAAC,WAAW,EAAE,oBAAoB;gBAC1C,aAAa;YACf;QACF;IACF,OAAO,IAAI,uBAAuB,mBAAmB;QACnD,MAAM,mBAAmB,oBAAoB;QAC7C,IAAI,oBAAoB,IAAI;YAC1B,OAAO;gBACL,QAAQ;gBACR,QAAQ,CAAC,SAAS,EAAE,iBAAiB,OAAO,EAC1C,qBAAqB,IAAI,MAAM,IAC/B;gBACF,aAAa;YACf;QACF,OAAO;YACL,MAAM,WAAW,SAAS,QAAQ,CAAC,EAAE;YACrC,MAAM,aAAa,SAAS,QAAQ,CAAC,EAAE;YACvC,MAAM,oBAAoB,GACxB,WAAW,KAAK,WAAW,KAAK,SACjC,CAAC,EAAE,WAAW,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAC1C,YAAY,KAAK,OAAO,MACxB;YACF,OAAO;gBACL,QAAQ;gBACR,QAAQ,CAAC,SAAS,EAAE,mBAAmB;gBACvC,aAAa;YACf;QACF;IACF,OAAO;QACL,OAAO;YACL,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;IACF;AACF;AAEA,MAAM,gCAAgC,CACpC;IAEA,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,OAAO,EAAE;IACX;IAEA,MAAM,OAAO;QACX;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,KAAK,GAAG,CAAC,CAAC,KAAK;QACpB,MAAM,UAAU,aAAa,CAAC,IAAI;QAClC,MAAM,UAAU,QAAQ,CAAC,MAAM;QAE/B,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE;YAC/B,OAAO;gBAAE,KAAK;gBAAS,OAAO;YAAS;QACzC;QAEA,MAAM,WAAW,WAAW,QAAQ,QAAQ;QAC5C,MAAM,YAAY,WAAW,QAAQ,SAAS;QAC9C,OAAO;YAAE,KAAK;YAAS,OAAO,GAAG,SAAS,GAAG,EAAE,WAAW;QAAC;IAC7D;AACF;AAEA,MAAM,aAAa,CAAC;IAClB,MAAM,CAAC,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC;IAClC,MAAM,UAAU,SAAS;IACzB,MAAM,SAAS,WAAW,KAAK,OAAO;IACtC,MAAM,cACJ,UAAU,KAAK,UAAU,KAAK,YAAY,IAAI,KAAK;IACrD,OAAO,GAAG,YAAY,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ;AAC7C;AAEe,SAAS,0BAA0B,EAChD,eAAe,EACf,iBAAiB,gBAAgB,EACF;IAC/B,MAAM,iBAAiB,kBAAkB,gBAAgB,cAAc;IAEvE,iBAAiB;IACjB,MAAM,cAAc;QAClB,gBAAgB,YAAY;QAC5B,gBAAgB,QAAQ;QACxB,gBAAgB,IAAI;QACpB,gBAAgB,KAAK;QACrB,gBAAgB,OAAO;KACxB,CACE,MAAM,CAAC,SACP,IAAI,CAAC;IAER,iCAAiC;IACjC,MAAM,oBAAoB,8BACxB,gBAAgB,cAAc;IAGhC,uCAAuC;IACvC,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,KAAK,EAAE;YACzB,MAAM,kBAAkB,gBAAgB,KAAK,CAAC,OAAO,CAAC,OAAO;YAC7D,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,iBAAiB,EAAE;QAC3C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,gBAAgB,aAAa,EAAE;YACjC,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,gBAAgB,aAAa,EAAE,EAAE;QACzD;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,eAAe,EAAE;YACnC,MAAM,kBAAkB,gBAAgB,eAAe,CAAC,OAAO,CAC7D,OACA;YAEF,MAAM,iBAAiB,gBAAgB,UAAU,CAAC,QAC9C,kBACA,CAAC,EAAE,EAAE,iBAAiB;YAC1B,MAAM,UAAU,mBACd,CAAC,GAAG,EAAE,gBAAgB,aAAa,CAAC,qFAAqF,CAAC;YAE5H,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,SAAS,EAAE;QACjE;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,gBAAgB,YAAY,EAAE;YAChC,OAAO,IAAI,CAAC,gBAAgB,YAAY,EAAE;QAC5C;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,gBAAgB,aAAa,EAAE;YACjC,OAAO,IAAI,CAAC,gBAAgB,aAAa,EAAE;QAC7C;IACF;IAEA,MAAM,YAAY;QAChB,oBAAoB;QACpB;YACE,MAAM,gNAAA,CAAA,YAAS;YACf,OAAO;YACP,OAAO,gBAAgB,aAAa,IAAI;YACxC,UAAU;QACZ;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,OAAO,gBAAgB,WAAW,IAAI;YACtC,UAAU;QACZ;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,gBAAgB,iBAAiB,IAAI;YAC5C,UAAU;QACZ;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO,gBAAgB,gBAAgB,GACnC,GAAG,gBAAgB,gBAAgB,EAAE,GACrC;YACJ,UAAU;QACZ;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,eAAe,MAAM;YAC5B,UAAU;YACV,aAAa,eAAe,WAAW;QACzC;QAEA,yDAAyD;WACrD,gBAAgB,KAAK,GACrB;YACE;gBACE,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,aAAa;gBACb,SAAS;YACX;SACD,GACD,EAAE;WACF,gBAAgB,aAAa,GAC7B;YACE;gBACE,MAAM,kMAAA,CAAA,OAAI;gBACV,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,aAAa;gBACb,SAAS;YACX;SACD,GACD,EAAE;QACN;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO,eAAe;YACtB,UAAU;QACZ;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,OAAO,gBAAgB,aAAa,IAAI;YACxC,UAAU;QACZ;QAEA,0DAA0D;WACvD,kBAAkB,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAK,CAAC;gBAC5C,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,OAAO;gBACP,UAAU;YACZ,CAAC;QAED,2CAA2C;WACvC,gBAAgB,eAAe,GAC/B;YACE;gBACE,MAAM,wNAAA,CAAA,gBAAa;gBACnB,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,aAAa;gBACb,SAAS;YACX;SACD,GACD,EAAE;WACF,gBAAgB,YAAY,GAC5B;YACE;gBACE,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,aAAa;gBACb,SAAS;YACX;SACD,GACD,EAAE;WACF,gBAAgB,aAAa,GAC7B;YACE;gBACE,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;gBACP,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,aAAa;gBACb,SAAS;YACX;SACD,GACD,EAAE;KACP;IAED,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;QACT;QACA;YAAE,IAAI;YAAW,MAAM;YAAmB,OAAO;QAAyB;QAC1E;YACE,IAAI;YACJ,MAAM;YACN,OAAO;QACT;QACA;YAAE,IAAI;YAAU,MAAM;YAAgB,OAAO;QAAyB;KACvE;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,WAAW,GAAG,CAAC,CAAC,UAAU;gBACzB,MAAM,eAAe,UAAU,MAAM,CACnC,CAAC,OAAS,KAAK,QAAQ,KAAK,SAAS,EAAE;gBAGzC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO,gBAAgB;wBAAK,UAAU;oBAAI;oBACxD,WAAW,CAAC,6EAA6E,EAAE,SAAS,KAAK,EAAE;;sCAE3G,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,SAAS,IAAI;;;;;;;;;;;sCAIlB,8OAAC,0HAAA,CAAA,QAAK;sCACJ,cAAA,8OAAC,0HAAA,CAAA,YAAS;0CACP,aAAa,GAAG,CAAC,CAAC,KAAK;oCACtB,MAAM,gBAAgB,IAAI,IAAI;oCAC9B,qBACE,8OAAC,0HAAA,CAAA,WAAQ;wCAEP,WAAU;;0DAEV,8OAAC,0HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAc,WAAU;;;;;;sEACzB,8OAAC;4DAAK,WAAU;sEAAW,IAAI,KAAK;;;;;;;;;;;;;;;;;0DAGxC,8OAAC,0HAAA,CAAA,YAAS;gDAAC,WAAU;0DACnB,cAAA,8OAAC;oDAAI,WAAU;8DACZ,AACC,IAKA,eAAe,iBACf,8OAAC,2HAAA,CAAA,SAAM;wDACL,SACE,AAAC,IAAgC,OAAO;wDAE1C,MAAK;wDACL,WAAW,CAAC,0FAA0F,EACpG,AAAC,IACE,WAAW,KAAK,UACf,6CACA,AAAC,IACE,WAAW,KAAK,UACnB,iDACA,AAAC,IACE,WAAW,KAAK,aACnB,+CACA,AAAC,IACE,WAAW,KAAK,aACnB,6CACA,AAAC,IACE,WAAW,KAAK,cACnB,6CACA,4CACJ;;4DAEA,IACC,WAAW,KAAK,yBACjB,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAEjB,IACC,WAAW,KAAK,yBACjB,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAEhB,IACC,WAAW,KAAK,4BACjB,8OAAC,2IAAA,CAAA,UAAY;gEAAC,WAAU;;;;;;4DAExB,IACC,WAAW,KAAK,4BACjB,8OAAC,2IAAA,CAAA,UAAY;gEAAC,WAAU;;;;;;4DAExB,IACC,WAAW,KAAK,6BACjB,8OAAC,4IAAA,CAAA,UAAa;gEAAC,WAAU;;;;;;4DAE1B,IAAI,KAAK;;;;;;+DAEV,AAAC,IACA,QAAQ,iBACX,8OAAC,2HAAA,CAAA,SAAM;wDACL,SAAS;4DACP,MAAM,aAAa;4DACnB,IAAI,WAAW,GAAG,EAAE;gEAClB,OAAO,IAAI,CAAC,WAAW,GAAG,EAAE;4DAC9B;wDACF;wDACA,MAAK;wDACL,WAAU;;0EAEV,8OAAC,8MAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DAAiB;;;;;;+DAGvC,IAAI,WAAW,iBACjB,8OAAC;wDACC,WAAW,CAAC,oBAAoB,EAAE,IAAI,WAAW,EAAE;kEAElD,IAAI,KAAK;;;;;+DAEV,IAAI,KAAK,CAAC,QAAQ,CAAC,aACrB,IAAI,KAAK,KAAK,yBACd,8OAAC,0HAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;kEAET,IAAI,KAAK;;;;;+DAEV,IAAI,KAAK,CAAC,QAAQ,CAAC,aACrB,IAAI,KAAK,KAAK,0BACd,8OAAC,0HAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,WAAU;kEAET,IAAI,KAAK;;;;;+DAEV,IAAI,KAAK,CAAC,QAAQ,CAAC,gCACrB,8OAAC,0HAAA,CAAA,QAAK;wDACJ,SACE,IAAI,KAAK,KAAK,QAAQ,YAAY;kEAGnC,IAAI,KAAK;;;;;6EAGZ,8OAAC;wDAAK,WAAU;kEACb,IAAI,KAAK;;;;;;;;;;;;;;;;;uCA/Gb,GAAG,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;gCAsHpC;;;;;;;;;;;;mBAxIC,SAAS,EAAE;;;;;YA6ItB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 7840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/BusinessDetails/EnhancedMetricsCards.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { Heart, Users, Star, Calendar } from \"lucide-react\";\r\nimport { formatIndianNumberShort } from \"@/lib/utils\";\r\n\r\ninterface EnhancedMetricsCardsProps {\r\n  totalLikes: number;\r\n  totalSubscriptions: number;\r\n  averageRating: number;\r\n  createdAt?: Date | string;\r\n  // Interactive button props\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  isLoadingInteraction: boolean;\r\n  onSubscribe: () => void;\r\n  onUnsubscribe: () => void;\r\n  onLike: () => void;\r\n  onUnlike: () => void;\r\n  onReviewClick: () => void;\r\n  isOwnBusiness: boolean;\r\n}\r\n\r\nconst cardVariants = {\r\n  hidden: { opacity: 0, y: 20, scale: 0.95 },\r\n  visible: (i: number) => ({\r\n    opacity: 1,\r\n    y: 0,\r\n    scale: 1,\r\n    transition: {\r\n      delay: i * 0.1,\r\n      duration: 0.6,\r\n      ease: \"easeOut\"\r\n    }\r\n  })\r\n};\r\n\r\nexport default function EnhancedMetricsCards({\r\n  totalLikes,\r\n  totalSubscriptions,\r\n  averageRating,\r\n  createdAt,\r\n  isSubscribed,\r\n  hasLiked,\r\n  isLoadingInteraction,\r\n  onSubscribe,\r\n  onUnsubscribe,\r\n  onLike,\r\n  onUnlike,\r\n  onReviewClick,\r\n  isOwnBusiness,\r\n}: EnhancedMetricsCardsProps) {\r\n  // Calculate platform membership duration\r\n  const membershipDays = createdAt ?\r\n    Math.floor((new Date().getTime() - new Date(createdAt).getTime()) / (1000 * 60 * 60 * 24)) : 0;\r\n\r\n  const getMembershipLabel = (days: number): string => {\r\n    if (days === 0) return \"New Member\";\r\n    if (days < 30) return `${days} days on platform`;\r\n    if (days < 365) return `${Math.floor(days / 30)} months on platform`;\r\n    return `${Math.floor(days / 365)} years on platform`;\r\n  };\r\n\r\n  const metrics = [\r\n    {\r\n      icon: Heart,\r\n      value: formatIndianNumberShort(totalLikes),\r\n      label: \"Likes\",\r\n      color: \"text-red-500\",\r\n      bgGradient: \"bg-white dark:bg-black\",\r\n      glowColor: \"shadow-red-500/40\",\r\n      borderColor: \"border-red-200/50 dark:border-red-700/50\",\r\n      innerGlow: \"bg-red-500/5 dark:bg-red-500/10\",\r\n      circleColor: \"bg-red-500/30\",\r\n      circleGlow: \"shadow-red-500/60\",\r\n      action: hasLiked ? onUnlike : onLike,\r\n      actionLabel: hasLiked ? \"Unlike\" : \"Like\",\r\n      isActive: hasLiked,\r\n      disabled: isOwnBusiness\r\n    },\r\n    {\r\n      icon: Users,\r\n      value: formatIndianNumberShort(totalSubscriptions),\r\n      label: \"Followers\",\r\n      color: \"text-blue-500\",\r\n      bgGradient: \"bg-white dark:bg-black\",\r\n      glowColor: \"shadow-blue-500/40\",\r\n      borderColor: \"border-blue-200/50 dark:border-blue-700/50\",\r\n      innerGlow: \"bg-blue-500/5 dark:bg-blue-500/10\",\r\n      circleColor: \"bg-blue-500/30\",\r\n      circleGlow: \"shadow-blue-500/60\",\r\n      action: isSubscribed ? onUnsubscribe : onSubscribe,\r\n      actionLabel: isSubscribed ? \"Unsubscribe\" : \"Subscribe\",\r\n      isActive: isSubscribed,\r\n      disabled: isOwnBusiness\r\n    },\r\n    {\r\n      icon: Star,\r\n      value: averageRating.toFixed(1),\r\n      label: \"Rating\",\r\n      color: \"text-yellow-500\",\r\n      bgGradient: \"bg-white dark:bg-black\",\r\n      glowColor: \"shadow-yellow-500/40\",\r\n      borderColor: \"border-yellow-200/50 dark:border-yellow-700/50\",\r\n      innerGlow: \"bg-yellow-500/5 dark:bg-yellow-500/10\",\r\n      circleColor: \"bg-yellow-500/30\",\r\n      circleGlow: \"shadow-yellow-500/60\",\r\n      action: onReviewClick,\r\n      actionLabel: \"Review\",\r\n      isActive: false,\r\n      disabled: false\r\n    },\r\n    {\r\n      icon: Calendar,\r\n      value: getMembershipLabel(membershipDays),\r\n      label: \"Platform Member\",\r\n      color: \"text-purple-500\",\r\n      bgGradient: \"bg-white dark:bg-black\",\r\n      glowColor: \"shadow-purple-500/40\",\r\n      borderColor: \"border-purple-200/50 dark:border-purple-700/50\",\r\n      innerGlow: \"bg-purple-500/5 dark:bg-purple-500/10\",\r\n      circleColor: \"bg-purple-500/30\",\r\n      circleGlow: \"shadow-purple-500/60\",\r\n      action: undefined,\r\n      actionLabel: \"\",\r\n      isActive: false,\r\n      disabled: true\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"p-6 border-b border-neutral-200/30 dark:border-neutral-700/30\">\r\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n        {metrics.map((metric, index) => {\r\n          const IconComponent = metric.icon;\r\n          return (\r\n            <motion.div\r\n              key={metric.label}\r\n              custom={index}\r\n              variants={cardVariants}\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              whileHover={{\r\n                scale: 1.05,\r\n                transition: { duration: 0.2 }\r\n              }}\r\n              className={`\r\n                relative overflow-hidden rounded-xl p-4\r\n                ${metric.bgGradient}\r\n                border ${metric.borderColor}\r\n                ${metric.glowColor} shadow-lg\r\n                hover:shadow-xl hover:${metric.glowColor}\r\n                transition-all duration-300\r\n                group cursor-default\r\n              `}\r\n            >\r\n              {/* Strong inner glow effect that fills the card */}\r\n              <div className={`absolute inset-0 ${metric.innerGlow} opacity-80 group-hover:opacity-100 transition-opacity duration-300`} />\r\n\r\n              {/* Content */}\r\n              <div className=\"relative z-10\">\r\n                {/* Icon */}\r\n                <div className=\"flex items-center justify-center mb-3\">\r\n                  <div className={`p-2 rounded-lg bg-white/50 dark:bg-neutral-800/50 ${metric.glowColor} group-hover:shadow-md transition-all duration-300`}>\r\n                    <IconComponent className={`w-5 h-5 ${metric.color}`} />\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Value */}\r\n                <div className=\"text-center\">\r\n                  <div className=\"text-xl font-bold text-neutral-900 dark:text-neutral-100 mb-1 group-hover:scale-110 transition-transform duration-300\">\r\n                    {metric.value}\r\n                  </div>\r\n                  <div className=\"text-xs font-medium text-neutral-600 dark:text-neutral-400\">\r\n                    {metric.label}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Interactive Button */}\r\n                {metric.action && metric.actionLabel && (\r\n                  <div className=\"mt-3 relative z-20\">\r\n                    <motion.button\r\n                      onClick={metric.action}\r\n                      disabled={metric.disabled || isLoadingInteraction}\r\n                      whileHover={{ scale: metric.disabled ? 1 : 1.05 }}\r\n                      whileTap={{ scale: metric.disabled ? 1 : 0.95 }}\r\n                      className={`\r\n                        w-full px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200\r\n                        ${metric.disabled\r\n                          ? 'bg-neutral-100 dark:bg-neutral-800 text-neutral-400 dark:text-neutral-600 cursor-not-allowed'\r\n                          : metric.isActive\r\n                            ? `${metric.color} bg-current/10 border border-current/20 hover:bg-current/20 cursor-pointer`\r\n                            : `${metric.color} bg-current/5 border border-current/10 hover:bg-current/15 hover:border-current/30 cursor-pointer`\r\n                        }\r\n                        ${isLoadingInteraction ? 'opacity-50 cursor-wait' : ''}\r\n                      `}\r\n                    >\r\n                      {isLoadingInteraction ? '...' : metric.actionLabel}\r\n                    </motion.button>\r\n                  </div>\r\n                )}\r\n              </div>\r\n\r\n              {/* Strong decorative colored glow elements */}\r\n              <div className={`absolute -top-2 -right-2 w-10 h-10 ${metric.circleColor} rounded-full ${metric.circleGlow} opacity-80 group-hover:opacity-100 transition-all duration-300 blur-md`} />\r\n              <div className={`absolute -bottom-2 -left-2 w-8 h-8 ${metric.circleColor} rounded-full ${metric.circleGlow} opacity-60 group-hover:opacity-90 transition-all duration-300 blur-md`} />\r\n            </motion.div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAuBA,MAAM,eAAe;IACnB,QAAQ;QAAE,SAAS;QAAG,GAAG;QAAI,OAAO;IAAK;IACzC,SAAS,CAAC,IAAc,CAAC;YACvB,SAAS;YACT,GAAG;YACH,OAAO;YACP,YAAY;gBACV,OAAO,IAAI;gBACX,UAAU;gBACV,MAAM;YACR;QACF,CAAC;AACH;AAEe,SAAS,qBAAqB,EAC3C,UAAU,EACV,kBAAkB,EAClB,aAAa,EACb,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACpB,WAAW,EACX,aAAa,EACb,MAAM,EACN,QAAQ,EACR,aAAa,EACb,aAAa,EACa;IAC1B,yCAAyC;IACzC,MAAM,iBAAiB,YACrB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,OAAO,KAAK,IAAI,KAAK,WAAW,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE,KAAK;IAE/F,MAAM,qBAAqB,CAAC;QAC1B,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,OAAO,IAAI,OAAO,GAAG,KAAK,iBAAiB,CAAC;QAChD,IAAI,OAAO,KAAK,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,IAAI,mBAAmB,CAAC;QACpE,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,KAAK,kBAAkB,CAAC;IACtD;IAEA,MAAM,UAAU;QACd;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;YAC/B,OAAO;YACP,OAAO;YACP,YAAY;YACZ,WAAW;YACX,aAAa;YACb,WAAW;YACX,aAAa;YACb,YAAY;YACZ,QAAQ,WAAW,WAAW;YAC9B,aAAa,WAAW,WAAW;YACnC,UAAU;YACV,UAAU;QACZ;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO,CAAA,GAAA,4GAAA,CAAA,0BAAuB,AAAD,EAAE;YAC/B,OAAO;YACP,OAAO;YACP,YAAY;YACZ,WAAW;YACX,aAAa;YACb,WAAW;YACX,aAAa;YACb,YAAY;YACZ,QAAQ,eAAe,gBAAgB;YACvC,aAAa,eAAe,gBAAgB;YAC5C,UAAU;YACV,UAAU;QACZ;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO,cAAc,OAAO,CAAC;YAC7B,OAAO;YACP,OAAO;YACP,YAAY;YACZ,WAAW;YACX,aAAa;YACb,WAAW;YACX,aAAa;YACb,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;YACV,UAAU;QACZ;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO,mBAAmB;YAC1B,OAAO;YACP,OAAO;YACP,YAAY;YACZ,WAAW;YACX,aAAa;YACb,WAAW;YACX,aAAa;YACb,YAAY;YACZ,QAAQ;YACR,aAAa;YACb,UAAU;YACV,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ;gBACpB,MAAM,gBAAgB,OAAO,IAAI;gBACjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,QAAQ;oBACR,UAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,YAAY;wBACV,OAAO;wBACP,YAAY;4BAAE,UAAU;wBAAI;oBAC9B;oBACA,WAAW,CAAC;;gBAEV,EAAE,OAAO,UAAU,CAAC;uBACb,EAAE,OAAO,WAAW,CAAC;gBAC5B,EAAE,OAAO,SAAS,CAAC;sCACG,EAAE,OAAO,SAAS,CAAC;;;cAG3C,CAAC;;sCAGD,8OAAC;4BAAI,WAAW,CAAC,iBAAiB,EAAE,OAAO,SAAS,CAAC,mEAAmE,CAAC;;;;;;sCAGzH,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,kDAAkD,EAAE,OAAO,SAAS,CAAC,kDAAkD,CAAC;kDACvI,cAAA,8OAAC;4CAAc,WAAW,CAAC,QAAQ,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;;;;;;;8CAKvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,OAAO,KAAK;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACZ,OAAO,KAAK;;;;;;;;;;;;gCAKhB,OAAO,MAAM,IAAI,OAAO,WAAW,kBAClC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS,OAAO,MAAM;wCACtB,UAAU,OAAO,QAAQ,IAAI;wCAC7B,YAAY;4CAAE,OAAO,OAAO,QAAQ,GAAG,IAAI;wCAAK;wCAChD,UAAU;4CAAE,OAAO,OAAO,QAAQ,GAAG,IAAI;wCAAK;wCAC9C,WAAW,CAAC;;wBAEV,EAAE,OAAO,QAAQ,GACb,iGACA,OAAO,QAAQ,GACb,GAAG,OAAO,KAAK,CAAC,0EAA0E,CAAC,GAC3F,GAAG,OAAO,KAAK,CAAC,iGAAiG,CAAC,CACvH;wBACD,EAAE,uBAAuB,2BAA2B,GAAG;sBACzD,CAAC;kDAEA,uBAAuB,QAAQ,OAAO,WAAW;;;;;;;;;;;;;;;;;sCAO1D,8OAAC;4BAAI,WAAW,CAAC,mCAAmC,EAAE,OAAO,WAAW,CAAC,cAAc,EAAE,OAAO,UAAU,CAAC,uEAAuE,CAAC;;;;;;sCACnL,8OAAC;4BAAI,WAAW,CAAC,mCAAmC,EAAE,OAAO,WAAW,CAAC,cAAc,EAAE,OAAO,UAAU,CAAC,sEAAsE,CAAC;;;;;;;mBApE7K,OAAO,KAAK;;;;;YAuEvB;;;;;;;;;;;AAMR", "debugId": null}}, {"offset": {"line": 8101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/EnhancedBusinessDetails.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { motion, useInView } from \"framer-motion\";\r\nimport { useRef } from \"react\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { cardVariants } from \"./animations\";\r\nimport ProfessionalBusinessTable from \"./BusinessDetails/ProfessionalBusinessTable\";\r\nimport EnhancedMetricsCards from \"./BusinessDetails/EnhancedMetricsCards\";\r\n\r\n// Define the BusinessProfile type\r\ntype BusinessProfile = BusinessCardData & {\r\n  total_reviews?: number;\r\n  subscription_status?: string;\r\n  has_active_subscription?: boolean;\r\n  trial_end_date?: Date | string | null;\r\n  total_visits?: number;\r\n  today_visits?: number;\r\n  yesterday_visits?: number;\r\n  visits_7_days?: number;\r\n  visits_30_days?: number;\r\n  created_at?: Date | string;\r\n  updated_at?: Date | string;\r\n};\r\n\r\ninterface EnhancedBusinessDetailsProps {\r\n  businessProfile: BusinessProfile;\r\n  isAuthenticated: boolean;\r\n  totalLikes: number;\r\n  totalSubscriptions: number;\r\n  averageRating: number;\r\n  // Interactive button props\r\n  isSubscribed: boolean;\r\n  hasLiked: boolean;\r\n  isLoadingInteraction: boolean;\r\n  onSubscribe: () => void;\r\n  onUnsubscribe: () => void;\r\n  onLike: () => void;\r\n  onUnlike: () => void;\r\n  onReviewClick: () => void;\r\n  isOwnBusiness: boolean;\r\n}\r\n\r\nexport default function EnhancedBusinessDetails({\r\n  businessProfile,\r\n  isAuthenticated,\r\n  totalLikes,\r\n  totalSubscriptions,\r\n  averageRating,\r\n  isSubscribed,\r\n  hasLiked,\r\n  isLoadingInteraction,\r\n  onSubscribe,\r\n  onUnsubscribe,\r\n  onLike,\r\n  onUnlike,\r\n  onReviewClick,\r\n  isOwnBusiness,\r\n}: EnhancedBusinessDetailsProps) {\r\n  const detailsRef = useRef<HTMLDivElement>(null);\r\n  const isInView = useInView(detailsRef, { once: false, amount: 0.2 });\r\n\r\n  return (\r\n    <motion.div\r\n      ref={detailsRef}\r\n      initial=\"hidden\"\r\n      animate={isInView ? \"visible\" : \"hidden\"}\r\n      variants={cardVariants}\r\n      className=\"bg-white dark:bg-black rounded-2xl overflow-hidden shadow-lg\"\r\n    >\r\n      {/* Enhanced Performance Metrics with Inner Glow */}\r\n      <EnhancedMetricsCards\r\n        totalLikes={totalLikes}\r\n        totalSubscriptions={totalSubscriptions}\r\n        averageRating={averageRating}\r\n        createdAt={businessProfile.created_at}\r\n        isSubscribed={isSubscribed}\r\n        hasLiked={hasLiked}\r\n        isLoadingInteraction={isLoadingInteraction}\r\n        onSubscribe={onSubscribe}\r\n        onUnsubscribe={onUnsubscribe}\r\n        onLike={onLike}\r\n        onUnlike={onUnlike}\r\n        onReviewClick={onReviewClick}\r\n        isOwnBusiness={isOwnBusiness}\r\n      />\r\n\r\n      {/* Comprehensive Professional Business Table */}\r\n      <ProfessionalBusinessTable\r\n        businessProfile={businessProfile}\r\n        isAuthenticated={isAuthenticated}\r\n      />\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAEA;AACA;AACA;AAPA;;;;;;;AA0Ce,SAAS,wBAAwB,EAC9C,eAAe,EACf,eAAe,EACf,UAAU,EACV,kBAAkB,EAClB,aAAa,EACb,YAAY,EACZ,QAAQ,EACR,oBAAoB,EACpB,WAAW,EACX,aAAa,EACb,MAAM,EACN,QAAQ,EACR,aAAa,EACb,aAAa,EACgB;IAC7B,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QAAE,MAAM;QAAO,QAAQ;IAAI;IAElE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,SAAQ;QACR,SAAS,WAAW,YAAY;QAChC,UAAU,+IAAA,CAAA,eAAY;QACtB,WAAU;;0BAGV,8OAAC,6KAAA,CAAA,UAAoB;gBACnB,YAAY;gBACZ,oBAAoB;gBACpB,eAAe;gBACf,WAAW,gBAAgB,UAAU;gBACrC,cAAc;gBACd,UAAU;gBACV,sBAAsB;gBACtB,aAAa;gBACb,eAAe;gBACf,QAAQ;gBACR,UAAU;gBACV,eAAe;gBACf,eAAe;;;;;;0BAIjB,8OAAC,kLAAA,CAAA,UAAyB;gBACxB,iBAAiB;gBACjB,iBAAiB;;;;;;;;;;;;AAIzB", "debugId": null}}, {"offset": {"line": 8171, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/components/ProductListItem.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { NearbyProduct } from \"@/app/(main)/discover/actions/types\";\r\n\r\ninterface ProductListItemProps {\r\n  product: ProductServiceData | NearbyProduct;\r\n  isLink?: boolean;\r\n}\r\n\r\n// Helper to format currency\r\nconst formatCurrency = (amount: number | null | undefined) => {\r\n  if (amount === null || amount === undefined) return null;\r\n  return amount.toLocaleString(\"en-IN\", {\r\n    style: \"currency\",\r\n    currency: \"INR\",\r\n    minimumFractionDigits: 0,\r\n    maximumFractionDigits: 2,\r\n  });\r\n};\r\n\r\n// Animation variants\r\nconst cardContainerVariants = {\r\n  hidden: { opacity: 0, y: 10 },\r\n  show: { opacity: 1, y: 0 },\r\n};\r\n\r\nconst discountBadgeVariants = {\r\n  initial: { scale: 0.9, opacity: 0 },\r\n  animate: {\r\n    scale: 1,\r\n    opacity: 1,\r\n    transition: {\r\n      duration: 0.5,\r\n      type: \"spring\",\r\n      stiffness: 400,\r\n      damping: 10,\r\n    },\r\n  },\r\n  hover: {\r\n    scale: 1.05,\r\n    rotate: -2,\r\n    transition: { type: \"spring\", stiffness: 500 },\r\n  },\r\n};\r\n\r\nexport default function ProductListItem({\r\n  product,\r\n  isLink = true,\r\n}: ProductListItemProps) {\r\n  // If isLink is true, we'll wrap the content in a link\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  const formattedBasePrice = formatCurrency(product.base_price);\r\n  const formattedDiscountedPrice = formatCurrency(product.discounted_price); // Format discounted price\r\n\r\n  // Determine final price and if there's a discount shown\r\n  let finalPrice = formattedBasePrice;\r\n  let priceToShowStrikethrough: string | null = null;\r\n  let discountPercentage = 0;\r\n\r\n  const hasDiscountedPrice =\r\n    typeof product.discounted_price === \"number\" &&\r\n    product.discounted_price > 0;\r\n  const hasBasePrice =\r\n    typeof product.base_price === \"number\" && product.base_price > 0;\r\n\r\n  if (\r\n    hasDiscountedPrice &&\r\n    hasBasePrice &&\r\n    product.discounted_price! < product.base_price!\r\n  ) {\r\n    // Scenario 1: Discounted price is valid and less than base price\r\n    finalPrice = formattedDiscountedPrice;\r\n    priceToShowStrikethrough = formattedBasePrice; // Strike through base price\r\n    discountPercentage = Math.round(\r\n      ((product.base_price! - product.discounted_price!) /\r\n        product.base_price!) *\r\n        100\r\n    );\r\n  } else {\r\n    // Scenario 2: No discount applicable, show base price\r\n    finalPrice = formattedBasePrice;\r\n    priceToShowStrikethrough = null;\r\n    discountPercentage = 0;\r\n  }\r\n\r\n  // Ensure finalPrice has a fallback if both prices are null/undefined\r\n  if (!finalPrice) {\r\n    finalPrice = \"Price unavailable\";\r\n  }\r\n\r\n  const showDiscountBadge = discountPercentage > 0;\r\n\r\n  // State for lazy loading images\r\n  const [isImageLoaded, setIsImageLoaded] = useState(false);\r\n\r\n  // Check if product is out of stock\r\n  const isOutOfStock = !product.is_available;\r\n\r\n  // Ensure we're not using business_id as a key\r\n  // Use the product's own ID for any keys needed\r\n  const content = (\r\n    <motion.div\r\n      variants={cardContainerVariants}\r\n      initial=\"hidden\"\r\n      animate=\"show\"\r\n      className=\"w-full overflow-hidden\"\r\n    >\r\n      <div className=\"relative h-full border border-neutral-200 dark:border-neutral-800 p-1 sm:p-1.5 md:p-2 overflow-hidden rounded-lg\">\r\n        <div className=\"relative w-full overflow-hidden rounded-lg\">\r\n          {/* Image container */}\r\n          <div className=\"relative w-full overflow-hidden rounded-t-xl\">\r\n            {/* Get the featured image from the images array if available, otherwise fall back to image_url */}\r\n            {/* Determine the image URL to use */}\r\n            {(() => {\r\n              // Get the image URL to display\r\n              let imageUrl = product.image_url;\r\n\r\n              // If product has images array and it's not empty, use the featured image\r\n              if (product.images && Array.isArray(product.images) && product.images.length > 0) {\r\n                const featuredIndex = typeof product.featured_image_index === 'number'\r\n                  ? Math.min(product.featured_image_index, product.images.length - 1)\r\n                  : 0;\r\n                imageUrl = product.images[featuredIndex];\r\n              }\r\n\r\n              if (imageUrl && !imageError) {\r\n                return (\r\n                  <div className=\"overflow-hidden\">\r\n                    {!isImageLoaded && (\r\n                      <Skeleton className=\"absolute inset-0 rounded-t-xl\" />\r\n                    )}\r\n                    <motion.div className=\"w-full\">\r\n                      <Image\r\n                        src={imageUrl}\r\n                        alt={product.name ?? \"Product image\"}\r\n                        width={500}\r\n                        height={750}\r\n                        className={`w-full aspect-square object-cover ${\r\n                          isOutOfStock\r\n                            ? \"filter grayscale opacity-70 transition-all duration-500\"\r\n                            : \"\"\r\n                        } ${\r\n                          isImageLoaded ? \"opacity-100\" : \"opacity-0\"\r\n                        } max-w-full`}\r\n                        loading=\"lazy\"\r\n                        onError={() => setImageError(true)}\r\n                        onLoad={() => setIsImageLoaded(true)}\r\n                        quality={80}\r\n                        blurDataURL=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=\"\r\n                        placeholder=\"blur\"\r\n                        style={{ objectFit: \"cover\" }}\r\n                      />\r\n                    </motion.div>\r\n                  </div>\r\n                );\r\n              } else {\r\n                return (\r\n                  <div className=\"w-full aspect-square flex items-center justify-center bg-neutral-100 dark:bg-neutral-800 rounded-t-xl\">\r\n                    <svg\r\n                      className=\"w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 text-neutral-400 dark:text-neutral-500\"\r\n                      fill=\"none\"\r\n                      stroke=\"currentColor\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                    >\r\n                      <path\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                        strokeWidth={1}\r\n                        d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\r\n                      />\r\n                    </svg>\r\n                  </div>\r\n                );\r\n              }\r\n            })()}\r\n\r\n            {/* Out of Stock Overlay */}\r\n            {isOutOfStock && (\r\n              <div className=\"absolute inset-0 flex items-center justify-center bg-gradient-to-t from-black/70 to-black/40\">\r\n                <div className=\"px-6 py-2 backdrop-blur-sm rounded-full bg-background/80 text-foreground\">\r\n                  <span className=\"font-medium tracking-wide uppercase text-xs sm:text-sm\">\r\n                    Out of Stock\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            )}\r\n\r\n            {/* Discount Badge Overlay */}\r\n            {showDiscountBadge && (\r\n              <AnimatePresence>\r\n                <motion.div\r\n                  key={`discount-badge-${product.id}`}\r\n                  variants={discountBadgeVariants}\r\n                  initial=\"initial\"\r\n                  animate=\"animate\"\r\n                  whileHover=\"hover\"\r\n                  className={cn(\r\n                    \"absolute top-2 sm:top-3 md:top-4 right-2 sm:right-3 md:right-4 px-1.5 py-0.5 rounded-md font-bold text-[8px] sm:text-xs shadow-lg\",\r\n                    \"bg-destructive\",\r\n                    \"text-destructive-foreground border border-destructive-foreground/20\",\r\n                    \"transform-gpu\"\r\n                  )}\r\n                >\r\n                  <div className=\"flex flex-col items-center justify-center\">\r\n                    <span className=\"text-[7px] sm:text-[9px] md:text-[10px] font-medium\">\r\n                      SAVE\r\n                    </span>\r\n                    <span className=\"text-[9px] sm:text-xs md:text-sm leading-none\">\r\n                      {discountPercentage}%\r\n                    </span>\r\n                  </div>\r\n                </motion.div>\r\n              </AnimatePresence>\r\n            )}\r\n          </div>\r\n\r\n          {/* Content Section */}\r\n          <div className=\"px-1.5 sm:px-2 pt-1 sm:pt-1.5 pb-1.5 sm:pb-2 space-y-0.5 sm:space-y-1\">\r\n            {/* Title */}\r\n            <p className=\"font-semibold text-xs sm:text-sm md:text-sm lg:text-base line-clamp-1 truncate text-neutral-800 dark:text-neutral-100 max-w-full overflow-hidden\">\r\n              {product.name ?? \"Unnamed Product\"}\r\n            </p>\r\n\r\n            {/* Description (optional) */}\r\n            {product.description && (\r\n              <p className=\"line-clamp-1 text-[10px] sm:text-xs text-neutral-500 dark:text-neutral-400 max-w-full overflow-hidden truncate\">\r\n                {product.description}\r\n              </p>\r\n            )}\r\n\r\n            {/* Price and Badge Container */}\r\n            <div className=\"flex items-center gap-1 sm:gap-2 pt-0.5 sm:pt-1\">\r\n              {/* Price Group */}\r\n              <div className=\"flex justify-between items-baseline space-x-1 sm:space-x-2 text-xs sm:text-sm md:text-sm lg:text-base flex-grow min-w-0 overflow-hidden w-full\">\r\n                {finalPrice && (\r\n                  <p className=\"truncate font-bold text-neutral-800 dark:text-neutral-100 max-w-full\">\r\n                    {finalPrice}\r\n                  </p>\r\n                )}\r\n                {priceToShowStrikethrough && (\r\n                  <p className=\"line-through opacity-60 truncate text-[10px] sm:text-xs text-neutral-500\">\r\n                    {priceToShowStrikethrough}\r\n                  </p>\r\n                )}\r\n              </div>\r\n\r\n              {/* Product Type Badge removed as per instructions */}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </motion.div>\r\n  );\r\n\r\n  // If isLink is true, wrap the content in a link to the product detail page\r\n  if (isLink && \"business_slug\" in product && product.business_slug) {\r\n    return (\r\n      <Link\r\n        href={`/${product.business_slug}/product/${product.slug || product.id}`}\r\n        className=\"block h-full\"\r\n      >\r\n        {content}\r\n      </Link>\r\n    );\r\n  }\r\n\r\n  // Otherwise, just return the content\r\n  return content;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AACA;AARA;;;;;;;;AAgBA,4BAA4B;AAC5B,MAAM,iBAAiB,CAAC;IACtB,IAAI,WAAW,QAAQ,WAAW,WAAW,OAAO;IACpD,OAAO,OAAO,cAAc,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB;AACF;AAEA,qBAAqB;AACrB,MAAM,wBAAwB;IAC5B,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAE;AAC3B;AAEA,MAAM,wBAAwB;IAC5B,SAAS;QAAE,OAAO;QAAK,SAAS;IAAE;IAClC,SAAS;QACP,OAAO;QACP,SAAS;QACT,YAAY;YACV,UAAU;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ,CAAC;QACT,YAAY;YAAE,MAAM;YAAU,WAAW;QAAI;IAC/C;AACF;AAEe,SAAS,gBAAgB,EACtC,OAAO,EACP,SAAS,IAAI,EACQ;IACrB,sDAAsD;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,eAAe,QAAQ,UAAU;IAC5D,MAAM,2BAA2B,eAAe,QAAQ,gBAAgB,GAAG,0BAA0B;IAErG,wDAAwD;IACxD,IAAI,aAAa;IACjB,IAAI,2BAA0C;IAC9C,IAAI,qBAAqB;IAEzB,MAAM,qBACJ,OAAO,QAAQ,gBAAgB,KAAK,YACpC,QAAQ,gBAAgB,GAAG;IAC7B,MAAM,eACJ,OAAO,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;IAEjE,IACE,sBACA,gBACA,QAAQ,gBAAgB,GAAI,QAAQ,UAAU,EAC9C;QACA,iEAAiE;QACjE,aAAa;QACb,2BAA2B,oBAAoB,4BAA4B;QAC3E,qBAAqB,KAAK,KAAK,CAC7B,AAAC,CAAC,QAAQ,UAAU,GAAI,QAAQ,gBAAgB,AAAC,IAC/C,QAAQ,UAAU,GAClB;IAEN,OAAO;QACL,sDAAsD;QACtD,aAAa;QACb,2BAA2B;QAC3B,qBAAqB;IACvB;IAEA,qEAAqE;IACrE,IAAI,CAAC,YAAY;QACf,aAAa;IACf;IAEA,MAAM,oBAAoB,qBAAqB;IAE/C,gCAAgC;IAChC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mCAAmC;IACnC,MAAM,eAAe,CAAC,QAAQ,YAAY;IAE1C,8CAA8C;IAC9C,+CAA+C;IAC/C,MAAM,wBACJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;4BAGZ,CAAC;gCACA,+BAA+B;gCAC/B,IAAI,WAAW,QAAQ,SAAS;gCAEhC,yEAAyE;gCACzE,IAAI,QAAQ,MAAM,IAAI,MAAM,OAAO,CAAC,QAAQ,MAAM,KAAK,QAAQ,MAAM,CAAC,MAAM,GAAG,GAAG;oCAChF,MAAM,gBAAgB,OAAO,QAAQ,oBAAoB,KAAK,WAC1D,KAAK,GAAG,CAAC,QAAQ,oBAAoB,EAAE,QAAQ,MAAM,CAAC,MAAM,GAAG,KAC/D;oCACJ,WAAW,QAAQ,MAAM,CAAC,cAAc;gCAC1C;gCAEA,IAAI,YAAY,CAAC,YAAY;oCAC3B,qBACE,8OAAC;wCAAI,WAAU;;4CACZ,CAAC,+BACA,8OAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DAEtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAAC,WAAU;0DACpB,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAK;oDACL,KAAK,QAAQ,IAAI,IAAI;oDACrB,OAAO;oDACP,QAAQ;oDACR,WAAW,CAAC,kCAAkC,EAC5C,eACI,4DACA,GACL,CAAC,EACA,gBAAgB,gBAAgB,YACjC,WAAW,CAAC;oDACb,SAAQ;oDACR,SAAS,IAAM,cAAc;oDAC7B,QAAQ,IAAM,iBAAiB;oDAC/B,SAAS;oDACT,aAAY;oDACZ,aAAY;oDACZ,OAAO;wDAAE,WAAW;oDAAQ;;;;;;;;;;;;;;;;;gCAKtC,OAAO;oCACL,qBACE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,OAAM;sDAEN,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;gCAKZ;4BACF,CAAC;4BAGA,8BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAyD;;;;;;;;;;;;;;;;4BAQ9E,mCACC,8OAAC,yLAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,YAAW;oCACX,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qIACA,kBACA,uEACA;8CAGF,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAsD;;;;;;0DAGtE,8OAAC;gDAAK,WAAU;;oDACb;oDAAmB;;;;;;;;;;;;;mCAjBnB,CAAC,eAAe,EAAE,QAAQ,EAAE,EAAE;;;;;;;;;;;;;;;;kCA0B3C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAE,WAAU;0CACV,QAAQ,IAAI,IAAI;;;;;;4BAIlB,QAAQ,WAAW,kBAClB,8OAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAKxB,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,4BACC,8OAAC;4CAAE,WAAU;sDACV;;;;;;wCAGJ,0CACC,8OAAC;4CAAE,WAAU;sDACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAanB,2EAA2E;IAC3E,IAAI,UAAU,mBAAmB,WAAW,QAAQ,aAAa,EAAE;QACjE,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;YACvE,WAAU;sBAET;;;;;;IAGP;IAEA,qCAAqC;IACrC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 8531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/ProductGridSkeleton.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\n\r\nexport default function ProductGridSkeleton() {\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Product Grid Skeleton */}\r\n      <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4\">\r\n        {Array.from({ length: 8 }).map((_, index) => (\r\n          <div\r\n            key={index}\r\n            className=\"border border-neutral-200/80 dark:border-neutral-800/80 rounded-xl overflow-hidden bg-transparent transition-all duration-300\"\r\n          >\r\n            <Skeleton className=\"h-48 w-full\" />\r\n            <div className=\"p-4 space-y-3\">\r\n              <Skeleton className=\"h-5 w-3/4\" />\r\n              <Skeleton className=\"h-4 w-1/2\" />\r\n              <div className=\"flex justify-between items-center pt-2\">\r\n                <Skeleton className=\"h-6 w-20\" />\r\n                <Skeleton className=\"h-8 w-8 rounded-full\" />\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,6HAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC,6HAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;mBATnB;;;;;;;;;;;;;;;AAiBjB", "debugId": null}}, {"offset": {"line": 8625, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { createClient } from \"@/utils/supabase/server\";\n\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\"; // Reuse type\n// Removed unused import - noStore\nimport { COLUMNS, TABLES } from \"@/lib/supabase/constants\";\n\n\n// Define sort options available on the public page\nexport type PublicProductSortBy =\n  | \"created_asc\"\n  | \"created_desc\"\n  | \"updated_asc\"\n  | \"updated_desc\"\n  | \"price_asc\"\n  | \"price_desc\"\n  | \"name_asc\"\n  | \"name_desc\";\n\n// Fetch subsequent pages of products for a specific business with search and filter\nexport async function fetchMoreProducts(\n  businessId: string,\n  page: number = 1,\n  sortBy: PublicProductSortBy = \"created_desc\",\n  pageSize: number = 20,\n  searchTerm?: string | null,\n  productType?: string | null\n): Promise<{\n  data?: ProductServiceData[];\n  error?: string;\n  totalCount?: number;\n}> {\n  if (!businessId) {\n    return { error: \"Business ID is required.\" };\n  }\n  if (page < 1) {\n    return { error: \"Page number must be 1 or greater.\" };\n  }\n  if (pageSize < 1) {\n    return { error: \"Page size must be 1 or greater.\" };\n  }\n\n  const supabase = await createClient();\n  const offset = (page - 1) * pageSize;\n\n  // Build count query first to get total count\n  let countQuery = supabase\n    .from(TABLES.PRODUCTS_SERVICES)\n    .select(COLUMNS.ID, { count: \"exact\" })\n    .eq(COLUMNS.BUSINESS_ID, businessId)\n    .eq(\"is_available\", true);\n\n  // Apply search filter if provided\n  if (searchTerm && searchTerm.trim().length > 0) {\n    countQuery = countQuery.ilike(COLUMNS.NAME, `%${searchTerm.trim()}%`);\n  }\n\n  // Apply product type filter if provided\n  if (productType && productType !== \"all\") {\n    countQuery = countQuery.eq(COLUMNS.PRODUCT_TYPE, productType);\n  }\n\n  // Get total count\n  const { count, error: countError } = await countQuery;\n\n  if (countError) {\n    return { error: \"Failed to count products\" };\n  }\n\n  // Build the main query for fetching products\n  let query = supabase\n    .from(TABLES.PRODUCTS_SERVICES)\n    .select(`\n            id,\n            business_id,\n            name,\n            description,\n            base_price,\n            discounted_price,\n            product_type,\n            is_available,\n            image_url,\n            created_at,\n            updated_at,\n            slug\n        `)\n    .eq(COLUMNS.BUSINESS_ID, businessId)\n    .eq(\"is_available\", true);\n\n  // Apply search filter if provided\n  if (searchTerm && searchTerm.trim().length > 0) {\n    query = query.ilike(\"name\", `%${searchTerm.trim()}%`);\n  }\n\n  // Apply product type filter if provided\n  if (productType && productType !== \"all\") {\n    query = query.eq(\"product_type\", productType);\n  }\n\n  // Apply Sorting\n  switch (sortBy) {\n    case \"created_asc\":\n      query = query.order(\"created_at\", { ascending: true });\n      break;\n    case \"updated_desc\":\n      query = query.order(\"updated_at\", { ascending: false });\n      break;\n    case \"price_asc\":\n      // Sort by discounted_price first, then base_price for price_asc\n      query = query\n        .order(\"discounted_price\", { ascending: true, nullsFirst: false })\n        .order(\"base_price\", { ascending: true, nullsFirst: false });\n      break;\n    case \"price_desc\":\n      // Sort by discounted_price first, then base_price for price_desc\n      query = query\n        .order(\"discounted_price\", { ascending: false, nullsFirst: false })\n        .order(\"base_price\", { ascending: false, nullsFirst: false });\n      break;\n    case \"name_asc\":\n      query = query.order(\"name\", { ascending: true });\n      break;\n    case \"name_desc\":\n      query = query.order(\"name\", { ascending: false });\n      break;\n    case \"created_desc\":\n    default:\n      query = query.order(\"created_at\", { ascending: false });\n      break;\n  }\n\n  query = query.range(offset, offset + pageSize - 1);\n\n  const { data, error } = await query;\n\n  if (error) {\n    return { error: \"Failed to fetch products.\" };\n  }\n\n  return {\n    data: (data as unknown as ProductServiceData[]) ?? [],\n    totalCount: count || 0,\n  };\n}\n\n// Removed visit tracking logic as metrics tables have been deleted"], "names": [], "mappings": ";;;;;;IAqBsB,oBAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 8638, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/ProductsTab.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\n// Note: We're using direct import for framer-motion here, as dynamic import was causing issues\r\nimport { Loader2, Search, Filter, SortAsc, X } from \"lucide-react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport Link from \"next/link\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\n\r\nimport ProductListItem from \"@/app/components/ProductListItem\";\r\n\r\nimport ProductGridSkeleton from \"./ProductGridSkeleton\";\r\nimport { ProductServiceData } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport { ProductSortBy } from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\nimport { fetchMoreProducts, PublicProductSortBy } from \"@/app/[cardSlug]/actions\";\r\nimport { toast } from \"sonner\";\r\n// Import product sort options from discovery page\r\nimport { ProductSortOption } from \"@/app/(main)/discover/context/types\";\r\n\r\n// Define the BusinessProfile type\r\ntype BusinessProfile = BusinessCardData & {\r\n  total_reviews?: number;\r\n  subscription_status?: string;\r\n  has_active_subscription?: boolean;\r\n  trial_end_date?: Date | string | null;\r\n};\r\n\r\ninterface ProductsTabProps {\r\n  businessProfile: BusinessProfile;\r\n  initialProducts: ProductServiceData[];\r\n  totalProductCount: number;\r\n  defaultSortPreference: ProductSortBy;\r\n\r\n}\r\n\r\nexport default function ProductsTab({\r\n  businessProfile,\r\n  initialProducts,\r\n  totalProductCount,\r\n  defaultSortPreference,\r\n}: ProductsTabProps) {\r\n  const PRODUCTS_PER_PAGE = 20;\r\n\r\n  // Map ProductSortBy to ProductSortOption\r\n  const mapSortByToProductSort = (sortBy: ProductSortBy): ProductSortOption => {\r\n    switch (sortBy) {\r\n      case \"created_desc\":\r\n        return \"newest\";\r\n      case \"name_asc\":\r\n        return \"name_asc\";\r\n      case \"name_desc\":\r\n        return \"name_desc\";\r\n      case \"price_asc\":\r\n        return \"price_low\";\r\n      case \"price_desc\":\r\n        return \"price_high\";\r\n      default:\r\n        return \"newest\";\r\n    }\r\n  };\r\n\r\n  // Map ProductSortOption to PublicProductSortBy (for public-facing pages)\r\n  const mapProductSortToSortBy = (\r\n    sortOption: ProductSortOption\r\n  ): PublicProductSortBy => {\r\n    switch (sortOption) {\r\n      case \"newest\":\r\n        return \"created_desc\";\r\n      case \"name_asc\":\r\n        return \"name_asc\";\r\n      case \"name_desc\":\r\n        return \"name_desc\";\r\n      case \"price_low\":\r\n        return \"price_asc\";\r\n      case \"price_high\":\r\n        return \"price_desc\";\r\n      default:\r\n        return \"created_desc\";\r\n    }\r\n  };\r\n\r\n  // State variables\r\n  const [products, setProducts] =\r\n    useState<ProductServiceData[]>(initialProducts);\r\n  const [currentPage, setCurrentPage] = useState<number>(1);\r\n  const [currentSortOrder, setCurrentSortOrder] = useState<PublicProductSortBy>(\r\n    defaultSortPreference as PublicProductSortBy\r\n  );\r\n  // Discovery page compatible sort option\r\n  const [currentDiscoverySortOrder, setCurrentDiscoverySortOrder] =\r\n    useState<ProductSortOption>(mapSortByToProductSort(defaultSortPreference));\r\n  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);\r\n  const [hasMore, setHasMore] = useState<boolean>(\r\n    initialProducts.length < totalProductCount\r\n  );\r\n\r\n  // Search and filter state\r\n  const [searchQuery, setSearchQuery] = useState<string>(\"\");\r\n  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState<string>(\"\");\r\n  const [productFilterBy, setProductFilterBy] = useState<string>(\"all\");\r\n  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Ref for infinite scrolling\r\n  const loadMoreRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Handle search input change with debounce\r\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const query = e.target.value;\r\n    setSearchQuery(query);\r\n\r\n    // Clear any existing timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n      searchTimeoutRef.current = null;\r\n    }\r\n\r\n    // Check if the user has manually cleared the search\r\n    const wasManuallyCleared = debouncedSearchQuery.length > 0 && query === \"\";\r\n\r\n    // If the search was manually cleared, trigger search immediately\r\n    if (wasManuallyCleared) {\r\n      setDebouncedSearchQuery(\"\");\r\n      // Add a small visual delay before triggering the search\r\n      setTimeout(() => {\r\n        handleSearch(\"\");\r\n      }, 100);\r\n      return;\r\n    }\r\n\r\n    // Only set a new timeout if the query is empty or at least 3 characters\r\n    if (query === \"\" || query.length >= 3) {\r\n      searchTimeoutRef.current = setTimeout(() => {\r\n        setDebouncedSearchQuery(query);\r\n        handleSearch(query);\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  // Handle clear search when clicking the X button\r\n  const handleClearSearch = () => {\r\n    // Clear any existing timeout\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n      searchTimeoutRef.current = null;\r\n    }\r\n\r\n    setSearchQuery(\"\");\r\n    setDebouncedSearchQuery(\"\");\r\n\r\n    // Add a small visual delay before triggering the search\r\n    setTimeout(() => {\r\n      handleSearch(\"\");\r\n    }, 100);\r\n  };\r\n\r\n  // Handle filter change\r\n  const handleFilterChange = (value: string) => {\r\n    setProductFilterBy(value);\r\n    handleSearch(debouncedSearchQuery, value);\r\n  };\r\n\r\n  // Handle search with current filters\r\n  const handleSearch = useCallback(\r\n    async (searchTerm: string, filterValue: string = productFilterBy) => {\r\n      setCurrentPage(1); // Reset page number\r\n      setProducts([]); // Clear existing products\r\n      setHasMore(true); // Assume there are more until fetch confirms otherwise\r\n      setIsLoadingMore(true); // Show loading state\r\n\r\n      const result = await fetchMoreProducts(\r\n        businessProfile.id!,\r\n        1, // Fetch first page\r\n        currentSortOrder,\r\n        PRODUCTS_PER_PAGE,\r\n        searchTerm,\r\n        filterValue === \"all\" ? null : filterValue\r\n      );\r\n\r\n      if (result.data) {\r\n        setProducts(result.data);\r\n        setHasMore(result.data.length < (result.totalCount || 0));\r\n      } else if (result.error) {\r\n        toast.error(`Failed to load products: ${result.error}`);\r\n        setHasMore(false); // Stop loading if error\r\n      } else {\r\n        setHasMore(false); // Stop loading if no data\r\n      }\r\n      setIsLoadingMore(false);\r\n    },\r\n    [\r\n      businessProfile.id,\r\n      currentSortOrder,\r\n      productFilterBy,\r\n      setCurrentPage,\r\n      setHasMore,\r\n      setIsLoadingMore,\r\n      setProducts,\r\n    ]\r\n  );\r\n\r\n  // Handle sort change\r\n  const handleSortChange = useCallback(\r\n    async (newSortOption: ProductSortOption) => {\r\n      const newSortOrder = mapProductSortToSortBy(newSortOption);\r\n      if (newSortOrder === currentSortOrder) return;\r\n\r\n      setCurrentDiscoverySortOrder(newSortOption);\r\n      setCurrentSortOrder(newSortOrder);\r\n      setCurrentPage(1); // Reset page number\r\n      setProducts([]); // Clear existing products\r\n      setHasMore(true); // Assume there are more until fetch confirms otherwise\r\n      setIsLoadingMore(true); // Show loading state\r\n\r\n      const result = await fetchMoreProducts(\r\n        businessProfile.id!,\r\n        1, // Fetch first page\r\n        newSortOrder,\r\n        PRODUCTS_PER_PAGE,\r\n        debouncedSearchQuery || null,\r\n        productFilterBy === \"all\" ? null : productFilterBy\r\n      );\r\n\r\n      if (result.data) {\r\n        setProducts(result.data);\r\n        setHasMore(result.data.length < (result.totalCount || 0));\r\n      } else if (result.error) {\r\n        toast.error(`Failed to load products: ${result.error}`);\r\n        setHasMore(false); // Stop loading if error\r\n      } else {\r\n        setHasMore(false); // Stop loading if no data\r\n      }\r\n      setIsLoadingMore(false);\r\n    },\r\n    [\r\n      businessProfile.id,\r\n      currentSortOrder,\r\n      debouncedSearchQuery,\r\n      productFilterBy,\r\n      setCurrentPage,\r\n      setCurrentSortOrder,\r\n      setHasMore,\r\n      setIsLoadingMore,\r\n      setProducts,\r\n    ]\r\n  );\r\n\r\n  // Load more products for infinite scrolling\r\n  const loadMoreProducts = useCallback(async () => {\r\n    if (isLoadingMore || !hasMore) return;\r\n\r\n    setIsLoadingMore(true);\r\n    const nextPage = currentPage + 1;\r\n    const result = await fetchMoreProducts(\r\n      businessProfile.id!, // Assert non-null as profile must exist\r\n      nextPage,\r\n      currentSortOrder,\r\n      PRODUCTS_PER_PAGE,\r\n      debouncedSearchQuery || null,\r\n      productFilterBy === \"all\" ? null : productFilterBy\r\n    );\r\n\r\n    if (result.data && result.data.length > 0) {\r\n      setProducts((prev) => [...prev, ...result.data!]);\r\n      setCurrentPage(nextPage);\r\n      // Recalculate hasMore based on the updated products length\r\n      setHasMore(\r\n        products.length + result.data.length <\r\n          (result.totalCount || totalProductCount)\r\n      );\r\n    } else if (result.error) {\r\n      toast.error(`Failed to load more products: ${result.error}`);\r\n      setHasMore(false); // Stop loading if error\r\n    } else {\r\n      setHasMore(false); // Stop loading if no more data\r\n    }\r\n\r\n    setIsLoadingMore(false);\r\n  }, [\r\n    isLoadingMore,\r\n    hasMore,\r\n    currentPage,\r\n    businessProfile.id,\r\n    currentSortOrder,\r\n    debouncedSearchQuery,\r\n    productFilterBy,\r\n    products.length,\r\n    totalProductCount,\r\n    setIsLoadingMore,\r\n    setProducts,\r\n    setCurrentPage,\r\n    setHasMore,\r\n  ]);\r\n\r\n  // Set up intersection observer for infinite scrolling\r\n  useEffect(() => {\r\n    const observer = new IntersectionObserver(\r\n      (entries) => {\r\n        if (entries[0].isIntersecting && hasMore && !isLoadingMore) {\r\n          loadMoreProducts();\r\n        }\r\n      },\r\n      { threshold: 0.1 }\r\n    );\r\n\r\n    const currentRef = loadMoreRef.current;\r\n    if (currentRef) {\r\n      observer.observe(currentRef);\r\n    }\r\n\r\n    return () => {\r\n      if (currentRef) {\r\n        observer.unobserve(currentRef);\r\n      }\r\n    };\r\n  }, [hasMore, isLoadingMore, loadMoreProducts]);\r\n\r\n  return (\r\n    <div>\r\n      {/* Search and Filter Controls */}\r\n      <motion.div\r\n        className=\"relative overflow-hidden bg-transparent p-5 rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80 transition-all duration-300 mb-6\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.4 }}\r\n      >\r\n        {/* Decorative elements */}\r\n        <div className=\"absolute inset-0 overflow-hidden pointer-events-none opacity-50\">\r\n          <div className=\"absolute -top-24 -right-24 w-48 h-48 rounded-full bg-[var(--brand-gold-rgb)]/5 blur-2xl dark:bg-[var(--brand-gold-rgb)]/10\"></div>\r\n          <div className=\"absolute -bottom-24 -left-24 w-48 h-48 rounded-full bg-[var(--brand-gold-rgb)]/5 blur-2xl dark:bg-[var(--brand-gold-rgb)]/10\"></div>\r\n        </div>\r\n\r\n        <div className=\"relative z-10 flex flex-col sm:flex-row gap-3 sm:gap-5 items-start sm:items-center justify-between\">\r\n          {/* Search Input */}\r\n          <div className=\"relative w-full sm:w-auto sm:flex-1 max-w-full sm:max-w-md\">\r\n            <form\r\n              onSubmit={(e) => {\r\n                e.preventDefault();\r\n                // Only trigger search if query is empty or at least 3 characters\r\n                if (searchQuery === \"\" || searchQuery.length >= 3) {\r\n                  // Clear any existing timeout\r\n                  if (searchTimeoutRef.current) {\r\n                    clearTimeout(searchTimeoutRef.current);\r\n                    searchTimeoutRef.current = null;\r\n                  }\r\n                  setDebouncedSearchQuery(searchQuery);\r\n                  handleSearch(searchQuery);\r\n                }\r\n              }}\r\n              className=\"w-full\"\r\n            >\r\n              <div className=\"relative group w-full\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--brand-gold)] transition-all duration-200\" />\r\n                <Input\r\n                  placeholder=\"Search products...\"\r\n                  value={searchQuery}\r\n                  onChange={handleSearchChange}\r\n                  className=\"pl-10 h-9 sm:h-10 w-full bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\"\r\n                />\r\n                {searchQuery && (\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleClearSearch}\r\n                    className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-300 transition-colors duration-200\"\r\n                  >\r\n                    <X className=\"h-4 w-4\" />\r\n                  </button>\r\n                )}\r\n                {/* Hidden submit button for form submission */}\r\n                <button type=\"submit\" className=\"hidden\">\r\n                  Search\r\n                </button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n\r\n          <div className=\"flex flex-row gap-2 sm:gap-3 w-full sm:w-auto sm:flex-shrink-0\">\r\n            {/* Filter Dropdown */}\r\n            <Select\r\n              value={productFilterBy}\r\n              onValueChange={handleFilterChange}\r\n              disabled={isLoadingMore}\r\n            >\r\n              <SelectTrigger className=\"w-full sm:min-w-[140px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\">\r\n                <div className=\"flex items-center\">\r\n                  <Filter className=\"mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]\" />\r\n                  <SelectValue\r\n                    placeholder=\"Filter\"\r\n                    className=\"text-xs sm:text-sm truncate\"\r\n                  />\r\n                </div>\r\n              </SelectTrigger>\r\n              <SelectContent className=\"min-w-[180px]\">\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5\">\r\n                    Product Type\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"all\" className=\"relative pl-8\">\r\n                    All Products\r\n                  </SelectItem>\r\n                  <SelectItem value=\"physical\" className=\"relative pl-8\">\r\n                    Physical Items\r\n                  </SelectItem>\r\n                  <SelectItem value=\"service\" className=\"relative pl-8\">\r\n                    Services\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n              </SelectContent>\r\n            </Select>\r\n\r\n            {/* Sort Dropdown - Using Discovery Page Sort Options */}\r\n            <Select\r\n              value={currentDiscoverySortOrder}\r\n              onValueChange={(value) =>\r\n                handleSortChange(value as ProductSortOption)\r\n              }\r\n              disabled={isLoadingMore}\r\n            >\r\n              <SelectTrigger className=\"w-full sm:min-w-[160px] h-9 sm:h-10 bg-transparent border-neutral-200/80 dark:border-neutral-700/80 rounded-lg focus-visible:ring-[var(--brand-gold)]/50 focus-visible:border-[var(--brand-gold)]/50 transition-all duration-200 text-sm\">\r\n                <div className=\"flex items-center\">\r\n                  <SortAsc className=\"mr-1 sm:mr-2 h-3.5 sm:h-4 w-3.5 sm:w-4 text-[var(--brand-gold)]\" />\r\n                  <SelectValue\r\n                    placeholder=\"Sort by\"\r\n                    className=\"text-xs sm:text-sm truncate\"\r\n                  />\r\n                </div>\r\n              </SelectTrigger>\r\n              <SelectContent className=\"min-w-[200px]\">\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5\">\r\n                    Date\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"newest\" className=\"relative pl-8\">\r\n                    Newest First\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1\">\r\n                    Price\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"price_low\" className=\"relative pl-8\">\r\n                    Price: Low to High\r\n                  </SelectItem>\r\n                  <SelectItem value=\"price_high\" className=\"relative pl-8\">\r\n                    Price: High to Low\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n                <SelectGroup>\r\n                  <SelectLabel className=\"text-xs font-semibold text-neutral-500 dark:text-neutral-400 px-2 py-1.5 mt-1\">\r\n                    Name\r\n                  </SelectLabel>\r\n                  <SelectItem value=\"name_asc\" className=\"relative pl-8\">\r\n                    Name: A to Z\r\n                  </SelectItem>\r\n                  <SelectItem value=\"name_desc\" className=\"relative pl-8\">\r\n                    Name: Z to A\r\n                  </SelectItem>\r\n                </SelectGroup>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Results Count */}\r\n      <motion.div\r\n        className=\"flex items-center justify-between px-2 mb-4\"\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: 1 }}\r\n        transition={{ duration: 0.3, delay: 0.2 }}\r\n      >\r\n        <div className=\"text-xs sm:text-sm text-neutral-500 dark:text-neutral-400 truncate\">\r\n          {debouncedSearchQuery && (\r\n            <span>\r\n              Showing results for{\" \"}\r\n              <span className=\"font-medium text-neutral-700 dark:text-neutral-300 max-w-[150px] sm:max-w-none inline-block truncate\">\r\n                &quot;{debouncedSearchQuery}&quot;\r\n              </span>\r\n            </span>\r\n          )}\r\n          {productFilterBy !== \"all\" && (\r\n            <span>\r\n              {debouncedSearchQuery ? \" • \" : \"\"}\r\n              <span className=\"font-medium text-neutral-700 dark:text-neutral-300\">\r\n                {productFilterBy === \"physical\" ? \"Physical Items\" : \"Services\"}\r\n              </span>\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        {debouncedSearchQuery && (\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            onClick={handleClearSearch}\r\n            className=\"text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 h-8 px-2 py-1\"\r\n          >\r\n            <X className=\"h-3.5 w-3.5 mr-1\" />\r\n            Clear Search\r\n          </Button>\r\n        )}\r\n      </motion.div>\r\n\r\n      {/* Product Grid and Loading/End Indicators */}\r\n      {isLoadingMore && products.length === 0 ? (\r\n        <ProductGridSkeleton />\r\n      ) : products.length > 0 ? (\r\n        <motion.div\r\n          className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 md:gap-4 overflow-hidden\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          {products.map((product, index) => (\r\n            <motion.div\r\n              key={product.id}\r\n              initial={{ opacity: 0, y: 20 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.3, delay: index * 0.05 }}\r\n            >\r\n              <Link href={`/${businessProfile.business_slug}/product/${product.slug || product.id}`} className=\"block h-full\">\r\n                <ProductListItem product={product} isLink={false} />\r\n              </Link>\r\n            </motion.div>\r\n          ))}\r\n        </motion.div>\r\n      ) : (\r\n        <motion.div\r\n          className=\"text-center py-16 px-4 bg-transparent rounded-2xl border border-neutral-200/80 dark:border-neutral-800/80\"\r\n          initial={{ opacity: 0, y: 20 }}\r\n          animate={{ opacity: 1, y: 0 }}\r\n          transition={{ duration: 0.4 }}\r\n        >\r\n          <div className=\"max-w-md mx-auto\">\r\n            <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-2\">\r\n              No Products Found\r\n            </h3>\r\n            <p className=\"text-neutral-500 dark:text-neutral-400 mb-4\">\r\n              We couldn&apos;t find any products\r\n              {debouncedSearchQuery\r\n                ? ` with \"${debouncedSearchQuery}\" in the name`\r\n                : \"\"}\r\n              {productFilterBy !== \"all\" &&\r\n                ` in the ${\r\n                  productFilterBy === \"physical\" ? \"Physical Items\" : \"Services\"\r\n                } category`}\r\n              . Try adjusting your search criteria or browse all products.\r\n            </p>\r\n            {(debouncedSearchQuery || productFilterBy !== \"all\") && (\r\n              <Button\r\n                variant=\"outline\"\r\n                className=\"mt-2 border-[var(--brand-gold)]/30 text-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/10\"\r\n                onClick={handleClearSearch}\r\n              >\r\n                <X className=\"h-4 w-4 mr-2\" />\r\n                Clear Filters\r\n              </Button>\r\n            )}\r\n          </div>\r\n        </motion.div>\r\n      )}\r\n\r\n      {/* Infinite scroll trigger and end message */}\r\n      {hasMore && (\r\n        <div ref={loadMoreRef} className=\"text-center py-10\">\r\n          {isLoadingMore && products.length > 0 && (\r\n            <Loader2 className=\"h-8 w-8 animate-spin mx-auto text-neutral-500\" />\r\n          )}\r\n        </div>\r\n      )}\r\n      {!hasMore && products.length > 0 && (\r\n        <div className=\"text-center py-10 text-sm text-neutral-500\">\r\n          You&apos;ve reached the end.\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA,+FAA+F;AAC/F;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAUA;AAEA;AAIA;AACA;AA1BA;;;;;;;;;;;;;AA8Ce,SAAS,YAAY,EAClC,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,qBAAqB,EACJ;IACjB,MAAM,oBAAoB;IAE1B,yCAAyC;IACzC,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,yEAAyE;IACzE,MAAM,yBAAyB,CAC7B;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAC3B,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrD;IAEF,wCAAwC;IACxC,MAAM,CAAC,2BAA2B,6BAA6B,GAC7D,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,uBAAuB;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACnC,gBAAgB,MAAM,GAAG;IAG3B,0BAA0B;IAC1B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAEvD,6BAA6B;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,2CAA2C;IAC3C,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QAEf,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,oDAAoD;QACpD,MAAM,qBAAqB,qBAAqB,MAAM,GAAG,KAAK,UAAU;QAExE,iEAAiE;QACjE,IAAI,oBAAoB;YACtB,wBAAwB;YACxB,wDAAwD;YACxD,WAAW;gBACT,aAAa;YACf,GAAG;YACH;QACF;QAEA,wEAAwE;QACxE,IAAI,UAAU,MAAM,MAAM,MAAM,IAAI,GAAG;YACrC,iBAAiB,OAAO,GAAG,WAAW;gBACpC,wBAAwB;gBACxB,aAAa;YACf,GAAG;QACL;IACF;IAEA,iDAAiD;IACjD,MAAM,oBAAoB;QACxB,6BAA6B;QAC7B,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;YACrC,iBAAiB,OAAO,GAAG;QAC7B;QAEA,eAAe;QACf,wBAAwB;QAExB,wDAAwD;QACxD,WAAW;YACT,aAAa;QACf,GAAG;IACL;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,mBAAmB;QACnB,aAAa,sBAAsB;IACrC;IAEA,qCAAqC;IACrC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC7B,OAAO,YAAoB,cAAsB,eAAe;QAC9D,eAAe,IAAI,oBAAoB;QACvC,YAAY,EAAE,GAAG,0BAA0B;QAC3C,WAAW,OAAO,uDAAuD;QACzE,iBAAiB,OAAO,qBAAqB;QAE7C,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EACnC,gBAAgB,EAAE,EAClB,GACA,kBACA,mBACA,YACA,gBAAgB,QAAQ,OAAO;QAGjC,IAAI,OAAO,IAAI,EAAE;YACf,YAAY,OAAO,IAAI;YACvB,WAAW,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,UAAU,IAAI,CAAC;QACzD,OAAO,IAAI,OAAO,KAAK,EAAE;YACvB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,KAAK,EAAE;YACtD,WAAW,QAAQ,wBAAwB;QAC7C,OAAO;YACL,WAAW,QAAQ,0BAA0B;QAC/C;QACA,iBAAiB;IACnB,GACA;QACE,gBAAgB,EAAE;QAClB;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,qBAAqB;IACrB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACjC,OAAO;QACL,MAAM,eAAe,uBAAuB;QAC5C,IAAI,iBAAiB,kBAAkB;QAEvC,6BAA6B;QAC7B,oBAAoB;QACpB,eAAe,IAAI,oBAAoB;QACvC,YAAY,EAAE,GAAG,0BAA0B;QAC3C,WAAW,OAAO,uDAAuD;QACzE,iBAAiB,OAAO,qBAAqB;QAE7C,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EACnC,gBAAgB,EAAE,EAClB,GACA,cACA,mBACA,wBAAwB,MACxB,oBAAoB,QAAQ,OAAO;QAGrC,IAAI,OAAO,IAAI,EAAE;YACf,YAAY,OAAO,IAAI;YACvB,WAAW,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,OAAO,UAAU,IAAI,CAAC;QACzD,OAAO,IAAI,OAAO,KAAK,EAAE;YACvB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,KAAK,EAAE;YACtD,WAAW,QAAQ,wBAAwB;QAC7C,OAAO;YACL,WAAW,QAAQ,0BAA0B;QAC/C;QACA,iBAAiB;IACnB,GACA;QACE,gBAAgB,EAAE;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,4CAA4C;IAC5C,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,IAAI,iBAAiB,CAAC,SAAS;QAE/B,iBAAiB;QACjB,MAAM,WAAW,cAAc;QAC/B,MAAM,SAAS,MAAM,CAAA,GAAA,2JAAA,CAAA,oBAAiB,AAAD,EACnC,gBAAgB,EAAE,EAClB,UACA,kBACA,mBACA,wBAAwB,MACxB,oBAAoB,QAAQ,OAAO;QAGrC,IAAI,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,GAAG,GAAG;YACzC,YAAY,CAAC,OAAS;uBAAI;uBAAS,OAAO,IAAI;iBAAE;YAChD,eAAe;YACf,2DAA2D;YAC3D,WACE,SAAS,MAAM,GAAG,OAAO,IAAI,CAAC,MAAM,GAClC,CAAC,OAAO,UAAU,IAAI,iBAAiB;QAE7C,OAAO,IAAI,OAAO,KAAK,EAAE;YACvB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,8BAA8B,EAAE,OAAO,KAAK,EAAE;YAC3D,WAAW,QAAQ,wBAAwB;QAC7C,OAAO;YACL,WAAW,QAAQ,+BAA+B;QACpD;QAEA,iBAAiB;IACnB,GAAG;QACD;QACA;QACA;QACA,gBAAgB,EAAE;QAClB;QACA;QACA;QACA,SAAS,MAAM;QACf;QACA;QACA;QACA;QACA;KACD;IAED,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,IAAI,qBACnB,CAAC;YACC,IAAI,OAAO,CAAC,EAAE,CAAC,cAAc,IAAI,WAAW,CAAC,eAAe;gBAC1D;YACF;QACF,GACA;YAAE,WAAW;QAAI;QAGnB,MAAM,aAAa,YAAY,OAAO;QACtC,IAAI,YAAY;YACd,SAAS,OAAO,CAAC;QACnB;QAEA,OAAO;YACL,IAAI,YAAY;gBACd,SAAS,SAAS,CAAC;YACrB;QACF;IACF,GAAG;QAAC;QAAS;QAAe;KAAiB;IAE7C,qBACE,8OAAC;;0BAEC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,UAAU,CAAC;wCACT,EAAE,cAAc;wCAChB,iEAAiE;wCACjE,IAAI,gBAAgB,MAAM,YAAY,MAAM,IAAI,GAAG;4CACjD,6BAA6B;4CAC7B,IAAI,iBAAiB,OAAO,EAAE;gDAC5B,aAAa,iBAAiB,OAAO;gDACrC,iBAAiB,OAAO,GAAG;4CAC7B;4CACA,wBAAwB;4CACxB,aAAa;wCACf;oCACF;oCACA,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC,0HAAA,CAAA,QAAK;gDACJ,aAAY;gDACZ,OAAO;gDACP,UAAU;gDACV,WAAU;;;;;;4CAEX,6BACC,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;0DAIjB,8OAAC;gDAAO,MAAK;gDAAS,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;0CAO/C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe;wCACf,UAAU;;0DAEV,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC,2HAAA,CAAA,cAAW;4DACV,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;0DAIhB,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,2HAAA,CAAA,cAAW;;sEACV,8OAAC,2HAAA,CAAA,cAAW;4DAAC,WAAU;sEAA2E;;;;;;sEAGlG,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;4DAAM,WAAU;sEAAgB;;;;;;sEAGlD,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;4DAAW,WAAU;sEAAgB;;;;;;sEAGvD,8OAAC,2HAAA,CAAA,aAAU;4DAAC,OAAM;4DAAU,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;kDAQ5D,8OAAC,2HAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe,CAAC,QACd,iBAAiB;wCAEnB,UAAU;;0DAEV,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8NAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC,2HAAA,CAAA,cAAW;4DACV,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;0DAIhB,8OAAC,2HAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,2HAAA,CAAA,cAAW;;0EACV,8OAAC,2HAAA,CAAA,cAAW;gEAAC,WAAU;0EAA2E;;;;;;0EAGlG,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAS,WAAU;0EAAgB;;;;;;;;;;;;kEAIvD,8OAAC,2HAAA,CAAA,cAAW;;0EACV,8OAAC,2HAAA,CAAA,cAAW;gEAAC,WAAU;0EAAgF;;;;;;0EAGvG,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAY,WAAU;0EAAgB;;;;;;0EAGxD,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAa,WAAU;0EAAgB;;;;;;;;;;;;kEAI3D,8OAAC,2HAAA,CAAA,cAAW;;0EACV,8OAAC,2HAAA,CAAA,cAAW;gEAAC,WAAU;0EAAgF;;;;;;0EAGvG,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAW,WAAU;0EAAgB;;;;;;0EAGvD,8OAAC,2HAAA,CAAA,aAAU;gEAAC,OAAM;gEAAY,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWpE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,8OAAC;wBAAI,WAAU;;4BACZ,sCACC,8OAAC;;oCAAK;oCACgB;kDACpB,8OAAC;wCAAK,WAAU;;4CAAuG;4CAC9G;4CAAqB;;;;;;;;;;;;;4BAIjC,oBAAoB,uBACnB,8OAAC;;oCACE,uBAAuB,QAAQ;kDAChC,8OAAC;wCAAK,WAAU;kDACb,oBAAoB,aAAa,mBAAmB;;;;;;;;;;;;;;;;;;oBAM5D,sCACC,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAqB;;;;;;;;;;;;;YAOvC,iBAAiB,SAAS,MAAM,KAAK,kBACpC,8OAAC,yJAAA,CAAA,UAAmB;;;;uBAClB,SAAS,MAAM,GAAG,kBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAE3B,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAK;kCAEjD,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAI,IAAI,QAAQ,EAAE,EAAE;4BAAE,WAAU;sCAC/F,cAAA,8OAAC,qIAAA,CAAA,UAAe;gCAAC,SAAS;gCAAS,QAAQ;;;;;;;;;;;uBANxC,QAAQ,EAAE;;;;;;;;;qCAYrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoE;;;;;;sCAGlF,8OAAC;4BAAE,WAAU;;gCAA8C;gCAExD,uBACG,CAAC,OAAO,EAAE,qBAAqB,aAAa,CAAC,GAC7C;gCACH,oBAAoB,SACnB,CAAC,QAAQ,EACP,oBAAoB,aAAa,mBAAmB,WACrD,SAAS,CAAC;gCAAC;;;;;;;wBAGf,CAAC,wBAAwB,oBAAoB,KAAK,mBACjD,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;;8CAET,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;YASvC,yBACC,8OAAC;gBAAI,KAAK;gBAAa,WAAU;0BAC9B,iBAAiB,SAAS,MAAM,GAAG,mBAClC,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;YAIxB,CAAC,WAAW,SAAS,MAAM,GAAG,mBAC7B,8OAAC;gBAAI,WAAU;0BAA6C;;;;;;;;;;;;AAMpE", "debugId": null}}, {"offset": {"line": 9506, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/GalleryTab.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef } from \"react\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { X, Maximize, ExternalLink } from \"lucide-react\";\r\nimport { GalleryImage } from \"@/lib/actions/gallery\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useInView } from \"react-intersection-observer\";\r\n\r\ninterface GalleryTabProps {\r\n  images: GalleryImage[];\r\n  totalCount: number;\r\n  businessName: string;\r\n  businessSlug: string;\r\n}\r\n\r\nexport default function GalleryTab({\r\n  images,\r\n  totalCount,\r\n  businessName,\r\n  businessSlug,\r\n}: GalleryTabProps) {\r\n  const [lightboxImage, setLightboxImage] = useState<string | null>(null);\r\n  const carouselRef = useRef<HTMLDivElement>(null);\r\n  const { ref: inViewRef } = useInView({\r\n    threshold: 0.1,\r\n    triggerOnce: true,\r\n  });\r\n\r\n\r\n\r\n\r\n\r\n  const openLightbox = (imageUrl: string) => {\r\n    setLightboxImage(imageUrl);\r\n  };\r\n\r\n  const closeLightbox = () => {\r\n    setLightboxImage(null);\r\n  };\r\n\r\n\r\n\r\n\r\n\r\n  if (images.length === 0) {\r\n    return (\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"text-center py-12\"\r\n      >\r\n        <div className=\"text-neutral-500 dark:text-neutral-400\">\r\n          <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-neutral-100 dark:bg-neutral-800 flex items-center justify-center\">\r\n            <ExternalLink className=\"w-8 h-8\" />\r\n          </div>\r\n          <h3 className=\"text-lg font-medium mb-2\">No Gallery Images</h3>\r\n          <p className=\"text-sm\">This business hasn&apos;t added any gallery images yet.</p>\r\n        </div>\r\n      </motion.div>\r\n    );\r\n  }\r\n\r\n  // Images are already limited to 4 from the API\r\n  const displayImages = images;\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full\"\r\n      initial={{ opacity: 0, y: 20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      ref={inViewRef}\r\n    >\r\n      {/* Header */}\r\n      <div className=\"mb-6\">\r\n        <h2 className=\"text-2xl font-bold text-neutral-800 dark:text-neutral-100\">\r\n          Gallery\r\n        </h2>\r\n        <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mt-1\">\r\n          {totalCount} {totalCount === 1 ? 'photo' : 'photos'}\r\n        </p>\r\n      </div>\r\n\r\n      {/* Gallery Grid */}\r\n      <motion.div\r\n        className=\"grid grid-cols-2 sm:grid-cols-4 gap-3 mb-6\"\r\n        ref={carouselRef}\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n      >\r\n        {displayImages.map((image, index) => (\r\n          <motion.div\r\n            key={image.id}\r\n            className=\"aspect-square relative overflow-hidden rounded-xl cursor-pointer group\"\r\n            onClick={() => openLightbox(image.url)}\r\n            whileHover={{ scale: 1.03 }}\r\n            transition={{ duration: 0.3 }}\r\n            initial={{ opacity: 0, scale: 0.9 }}\r\n            animate={{ opacity: 1, scale: 1 }}\r\n            style={{ transitionDelay: `${index * 0.1}s` }}\r\n          >\r\n            <Image\r\n              src={image.url}\r\n              alt={`${businessName} gallery image`}\r\n              fill\r\n              className=\"object-cover transition-transform duration-300 group-hover:scale-110\"\r\n              sizes=\"(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw\"\r\n            />\r\n\r\n            {/* Overlay - similar to gallery page */}\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/0 to-black/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\r\n              <div className=\"absolute bottom-2 right-2 bg-black/50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\r\n                <Maximize className=\"w-4 h-4\" />\r\n              </div>\r\n            </div>\r\n          </motion.div>\r\n        ))}\r\n      </motion.div>\r\n\r\n      {/* Go to Gallery Button */}\r\n      {displayImages.length > 0 && (\r\n        <div className=\"flex justify-center\">\r\n          <Link href={`/${businessSlug}/gallery`} passHref>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"gap-2 hover:bg-primary hover:text-primary-foreground transition-colors\"\r\n            >\r\n              <span>Go to Gallery</span>\r\n              <ExternalLink className=\"h-4 w-4\" />\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n      )}\r\n\r\n      {/* Lightbox - similar to gallery page */}\r\n      <AnimatePresence>\r\n        {lightboxImage && (\r\n          <motion.div\r\n            className=\"fixed inset-0 z-50 bg-black/90 flex items-center justify-center\"\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            exit={{ opacity: 0 }}\r\n            onClick={closeLightbox}\r\n          >\r\n            {/* Close button */}\r\n            <button\r\n              className=\"absolute top-4 right-4 z-10 p-2 bg-black/50 rounded-full text-white hover:bg-black/70 transition-colors\"\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                closeLightbox();\r\n              }}\r\n            >\r\n              <X className=\"w-6 h-6\" />\r\n            </button>\r\n\r\n\r\n\r\n            {/* Image */}\r\n            <div\r\n              className=\"relative w-full h-full max-w-4xl max-h-[80vh] mx-auto p-4 flex items-center justify-center\"\r\n              onClick={(e) => e.stopPropagation()}\r\n            >\r\n              <Image\r\n                src={lightboxImage}\r\n                alt={`${businessName} gallery image`}\r\n                fill\r\n                className=\"object-contain\"\r\n                sizes=\"100vw\"\r\n                priority\r\n              />\r\n            </div>\r\n\r\n\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAEA;AACA;AATA;;;;;;;;;AAkBe,SAAS,WAAW,EACjC,MAAM,EACN,UAAU,EACV,YAAY,EACZ,YAAY,EACI;IAChB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EAAE,KAAK,SAAS,EAAE,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QACnC,WAAW;QACX,aAAa;IACf;IAMA,MAAM,eAAe,CAAC;QACpB,iBAAiB;IACnB;IAEA,MAAM,gBAAgB;QACpB,iBAAiB;IACnB;IAMA,IAAI,OAAO,MAAM,KAAK,GAAG;QACvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;kCAE1B,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;IAI/B;IAEA,+CAA+C;IAC/C,MAAM,gBAAgB;IAEtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,KAAK;;0BAGL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAG1E,8OAAC;wBAAE,WAAU;;4BACV;4BAAW;4BAAE,eAAe,IAAI,UAAU;;;;;;;;;;;;;0BAK/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,KAAK;gBACL,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE3B,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,SAAS,IAAM,aAAa,MAAM,GAAG;wBACrC,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,OAAO;4BAAE,iBAAiB,GAAG,QAAQ,IAAI,CAAC,CAAC;wBAAC;;0CAE5C,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK,MAAM,GAAG;gCACd,KAAK,GAAG,aAAa,cAAc,CAAC;gCACpC,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;0CAIR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;;;;;;uBApBnB,MAAM,EAAE;;;;;;;;;;YA4BlB,cAAc,MAAM,GAAG,mBACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAM,CAAC,CAAC,EAAE,aAAa,QAAQ,CAAC;oBAAE,QAAQ;8BAC9C,cAAA,8OAAC,2HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,8OAAC;0CAAK;;;;;;0CACN,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC,yLAAA,CAAA,kBAAe;0BACb,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS;;sCAGT,8OAAC;4BACC,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB;4BACF;sCAEA,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAMf,8OAAC;4BACC,WAAU;4BACV,SAAS,CAAC,IAAM,EAAE,eAAe;sCAEjC,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACJ,KAAK;gCACL,KAAK,GAAG,aAAa,cAAc,CAAC;gCACpC,IAAI;gCACJ,WAAU;gCACV,OAAM;gCACN,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB", "debugId": null}}, {"offset": {"line": 9841, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/EnhancedPublicCardPageWrapper.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\nimport { toast } from \"sonner\";\r\nimport { useSearchParams, useRouter } from \"next/navigation\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport {\r\n  ProductServiceData,\r\n  ProductSortBy,\r\n} from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\n\r\nimport { GalleryImage } from \"@/lib/actions/gallery\";\r\nimport { AdData } from \"@/types/ad\";\r\nimport ReviewsTab from \"../../components/ReviewsTab\";\r\nimport EnhancedTabsToggle from \"./EnhancedTabsToggle\";\r\n\r\nimport {\r\n  getInteractionStatus,\r\n  likeBusiness,\r\n  subscribeToBusiness,\r\n  unlikeBusiness,\r\n  unsubscribeFromBusiness,\r\n} from \"@/lib/actions/interactions\";\r\n\r\n// Import our components\r\nimport EnhancedBusinessCardSection from \"./EnhancedBusinessCardSection\";\r\nimport EnhancedAdSection from \"./EnhancedAdSection\";\r\nimport EnhancedBusinessDetails from \"./EnhancedBusinessDetails\";\r\n\r\n\r\nimport ProductsTab from \"../components/ProductsTab\";\r\nimport GalleryTab from \"./GalleryTab\";\r\n// Removed VisitTracker import as metrics tables have been deleted\r\n\r\n// Import animations\r\nimport {\r\n  containerVariants,\r\n  tabVariants,\r\n  tabContentVariants,\r\n} from \"./animations\";\r\n\r\n// Define the BusinessProfile type\r\ntype BusinessProfile = BusinessCardData & {\r\n  total_reviews?: number;\r\n  subscription_status?: string;\r\n  has_active_subscription?: boolean;\r\n  trial_end_date?: Date | string | null;\r\n};\r\n\r\ninterface EnhancedPublicCardPageWrapperProps {\r\n  businessProfile: BusinessProfile;\r\n  initialProducts: ProductServiceData[];\r\n  totalProductCount: number;\r\n  defaultSortPreference: ProductSortBy;\r\n  isAuthenticated: boolean;\r\n  currentUserId: string | null;\r\n  userPlan: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | undefined;\r\n  topAdData: AdData;\r\n\r\n  galleryImages?: GalleryImage[];\r\n  galleryTotalCount?: number;\r\n}\r\n\r\nexport default function EnhancedPublicCardPageWrapper({\r\n  businessProfile,\r\n  initialProducts,\r\n  totalProductCount,\r\n  defaultSortPreference,\r\n  isAuthenticated,\r\n  currentUserId,\r\n  userPlan,\r\n  topAdData,\r\n\r\n  galleryImages = [],\r\n  galleryTotalCount = 0,\r\n}: EnhancedPublicCardPageWrapperProps) {\r\n  // Get the tab from URL params\r\n  const searchParams = useSearchParams();\r\n  const router = useRouter();\r\n  const tabParam = searchParams.get(\"tab\");\r\n\r\n  // State variables\r\n  const [activeTab, setActiveTab] = useState<string>(\r\n    tabParam === \"reviews\" ? \"reviews\" : tabParam === \"gallery\" ? \"gallery\" : \"products\"\r\n  );\r\n  const [isSubscribed, setIsSubscribed] = useState<boolean>(false);\r\n  const [hasLiked, setHasLiked] = useState<boolean>(false);\r\n  const [isLoadingInteraction, setIsLoadingInteraction] =\r\n    useState<boolean>(false);\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n\r\n\r\n\r\n\r\n  // Check if the current user is a business user\r\n  const isCurrentUserBusiness = currentUserId\r\n    ? currentUserId.startsWith(\"business_\")\r\n    : false;\r\n\r\n  // Effect to scroll to reviews section when tab changes to reviews\r\n  useEffect(() => {\r\n    if (activeTab === \"reviews\") {\r\n      // Use setTimeout to ensure the DOM has fully updated and the tab content is rendered\r\n      setTimeout(() => {\r\n        const reviewsSection = document.getElementById(\"reviews-section\");\r\n        if (reviewsSection) {\r\n          const offsetTop = reviewsSection.offsetTop;\r\n          window.scrollTo({\r\n            top: offsetTop - 100,\r\n            behavior: \"smooth\"\r\n          });\r\n        }\r\n      }, 300);\r\n    }\r\n  }, [activeTab]);\r\n\r\n  // Handle tab change\r\n  const handleTabChange = (value: string) => {\r\n    setActiveTab(value);\r\n    // Update URL without full page reload\r\n    const params = new URLSearchParams(searchParams);\r\n    if (value === \"reviews\") {\r\n      params.set(\"tab\", \"reviews\");\r\n\r\n      // Add direct scroll for reviews tab\r\n      setTimeout(() => {\r\n        const reviewsSection = document.getElementById(\"reviews-section\");\r\n        if (reviewsSection) {\r\n          const offsetTop = reviewsSection.offsetTop;\r\n          window.scrollTo({\r\n            top: offsetTop - 100,\r\n            behavior: \"smooth\"\r\n          });\r\n        }\r\n      }, 300);\r\n    } else if (value === \"gallery\") {\r\n      params.set(\"tab\", \"gallery\");\r\n\r\n      // Add direct scroll for gallery tab\r\n      setTimeout(() => {\r\n        const gallerySection = document.getElementById(\"gallery-section\");\r\n        if (gallerySection) {\r\n          const offsetTop = gallerySection.offsetTop;\r\n          window.scrollTo({\r\n            top: offsetTop - 100,\r\n            behavior: \"smooth\"\r\n          });\r\n        }\r\n      }, 300);\r\n    } else {\r\n      params.delete(\"tab\");\r\n    }\r\n    router.replace(`/${businessProfile.business_slug}?${params.toString()}`, {\r\n      scroll: false,\r\n    });\r\n  };\r\n\r\n  // Handle review button click\r\n  const handleReviewClick = () => {\r\n    // If not authenticated, redirect to login with redirect back to this card\r\n    if (!isAuthenticated) {\r\n      const cardSlug = businessProfile.business_slug;\r\n      const currentPath = `/${cardSlug}`;\r\n      window.location.href = `/login?message=Please log in to leave a review&redirect=${encodeURIComponent(currentPath)}`;\r\n      return;\r\n    }\r\n\r\n    // Set active tab to reviews\r\n    setActiveTab(\"reviews\");\r\n\r\n    // Update URL without full page reload\r\n    const params = new URLSearchParams(searchParams);\r\n    params.set(\"tab\", \"reviews\");\r\n    router.replace(`/${businessProfile.business_slug}?${params.toString()}`, {\r\n      scroll: false,\r\n    });\r\n\r\n    // Force scroll to the reviews tab section with a sufficient delay\r\n    // to ensure the tab content is fully rendered\r\n    setTimeout(() => {\r\n      const reviewsSection = document.getElementById(\"reviews-section\");\r\n      if (reviewsSection) {\r\n        const offsetTop = reviewsSection.offsetTop;\r\n        window.scrollTo({\r\n          top: offsetTop - 100,\r\n          behavior: \"smooth\"\r\n        });\r\n      }\r\n    }, 300);\r\n  };\r\n\r\n  // Fetch interaction status on component mount\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n\r\n    const fetchData = async () => {\r\n      try {\r\n        if (isAuthenticated && businessProfile.id) {\r\n          const result = await getInteractionStatus(businessProfile.id || \"\");\r\n          setIsSubscribed(result.isSubscribed || false);\r\n          setHasLiked(result.hasLiked || false);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching interaction status:\", error);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [isAuthenticated, businessProfile.id]);\r\n\r\n  // Generic interaction handler\r\n  const handleInteraction = async (\r\n    action: (_businessId: string) => Promise<{ success: boolean; error?: string }>,\r\n    onSuccess: () => void,\r\n    successMessage: string,\r\n    errorPrefix: string\r\n  ) => {\r\n    if (!isAuthenticated) {\r\n      toast.error(\"Please log in to interact with this business card\");\r\n      return;\r\n    }\r\n\r\n    setIsLoadingInteraction(true);\r\n    const result = await action(businessProfile.id || \"\");\r\n    setIsLoadingInteraction(false);\r\n\r\n    if (result.success) {\r\n      toast.success(successMessage);\r\n      onSuccess();\r\n    } else {\r\n      toast.error(`${errorPrefix}: ${result.error || \"Unknown error\"}`);\r\n    }\r\n  };\r\n\r\n  // Handle subscribe\r\n  const handleSubscribe = () =>\r\n    handleInteraction(\r\n      subscribeToBusiness,\r\n      () => setIsSubscribed(true),\r\n      \"Subscribed successfully!\",\r\n      \"Failed to subscribe\"\r\n    );\r\n\r\n  // Handle unsubscribe\r\n  const handleUnsubscribe = () =>\r\n    handleInteraction(\r\n      unsubscribeFromBusiness,\r\n      () => setIsSubscribed(false),\r\n      \"Unsubscribed successfully.\",\r\n      \"Failed to unsubscribe\"\r\n    );\r\n\r\n  // Handle like\r\n  const handleLike = () =>\r\n    handleInteraction(\r\n      likeBusiness,\r\n      () => setHasLiked(true),\r\n      \"Liked!\",\r\n      \"Failed to like\"\r\n    );\r\n\r\n  // Handle unlike\r\n  const handleUnlike = () =>\r\n    handleInteraction(\r\n      unlikeBusiness,\r\n      () => setHasLiked(false),\r\n      \"Unliked.\",\r\n      \"Failed to unlike\"\r\n    );\r\n\r\n  if (!isClient) {\r\n    return null; // Render nothing on the server\r\n  }\r\n\r\n  // We don't need to show a loading indicator here anymore\r\n  // The parent component (PublicCardPageClient) is already showing one\r\n\r\n  return (\r\n    <motion.div\r\n      initial=\"hidden\"\r\n      animate=\"visible\"\r\n      variants={containerVariants}\r\n      className=\"container mx-auto px-2 sm:px-4 max-w-full min-h-screen\"\r\n    >\r\n      {/* Removed VisitTracker component as metrics tables have been deleted */}\r\n\r\n      {/* Main content area - No background container */}\r\n      <div className=\"flex flex-col lg:flex-row gap-8 lg:gap-16 pb-8 relative min-h-[80vh]\">\r\n        {/* Left Column: Card - 2/5 width on desktop, sticky */}\r\n        <div className=\"w-full lg:w-2/5 lg:sticky lg:top-24 self-start h-fit space-y-4 md:space-y-6 lg:space-y-8 z-10\">\r\n          <EnhancedBusinessCardSection\r\n            businessProfile={businessProfile}\r\n            isAuthenticated={isAuthenticated}\r\n            userPlan={userPlan}\r\n            totalLikes={businessProfile.total_likes ?? 0}\r\n            totalSubscriptions={businessProfile.total_subscriptions ?? 0}\r\n            averageRating={businessProfile.average_rating ?? 0}\r\n            isSubscribed={isSubscribed}\r\n            hasLiked={hasLiked}\r\n            isLoadingInteraction={isLoadingInteraction}\r\n            onSubscribe={handleSubscribe}\r\n            onUnsubscribe={handleUnsubscribe}\r\n            onLike={handleLike}\r\n            onUnlike={handleUnlike}\r\n            onReviewClick={handleReviewClick}\r\n            isOwnBusiness={currentUserId === businessProfile.id}\r\n            isCurrentUserBusiness={isCurrentUserBusiness}\r\n          />\r\n        </div>\r\n\r\n        {/* Right Column: Business Details - 3/5 width on desktop, sticky */}\r\n        <div className=\"w-full lg:w-3/5 flex flex-col gap-4 lg:sticky lg:top-24 self-start h-fit z-10\">\r\n          {/* Enhanced Business Details Section */}\r\n          <EnhancedBusinessDetails\r\n            businessProfile={businessProfile}\r\n            isAuthenticated={isAuthenticated}\r\n            totalLikes={businessProfile.total_likes ?? 0}\r\n            totalSubscriptions={businessProfile.total_subscriptions ?? 0}\r\n            averageRating={businessProfile.average_rating ?? 0}\r\n            isSubscribed={isSubscribed}\r\n            hasLiked={hasLiked}\r\n            isLoadingInteraction={isLoadingInteraction}\r\n            onSubscribe={handleSubscribe}\r\n            onUnsubscribe={handleUnsubscribe}\r\n            onLike={handleLike}\r\n            onUnlike={handleUnlike}\r\n            onReviewClick={handleReviewClick}\r\n            isOwnBusiness={currentUserId === businessProfile.id}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Ad Section - Below sticky content */}\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5, delay: 0.2 }}\r\n        className=\"w-full mb-8\"\r\n      >\r\n        <EnhancedAdSection\r\n          topAdData={topAdData}\r\n          businessCustomAd={null}\r\n          userPlan={userPlan}\r\n        />\r\n      </motion.div>\r\n\r\n      {/* Enhanced Tabs Section */}\r\n      <motion.div\r\n        variants={tabVariants}\r\n        className=\"mt-8 mb-16\"\r\n      >\r\n        {/* Enhanced Tabs Toggle */}\r\n        <EnhancedTabsToggle\r\n          activeTab={activeTab}\r\n          onTabChange={handleTabChange}\r\n          galleryCount={galleryTotalCount}\r\n        />\r\n\r\n        {/* AnimatePresence for smooth tab transitions */}\r\n        <AnimatePresence mode=\"wait\">\r\n          {/* Products & Services Tab */}\r\n          {activeTab === \"products\" && (\r\n            <motion.div\r\n              key=\"products\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              exit=\"exit\"\r\n              variants={tabContentVariants}\r\n            >\r\n              <ProductsTab\r\n                businessProfile={businessProfile}\r\n                initialProducts={initialProducts}\r\n                totalProductCount={totalProductCount}\r\n                defaultSortPreference={defaultSortPreference}\r\n              />\r\n            </motion.div>\r\n          )}\r\n\r\n          {/* Gallery Tab */}\r\n          {activeTab === \"gallery\" && galleryImages.length > 0 && (\r\n            <motion.div\r\n              key=\"gallery\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              exit=\"exit\"\r\n              variants={tabContentVariants}\r\n              id=\"gallery-section\"\r\n              className=\"scroll-mt-24 relative pt-4\"\r\n            >\r\n              {/* Visual marker for scroll target */}\r\n              <div className=\"absolute -top-2 left-0 right-0 h-1 bg-amber-500/20 rounded-full\"></div>\r\n              <GalleryTab\r\n                images={galleryImages}\r\n                totalCount={galleryTotalCount}\r\n                businessName={businessProfile.business_name || \"\"}\r\n                businessSlug={businessProfile.business_slug || \"\"}\r\n              />\r\n            </motion.div>\r\n          )}\r\n\r\n          {/* Reviews Tab */}\r\n          {activeTab === \"reviews\" && (\r\n            <motion.div\r\n              key=\"reviews\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              exit=\"exit\"\r\n              variants={tabContentVariants}\r\n              id=\"reviews-section\"\r\n              className=\"scroll-mt-24 relative pt-4\" // Increased scroll margin and added padding\r\n            >\r\n              {/* Visual marker for scroll target */}\r\n              <div className=\"absolute -top-2 left-0 right-0 h-1 bg-amber-500/20 rounded-full\"></div>\r\n              <ReviewsTab\r\n                businessProfileId={businessProfile.id || \"\"}\r\n                isAuthenticated={isAuthenticated}\r\n                currentUserId={currentUserId}\r\n                averageRating={businessProfile.average_rating ?? 0}\r\n                totalReviews={businessProfile.total_reviews ?? 0}\r\n              />\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AASA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA,wBAAwB;AACxB;AACA;AACA;AAGA;AACA;AACA,kEAAkE;AAElE,oBAAoB;AACpB;AApCA;;;;;;;;;;;;;;;AAgEe,SAAS,8BAA8B,EACpD,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,qBAAqB,EACrB,eAAe,EACf,aAAa,EACb,QAAQ,EACR,SAAS,EAET,gBAAgB,EAAE,EAClB,oBAAoB,CAAC,EACc;IACnC,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,aAAa,GAAG,CAAC;IAElC,kBAAkB;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvC,aAAa,YAAY,YAAY,aAAa,YAAY,YAAY;IAE5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClD,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAMzC,+CAA+C;IAC/C,MAAM,wBAAwB,gBAC1B,cAAc,UAAU,CAAC,eACzB;IAEJ,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,WAAW;YAC3B,qFAAqF;YACrF,WAAW;gBACT,MAAM,iBAAiB,SAAS,cAAc,CAAC;gBAC/C,IAAI,gBAAgB;oBAClB,MAAM,YAAY,eAAe,SAAS;oBAC1C,OAAO,QAAQ,CAAC;wBACd,KAAK,YAAY;wBACjB,UAAU;oBACZ;gBACF;YACF,GAAG;QACL;IACF,GAAG;QAAC;KAAU;IAEd,oBAAoB;IACpB,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,sCAAsC;QACtC,MAAM,SAAS,IAAI,gBAAgB;QACnC,IAAI,UAAU,WAAW;YACvB,OAAO,GAAG,CAAC,OAAO;YAElB,oCAAoC;YACpC,WAAW;gBACT,MAAM,iBAAiB,SAAS,cAAc,CAAC;gBAC/C,IAAI,gBAAgB;oBAClB,MAAM,YAAY,eAAe,SAAS;oBAC1C,OAAO,QAAQ,CAAC;wBACd,KAAK,YAAY;wBACjB,UAAU;oBACZ;gBACF;YACF,GAAG;QACL,OAAO,IAAI,UAAU,WAAW;YAC9B,OAAO,GAAG,CAAC,OAAO;YAElB,oCAAoC;YACpC,WAAW;gBACT,MAAM,iBAAiB,SAAS,cAAc,CAAC;gBAC/C,IAAI,gBAAgB;oBAClB,MAAM,YAAY,eAAe,SAAS;oBAC1C,OAAO,QAAQ,CAAC;wBACd,KAAK,YAAY;wBACjB,UAAU;oBACZ;gBACF;YACF,GAAG;QACL,OAAO;YACL,OAAO,MAAM,CAAC;QAChB;QACA,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE,gBAAgB,aAAa,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,EAAE;YACvE,QAAQ;QACV;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,0EAA0E;QAC1E,IAAI,CAAC,iBAAiB;YACpB,MAAM,WAAW,gBAAgB,aAAa;YAC9C,MAAM,cAAc,CAAC,CAAC,EAAE,UAAU;YAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,wDAAwD,EAAE,mBAAmB,cAAc;YACnH;QACF;QAEA,4BAA4B;QAC5B,aAAa;QAEb,sCAAsC;QACtC,MAAM,SAAS,IAAI,gBAAgB;QACnC,OAAO,GAAG,CAAC,OAAO;QAClB,OAAO,OAAO,CAAC,CAAC,CAAC,EAAE,gBAAgB,aAAa,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,EAAE;YACvE,QAAQ;QACV;QAEA,kEAAkE;QAClE,8CAA8C;QAC9C,WAAW;YACT,MAAM,iBAAiB,SAAS,cAAc,CAAC;YAC/C,IAAI,gBAAgB;gBAClB,MAAM,YAAY,eAAe,SAAS;gBAC1C,OAAO,QAAQ,CAAC;oBACd,KAAK,YAAY;oBACjB,UAAU;gBACZ;YACF;QACF,GAAG;IACL;IAEA,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QAEZ,MAAM,YAAY;YAChB,IAAI;gBACF,IAAI,mBAAmB,gBAAgB,EAAE,EAAE;oBACzC,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD,EAAE,gBAAgB,EAAE,IAAI;oBAChE,gBAAgB,OAAO,YAAY,IAAI;oBACvC,YAAY,OAAO,QAAQ,IAAI;gBACjC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;QAEA;IACF,GAAG;QAAC;QAAiB,gBAAgB,EAAE;KAAC;IAExC,8BAA8B;IAC9B,MAAM,oBAAoB,OACxB,QACA,WACA,gBACA;QAEA,IAAI,CAAC,iBAAiB;YACpB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,wBAAwB;QACxB,MAAM,SAAS,MAAM,OAAO,gBAAgB,EAAE,IAAI;QAClD,wBAAwB;QAExB,IAAI,OAAO,OAAO,EAAE;YAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;QACF,OAAO;YACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,YAAY,EAAE,EAAE,OAAO,KAAK,IAAI,iBAAiB;QAClE;IACF;IAEA,mBAAmB;IACnB,MAAM,kBAAkB,IACtB,kBACE,sJAAA,CAAA,sBAAmB,EACnB,IAAM,gBAAgB,OACtB,4BACA;IAGJ,qBAAqB;IACrB,MAAM,oBAAoB,IACxB,kBACE,sJAAA,CAAA,0BAAuB,EACvB,IAAM,gBAAgB,QACtB,8BACA;IAGJ,cAAc;IACd,MAAM,aAAa,IACjB,kBACE,sJAAA,CAAA,eAAY,EACZ,IAAM,YAAY,OAClB,UACA;IAGJ,gBAAgB;IAChB,MAAM,eAAe,IACnB,kBACE,sJAAA,CAAA,iBAAc,EACd,IAAM,YAAY,QAClB,YACA;IAGJ,IAAI,CAAC,UAAU;QACb,OAAO,MAAM,+BAA+B;IAC9C;IAEA,yDAAyD;IACzD,qEAAqE;IAErE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAQ;QACR,SAAQ;QACR,UAAU,+IAAA,CAAA,oBAAiB;QAC3B,WAAU;;0BAKV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iKAAA,CAAA,UAA2B;4BAC1B,iBAAiB;4BACjB,iBAAiB;4BACjB,UAAU;4BACV,YAAY,gBAAgB,WAAW,IAAI;4BAC3C,oBAAoB,gBAAgB,mBAAmB,IAAI;4BAC3D,eAAe,gBAAgB,cAAc,IAAI;4BACjD,cAAc;4BACd,UAAU;4BACV,sBAAsB;4BACtB,aAAa;4BACb,eAAe;4BACf,QAAQ;4BACR,UAAU;4BACV,eAAe;4BACf,eAAe,kBAAkB,gBAAgB,EAAE;4BACnD,uBAAuB;;;;;;;;;;;kCAK3B,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC,6JAAA,CAAA,UAAuB;4BACtB,iBAAiB;4BACjB,iBAAiB;4BACjB,YAAY,gBAAgB,WAAW,IAAI;4BAC3C,oBAAoB,gBAAgB,mBAAmB,IAAI;4BAC3D,eAAe,gBAAgB,cAAc,IAAI;4BACjD,cAAc;4BACd,UAAU;4BACV,sBAAsB;4BACtB,aAAa;4BACb,eAAe;4BACf,QAAQ;4BACR,UAAU;4BACV,eAAe;4BACf,eAAe,kBAAkB,gBAAgB,EAAE;;;;;;;;;;;;;;;;;0BAMzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC,uJAAA,CAAA,UAAiB;oBAChB,WAAW;oBACX,kBAAkB;oBAClB,UAAU;;;;;;;;;;;0BAKd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU,+IAAA,CAAA,cAAW;gBACrB,WAAU;;kCAGV,8OAAC,wJAAA,CAAA,UAAkB;wBACjB,WAAW;wBACX,aAAa;wBACb,cAAc;;;;;;kCAIhB,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;;4BAEnB,cAAc,4BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAQ;gCACR,SAAQ;gCACR,MAAK;gCACL,UAAU,+IAAA,CAAA,qBAAkB;0CAE5B,cAAA,8OAAC,iJAAA,CAAA,UAAW;oCACV,iBAAiB;oCACjB,iBAAiB;oCACjB,mBAAmB;oCACnB,uBAAuB;;;;;;+BAVrB;;;;;4BAgBP,cAAc,aAAa,cAAc,MAAM,GAAG,mBACjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAQ;gCACR,SAAQ;gCACR,MAAK;gCACL,UAAU,+IAAA,CAAA,qBAAkB;gCAC5B,IAAG;gCACH,WAAU;;kDAGV,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC,gJAAA,CAAA,UAAU;wCACT,QAAQ;wCACR,YAAY;wCACZ,cAAc,gBAAgB,aAAa,IAAI;wCAC/C,cAAc,gBAAgB,aAAa,IAAI;;;;;;;+BAd7C;;;;;4BAoBP,cAAc,2BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAQ;gCACR,SAAQ;gCACR,MAAK;gCACL,UAAU,+IAAA,CAAA,qBAAkB;gCAC5B,IAAG;gCACH,WAAU,6BAA6B,4CAA4C;;;kDAGnF,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC,gIAAA,CAAA,UAAU;wCACT,mBAAmB,gBAAgB,EAAE,IAAI;wCACzC,iBAAiB;wCACjB,eAAe;wCACf,eAAe,gBAAgB,cAAc,IAAI;wCACjD,cAAc,gBAAgB,aAAa,IAAI;;;;;;;+BAf7C;;;;;;;;;;;;;;;;;;;;;;;AAuBlB", "debugId": null}}, {"offset": {"line": 10252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/PublicCardPageClient.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Suspense, useState, useEffect } from \"react\";\r\nimport { Loader2 } from \"lucide-react\";\r\nimport { BusinessCardData } from \"@/app/(dashboard)/dashboard/business/card/schema\";\r\nimport {\r\n  ProductServiceData,\r\n  ProductSortBy,\r\n} from \"@/app/(dashboard)/dashboard/business/products/actions\";\r\n\r\nimport { GalleryImage } from \"@/lib/actions/gallery\";\r\nimport { AdData } from \"@/types/ad\";\r\nimport EnhancedPublicCardPageWrapper from \"./components/EnhancedPublicCardPageWrapper\";\r\n\r\n// Define the BusinessProfile type\r\ntype BusinessProfile = BusinessCardData & {\r\n  total_reviews?: number;\r\n  subscription_status?: string;\r\n  has_active_subscription?: boolean;\r\n  trial_end_date?: Date | string | null;\r\n};\r\n\r\ninterface PublicCardPageClientProps {\r\n  businessProfile: BusinessProfile;\r\n  initialProducts: ProductServiceData[];\r\n  totalProductCount: number;\r\n  defaultSortPreference: ProductSortBy;\r\n  isAuthenticated: boolean;\r\n  currentUserId: string | null;\r\n  userPlan: \"free\" | \"basic\" | \"growth\" | \"pro\" | \"enterprise\" | \"trial\" | undefined;\r\n  topAdData: AdData;\r\n\r\n  galleryImages?: GalleryImage[];\r\n  galleryTotalCount?: number;\r\n}\r\n\r\nexport default function PublicCardPageClient({\r\n  businessProfile,\r\n  initialProducts,\r\n  totalProductCount,\r\n  defaultSortPreference,\r\n  isAuthenticated,\r\n  currentUserId,\r\n  userPlan,\r\n  topAdData,\r\n\r\n  galleryImages = [],\r\n  galleryTotalCount = 0,\r\n}: PublicCardPageClientProps) {\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  // Set loading to false after component mounts\r\n  useEffect(() => {\r\n    const timer = setTimeout(() => {\r\n      setIsLoading(false);\r\n    }, 300); // Short delay to ensure the loader is visible\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* Always show loader initially, then fade it out */}\r\n      <div\r\n        className={`fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50 transition-opacity duration-300 ${isLoading ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}\r\n      >\r\n        <div className=\"flex flex-col items-center gap-2\">\r\n          <Loader2 className=\"h-8 w-8 animate-spin text-[var(--brand-gold)]\" />\r\n          <p className=\"text-sm text-muted-foreground\">Loading business card...</p>\r\n        </div>\r\n      </div>\r\n\r\n      <Suspense fallback={\r\n        <div className=\"fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50\">\r\n          <div className=\"flex flex-col items-center gap-2\">\r\n            <Loader2 className=\"h-8 w-8 animate-spin text-[var(--brand-gold)]\" />\r\n            <p className=\"text-sm text-muted-foreground\">Loading business card...</p>\r\n          </div>\r\n        </div>\r\n      }>\r\n        <EnhancedPublicCardPageWrapper\r\n          businessProfile={businessProfile}\r\n          initialProducts={initialProducts}\r\n          totalProductCount={totalProductCount}\r\n          defaultSortPreference={defaultSortPreference}\r\n          isAuthenticated={isAuthenticated}\r\n          currentUserId={currentUserId}\r\n          userPlan={userPlan}\r\n          topAdData={topAdData}\r\n\r\n          galleryImages={galleryImages}\r\n          galleryTotalCount={galleryTotalCount}\r\n        />\r\n      </Suspense>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAZA;;;;;AAoCe,SAAS,qBAAqB,EAC3C,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,qBAAqB,EACrB,eAAe,EACf,aAAa,EACb,QAAQ,EACR,SAAS,EAET,gBAAgB,EAAE,EAClB,oBAAoB,CAAC,EACK;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,WAAW;YACvB,aAAa;QACf,GAAG,MAAM,8CAA8C;QAEvD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC;gBACC,WAAW,CAAC,sHAAsH,EAAE,YAAY,gBAAgB,iCAAiC;0BAEjM,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;0BAIjD,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBACR,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;0BAIjD,cAAA,8OAAC,mKAAA,CAAA,UAA6B;oBAC5B,iBAAiB;oBACjB,iBAAiB;oBACjB,mBAAmB;oBACnB,uBAAuB;oBACvB,iBAAiB;oBACjB,eAAe;oBACf,UAAU;oBACV,WAAW;oBAEX,eAAe;oBACf,mBAAmB;;;;;;;;;;;;;AAK7B", "debugId": null}}, {"offset": {"line": 10368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/web-app/dukancard/app/%5BcardSlug%5D/components/OfflineBusinessMessage.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Store } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nexport default function OfflineBusinessMessage() {\r\n  const router = useRouter();\r\n  const [isClient, setIsClient] = useState(false);\r\n\r\n  // Use useEffect to detect client-side rendering\r\n  useEffect(() => {\r\n    setIsClient(true);\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"min-h-screen flex flex-col items-center justify-center p-4 bg-white dark:bg-black\">\r\n      <motion.div\r\n        initial={{ opacity: 0, y: 20 }}\r\n        animate={{ opacity: 1, y: 0 }}\r\n        transition={{ duration: 0.5 }}\r\n        className=\"max-w-md w-full bg-white dark:bg-neutral-900 rounded-xl shadow-lg p-8 border border-neutral-200 dark:border-neutral-800 text-center\"\r\n      >\r\n        <div className=\"mb-6 flex justify-center\">\r\n          <div className=\"p-4 bg-neutral-100 dark:bg-neutral-800 rounded-full\">\r\n            <Store className=\"h-12 w-12 text-[var(--brand-gold)]\" />\r\n          </div>\r\n        </div>\r\n\r\n        <h2 className=\"text-2xl font-bold mb-3 text-neutral-900 dark:text-neutral-100\">\r\n          This Business is Currently Offline\r\n        </h2>\r\n        \r\n        <p className=\"text-neutral-600 dark:text-neutral-400 mb-6\">\r\n          This business card is currently set to private or offline mode. \r\n          Meanwhile, you can discover other businesses in your locality.\r\n        </p>\r\n\r\n        <div className=\"relative group\">\r\n          {/* Button glow effect with properly rounded corners */}\r\n          {isClient && (\r\n            <motion.div\r\n              className=\"absolute -inset-0.5 rounded-md blur-md\"\r\n              style={{\r\n                background: \"linear-gradient(to right, rgba(var(--brand-gold-rgb), 0.6), rgba(var(--brand-gold-rgb), 0.8))\"\r\n              }}\r\n              initial={{ opacity: 0.7 }}\r\n              animate={{\r\n                opacity: [0.7, 0.9, 0.7],\r\n                boxShadow: [\r\n                  \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\",\r\n                  \"0 0 20px 4px rgba(var(--brand-gold-rgb), 0.5)\",\r\n                  \"0 0 15px 2px rgba(var(--brand-gold-rgb), 0.3)\"\r\n                ]\r\n              }}\r\n              transition={{\r\n                duration: 2,\r\n                repeat: Infinity,\r\n                repeatType: \"reverse\"\r\n              }}\r\n            />\r\n          )}\r\n\r\n          <Button\r\n            onClick={() => router.push(\"/discover\")}\r\n            className=\"relative w-full bg-[var(--brand-gold)] hover:bg-[var(--brand-gold)]/90 text-black font-medium px-8 py-6 h-12 rounded-md shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer\"\r\n          >\r\n            <span className=\"relative z-10 flex items-center justify-center gap-2\">\r\n              Discover Businesses\r\n              <Store className=\"h-5 w-5\" />\r\n            </span>\r\n          </Button>\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAG;YAC7B,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;;;;;;8BAIrB,8OAAC;oBAAG,WAAU;8BAAiE;;;;;;8BAI/E,8OAAC;oBAAE,WAAU;8BAA8C;;;;;;8BAK3D,8OAAC;oBAAI,WAAU;;wBAEZ,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCACL,YAAY;4BACd;4BACA,SAAS;gCAAE,SAAS;4BAAI;4BACxB,SAAS;gCACP,SAAS;oCAAC;oCAAK;oCAAK;iCAAI;gCACxB,WAAW;oCACT;oCACA;oCACA;iCACD;4BACH;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,YAAY;4BACd;;;;;;sCAIJ,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;sCAEV,cAAA,8OAAC;gCAAK,WAAU;;oCAAuD;kDAErE,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B", "debugId": null}}]}