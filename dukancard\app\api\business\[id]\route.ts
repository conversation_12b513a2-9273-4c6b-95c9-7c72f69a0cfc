import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { extractBearerToken, verifyJWTToken } from '@/lib/middleware/jwt';
import { verifyHMACMiddleware } from '@/lib/middleware/hmac';
import { createClient } from '@/utils/supabase/server';
import { 
  bruteForceProtectionMiddleware, 
  getClientIP 
} from '@/lib/middleware/bruteForceProtection';

// Validation schema for updates
const updateBusinessSchema = z.object({
  business_name: z.string().min(1, "Business name is required").optional(),
  contact_email: z.string().email("Invalid email address").optional(),
  member_name: z.string().min(1, "Member name is required").optional(),
  title: z.string().min(1, "Title is required").optional(),
  business_category: z.string().optional().nullable(),
  phone: z.string().optional().nullable(),
  whatsapp_number: z.string().length(10, "WhatsApp number must be 10 digits").optional().nullable(),
  address_line: z.string().optional().nullable(),
  city: z.string().optional().nullable(),
  state: z.string().optional().nullable(),
  pincode: z.string().optional().nullable(),
  locality: z.string().optional().nullable(),
  about_bio: z.string().max(250, "About/bio cannot exceed 250 characters").optional().nullable(),
  business_slug: z.string().min(3, "Slug must be at least 3 characters").optional(),
  status: z.enum(["online", "offline"]).optional(),
  logo_url: z.string().url().optional().nullable(),
  instagram_url: z.string().url().optional().nullable(),
  facebook_url: z.string().url().optional().nullable(),
  established_year: z.number().int().min(1900).max(new Date().getFullYear()).optional().nullable(),
  delivery_info: z.string().optional().nullable(),
  business_hours: z.record(z.string(), z.any()).optional().nullable(),
  latitude: z.number().optional().nullable(),
  longitude: z.number().optional().nullable(),
});

/**
 * Security middleware wrapper for business API routes
 */
async function applySecurityMiddleware(req: NextRequest, requireHMAC: boolean = true) {
  // 1. Apply rate limiting and brute force protection
  const ipAddress = getClientIP(req);
  const bruteForceCheck = await bruteForceProtectionMiddleware(req, {
    operation: 'business_api',
    ipAddress,
  });

  if (bruteForceCheck) {
    return bruteForceCheck;
  }

  // 2. Verify JWT token
  const token = extractBearerToken(req);
  if (!token) {
    return new NextResponse(JSON.stringify({ error: 'Authorization token required' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtResult = await verifyJWTToken(token);
  if (!jwtResult.success) {
    return new NextResponse(JSON.stringify({ error: 'Invalid or expired token' }), {
      status: 401,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  const jwtPayload = jwtResult.payload;

  // 3. Verify HMAC signature (if required)
  if (requireHMAC) {
    const hmacResult = await verifyHMACMiddleware(req, true);
    if (!hmacResult.success) {
      return new NextResponse(JSON.stringify({ error: hmacResult.error }), {
        status: hmacResult.status || 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }
  }

  return { jwtPayload };
}

/**
 * GET /api/business/[id] - Get a specific business profile
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Apply security middleware (no HMAC required for business profile viewing)
    const securityResult = await applySecurityMiddleware(req, false);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    // Validate ID format
    if (!id || typeof id !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid business ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Fetch business profile
    const { data: business, error } = await supabase
      .from('business_profiles')
      .select(`
        id, business_name, business_slug, contact_email, member_name, title,
        business_category, phone, whatsapp_number, address_line, city, state,
        pincode, locality, about_bio, status, logo_url, instagram_url,
        facebook_url, established_year, delivery_info, business_hours,
        latitude, longitude, total_likes, total_subscriptions, average_rating,
        created_at, updated_at
      `)
      .eq('id', id)
      .maybeSingle();

    if (error) {
      console.error('Error fetching business profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to fetch business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    if (!business) {
      return new NextResponse(JSON.stringify({ error: 'Business profile not found' }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({ business });

  } catch (error) {
    console.error('Unexpected error in GET /api/business/[id]:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * PATCH /api/business/[id] - Update a business profile
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate ID format
    if (!id || typeof id !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid business ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Authorization check: users can only update their own business profile
    if (jwtPayload.user_id !== id) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized: You can only update your own business profile' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Parse and validate request body
    const body = await req.json();
    const validation = updateBusinessSchema.safeParse(body);
    if (!validation.success) {
      return new NextResponse(JSON.stringify({
        error: 'Validation failed',
        details: validation.error.issues,
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const updateData = validation.data;
    const supabase = await createClient();

    // If updating business_slug, check if it's unique
    if (updateData.business_slug) {
      const { data: slugExists } = await supabase
        .from('business_profiles')
        .select('id')
        .eq('business_slug', updateData.business_slug)
        .neq('id', id)
        .maybeSingle();

      if (slugExists) {
        return new NextResponse(JSON.stringify({ error: 'Business slug already exists' }), {
          status: 409,
          headers: { 'Content-Type': 'application/json' },
        });
      }
    }

    // Update business profile
    const { data: updatedBusiness, error } = await supabase
      .from('business_profiles')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating business profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to update business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({
      business: updatedBusiness,
      message: 'Business profile updated successfully',
    });

  } catch (error) {
    console.error('Unexpected error in PATCH /api/business/[id]:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}

/**
 * DELETE /api/business/[id] - Delete a business profile
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Apply security middleware
    const securityResult = await applySecurityMiddleware(req, true);
    if (securityResult instanceof NextResponse) {
      return securityResult;
    }

    const { jwtPayload } = securityResult;

    if (!jwtPayload) {
      return new NextResponse(JSON.stringify({ error: 'Invalid token payload' }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Validate ID format
    if (!id || typeof id !== 'string') {
      return new NextResponse(JSON.stringify({ error: 'Invalid business ID' }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    // Authorization check: users can only delete their own business profile
    if (jwtPayload.user_id !== id) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized: You can only delete your own business profile' }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    const supabase = await createClient();

    // Delete business profile
    const { error } = await supabase
      .from('business_profiles')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting business profile:', error);
      return new NextResponse(JSON.stringify({ error: 'Failed to delete business profile' }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      });
    }

    return NextResponse.json({
      message: 'Business profile deleted successfully',
    });

  } catch (error) {
    console.error('Unexpected error in DELETE /api/business/[id]:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal server error' }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' },
    });
  }
}
