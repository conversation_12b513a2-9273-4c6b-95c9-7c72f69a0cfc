"use server";

import { createClient } from "@/utils/supabase/server";


import { BusinessSortBy } from "@/lib/actions/businessProfiles";
import { NearbyProduct } from "./types";
import { searchDiscoverCombined } from "./combinedActions";

// Define a type for the product result from Supabase
// Unused type kept for reference
/* type ProductResult = {
  id: string;
  business_id: string | null;
  name: string | null;
  description: string | null;
  base_price: number | null;
  discounted_price: number | null;
  product_type: "physical" | "service" | null;
  is_available: boolean | null;
  image_url: string | null;
  created_at: string | null;
  updated_at: string | null;
  business_profiles: {
    business_slug: string | null;
  } | null;
}; */

// Helper function to convert any product result to NearbyProduct
function convertToNearbyProduct(
  product: Record<string, unknown>
): NearbyProduct {
  // Extract business_slug from the joined business_profiles
  let business_slug = null;
  if (product.business_profiles) {
    // Handle both object and array formats
    if (Array.isArray(product.business_profiles)) {
      business_slug = product.business_profiles[0]?.business_slug || null;
    } else if (
      product.business_profiles &&
      typeof product.business_profiles === "object"
    ) {
      business_slug =
        ((product.business_profiles as Record<string, unknown>)
          .business_slug as string) || null;
    }
  }

  return {
    id: product.id as string,
    business_id: product.business_id as string | undefined,
    name: (product.name as string) || "",
    description: (product.description as string) || "",
    base_price: Number(product.base_price) || 0,
    discounted_price: product.discounted_price
      ? Number(product.discounted_price)
      : undefined,
    product_type:
      (product.product_type as "physical" | "service") || "physical",
    is_available: Boolean(product.is_available) || false,
    image_url: product.image_url as string | undefined,
    created_at: product.created_at as string | undefined,
    updated_at: product.updated_at as string | undefined,
    slug: product.slug as string | undefined,
    business_slug: business_slug,
    featured_image_index: 0, // Default value for NearbyProduct
    images: [], // Default empty array for images
  };
}

// Function to fetch more products with combined search for infinite scroll
export async function fetchMoreProductsCombined(params: {
  businessName?: string | null;
  productName?: string | null;
  pincode?: string | null;
  locality?: string | null;
  page: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productSort?: string;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  // Reuse the searchDiscoverCombined function with viewType set to "products"
  const result = await searchDiscoverCombined({
    ...params,
    viewType: "products",
  });

  if (result.error) {
    return { error: result.error };
  }

  if (!result.data?.products) {
    return { error: "No product data found" };
  }

  return {
    data: {
      products: result.data.products,
      totalCount: result.data.totalCount,
      hasMore: result.data.hasMore,
      nextPage: result.data.nextPage,
    },
  };
}

// Function to fetch all products
export async function fetchAllProducts(params: {
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
  pincode?: string | null;
  locality?: string | null;
  productName?: string | null;
  category?: string | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
    pincode = null,
    locality = null,
    productName = null,
    category = null,
  } = params;

  // Check Authentication
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  const isAuthenticated = !!session;

  try {
    // Build query parameters for the products API
    const queryParams = new URLSearchParams();
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('is_available', 'true');

    // Add filters
    if (productType) {
      queryParams.set('product_type', productType);
    }
    if (pincode) {
      queryParams.set('pincode', pincode);
    }
    if (locality) {
      queryParams.set('locality', locality);
    }
    if (productName) {
      queryParams.set('search', productName);
    }
    if (category) {
      queryParams.set('category', category);
    }

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      case 'likes_asc':
      case 'likes_desc':
      case 'subscriptions_asc':
      case 'subscriptions_desc':
      case 'rating_asc':
      case 'rating_desc':
        // These sorts are not supported for products, fall back to created_desc
        apiSortBy = 'created_desc';
        break;
      default:
        apiSortBy = 'created_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the products API endpoint
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authentication if available
    if (session) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/products?${queryParams.toString()}`, {
      method: 'GET',
      headers,
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("Products API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch products",
      };
    }

    const productsData = result.products || [];
    const pagination = result.pagination || {};
    const totalCount = pagination.total || 0;

    // Process the products data to include business_slug
    const products = productsData.map(convertToNearbyProduct);

    // Calculate pagination info
    const hasMore = pagination.hasMore || false;
    const nextPage = hasMore ? page + 1 : null;

    return {
      data: {
        products,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Unexpected error in fetchAllProducts:", error);
    return { error: "An unexpected error occurred" };
  }
}

// Function to fetch products by business IDs
export async function fetchProductsByBusinessIds(params: {
  businessIds: string[];
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  productType?: "physical" | "service" | null;
}): Promise<{
  data?: {
    products: NearbyProduct[];
    isAuthenticated: boolean;
    totalCount: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  error?: string;
}> {
  const {
    businessIds,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    productType = null,
  } = params;

  if (!businessIds || businessIds.length === 0) {
    return {
      error: "No business IDs provided",
    };
  }

  // Check Authentication
  const supabase = await createClient();
  const {
    data: { session },
  } = await supabase.auth.getSession();
  const isAuthenticated = !!session;

  try {
    // Build query parameters for the products API
    const queryParams = new URLSearchParams();
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());
    queryParams.set('is_available', 'true');

    // Add business IDs filter (the API doesn't support multiple business IDs directly,
    // so we'll need to make multiple calls or modify the API)
    // For now, let's make a single call and filter client-side
    if (productType) {
      queryParams.set('product_type', productType);
    }

    // Convert sortBy to API format
    let apiSortBy = 'created_desc';
    switch (sortBy) {
      case 'name_asc':
        apiSortBy = 'name_asc';
        break;
      case 'name_desc':
        apiSortBy = 'name_desc';
        break;
      case 'created_asc':
        apiSortBy = 'created_asc';
        break;
      case 'created_desc':
        apiSortBy = 'created_desc';
        break;
      default:
        apiSortBy = 'created_desc';
        break;
    }
    queryParams.set('sort_by', apiSortBy);

    // Use the products API endpoint
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    // Add authentication if available
    if (session) {
      headers['Authorization'] = `Bearer ${session.access_token}`;
    }

    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/api/products?${queryParams.toString()}`, {
      method: 'GET',
      headers,
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("Products API Fetch Error:", result);
      return {
        error: result.error || "Failed to fetch products",
      };
    }

    const allProducts = result.products || [];

    // Filter products by business IDs
    const filteredProducts = allProducts.filter((product: { business_id: string }) =>
      businessIds.includes(product.business_id)
    );

    // Process the products data
    const products = filteredProducts.map(convertToNearbyProduct);

    // Calculate pagination info (simplified since we're filtering client-side)
    const totalCount = products.length;
    const hasMore = false; // Simplified for now
    const nextPage = null;

    return {
      data: {
        products,
        isAuthenticated,
        totalCount,
        hasMore,
        nextPage,
      },
    };
  } catch (error) {
    console.error("Unexpected error in fetchProductsByBusinessIds:", error);
    return { error: "An unexpected error occurred" };
  }
}
