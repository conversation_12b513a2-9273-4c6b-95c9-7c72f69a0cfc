"use server";

import { createClient } from "@/utils/supabase/server";
import { internalGet } from "@/lib/utils/internalApiClient";

import { ProductFilters, ProductSortBy } from "./types";
import { ProductWithVariantInfo } from "@/types/products";

// Fetch all products/services with variant information
export async function getProductServices(
  page: number = 1,
  limit: number = 10,
  filters: ProductFilters = {},
  sortBy: ProductSortBy = "created_desc"
): Promise<{
  data?: ProductWithVariantInfo[];
  count?: number;
  error?: string;
}> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user) return { error: "User not authenticated." };

  try {
    // Build query parameters for the products API
    const queryParams = new URLSearchParams();
    queryParams.set('page', page.toString());
    queryParams.set('limit', limit.toString());

    // Apply filters
    if (filters.searchTerm) {
      queryParams.set('search', filters.searchTerm);
    }
    if (filters.productType) {
      queryParams.set('product_type', filters.productType);
    }
    if (filters.hasVariants !== undefined) {
      queryParams.set('has_variants', filters.hasVariants.toString());
    }

    // Apply sorting
    let sortByParam = 'created_desc'; // default
    switch (sortBy) {
      case "created_asc":
        sortByParam = 'created_asc';
        break;
      case "price_asc":
        sortByParam = 'price_asc';
        break;
      case "price_desc":
        sortByParam = 'price_desc';
        break;
      case "name_asc":
        sortByParam = 'name_asc';
        break;
      case "name_desc":
        sortByParam = 'name_desc';
        break;
      
      default:
        sortByParam = 'created_desc';
    }
    queryParams.set('sort_by', sortByParam);

    // Use internal API client to fetch products
    const response = await internalGet(`/api/products?${queryParams.toString()}`, { userId: user.id });

    if (!response.success) {
      console.error("Error fetching products:", response.error);
      return { error: response.error || "Failed to fetch products" };
    }

    return {
      data: response.data?.products || [],
      count: response.data?.pagination?.total || 0,
    };

  } catch (error) {
    console.error("Unexpected error fetching products:", error);
    return { error: "An unexpected error occurred while fetching products." };
  }
}
  
