"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { PostFormData } from "@/lib/types/posts";
import { ActionResponse } from "@/lib/types/api";
import { internalPost, internalPatch, internalDelete } from "@/lib/utils/internalApiClient";

/**
 * Create a new post
 */
export async function createPost(
  formData: PostFormData
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to create a post",
    };
  }

  try {
    // Use internal API client to create post
    const postData = {
      content: formData.content,
      image_url: formData.image_url || null,
      product_ids: formData.product_ids || [],
      mentioned_business_ids: formData.mentioned_business_ids || [],
      post_type: "business" as const,
    };

    const response = await internalPost('/api/posts', postData, { userId: user.id });

    if (!response.success) {
      return {
        success: false,
        message: "Failed to create post",
        error: response.error || "Unknown error occurred",
      };
    }

    // Revalidate the feed pages
    revalidatePath("/dashboard/business");
    revalidatePath("/dashboard/business/posts");

    return {
      success: true,
      message: "Post created successfully",
      data: response.data?.post,
    };
  } catch (error) {
    console.error("Unexpected error creating post:", error);
    return {
      success: false,
      message: "Failed to create post",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Update only the content of an existing post (for inline editing)
 * Handles both business and customer posts
 */
export async function updatePostContent(
  postId: string,
  content: string
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to update a post",
    };
  }

  try {
    // Use internal API client to update post content
    const updateData = { content: content.trim() };
    const response = await internalPatch(`/api/posts/${postId}`, updateData, { userId: user.id });

    if (!response.success) {
      return {
        success: false,
        message: "Failed to update post",
        error: response.error || "Unknown error occurred",
      };
    }

    // Revalidate the feed pages
    revalidatePath("/dashboard/business");
    revalidatePath("/dashboard/customer");
    revalidatePath("/dashboard/business/posts");

    return {
      success: true,
      message: "Post updated successfully",
      data: response.data?.post,
    };
  } catch (error) {
    console.error("Unexpected error updating post content:", error);
    return {
      success: false,
      message: "Failed to update post",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Update only the product_ids of an existing post (for inline editing)
 * Only applies to business posts - customer posts don't have linked products
 */
export async function updatePostProducts(
  postId: string,
  productIds: string[]
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to update a post",
    };
  }

  try {
    // Use internal API client to update post products
    const updateData = { product_ids: productIds };
    const response = await internalPatch(`/api/posts/${postId}`, updateData, { userId: user.id });

    if (!response.success) {
      return {
        success: false,
        message: "Failed to update post",
        error: response.error || "Unknown error occurred",
      };
    }

    // Revalidate the feed pages
    revalidatePath("/dashboard/business");
    revalidatePath("/dashboard/business/posts");

    return {
      success: true,
      message: "Post updated successfully",
      data: response.data?.post,
    };
  } catch (error) {
    console.error("Unexpected error updating post products:", error);
    return {
      success: false,
      message: "Failed to update post",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Update an existing post (full update for form submissions)
 * Handles both business and customer posts
 */
export async function updatePost(
  postId: string,
  formData: PostFormData
): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to update a post",
    };
  }

  try {
    // Use internal API client to update post
    const updateData = {
      content: formData.content,
      image_url: formData.image_url || null,
      product_ids: formData.product_ids || [],
      mentioned_business_ids: formData.mentioned_business_ids || [],
    };

    const response = await internalPatch(`/api/posts/${postId}`, updateData, { userId: user.id });

    if (!response.success) {
      return {
        success: false,
        message: "Failed to update post",
        error: response.error || "Unknown error occurred",
      };
    }

    // Revalidate the feed pages
    revalidatePath("/dashboard/business");
    revalidatePath("/dashboard/customer");
    revalidatePath("/dashboard/business/posts");

    return {
      success: true,
      message: "Post updated successfully",
      data: response.data?.post,
    };
  } catch (error) {
    console.error("Unexpected error updating post:", error);
    return {
      success: false,
      message: "Failed to update post",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Delete a post (handles both business and customer posts)
 */
export async function deletePost(postId: string): Promise<ActionResponse> {
  const supabase = await createClient();

  // Get the current user
  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser();

  if (userError || !user) {
    return {
      success: false,
      message: "Authentication required",
      error: "You must be logged in to delete a post",
    };
  }

  try {
    // Use internal API client to delete post
    const response = await internalDelete(`/api/posts/${postId}`, { userId: user.id });

    if (!response.success) {
      return {
        success: false,
        message: "Failed to delete post",
        error: response.error || "Unknown error occurred",
      };
    }

    // Revalidate the feed pages
    revalidatePath("/dashboard/business");
    revalidatePath("/dashboard/customer");
    revalidatePath("/dashboard/business/posts");

    return {
      success: true,
      message: "Post deleted successfully",
    };
  } catch (error) {
    console.error("Unexpected error deleting post:", error);
    return {
      success: false,
      message: "Failed to delete post",
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
