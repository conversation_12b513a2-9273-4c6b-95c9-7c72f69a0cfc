import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface Post {
  id: string;
  content: string;
  image_url?: string;
  images?: string[];
  post_type: 'business' | 'customer';
  business_id?: string;
  customer_id?: string;
  created_at: string;
  updated_at: string;
  // Additional fields from unified_posts view
  author_name?: string;
  author_avatar?: string;
  business_name?: string;
  business_logo?: string;
  city_slug?: string;
  state_slug?: string;
  locality_slug?: string;
  pincode?: string;
}

export interface PostsState {
  posts: Post[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
    nextPage: number | null;
  };
  filters: {
    filter?: 'all' | 'smart' | 'subscribed';
    post_type?: 'business' | 'customer' | 'all';
    city_slug?: string;
    state_slug?: string;
    locality_slug?: string;
    pincode?: string;
    business_id?: string;
    customer_id?: string;
  };
}

export interface PostsActions {
  // Fetch posts with filters
  fetchPosts: (_filters?: Partial<PostsState['filters']>, _page?: number) => Promise<void>;

  // Load more posts (for infinite scroll)
  loadMorePosts: () => Promise<void>;

  // Create a new post
  createPost: (_postData: {
    content: string;
    image_url?: string;
    images?: string[];
    post_type: 'business' | 'customer';
    business_id?: string;
    customer_id?: string;
  }) => Promise<Post | null>;

  // Set filters
  setFilters: (_filters: Partial<PostsState['filters']>) => void;
  
  // Clear filters
  clearFilters: () => void;
  
  // Reset state
  reset: () => void;
  
  // Clear error
  clearError: () => void;
}

type PostsStore = PostsState & PostsActions;

const initialState: PostsState = {
  posts: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasMore: false,
    nextPage: null,
  },
  filters: {
    filter: 'all',
    post_type: 'all',
  },
};

export const usePostsStore = create<PostsStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      fetchPosts: async (filters = {}, page = 1) => {
        set({ loading: true, error: null });

        try {
          // Merge with existing filters
          const currentFilters = get().filters;
          const newFilters = { ...currentFilters, ...filters };

          // Build query parameters
          const queryParams = new URLSearchParams();
          queryParams.set('page', page.toString());
          queryParams.set('limit', get().pagination.limit.toString());

          // Add filters to query params
          Object.entries(newFilters).forEach(([key, value]) => {
            if (value !== undefined && value !== null && value !== '') {
              queryParams.set(key, value.toString());
            }
          });

          // Get auth token if available
          const headers: Record<string, string> = {
            'Content-Type': 'application/json',
          };

          // Try to get session from Supabase (this is just for auth, not direct DB access)
          try {
            const { createClient } = await import('@/utils/supabase/client');
            const supabase = createClient();
            const { data: { session } } = await supabase.auth.getSession();
            
            if (session) {
              headers['Authorization'] = `Bearer ${session.access_token}`;
            }
          } catch (authError) {
            console.warn('Could not get auth session:', authError);
          }

          const response = await fetch(`/api/posts?${queryParams.toString()}`, {
            method: 'GET',
            headers,
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to fetch posts');
          }

          const posts = result.posts || [];
          const pagination = result.pagination || {};

          set({
            posts: page === 1 ? posts : [...get().posts, ...posts],
            pagination: {
              page,
              limit: pagination.limit || get().pagination.limit,
              total: pagination.total || 0,
              hasMore: pagination.hasMore || false,
              nextPage: pagination.nextPage || null,
            },
            filters: newFilters,
            loading: false,
            error: null,
          });
        } catch (error) {
          console.error('Error fetching posts:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch posts',
          });
        }
      },

      loadMorePosts: async () => {
        const { pagination, filters } = get();
        if (!pagination.hasMore || get().loading) return;

        await get().fetchPosts(filters, pagination.page + 1);
      },

      createPost: async (postData) => {
        set({ loading: true, error: null });

        try {
          // Get auth token
          const { createClient } = await import('@/utils/supabase/client');
          const supabase = createClient();
          const { data: { session } } = await supabase.auth.getSession();

          if (!session) {
            throw new Error('Authentication required');
          }

          const response = await fetch('/api/posts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${session.access_token}`,
            },
            body: JSON.stringify(postData),
          });

          const result = await response.json();

          if (!response.ok) {
            throw new Error(result.error || 'Failed to create post');
          }

          const newPost = result.post;

          // Add to the beginning of the posts list
          set({
            posts: [newPost, ...get().posts],
            loading: false,
            error: null,
          });

          return newPost;
        } catch (error) {
          console.error('Error creating post:', error);
          set({
            loading: false,
            error: error instanceof Error ? error.message : 'Failed to create post',
          });
          return null;
        }
      },

      setFilters: (filters) => {
        set({ filters: { ...get().filters, ...filters } });
      },

      clearFilters: () => {
        set({ filters: { filter: 'all', post_type: 'all' } });
      },

      reset: () => {
        set(initialState);
      },

      clearError: () => {
        set({ error: null });
      },
    }),
    {
      name: 'posts-store',
    }
  )
);
