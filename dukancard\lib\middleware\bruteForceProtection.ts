import { NextRequest, NextResponse } from 'next/server';
import { applyRateLimiting, RateLimitResult, RateLimitConfig } from './rateLimiter';

export interface BruteForceProtectionConfig {
  /** Maximum login attempts per IP per hour (default: 10) */
  maxLoginAttemptsPerIP?: number;
  /** Time window for login attempts in seconds (default: 3600 = 1 hour) */
  loginWindowSeconds?: number;
  /** Maximum login attempts per email per hour (default: 5) */
  maxLoginAttemptsPerEmail?: number;
  /** Maximum token refresh attempts per device per hour (default: 20) */
  maxRefreshAttemptsPerDevice?: number;
  /** Time window for token refresh in seconds (default: 3600 = 1 hour) */
  refreshWindowSeconds?: number;
  /** Whether to enable progressive delays (default: true) */
  enableProgressiveDelays?: boolean;
}

export interface BruteForceContext {
  /** IP address of the request */
  ipAddress?: string;
  /** Email being used for login (if applicable) */
  email?: string;
  /** Device ID for token refresh (if applicable) */
  deviceId?: string;
  /** User ID (if available) */
  userId?: string;
  /** Type of authentication operation */
  operation: 'login' | 'refresh' | 'device_register' | 'send_otp' | 'verify_otp' |
             'business_api' | 'business_access_check' | 'business_search' | 'sitemap_generation' |
             'profile_exists_api' | 'customer_profile_api' | 'likes_api' | 'location_api' |
             'posts_api' | 'product_api' | 'products_api' | 'storage_api';
}

/**
 * Calculate progressive delay based on attempt count
 * Implements exponential backoff with jitter
 */
export function calculateProgressiveDelay(attemptCount: number): number {
  if (attemptCount <= 3) return 0;
  
  // Base delay starts at 1 second for 4th attempt
  const baseDelay = Math.pow(2, attemptCount - 4) * 1000;
  
  // Cap at 30 seconds
  const cappedDelay = Math.min(baseDelay, 30000);
  
  // Add jitter (±20%) but ensure result doesn't exceed cap
  const jitter = cappedDelay * 0.2 * (Math.random() - 0.5);
  const finalDelay = cappedDelay + jitter;
  
  // Ensure final delay doesn't exceed the cap and is not negative
  return Math.floor(Math.max(0, Math.min(finalDelay, 30000)));
}

/**
 * Apply brute-force protection for authentication endpoints
 */
export async function applyBruteForceProtection(
  req: NextRequest,
  context: BruteForceContext,
  config: BruteForceProtectionConfig = {}
): Promise<RateLimitResult> {
  const {
    maxLoginAttemptsPerIP = parseInt(process.env.BRUTE_FORCE_LOGIN_IP_LIMIT || '10', 10),
    loginWindowSeconds = parseInt(process.env.BRUTE_FORCE_LOGIN_WINDOW || '3600', 10),
    maxLoginAttemptsPerEmail = parseInt(process.env.BRUTE_FORCE_EMAIL_LIMIT || '5', 10),
    maxRefreshAttemptsPerDevice = parseInt(process.env.BRUTE_FORCE_REFRESH_LIMIT || '20', 10),
    refreshWindowSeconds = parseInt(process.env.BRUTE_FORCE_REFRESH_WINDOW || '3600', 10),
    enableProgressiveDelays = true,
  } = config;

  const strategies: Parameters<typeof applyRateLimiting>[1] = {};

  switch (context.operation) {
    case 'login':
      // For login, we apply both IP-based and email-based rate limiting
      strategies.byIP = true;
      strategies.customLimits = {
        ip: {
          maxRequests: maxLoginAttemptsPerIP,
          windowSeconds: loginWindowSeconds,
          identifier: 'login_ip',
        } as RateLimitConfig,
      };

      // If we have an email, add email-based rate limiting
      if (context.email) {
        strategies.byUser = true;
        strategies.userId = `email:${context.email}`;
        strategies.customLimits.user = {
          maxRequests: maxLoginAttemptsPerEmail,
          windowSeconds: loginWindowSeconds,
          identifier: 'login_email',
        } as RateLimitConfig;
      }
      break;

    case 'refresh':
      // For token refresh, we apply device-based and IP-based rate limiting
      strategies.byIP = true;
      strategies.customLimits = {
        ip: {
          maxRequests: maxRefreshAttemptsPerDevice * 2, // More lenient for IP
          windowSeconds: refreshWindowSeconds,
          identifier: 'refresh_ip',
        } as RateLimitConfig,
      };

      if (context.deviceId) {
        strategies.byDevice = true;
        strategies.deviceId = context.deviceId;
        strategies.customLimits.device = {
          maxRequests: maxRefreshAttemptsPerDevice,
          windowSeconds: refreshWindowSeconds,
          identifier: 'refresh_device',
        } as RateLimitConfig;
      }
      break;

    case 'device_register':
      // For device registration, apply IP-based rate limiting
      strategies.byIP = true;
      strategies.customLimits = {
        ip: {
          maxRequests: 5, // Very restrictive for device registration
          windowSeconds: 3600,
          identifier: 'device_register_ip',
        } as RateLimitConfig,
      };
      break;

    default:
      return {
        success: false,
        error: 'Invalid operation type',
        status: 400,
      };
  }

  const result = await applyRateLimiting(req, strategies);

  // If rate limiting failed and progressive delays are enabled,
  // add delay information to the result
  if (!result.success && enableProgressiveDelays) {
    const attemptCount = (result.limit || 0) - (result.remaining || 0);
    const delay = calculateProgressiveDelay(attemptCount);
    
    if (delay > 0) {
      result.retryAfter = Math.max(result.retryAfter || 0, Math.ceil(delay / 1000));
    }
  }

  return result;
}

/**
 * Middleware wrapper for brute-force protection
 * Returns a NextResponse if the request should be blocked, null if it should continue
 */
export async function bruteForceProtectionMiddleware(
  req: NextRequest,
  context: BruteForceContext,
  config: BruteForceProtectionConfig = {}
): Promise<NextResponse | null> {
  const result = await applyBruteForceProtection(req, context, config);

  if (!result.success) {
    const response = new NextResponse(JSON.stringify({ 
      error: result.error,
      retryAfter: result.retryAfter,
      message: 'Too many attempts. Please try again later.',
    }), {
      status: result.status || 429,
      headers: {
        'Content-Type': 'application/json',
        'X-RateLimit-Limit': result.limit?.toString() || '',
        'X-RateLimit-Remaining': result.remaining?.toString() || '0',
        'X-RateLimit-Reset': result.reset?.toString() || '',
        'Retry-After': result.retryAfter?.toString() || '',
      },
    });

    return response;
  }

  return null; // Continue to next middleware/handler
}

/**
 * Extract email from login request body safely
 */
export function extractEmailFromLoginRequest(body: unknown): string | undefined {
  try {
    if (typeof body === 'object' && body !== null && 'email' in body && typeof (body as { email: unknown }).email === 'string') {
      return (body as { email: string }).email.toLowerCase().trim();
    }
  } catch (_error) {
    // Ignore parsing errors
  }
  return undefined;
}

/**
 * Extract device ID from request body safely
 */
export function extractDeviceIdFromRequest(body: unknown): string | undefined {
  try {
    if (typeof body === 'object' && body !== null && 'deviceId' in body && typeof (body as { deviceId: unknown }).deviceId === 'string') {
      return (body as { deviceId: string }).deviceId;
    }
  } catch (_error) {
    // Ignore parsing errors
  }
  return undefined;
}

/**
 * Get client IP address from request (same as rate limiter)
 */
export function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const remoteAddress = req.headers.get('x-remote-address');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || remoteAddress || 'unknown';
}