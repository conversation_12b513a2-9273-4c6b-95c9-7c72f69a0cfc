/**
 * Internal API Client for Server-Side Next.js Code
 * 
 * This client allows server-side code (server actions, server components) to call
 * API routes without external authentication. It bypasses HMAC verification by
 * setting the internal call header.
 */

import { headers } from 'next/headers';
import { createClient } from '@/utils/supabase/server';

interface InternalApiOptions extends RequestInit {
  userId?: string; // Optional user ID for authenticated calls
}

interface InternalApiResponse<T = any> {
  data: T | null;
  error: string | null;
  success: boolean;
}

/**
 * Internal API client for server-side calls
 */
export class InternalApiClient {
  private baseUrl: string;

  constructor() {
    // Use the same base URL as the application
    this.baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  }

  /**
   * Make an internal API call with automatic authentication
   */
  async call<T = any>(
    endpoint: string,
    options: InternalApiOptions = {}
  ): Promise<InternalApiResponse<T>> {
    try {
      const { userId, ...fetchOptions } = options;

      // Get user authentication if userId is provided or try to get current user
      let authToken: string | null = null;
      
      if (userId) {
        // If userId is provided, we can create a token for that user
        // For now, we'll use the service role approach
        authToken = await this.getServiceRoleToken();
      } else {
        // Try to get current user's token
        try {
          const supabase = await createClient();
          const { data: { session } } = await supabase.auth.getSession();
          authToken = session?.access_token || null;
        } catch (error) {
          console.warn('Could not get user session for internal API call:', error);
        }
      }

      // Prepare headers
      const requestHeaders: HeadersInit = {
        'Content-Type': 'application/json',
        'X-Internal-Call': 'true', // Mark as internal call to bypass HMAC
        ...fetchOptions.headers,
      };

      // Add authorization if available
      if (authToken) {
        (requestHeaders as Record<string, string>)['Authorization'] = `Bearer ${authToken}`;
      }

      // Make the request
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...fetchOptions,
        headers: requestHeaders,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          data: null,
          error: data.error || `Request failed with status ${response.status}`,
          success: false,
        };
      }

      return {
        data,
        error: null,
        success: true,
      };

    } catch (error) {
      console.error('Internal API call failed:', error);
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Internal API call failed',
        success: false,
      };
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, options: InternalApiOptions = {}): Promise<InternalApiResponse<T>> {
    return this.call<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, body?: any, options: InternalApiOptions = {}): Promise<InternalApiResponse<T>> {
    return this.call<T>(endpoint, {
      ...options,
      method: 'POST',
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, body?: any, options: InternalApiOptions = {}): Promise<InternalApiResponse<T>> {
    return this.call<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, body?: any, options: InternalApiOptions = {}): Promise<InternalApiResponse<T>> {
    return this.call<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: body ? JSON.stringify(body) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, options: InternalApiOptions = {}): Promise<InternalApiResponse<T>> {
    return this.call<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Get service role token for elevated permissions
   * This should only be used for internal operations
   */
  private async getServiceRoleToken(): Promise<string | null> {
    try {
      // For internal calls, we can use the service role key directly
      // This bypasses RLS and should only be used for trusted internal operations
      return process.env.SUPABASE_SERVICE_ROLE_KEY || null;
    } catch (error) {
      console.error('Failed to get service role token:', error);
      return null;
    }
  }
}

// Singleton instance
let internalApiClient: InternalApiClient | null = null;

/**
 * Get the internal API client instance
 */
export function getInternalApiClient(): InternalApiClient {
  if (!internalApiClient) {
    internalApiClient = new InternalApiClient();
  }
  return internalApiClient;
}

/**
 * Convenience functions for common operations
 */

/**
 * Make an internal API call for the current user
 */
export async function internalApiCall<T = any>(
  endpoint: string,
  options: InternalApiOptions = {}
): Promise<InternalApiResponse<T>> {
  const client = getInternalApiClient();
  return client.call<T>(endpoint, options);
}

/**
 * Make an internal API call for a specific user
 */
export async function internalApiCallForUser<T = any>(
  endpoint: string,
  userId: string,
  options: InternalApiOptions = {}
): Promise<InternalApiResponse<T>> {
  const client = getInternalApiClient();
  return client.call<T>(endpoint, { ...options, userId });
}

/**
 * Make an internal GET request
 */
export async function internalGet<T = any>(
  endpoint: string,
  options: InternalApiOptions = {}
): Promise<InternalApiResponse<T>> {
  const client = getInternalApiClient();
  return client.get<T>(endpoint, options);
}

/**
 * Make an internal POST request
 */
export async function internalPost<T = any>(
  endpoint: string,
  body?: any,
  options: InternalApiOptions = {}
): Promise<InternalApiResponse<T>> {
  const client = getInternalApiClient();
  return client.post<T>(endpoint, body, options);
}

/**
 * Make an internal PATCH request
 */
export async function internalPatch<T = any>(
  endpoint: string,
  body?: any,
  options: InternalApiOptions = {}
): Promise<InternalApiResponse<T>> {
  const client = getInternalApiClient();
  return client.patch<T>(endpoint, body, options);
}

/**
 * Make an internal DELETE request
 */
export async function internalDelete<T = any>(
  endpoint: string,
  options: InternalApiOptions = {}
): Promise<InternalApiResponse<T>> {
  const client = getInternalApiClient();
  return client.delete<T>(endpoint, options);
}
