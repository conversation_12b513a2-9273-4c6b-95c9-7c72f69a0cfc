import crypto from 'crypto';

/**
 * Generates an HMAC-SHA256 signature for API request validation
 * @param method - HTTP method (GET, POST, etc.)
 * @param path - API path (/api/auth/login)
 * @param timestamp - Unix timestamp in milliseconds
 * @param body - Request body (empty string for GET requests)
 * @param deviceSecret - The device secret (plaintext)
 * @returns Base64 encoded HMAC signature
 */
export function generateHMACSignature(
  method: string,
  path: string,
  timestamp: string,
  body: string,
  deviceSecret: string
): string {
  // 1. Create SHA256 hash of the request body
  const bodyHash = crypto
    .createHash('sha256')
    .update(body || '')
    .digest('hex');

  // 2. Create the string to be signed: method + path + timestamp + bodyHash
  const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;

  // 3. Generate HMAC-SHA256 signature using device secret
  const signature = crypto
    .createHmac('sha256', deviceSecret)
    .update(stringToSign)
    .digest('base64');

  return signature;
}

/**
 * Verifies an HMAC signature using constant-time comparison
 * @param receivedSignature - The signature from the client
 * @param expectedSignature - The signature calculated by the server
 * @returns true if signatures match, false otherwise
 */
export function verifyHMACSignature(
  receivedSignature: string,
  expectedSignature: string
): boolean {
  // Convert to buffers for constant-time comparison
  const receivedBuffer = Buffer.from(receivedSignature, 'base64');
  const expectedBuffer = Buffer.from(expectedSignature, 'base64');

  // Ensure both signatures are the same length to prevent timing attacks
  if (receivedBuffer.length !== expectedBuffer.length) {
    return false;
  }

  // Use crypto.timingSafeEqual for constant-time comparison
  return crypto.timingSafeEqual(receivedBuffer, expectedBuffer);
}

/**
 * Validates timestamp to prevent replay attacks
 * @param timestamp - Unix timestamp in milliseconds as string
 * @param windowSeconds - Allowed time window in seconds (default: 120)
 * @returns true if timestamp is within the allowed window
 */
export function validateTimestamp(
  timestamp: string,
  windowSeconds: number = 120
): boolean {
  const requestTime = parseInt(timestamp, 10);
  const currentTime = Date.now();
  const windowMs = windowSeconds * 1000;

  // Check if timestamp is a valid number
  if (isNaN(requestTime)) {
    return false;
  }

  // Check if request is within the allowed time window
  const timeDiff = Math.abs(currentTime - requestTime);
  return timeDiff <= windowMs;
}

/**
 * Extracts required headers for HMAC verification
 * @param headers - Request headers
 * @returns Object with deviceId, timestamp, and signature, or null if any are missing
 */
export function extractHMACHeaders(headers: Headers | { [key: string]: string | undefined }): {
  deviceId: string;
  timestamp: string;
  signature: string;
} | null {
  const deviceId = typeof headers.get === 'function' 
    ? headers.get('x-device-id') 
    : (headers as any)['x-device-id'];
  const timestamp = typeof headers.get === 'function'
    ? headers.get('x-timestamp')
    : (headers as any)['x-timestamp'];
  const signature = typeof headers.get === 'function'
    ? headers.get('x-signature')
    : (headers as any)['x-signature'];

  if (!deviceId || !timestamp || !signature) {
    return null;
  }

  return { deviceId, timestamp, signature };
}