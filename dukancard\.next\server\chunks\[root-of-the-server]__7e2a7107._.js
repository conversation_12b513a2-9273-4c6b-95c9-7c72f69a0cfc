module.exports = {

"[project]/.next-internal/server/app/api/products/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/utils/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
;
async function createClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJuam9sY29lY29nemdnbG5ibHFuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMwNTIwNTYsImV4cCI6MjA1ODYyODA1Nn0.k8DuvOrrKQlvxGb5qD78_vXDqIRkmk7ZRUj1Hb5PL4o");
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Check if we're in a test environment
    let headersList = null;
    let cookieStore = null;
    try {
        // Dynamically import next/headers to avoid issues in edge runtime
        const { headers, cookies } = await __turbopack_context__.r("[project]/node_modules/next/headers.js [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
        headersList = await headers();
        cookieStore = await cookies();
    } catch (error) {
        // If next/headers is not available (e.g., in edge runtime), continue without it
        console.warn('next/headers not available in this context, using fallback');
    }
    const isTestEnvironment = ("TURBOPACK compile-time value", "development") === 'test' || process.env.PLAYWRIGHT_TESTING === 'true' || headersList && headersList.get('x-playwright-testing') === 'true';
    if (isTestEnvironment && headersList) {
        // Return a mocked Supabase client for testing
        return createMockSupabaseClient(headersList);
    }
    // If cookies are not available, create a basic server client
    if (!cookieStore) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
            cookies: {
                getAll () {
                    return [];
                },
                setAll () {
                // No-op when cookies are not available
                }
            }
        });
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(supabaseUrl, supabaseAnonKey, {
        cookies: {
            async getAll () {
                return await cookieStore.getAll();
            },
            async setAll (cookiesToSet) {
                try {
                    for (const { name, value, options } of cookiesToSet){
                        await cookieStore.set(name, value, options);
                    }
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
function createMockSupabaseClient(headersList) {
    const testAuthState = headersList.get('x-test-auth-state');
    const testUserType = headersList.get('x-test-user-type');
    const testHasProfile = testUserType === 'customer' || testUserType === 'business';
    const testBusinessSlug = headersList.get('x-test-business-slug');
    const testPlanId = headersList.get('x-test-plan-id') || 'free';
    return {
        auth: {
            getUser: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            user: {
                                id: 'test-user-id',
                                email: '<EMAIL>'
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        user: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            getSession: async ()=>{
                if (testAuthState === 'authenticated') {
                    return {
                        data: {
                            session: {
                                user: {
                                    id: 'test-user-id',
                                    email: '<EMAIL>'
                                }
                            }
                        },
                        error: null
                    };
                }
                return {
                    data: {
                        session: null
                    },
                    error: {
                        message: 'Unauthorized',
                        name: 'AuthApiError',
                        status: 401
                    }
                };
            },
            signInWithOtp: async ()=>({
                    data: {
                        user: null,
                        session: null
                    },
                    error: null
                }),
            signOut: async ()=>({
                    error: null
                })
        },
        from: (table)=>createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
    };
}
function createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    const getMockData = ()=>getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);
    const createChainableMock = (data)=>({
            select: (_columns)=>createChainableMock(data),
            eq: (_column, _value)=>createChainableMock(data),
            neq: (_column, _value)=>createChainableMock(data),
            gt: (_column, _value)=>createChainableMock(data),
            gte: (_column, _value)=>createChainableMock(data),
            lt: (_column, _value)=>createChainableMock(data),
            lte: (_column, _value)=>createChainableMock(data),
            like: (_column, _pattern)=>createChainableMock(data),
            ilike: (_column, _pattern)=>createChainableMock(data),
            is: (_column, _value)=>createChainableMock(data),
            in: (_column, _values)=>createChainableMock(data),
            contains: (_column, _value)=>createChainableMock(data),
            containedBy: (_column, _value)=>createChainableMock(data),
            rangeGt: (_column, _value)=>createChainableMock(data),
            rangeGte: (_column, _value)=>createChainableMock(data),
            rangeLt: (_column, _value)=>createChainableMock(data),
            rangeLte: (_column, _value)=>createChainableMock(data),
            rangeAdjacent: (_column, _value)=>createChainableMock(data),
            overlaps: (_column, _value)=>createChainableMock(data),
            textSearch: (_column, _query)=>createChainableMock(data),
            match: (_query)=>createChainableMock(data),
            not: (_column, _operator, _value)=>createChainableMock(data),
            or: (_filters)=>createChainableMock(data),
            filter: (_column, _operator, _value)=>createChainableMock(data),
            order: (_column, _options)=>createChainableMock(data),
            limit: (_count, _options)=>createChainableMock(data),
            range: (_from, _to, _options)=>createChainableMock(data),
            abortSignal: (_signal)=>createChainableMock(data),
            single: async ()=>getMockData(),
            maybeSingle: async ()=>getMockData(),
            then: async (callback)=>{
                const result = getMockData();
                return callback ? callback(result) : result;
            },
            data: data || [],
            error: null,
            count: data ? data.length : 0,
            status: 200,
            statusText: 'OK'
        });
    return {
        select: (_columns)=>createChainableMock(),
        insert: (data)=>({
                select: (_columns)=>({
                        single: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        maybeSingle: async ()=>({
                                data: Array.isArray(data) ? data[0] : data,
                                error: null
                            }),
                        then: async (_callback)=>{
                            const result = {
                                data: Array.isArray(data) ? data : [
                                    data
                                ],
                                error: null
                            };
                            return _callback ? _callback(result) : result;
                        }
                    }),
                then: async (_callback)=>{
                    const result = {
                        data: Array.isArray(data) ? data : [
                            data
                        ],
                        error: null
                    };
                    return _callback ? _callback(result) : result;
                }
            }),
        update: (data)=>createChainableMock(data),
        upsert: (data)=>createChainableMock(data),
        delete: ()=>createChainableMock(),
        rpc: (_functionName, _params)=>createChainableMock()
    };
}
/**
 * Helper function to get mock table data based on test state
 */ function getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId) {
    if (table === 'customer_profiles') {
        const hasCustomerProfile = testHasProfile && testUserType === 'customer';
        return {
            data: hasCustomerProfile ? {
                id: 'test-user-id',
                name: 'Test Customer',
                avatar_url: null,
                phone: '+1234567890',
                email: '<EMAIL>',
                address: 'Test Address',
                city: 'Test City',
                state: 'Test State',
                pincode: '123456'
            } : null,
            error: null
        };
    }
    if (table === 'business_profiles') {
        const hasBusinessProfile = testHasProfile && testUserType === 'business';
        return {
            data: hasBusinessProfile ? {
                id: 'test-user-id',
                business_slug: testBusinessSlug || null,
                trial_end_date: null,
                has_active_subscription: true,
                business_name: 'Test Business',
                city_slug: 'test-city',
                state_slug: 'test-state',
                locality_slug: 'test-locality',
                pincode: '123456',
                business_description: 'Test business description',
                business_category: 'retail',
                phone: '+1234567890',
                email: '<EMAIL>',
                website: 'https://testbusiness.com'
            } : null,
            error: null
        };
    }
    if (table === 'payment_subscriptions') {
        return {
            data: testUserType === 'business' ? {
                id: 'test-subscription-id',
                plan_id: testPlanId,
                business_profile_id: 'test-user-id',
                status: 'active',
                created_at: '2024-01-01T00:00:00Z'
            } : null,
            error: null
        };
    }
    if (table === 'products') {
        return {
            data: testUserType === 'business' ? [
                {
                    id: 'test-product-1',
                    name: 'Test Product 1',
                    price: 100,
                    business_profile_id: 'test-user-id',
                    available: true
                },
                {
                    id: 'test-product-2',
                    name: 'Test Product 2',
                    price: 200,
                    business_profile_id: 'test-user-id',
                    available: false
                }
            ] : [],
            error: null
        };
    }
    // Default return for unknown tables
    return {
        data: null,
        error: null
    };
}
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[project]/lib/middleware/rateLimiter.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addRateLimitHeaders": (()=>addRateLimitHeaders),
    "applyRateLimiting": (()=>applyRateLimiting),
    "rateLimitMiddleware": (()=>rateLimitMiddleware),
    "rateLimitingMiddleware": (()=>rateLimitingMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@upstash/redis/nodejs.mjs [app-route] (ecmascript) <locals>");
;
;
// Initialize Redis client
const redis = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$upstash$2f$redis$2f$nodejs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Redis"]({
    url: process.env.UPSTASH_REDIS_REST_URL,
    token: process.env.UPSTASH_REDIS_REST_TOKEN
});
/**
 * Get client IP address from request
 */ function getClientIP(req) {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    const remoteAddress = req.headers.get('x-remote-address');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || remoteAddress || 'unknown';
}
/**
 * Generate Redis key for rate limiting
 */ function generateRateLimitKey(identifier, value, windowStart) {
    return `ratelimit:${identifier}:${value}:${windowStart}`;
}
/**
 * Get the start of the current time window
 */ function getWindowStart(windowSeconds) {
    const now = Math.floor(Date.now() / 1000);
    return Math.floor(now / windowSeconds) * windowSeconds;
}
/**
 * Check rate limit for a specific identifier and value
 */ async function checkRateLimit(identifier, value, config) {
    try {
        const windowStart = getWindowStart(config.windowSeconds);
        const key = generateRateLimitKey(identifier, value, windowStart);
        const windowEnd = windowStart + config.windowSeconds;
        // Get current count
        const currentCount = await redis.get(key) || 0;
        if (currentCount >= config.maxRequests) {
            // Rate limit exceeded
            const retryAfter = windowEnd - Math.floor(Date.now() / 1000);
            return {
                success: false,
                error: 'Rate limit exceeded',
                status: 429,
                limit: config.maxRequests,
                remaining: 0,
                reset: windowEnd,
                retryAfter: Math.max(retryAfter, 0)
            };
        }
        // Increment counter
        const newCount = await redis.incr(key);
        // Set expiration if this is the first request in the window
        if (newCount === 1) {
            await redis.expire(key, config.windowSeconds);
        }
        // Calculate remaining requests
        const remaining = Math.max(config.maxRequests - newCount, 0);
        return {
            success: true,
            limit: config.maxRequests,
            remaining,
            reset: windowEnd
        };
    } catch (error) {
        console.error(`Rate limit check failed for ${identifier}:${value}:`, error);
        // In case of Redis error, allow the request but log the error
        return {
            success: true,
            limit: config.maxRequests,
            remaining: config.maxRequests,
            reset: Math.floor(Date.now() / 1000) + config.windowSeconds
        };
    }
}
async function rateLimitMiddleware(req, strategies = []) {
    // Default strategies if none provided
    if (strategies.length === 0) {
        const defaultMaxRequests = parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10);
        const defaultWindowSeconds = parseInt(process.env.RATE_LIMIT_WINDOW_SECONDS || '60', 10);
        strategies = [
            {
                identifier: 'ip',
                getValue: (req)=>getClientIP(req),
                config: {
                    maxRequests: defaultMaxRequests,
                    windowSeconds: defaultWindowSeconds,
                    identifier: 'ip'
                }
            }
        ];
    }
    let finalResult = {
        success: true,
        limit: strategies[0]?.config.maxRequests || 100,
        remaining: strategies[0]?.config.maxRequests || 100,
        reset: Math.floor(Date.now() / 1000) + (strategies[0]?.config.windowSeconds || 60)
    };
    // Apply strategies in order
    for (const strategy of strategies){
        const value = strategy.getValue(req);
        // Skip strategy if value cannot be determined
        if (!value || value === 'unknown') {
            continue;
        }
        const result = await checkRateLimit(strategy.identifier, value, strategy.config);
        if (!result.success) {
            return result;
        }
        // Update final result with the most restrictive limits
        if (result.remaining !== undefined && result.remaining < (finalResult.remaining || Infinity)) {
            finalResult = {
                ...finalResult,
                limit: result.limit,
                remaining: result.remaining,
                reset: result.reset
            };
        }
    }
    return finalResult;
}
async function applyRateLimiting(req, options = {}) {
    const strategies = [];
    // Default configurations
    const defaultIPLimit = {
        maxRequests: parseInt(process.env.RATE_LIMIT_IP_MAX_REQUESTS || '100', 10),
        windowSeconds: parseInt(process.env.RATE_LIMIT_IP_WINDOW_SECONDS || '60', 10),
        identifier: 'ip'
    };
    const defaultDeviceLimit = {
        maxRequests: parseInt(process.env.RATE_LIMIT_DEVICE_MAX_REQUESTS || '200', 10),
        windowSeconds: parseInt(process.env.RATE_LIMIT_DEVICE_WINDOW_SECONDS || '60', 10),
        identifier: 'device'
    };
    const defaultUserLimit = {
        maxRequests: parseInt(process.env.RATE_LIMIT_USER_MAX_REQUESTS || '500', 10),
        windowSeconds: parseInt(process.env.RATE_LIMIT_USER_WINDOW_SECONDS || '60', 10),
        identifier: 'user'
    };
    // Add IP rate limiting (enabled by default)
    if (options.byIP !== false) {
        strategies.push({
            identifier: 'ip',
            getValue: (req)=>getClientIP(req),
            config: options.customLimits?.ip || defaultIPLimit
        });
    }
    // Add device rate limiting
    if (options.byDevice && options.deviceId) {
        strategies.push({
            identifier: 'device',
            getValue: ()=>options.deviceId,
            config: options.customLimits?.device || defaultDeviceLimit
        });
    }
    // Add user rate limiting
    if (options.byUser && options.userId) {
        strategies.push({
            identifier: 'user',
            getValue: ()=>options.userId,
            config: options.customLimits?.user || defaultUserLimit
        });
    }
    return rateLimitMiddleware(req, strategies);
}
async function rateLimitingMiddleware(req, options = {}) {
    const result = await applyRateLimiting(req, options);
    if (!result.success) {
        const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: result.error
        }), {
            status: result.status || 429,
            headers: {
                'Content-Type': 'application/json',
                'X-RateLimit-Limit': result.limit?.toString() || '',
                'X-RateLimit-Remaining': result.remaining?.toString() || '0',
                'X-RateLimit-Reset': result.reset?.toString() || '',
                'Retry-After': result.retryAfter?.toString() || ''
            }
        });
        return response;
    }
    // Add rate limit headers to successful responses
    // Note: This will be handled by the calling code since we're returning null to continue
    return null;
}
function addRateLimitHeaders(response, result) {
    if (result.limit !== undefined) {
        response.headers.set('X-RateLimit-Limit', result.limit.toString());
    }
    if (result.remaining !== undefined) {
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    }
    if (result.reset !== undefined) {
        response.headers.set('X-RateLimit-Reset', result.reset.toString());
    }
    return response;
}
}}),
"[project]/lib/middleware/bruteForceProtection.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyBruteForceProtection": (()=>applyBruteForceProtection),
    "bruteForceProtectionMiddleware": (()=>bruteForceProtectionMiddleware),
    "calculateProgressiveDelay": (()=>calculateProgressiveDelay),
    "extractDeviceIdFromRequest": (()=>extractDeviceIdFromRequest),
    "extractEmailFromLoginRequest": (()=>extractEmailFromLoginRequest),
    "getClientIP": (()=>getClientIP)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/middleware/rateLimiter.ts [app-route] (ecmascript)");
;
;
function calculateProgressiveDelay(attemptCount) {
    if (attemptCount <= 3) return 0;
    // Base delay starts at 1 second for 4th attempt
    const baseDelay = Math.pow(2, attemptCount - 4) * 1000;
    // Cap at 30 seconds
    const cappedDelay = Math.min(baseDelay, 30000);
    // Add jitter (±20%) but ensure result doesn't exceed cap
    const jitter = cappedDelay * 0.2 * (Math.random() - 0.5);
    const finalDelay = cappedDelay + jitter;
    // Ensure final delay doesn't exceed the cap and is not negative
    return Math.floor(Math.max(0, Math.min(finalDelay, 30000)));
}
async function applyBruteForceProtection(req, context, config = {}) {
    const { maxLoginAttemptsPerIP = parseInt(process.env.BRUTE_FORCE_LOGIN_IP_LIMIT || '10', 10), loginWindowSeconds = parseInt(process.env.BRUTE_FORCE_LOGIN_WINDOW || '3600', 10), maxLoginAttemptsPerEmail = parseInt(process.env.BRUTE_FORCE_EMAIL_LIMIT || '5', 10), maxRefreshAttemptsPerDevice = parseInt(process.env.BRUTE_FORCE_REFRESH_LIMIT || '20', 10), refreshWindowSeconds = parseInt(process.env.BRUTE_FORCE_REFRESH_WINDOW || '3600', 10), enableProgressiveDelays = true } = config;
    const strategies = {};
    switch(context.operation){
        case 'login':
            // For login, we apply both IP-based and email-based rate limiting
            strategies.byIP = true;
            strategies.customLimits = {
                ip: {
                    maxRequests: maxLoginAttemptsPerIP,
                    windowSeconds: loginWindowSeconds,
                    identifier: 'login_ip'
                }
            };
            // If we have an email, add email-based rate limiting
            if (context.email) {
                strategies.byUser = true;
                strategies.userId = `email:${context.email}`;
                strategies.customLimits.user = {
                    maxRequests: maxLoginAttemptsPerEmail,
                    windowSeconds: loginWindowSeconds,
                    identifier: 'login_email'
                };
            }
            break;
        case 'refresh':
            // For token refresh, we apply device-based and IP-based rate limiting
            strategies.byIP = true;
            strategies.customLimits = {
                ip: {
                    maxRequests: maxRefreshAttemptsPerDevice * 2,
                    windowSeconds: refreshWindowSeconds,
                    identifier: 'refresh_ip'
                }
            };
            if (context.deviceId) {
                strategies.byDevice = true;
                strategies.deviceId = context.deviceId;
                strategies.customLimits.device = {
                    maxRequests: maxRefreshAttemptsPerDevice,
                    windowSeconds: refreshWindowSeconds,
                    identifier: 'refresh_device'
                };
            }
            break;
        case 'device_register':
            // For device registration, apply IP-based rate limiting
            strategies.byIP = true;
            strategies.customLimits = {
                ip: {
                    maxRequests: 5,
                    windowSeconds: 3600,
                    identifier: 'device_register_ip'
                }
            };
            break;
        default:
            return {
                success: false,
                error: 'Invalid operation type',
                status: 400
            };
    }
    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$rateLimiter$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["applyRateLimiting"])(req, strategies);
    // If rate limiting failed and progressive delays are enabled,
    // add delay information to the result
    if (!result.success && enableProgressiveDelays) {
        const attemptCount = (result.limit || 0) - (result.remaining || 0);
        const delay = calculateProgressiveDelay(attemptCount);
        if (delay > 0) {
            result.retryAfter = Math.max(result.retryAfter || 0, Math.ceil(delay / 1000));
        }
    }
    return result;
}
async function bruteForceProtectionMiddleware(req, context, config = {}) {
    const result = await applyBruteForceProtection(req, context, config);
    if (!result.success) {
        const response = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: result.error,
            retryAfter: result.retryAfter,
            message: 'Too many attempts. Please try again later.'
        }), {
            status: result.status || 429,
            headers: {
                'Content-Type': 'application/json',
                'X-RateLimit-Limit': result.limit?.toString() || '',
                'X-RateLimit-Remaining': result.remaining?.toString() || '0',
                'X-RateLimit-Reset': result.reset?.toString() || '',
                'Retry-After': result.retryAfter?.toString() || ''
            }
        });
        return response;
    }
    return null; // Continue to next middleware/handler
}
function extractEmailFromLoginRequest(body) {
    try {
        if (typeof body === 'object' && body !== null && typeof body.email === 'string') {
            return body.email.toLowerCase().trim();
        }
    } catch (error) {
    // Ignore parsing errors
    }
    return undefined;
}
function extractDeviceIdFromRequest(body) {
    try {
        if (typeof body === 'object' && body !== null && typeof body.deviceId === 'string') {
            return body.deviceId;
        }
    } catch (error) {
    // Ignore parsing errors
    }
    return undefined;
}
function getClientIP(req) {
    const forwarded = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    const remoteAddress = req.headers.get('x-remote-address');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    return realIP || remoteAddress || 'unknown';
}
}}),
"[project]/lib/security/hmac.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "extractHMACHeaders": (()=>extractHMACHeaders),
    "generateHMACSignature": (()=>generateHMACSignature),
    "validateTimestamp": (()=>validateTimestamp),
    "verifyHMACSignature": (()=>verifyHMACSignature)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
function generateHMACSignature(method, path, timestamp, body, deviceSecret) {
    // 1. Create SHA256 hash of the request body
    const bodyHash = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHash('sha256').update(body || '').digest('hex');
    // 2. Create the string to be signed: method + path + timestamp + bodyHash
    const stringToSign = `${method.toUpperCase()}${path}${timestamp}${bodyHash}`;
    // 3. Generate HMAC-SHA256 signature using device secret
    const signature = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].createHmac('sha256', deviceSecret).update(stringToSign).digest('base64');
    return signature;
}
function verifyHMACSignature(receivedSignature, expectedSignature) {
    // Convert to buffers for constant-time comparison
    const receivedBuffer = Buffer.from(receivedSignature, 'base64');
    const expectedBuffer = Buffer.from(expectedSignature, 'base64');
    // Ensure both signatures are the same length to prevent timing attacks
    if (receivedBuffer.length !== expectedBuffer.length) {
        return false;
    }
    // Use crypto.timingSafeEqual for constant-time comparison
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].timingSafeEqual(receivedBuffer, expectedBuffer);
}
function validateTimestamp(timestamp, windowSeconds = 120) {
    const requestTime = parseInt(timestamp, 10);
    const currentTime = Date.now();
    const windowMs = windowSeconds * 1000;
    // Check if timestamp is a valid number
    if (isNaN(requestTime)) {
        return false;
    }
    // Check if request is within the allowed time window
    const timeDiff = Math.abs(currentTime - requestTime);
    return timeDiff <= windowMs;
}
function extractHMACHeaders(headers) {
    const deviceId = typeof headers.get === 'function' ? headers.get('x-device-id') : headers['x-device-id'];
    const timestamp = typeof headers.get === 'function' ? headers.get('x-timestamp') : headers['x-timestamp'];
    const signature = typeof headers.get === 'function' ? headers.get('x-signature') : headers['x-signature'];
    if (!deviceId || !timestamp || !signature) {
        return null;
    }
    return {
        deviceId,
        timestamp,
        signature
    };
}
}}),
"[project]/utils/supabase/service-role.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createServiceRoleClient": (()=>createServiceRoleClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
function createServiceRoleClient() {
    const supabaseUrl = ("TURBOPACK compile-time value", "https://rnjolcoecogzgglnblqn.supabase.co");
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    if (!supabaseUrl || !supabaseServiceRoleKey) {
        throw new Error('Missing required Supabase environment variables for service role');
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceRoleKey, {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    });
}
}}),
"[project]/lib/middleware/hmac.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "hmacMiddleware": (()=>hmacMiddleware),
    "verifyHMACMiddleware": (()=>verifyHMACMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/security/hmac.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/service-role.ts [app-route] (ecmascript)");
;
;
;
async function verifyHMACMiddleware(req, requireHMAC = true) {
    try {
        // Skip HMAC verification if not required (e.g., for login endpoint)
        if (!requireHMAC) {
            return {
                success: true
            };
        }
        // 1. Extract required headers
        const hmacHeaders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractHMACHeaders"])(req.headers);
        if (!hmacHeaders) {
            return {
                success: false,
                error: 'Missing required headers: X-Device-Id, X-Timestamp, or X-Signature',
                status: 400
            };
        }
        const { deviceId, timestamp, signature } = hmacHeaders;
        // 2. Validate timestamp to prevent replay attacks
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateTimestamp"])(timestamp)) {
            return {
                success: false,
                error: 'Request has expired',
                status: 408
            };
        }
        // 3. Fetch device from database
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$service$2d$role$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServiceRoleClient"])();
        const { data: device, error: deviceError } = await supabase.from('devices').select('device_id, device_secret_hash, hmac_key_hash, revoked').eq('device_id', deviceId).single();
        if (deviceError || !device) {
            return {
                success: false,
                error: 'Invalid device ID',
                status: 403
            };
        }
        // 4. Check if device is revoked
        if (device.revoked) {
            return {
                success: false,
                error: 'Device has been revoked',
                status: 403
            };
        }
        // 5. Get request body for signature verification
        let requestBody = '';
        try {
            // Clone the request to read the body without consuming it
            const clonedReq = req.clone();
            requestBody = await clonedReq.text();
        } catch (error) {
            requestBody = '';
        }
        // 6. Generate expected signature using stored HMAC key
        const method = req.method;
        const path = new URL(req.url).pathname;
        const expectedSignature = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generateHMACSignature"])(method, path, timestamp, requestBody, device.hmac_key_hash // Use the stored HMAC key
        );
        // 7. Verify signature using constant-time comparison
        const isValidSignature = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$security$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyHMACSignature"])(signature, expectedSignature);
        if (!isValidSignature) {
            return {
                success: false,
                error: 'Invalid signature',
                status: 403
            };
        }
        // 8. HMAC verification successful
        return {
            success: true,
            deviceId: deviceId
        };
    } catch (error) {
        console.error('Unexpected error in HMAC verification:', error);
        return {
            success: false,
            error: 'Internal Server Error',
            status: 500
        };
    }
}
async function hmacMiddleware(req, requireHMAC = true) {
    const result = await verifyHMACMiddleware(req, requireHMAC);
    if (!result.success) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: result.error
        }), {
            status: result.status || 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    return null; // Continue to next middleware/handler
}
}}),
"[project]/app/api/products/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/utils/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zod/lib/index.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$bruteForceProtection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/middleware/bruteForceProtection.ts [app-route] (ecmascript)");
(()=>{
    const e = new Error("Cannot find module '@/lib/middleware/jwt'");
    e.code = 'MODULE_NOT_FOUND';
    throw e;
})();
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/middleware/hmac.ts [app-route] (ecmascript)");
;
;
;
;
;
;
/**
 * Security middleware wrapper for products API routes
 */ async function applySecurityMiddleware(req, requireHMAC = true) {
    // 1. Apply rate limiting and brute force protection
    const ipAddress = (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$bruteForceProtection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getClientIP"])(req);
    const bruteForceCheck = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$bruteForceProtection$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["bruteForceProtectionMiddleware"])(req, {
        operation: 'products_api',
        ipAddress
    });
    if (bruteForceCheck) {
        return bruteForceCheck;
    }
    // 2. Verify JWT token
    const token = extractBearerToken(req);
    if (!token) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: 'Authorization token required'
        }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    const jwtResult = await verifyJWTToken(token);
    if (!jwtResult.success) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: jwtResult.error
        }), {
            status: 401,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    // 3. Verify HMAC signature if required
    if (requireHMAC) {
        const hmacResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$middleware$2f$hmac$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["verifyHMACMiddleware"])(req, requireHMAC);
        if (!hmacResult.success) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: hmacResult.error
            }), {
                status: hmacResult.status || 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
    }
    return {
        success: true,
        jwtPayload: jwtResult.payload
    };
}
// Schema for listing products
const listProductsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    page: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().transform((val)=>parseInt(val, 10)).pipe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1)).optional(),
    limit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().transform((val)=>parseInt(val, 10)).pipe(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(1).max(100)).optional(),
    search: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    business_id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    product_type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
        "physical",
        "service"
    ]).optional(),
    is_available: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().transform((val)=>val === 'true').optional(),
    sort_by: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
        "name_asc",
        "name_desc",
        "created_asc",
        "created_desc",
        "price_asc",
        "price_desc"
    ]).optional(),
    pincode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    locality: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    city: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    state: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional()
});
async function GET(req) {
    try {
        // Apply security middleware (no HMAC required for public product listing)
        const securityResult = await applySecurityMiddleware(req, false);
        if (securityResult instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"]) {
            return securityResult;
        }
        // Parse and validate query parameters
        const url = new URL(req.url);
        const queryParams = Object.fromEntries(url.searchParams.entries());
        const validation = listProductsSchema.safeParse(queryParams);
        if (!validation.success) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: 'Invalid query parameters',
                details: validation.error.errors
            }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        const { page = 1, limit = 20, search, category, business_id, product_type, is_available = true, sort_by = "created_desc", pincode, locality, city, state } = validation.data;
        const offset = (page - 1) * limit;
        // Build query
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        let query = supabase.from('products_services').select(`
        id, business_id, name, description, base_price, discounted_price,
        product_type, is_available, image_url, images, featured_image_index,
        slug, created_at, updated_at,
        business_profiles!business_id(
          id, business_name, business_slug, city, state, pincode, locality,
          logo_url, status
        )
      `, {
            count: 'exact'
        }).eq('is_available', is_available);
        // Add filters
        if (search) {
            query = query.ilike('name', `%${search}%`);
        }
        if (business_id) {
            query = query.eq('business_id', business_id);
        }
        if (product_type) {
            query = query.eq('product_type', product_type);
        }
        // Location filters (filter by business location)
        if (pincode) {
            query = query.eq('business_profiles.pincode', pincode);
        }
        if (locality) {
            query = query.eq('business_profiles.locality', locality);
        }
        if (city) {
            query = query.eq('business_profiles.city', city);
        }
        if (state) {
            query = query.eq('business_profiles.state', state);
        }
        // Only show products from online businesses
        query = query.eq('business_profiles.status', 'online');
        // Add sorting
        const [sortField, sortDirection] = sort_by.split('_');
        const ascending = sortDirection === 'asc';
        let orderColumn = sortField;
        if (sortField === 'created') {
            orderColumn = 'created_at';
        } else if (sortField === 'price') {
            orderColumn = 'base_price';
        }
        query = query.order(orderColumn, {
            ascending
        });
        // Add pagination
        query = query.range(offset, offset + limit - 1);
        const { data: products, error, count } = await query;
        if (error) {
            console.error('Error fetching products:', error);
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: 'Failed to fetch products'
            }), {
                status: 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        const totalCount = count || 0;
        const hasMore = offset + limit < totalCount;
        const nextPage = hasMore ? page + 1 : null;
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            products: products || [],
            pagination: {
                page,
                limit,
                total: totalCount,
                hasMore,
                nextPage
            }
        }), {
            status: 200,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('Unexpected error in products API:', error);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: 'Internal server error'
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
// Schema for creating products
const createProductSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].object({
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().min(1).max(255),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional(),
    base_price: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(0),
    discounted_price: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(0).optional(),
    product_type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].enum([
        "physical",
        "service"
    ]),
    is_available: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].boolean().optional().default(true),
    image_url: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().url().optional(),
    images: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().url()).optional(),
    featured_image_index: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].number().min(0).optional(),
    slug: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$lib$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["z"].string().optional()
});
async function POST(req) {
    try {
        // Apply security middleware (HMAC required for mutations)
        const securityResult = await applySecurityMiddleware(req, true);
        if (securityResult instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"]) {
            return securityResult;
        }
        const { jwtPayload } = securityResult;
        // Parse and validate request body
        const body = await req.json();
        const validation = createProductSchema.safeParse(body);
        if (!validation.success) {
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: 'Invalid request body',
                details: validation.error.errors
            }), {
                status: 400,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        const productData = validation.data;
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        // Create the product
        const { data: product, error } = await supabase.from('products_services').insert({
            ...productData,
            business_id: jwtPayload.sub
        }).select().single();
        if (error) {
            console.error('Error creating product:', error);
            return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
                error: 'Failed to create product'
            }), {
                status: 500,
                headers: {
                    'Content-Type': 'application/json'
                }
            });
        }
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            product,
            message: 'Product created successfully'
        }), {
            status: 201,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    } catch (error) {
        console.error('Unexpected error in create product API:', error);
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"](JSON.stringify({
            error: 'Internal server error'
        }), {
            status: 500,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__7e2a7107._.js.map